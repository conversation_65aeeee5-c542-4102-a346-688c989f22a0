"""
Unit tests for the validation module.
"""

import unittest
import tempfile
import os
from pathlib import Path

from absolute_zero_reasoner.validation import (
    TypeRule, RangeRule, RegexRule, LengthRule, ChoiceRule, PathRule, SecurityRule,
    Validator, ConfigValidator, create_training_config_validator,
    create_code_execution_validator, sanitize_string
)
from absolute_zero_reasoner.exceptions import ValidationError, SecurityError


class TestValidationRules(unittest.TestCase):
    """Test individual validation rules."""

    def test_type_rule(self):
        """Test TypeRule validation."""
        rule = TypeRule(int)

        self.assertTrue(rule.validate(42))
        self.assertTrue(rule.validate(0))
        self.assertTrue(rule.validate(-10))

        self.assertFalse(rule.validate("42"))
        self.assertFalse(rule.validate(42.0))
        self.assertFalse(rule.validate([42]))

    def test_range_rule(self):
        """Test RangeRule validation."""
        rule = RangeRule(0, 100)

        self.assertTrue(rule.validate(0))
        self.assertTrue(rule.validate(50))
        self.assertTrue(rule.validate(100))

        self.assertFalse(rule.validate(-1))
        self.assertFalse(rule.validate(101))
        self.assertFalse(rule.validate("50"))

    def test_range_rule_unbounded(self):
        """Test RangeRule with unbounded limits."""
        min_only = RangeRule(min_val=0)
        self.assertTrue(min_only.validate(0))
        self.assertTrue(min_only.validate(1000))
        self.assertFalse(min_only.validate(-1))

        max_only = RangeRule(max_val=100)
        self.assertTrue(max_only.validate(100))
        self.assertTrue(max_only.validate(-1000))
        self.assertFalse(max_only.validate(101))

    def test_regex_rule(self):
        """Test RegexRule validation."""
        email_rule = RegexRule(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')

        self.assertTrue(email_rule.validate("<EMAIL>"))
        self.assertTrue(email_rule.validate("<EMAIL>"))

        self.assertFalse(email_rule.validate("invalid-email"))
        self.assertFalse(email_rule.validate("@example.com"))
        self.assertFalse(email_rule.validate("test@"))
        self.assertFalse(email_rule.validate(123))

    def test_length_rule(self):
        """Test LengthRule validation."""
        rule = LengthRule(3, 10)

        self.assertTrue(rule.validate("hello"))
        self.assertTrue(rule.validate("abc"))
        self.assertTrue(rule.validate("1234567890"))
        self.assertTrue(rule.validate([1, 2, 3, 4, 5]))

        self.assertFalse(rule.validate("hi"))
        self.assertFalse(rule.validate("this is too long"))
        self.assertFalse(rule.validate([1, 2]))
        self.assertFalse(rule.validate(123))  # No __len__ method

    def test_choice_rule(self):
        """Test ChoiceRule validation."""
        rule = ChoiceRule(['red', 'green', 'blue'])

        self.assertTrue(rule.validate('red'))
        self.assertTrue(rule.validate('green'))
        self.assertTrue(rule.validate('blue'))

        self.assertFalse(rule.validate('yellow'))
        self.assertFalse(rule.validate('Red'))  # Case sensitive
        self.assertFalse(rule.validate(1))

    def test_path_rule(self):
        """Test PathRule validation."""
        # Test basic path validation
        rule = PathRule()

        self.assertTrue(rule.validate("/path/to/file"))
        self.assertTrue(rule.validate("relative/path"))
        self.assertTrue(rule.validate(Path("/path/to/file")))

        self.assertFalse(rule.validate(123))
        self.assertFalse(rule.validate(None))

        # Test with temporary file
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp_path = tmp.name

        try:
            exist_rule = PathRule(must_exist=True)
            self.assertTrue(exist_rule.validate(tmp_path))

            file_rule = PathRule(must_exist=True, must_be_file=True)
            self.assertTrue(file_rule.validate(tmp_path))

            dir_rule = PathRule(must_exist=True, must_be_dir=True)
            self.assertFalse(dir_rule.validate(tmp_path))
        finally:
            os.unlink(tmp_path)

    def test_security_rule(self):
        """Test SecurityRule validation."""
        rule = SecurityRule()

        # Safe code
        self.assertTrue(rule.validate("print('hello world')"))
        self.assertTrue(rule.validate("def fibonacci(n): return n"))
        self.assertTrue(rule.validate("x = 1 + 2"))
        self.assertTrue(rule.validate(123))  # Non-string values pass

        # Dangerous code
        self.assertFalse(rule.validate("exec('malicious code')"))
        self.assertFalse(rule.validate("__import__('os')"))
        self.assertFalse(rule.validate("import os; os.system('rm -rf /')"))
        self.assertFalse(rule.validate("eval('1+1')"))
        self.assertFalse(rule.validate("open('/etc/passwd')"))


class TestValidator(unittest.TestCase):
    """Test the Validator class."""

    def setUp(self):
        """Set up test fixtures."""
        self.age_validator = Validator([
            TypeRule(int),
            RangeRule(0, 150)
        ])

    def test_valid_input(self):
        """Test validation with valid input."""
        # Should not raise any exception
        self.age_validator.validate(25, "age")
        self.age_validator.validate(0, "age")
        self.age_validator.validate(150, "age")

    def test_invalid_type(self):
        """Test validation with invalid type."""
        with self.assertRaises(ValidationError) as cm:
            self.age_validator.validate("25", "age")

        self.assertIn("Expected type int", str(cm.exception))
        self.assertEqual(cm.exception.field, "age")

    def test_invalid_range(self):
        """Test validation with invalid range."""
        with self.assertRaises(ValidationError) as cm:
            self.age_validator.validate(-5, "age")

        self.assertIn("Value must be between 0 and 150", str(cm.exception))

    def test_is_valid(self):
        """Test is_valid method."""
        self.assertTrue(self.age_validator.is_valid(25))
        self.assertFalse(self.age_validator.is_valid("25"))
        self.assertFalse(self.age_validator.is_valid(-5))

    def test_security_error(self):
        """Test that SecurityRule raises SecurityError."""
        security_validator = Validator([SecurityRule()])

        with self.assertRaises(SecurityError) as cm:
            security_validator.validate("exec('code')", "code")

        self.assertIn("dangerous content", str(cm.exception))


class TestConfigValidator(unittest.TestCase):
    """Test the ConfigValidator class."""

    def setUp(self):
        """Set up test fixtures."""
        self.config_validator = create_training_config_validator()

    def test_valid_config(self):
        """Test validation with valid configuration."""
        valid_config = {
            "learning_rate": 0.001,
            "batch_size": 32,
            "epochs": 100,
            "model_path": "/path/to/model",
            "optimizer": "adam"
        }

        # Should not raise any exception
        self.config_validator.validate_config(valid_config)
        self.assertTrue(self.config_validator.is_valid_config(valid_config))

    def test_missing_required_field(self):
        """Test validation with missing required field."""
        invalid_config = {
            "learning_rate": 0.001,
            "batch_size": 32,
            # Missing epochs and model_path
        }

        with self.assertRaises(ValidationError) as cm:
            self.config_validator.validate_config(invalid_config)

        self.assertIn("Required field", str(cm.exception))
        self.assertFalse(self.config_validator.is_valid_config(invalid_config))

    def test_invalid_field_value(self):
        """Test validation with invalid field value."""
        invalid_config = {
            "learning_rate": -0.001,  # Invalid: negative
            "batch_size": 32,
            "epochs": 100,
            "model_path": "/path/to/model"
        }

        with self.assertRaises(ValidationError) as cm:
            self.config_validator.validate_config(invalid_config)

        self.assertIn("learning_rate", str(cm.exception))

    def test_invalid_choice(self):
        """Test validation with invalid choice."""
        invalid_config = {
            "learning_rate": 0.001,
            "batch_size": 32,
            "epochs": 100,
            "model_path": "/path/to/model",
            "optimizer": "invalid_optimizer"
        }

        with self.assertRaises(ValidationError) as cm:
            self.config_validator.validate_config(invalid_config)

        self.assertIn("optimizer", str(cm.exception))


class TestSanitization(unittest.TestCase):
    """Test string sanitization functions."""

    def test_sanitize_normal_string(self):
        """Test sanitization of normal strings."""
        result = sanitize_string("Hello, World!")
        self.assertEqual(result, "Hello, World!")

    def test_sanitize_dangerous_content(self):
        """Test sanitization of dangerous content."""
        dangerous = "<script>alert('xss')</script>Hello"
        result = sanitize_string(dangerous)
        self.assertEqual(result, "Hello")
        self.assertNotIn("<script>", result)

    def test_sanitize_long_string(self):
        """Test sanitization of overly long strings."""
        long_string = "A" * 2000
        result = sanitize_string(long_string, max_length=100)
        self.assertEqual(len(result), 100)
        self.assertEqual(result, "A" * 100)

    def test_sanitize_control_characters(self):
        """Test removal of control characters."""
        with_control = "Hello\x00\x01World\x1f"
        result = sanitize_string(with_control)
        self.assertEqual(result, "HelloWorld")

    def test_sanitize_non_string(self):
        """Test sanitization of non-string input."""
        result = sanitize_string(123)
        self.assertEqual(result, "123")


class TestCodeExecutionValidator(unittest.TestCase):
    """Test code execution validator."""

    def setUp(self):
        """Set up test fixtures."""
        self.code_validator = create_code_execution_validator()

    def test_safe_code(self):
        """Test validation of safe code."""
        safe_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
        """

        # Should not raise any exception
        self.code_validator.validate(safe_code, "code")
        self.assertTrue(self.code_validator.is_valid(safe_code))

    def test_dangerous_code(self):
        """Test validation of dangerous code."""
        dangerous_code = "import os; os.system('rm -rf /')"

        with self.assertRaises(SecurityError):
            self.code_validator.validate(dangerous_code, "code")

        self.assertFalse(self.code_validator.is_valid(dangerous_code))

    def test_code_with_exec(self):
        """Test validation of code with exec."""
        exec_code = "exec('print(hello)')"

        with self.assertRaises(SecurityError):
            self.code_validator.validate(exec_code, "code")

    def test_empty_code(self):
        """Test validation of empty code."""
        with self.assertRaises(ValidationError):
            self.code_validator.validate("", "code")

    def test_very_long_code(self):
        """Test validation of very long code."""
        long_code = "print('hello')\n" * 10000  # Very long code

        with self.assertRaises(ValidationError):
            self.code_validator.validate(long_code, "code")


if __name__ == '__main__':
    unittest.main()