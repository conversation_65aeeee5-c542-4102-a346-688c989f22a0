{"snippet": "def f(a):\n    return a", "input": "\"Hello world\"", "output": "\"Hello world\"", "imports": [], "original_snippet": "def f(a):\n    return a", "composite_functions": []}
{"snippet": "def f(c):\n    for i in c:\n        if i == '0':\n            return True\n        elif i == '1':\n            return False\n        else:\n            return f(eval(i))", "input": "\"100 + 20 + 30 - 40 - 50 * 60\"", "output": "False", "imports": [], "original_snippet": "def f(c):\n    for i in c:\n        if i == '0':\n            return True\n        elif i == '1':\n            return False\n        else:\n            return f(eval(i))", "composite_functions": [], "_input_type": "str", "_output_type": "bool"}
{"snippet": "def f(a, b):\n    return a * b", "input": "'moisturizing hand cream for dry skin', 20", "output": "'moisturizing hand cream for dry skinmoisturizing hand cream for dry skinmoisturizing hand cream for dry skinmoisturizing hand cream for dry skinmoisturizing hand cream for dry skinmoisturizing hand c... cream for dry skinmoisturizing hand cream for dry skinmoisturizing hand cream for dry skinmoisturizing hand cream for dry skinmoisturizing hand cream for dry skinmoisturizing hand cream for dry skin'", "imports": [], "original_snippet": "def f(a, b):\n    return a * b", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def has_direct_redundancy_in_lines(statement, symbol):\n    for line in f(statement):\n        if has_redundancy(line, symbol):\n            return True\n    return False\ndef has_redundancy(line, symbol):\n    parts = line.strip().split(' ')\n    (p0, p1, p2) = parts\n    if p0 == symbol and p1 == '=' and (p2 == p1):\n        return True\n    return False\ndef f(statement):\n    for lines in statement.split('\\n'):\n        if lines.strip() != '':\n            yield lines", "input": "\"I would like to order an apple pie, a green salad, and a rose\"", "output": "", "imports": [], "original_snippet": "def has_direct_redundancy_in_lines(statement, symbol):\n    for line in f(statement):\n        if has_redundancy(line, symbol):\n            return True\n    return False\ndef has_redundancy(line, symbol):\n    parts = line.strip().split(' ')\n    (p0, p1, p2) = parts\n    if p0 == symbol and p1 == '=' and (p2 == p1):\n        return True\n    return False\ndef f(statement):\n    for lines in statement.split('\\n'):\n        if lines.strip() != '':\n            yield lines", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(a):\n    return a", "input": "\"Hello world\"", "output": "'Hello world'", "imports": [], "original_snippet": "def f(a):\n    return a", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(n: int) -> int:\n    return n ** 2", "input": "3", "output": "9", "imports": [], "original_snippet": "def f(n: int) -> int:\n    return n ** 2", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(a):\n    return a", "input": "\"Hello world\"", "output": "'Hello world'", "imports": [], "original_snippet": "def f(a):\n    return a", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(n: int) -> str:\n    if n == 0:\n        return \"I'm no longer asking you\"\n    else:\n        return 'My next question is: ' + str(n) + '?'", "input": "\"My chess game just started. I now need to move my Knight to the square B5. Please tell me all of your moves.\"", "output": "'My next question is: My chess game just started. I now need to move my Knight to the square B5. Please tell me all of your moves.?'", "imports": [], "original_snippet": "def f(n: int) -> str:\n    if n == 0:\n        return \"I'm no longer asking you\"\n    else:\n        return 'My next question is: ' + str(n) + '?'", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(a, b, c, d, e):\n    return a * (b + c) // d - e * 2", "input": "1, 2, 3, 4, 5", "output": "-9", "imports": [], "original_snippet": "def f(a, b, c, d, e):\n    return a * (b + c) // d - e * 2", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(x: str) -> tuple:\n    return (x[::-1], x[::-1][-1::-2], x[::-1][1::2])", "input": "'The earth revolves around the sun'", "output": "('nus eht dnuora sevlover htrae ehT', 'Teerhrvle rudtesn', 'u h noasvoe ta h')", "imports": [], "original_snippet": "def f(x: str) -> tuple:\n    return (x[::-1], x[::-1][-1::-2], x[::-1][1::2])", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(a, b):\n    c = 100\n    d = 200\n    e = 30\n    return c + d + e // (a + b ** 2) * b", "input": "10, 20", "output": "300", "imports": [], "original_snippet": "def f(a, b):\n    c = 100\n    d = 200\n    e = 30\n    return c + d + e // (a + b ** 2) * b", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(a, b, c):\n    ...", "input": "1, 2, 3", "output": "None", "imports": [], "original_snippet": "def f(a, b, c):\n    ...", "composite_functions": [], "_input_type": "tuple", "_output_type": "NoneType"}
{"snippet": "def f(a: list, b: list, c: list):\n    return sum([min(a[i], b[i], c[i]) for i in range(len(a))])", "input": "[2, 4, 6, 7, 9], [1, 3, 4, 6, 9], [1, 2, 3, 4, 5]", "output": "15", "imports": [], "original_snippet": "def f(a: list, b: list, c: list):\n    return sum([min(a[i], b[i], c[i]) for i in range(len(a))])", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(nums):\n    if len(nums) == 0:\n        return []\n    if len(nums) == 1:\n        return [nums[0]] * 2\n    half = int(len(nums) / 2)\n    left = f(nums[:half])\n    right = f(nums[half:])\n    ans = []\n    i = 0\n    j = 0\n    while i < len(left) and j < len(right):\n        if left[i] == right[j]:\n            ans.append(2 * left[i])\n            i += 1\n            j += 1\n        elif left[i] > right[j]:\n            ans.append(right[j])\n            j += 1\n        else:\n            ans.append(left[i])\n            i += 1\n    while i < len(left):\n        ans.append(left[i])\n        i += 1\n    while j < len(right):\n        ans.append(right[j])\n        j += 1\n    return ans", "input": "[1,2,2,3,3,3]", "output": "[1, 1, 3, 3, 4, 4, 6, 6]", "imports": [], "original_snippet": "def f(nums):\n    if len(nums) == 0:\n        return []\n    if len(nums) == 1:\n        return [nums[0]] * 2\n    half = int(len(nums) / 2)\n    left = f(nums[:half])\n    right = f(nums[half:])\n    ans = []\n    i = 0\n    j = 0\n    while i < len(left) and j < len(right):\n        if left[i] == right[j]:\n            ans.append(2 * left[i])\n            i += 1\n            j += 1\n        elif left[i] > right[j]:\n            ans.append(right[j])\n            j += 1\n        else:\n            ans.append(left[i])\n            i += 1\n    while i < len(left):\n        ans.append(left[i])\n        i += 1\n    while j < len(right):\n        ans.append(right[j])\n        j += 1\n    return ans", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(n: int) -> int:\n    if n == 0:\n        return 0\n    elif n > 0:\n        return f(n - 1) + 1\n    else:\n        return f(n - 1) - 1", "input": "6", "output": "6", "imports": [], "original_snippet": "def f(n: int) -> int:\n    if n == 0:\n        return 0\n    elif n > 0:\n        return f(n - 1) + 1\n    else:\n        return f(n - 1) - 1", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(a, b, c):\n    pass", "input": "1, 3, 2", "output": "None", "imports": [], "original_snippet": "def f(a, b, c):\n    pass", "composite_functions": [], "_input_type": "tuple", "_output_type": "NoneType"}
{"snippet": "def f(x):\n    if x < 0:\n        return False\n    if x == 0:\n        return True\n    while x > 0:\n        x -= 1\n        if x == 0:\n            return x\n        if x % 3 == 0:\n            return x\n        for _ in range(1, x - 1):\n            if _ % 3 == 0:\n                return _\n    return False", "input": "20", "output": "3", "imports": [], "original_snippet": "def f(x):\n    if x < 0:\n        return False\n    if x == 0:\n        return True\n    while x > 0:\n        x -= 1\n        if x == 0:\n            return x\n        if x % 3 == 0:\n            return x\n        for _ in range(1, x - 1):\n            if _ % 3 == 0:\n                return _\n    return False", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(a):\n    return len(a)", "input": "\"Hello world\"", "output": "11", "imports": [], "original_snippet": "def f(a):\n    return len(a)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(n):\n    num = 6\n    L = [1 for i in range(num)]\n    for i in range(1, num):\n        t = sum(L[:i])\n        if n / t >= L[i]:\n            for _ in range(L[i]):\n                L.append(n // t)\n    for _ in range(len(L) - num):\n        L.append(0)\n    return L", "input": "100", "output": "[1, 1, 1, 1, 1, 1, 100, 50, 33, 25, 20, 0, 0, 0, 0, 0]", "imports": [], "original_snippet": "def f(n):\n    num = 6\n    L = [1 for i in range(num)]\n    for i in range(1, num):\n        t = sum(L[:i])\n        if n / t >= L[i]:\n            for _ in range(L[i]):\n                L.append(n // t)\n    for _ in range(len(L) - num):\n        L.append(0)\n    return L", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(pocc, maar, veenni):\n    return pocc and (not veenni) or maar or f(5, 6, 7)", "input": "\"104\", \"35\", \"56\"", "output": "'35'", "imports": [], "original_snippet": "def f(pocc, maar, veenni):\n    return pocc and (not veenni) or maar or f(5, 6, 7)", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "import re\ndef f(text):\n    snippets = re.split('(bad|bad word|junk|dirt(ypop)?)', text)\n    merged = []\n    for snippet in snippets:\n        if snippet not in ('', ' ', '  ', '   ', '\\n'):\n            merged.append(snippet)\n    return ''.join(merged)", "input": "\"I would like to order an apple pie, a green salad, and a rose\"", "output": "'I would like to order an apple pie, a green salad, and a rose'", "imports": ["import re"], "original_snippet": "import re\ndef f(text):\n    snippets = re.split('(bad|bad word|junk|dirt(ypop)?)', text)\n    merged = []\n    for snippet in snippets:\n        if snippet not in ('', ' ', '  ', '   ', '\\n'):\n            merged.append(snippet)\n    return ''.join(merged)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(a, b, c):\n    pass", "input": "1, 4, 7", "output": "None", "imports": [], "original_snippet": "def f(a, b, c):\n    pass", "composite_functions": [], "_input_type": "tuple", "_output_type": "NoneType"}
{"snippet": "def f(name: int, info: dict):\n    if name == 'John':\n        score = info.get('age')\n    else:\n        score = 0\n    return score", "input": "'John', {'age': 20, 'city': 'New York'}", "output": "20", "imports": [], "original_snippet": "def f(name: int, info: dict):\n    if name == 'John':\n        score = info.get('age')\n    else:\n        score = 0\n    return score", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "from typing import List, Dict\nclass StudentInfo(dict):\n    def __init__(self, name: str, age: int, city: str):\n        self['name'] = name\n        self['age'] = age\n        self['city'] = city\ndef f(student_info: StudentInfo) -> str:\n    result: str = ''\n    for i in range(5):\n        name: str = student_info['name']\n        age: int = student_info['age']\n        city: str = student_info['city']\n        if name == 'John' and age >= 20 and (city == 'New York'):\n            result += 'John is a senior student from New York City, currently taking a course on...'\n        else:\n            result += f'{name} is a student from {city}, studied for {age} years before enrolling...'\n    return result", "input": "{\n    \"name\": \"John\", \n    \"age\": 20, \n    \"city\": \"New York\"\n}", "output": "'John is a senior student from New York City, currently taking a course on...John is a senior student from New York City, currently taking a course on...John is a senior student from New York City, currently taking a course on...John is a senior student from New York City, currently taking a course on...John is a senior student from New York City, currently taking a course on...'", "imports": ["from typing import List, Dict"], "original_snippet": "from typing import List, Dict\nclass StudentInfo(dict):\n    def __init__(self, name: str, age: int, city: str):\n        self['name'] = name\n        self['age'] = age\n        self['city'] = city\ndef f(student_info: StudentInfo) -> str:\n    result: str = ''\n    for i in range(5):\n        name: str = student_info['name']\n        age: int = student_info['age']\n        city: str = student_info['city']\n        if name == 'John' and age >= 20 and (city == 'New York'):\n            result += 'John is a senior student from New York City, currently taking a course on...'\n        else:\n            result += f'{name} is a student from {city}, studied for {age} years before enrolling...'\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "str"}
{"snippet": "def f(k1, k2, a, b, c, d, e, f):\n    if k1 == k2 and k1 <= 0 and (k2 >= 1):\n        k1 = (k1 * 2 + c + e) // (2 * f)\n        k2 = (k1 * 2 - c + e) // (2 * f)\n        a = (k1 + b - c) // (2 * f)\n        b = (k2 - b + c) // (2 * f)\n        d = c + d + e\n        if a + b == d and a > 0 and (b < 0) and (e == 2):\n            return (k1, k2, a, b, c, d, e, f)\n    return None", "input": "-2, 1, 4, 5, 6, 7, 8, 9", "output": "None", "imports": [], "original_snippet": "def f(k1, k2, a, b, c, d, e, f):\n    if k1 == k2 and k1 <= 0 and (k2 >= 1):\n        k1 = (k1 * 2 + c + e) // (2 * f)\n        k2 = (k1 * 2 - c + e) // (2 * f)\n        a = (k1 + b - c) // (2 * f)\n        b = (k2 - b + c) // (2 * f)\n        d = c + d + e\n        if a + b == d and a > 0 and (b < 0) and (e == 2):\n            return (k1, k2, a, b, c, d, e, f)\n    return None", "composite_functions": [], "_input_type": "tuple", "_output_type": "NoneType"}
{"snippet": "def f(a, b):\n    sequence = [a, b]\n    while len(sequence) < 100:\n        sequence.append(sequence[-1] + sequence[-2])\n    return sequence", "input": "1, 2", "output": "[1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987, 1597, 2584, 4181, 6765, 10946, 17711, 28657, 46368, 75025, 121393, 196418, 317811, 514229, 832040, 1346269, 2178309, 3524578, 5702887, 9227...2200160415121876738, 19740274219868223167, 31940434634990099905, 51680708854858323072, 83621143489848422977, 135301852344706746049, 218922995834555169026, 354224848179261915075, 573147844013817084101]", "imports": [], "original_snippet": "def f(a, b):\n    sequence = [a, b]\n    while len(sequence) < 100:\n        sequence.append(sequence[-1] + sequence[-2])\n    return sequence", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(integer_input):\n    result_list = []\n    quotient = integer_input // 2\n    remainder = integer_input % 2\n    result_list.append(remainder)\n    while quotient != 0:\n        (quotient, remainder) = (quotient // 2, quotient % 2)\n        result_list.append(remainder)\n    return ''.join((str(int) for int in result_list[::-1]))", "input": "117", "output": "'1110101'", "imports": [], "original_snippet": "def f(integer_input):\n    result_list = []\n    quotient = integer_input // 2\n    remainder = integer_input % 2\n    result_list.append(remainder)\n    while quotient != 0:\n        (quotient, remainder) = (quotient // 2, quotient % 2)\n        result_list.append(remainder)\n    return ''.join((str(int) for int in result_list[::-1]))", "composite_functions": [], "_input_type": "int", "_output_type": "str"}
{"snippet": "def f(n: int) -> str:\n    if n == 0:\n        return \"I'm no longer asking you\"\n    else:\n        return 'My next question is:' + str(n) + '?'", "input": "\"My chess game just started. I now need to move my Knight to the square B5. Please tell me all of your moves.\"", "output": "'My next question is:My chess game just started. I now need to move my Knight to the square B5. Please tell me all of your moves.?'", "imports": [], "original_snippet": "def f(n: int) -> str:\n    if n == 0:\n        return \"I'm no longer asking you\"\n    else:\n        return 'My next question is:' + str(n) + '?'", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(x):\n    return x ** 3 - 2", "input": "f(1)", "output": "-3", "imports": [], "original_snippet": "def f(x):\n    return x ** 3 - 2", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(n):\n    (a, b) = (0, 1)\n    for __ in range(n):\n        (a, b) = (b, a + b)\n    return b", "input": "50", "output": "20365011074", "imports": [], "original_snippet": "def f(n):\n    (a, b) = (0, 1)\n    for __ in range(n):\n        (a, b) = (b, a + b)\n    return b", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(name: int, info: dict):\n    if name == 'John':\n        score = info.get('age')\n        if score < 20:\n            ...", "input": "'John', {'age': 20, 'city': 'New York'}", "output": "None", "imports": [], "original_snippet": "def f(name: int, info: dict):\n    if name == 'John':\n        score = info.get('age')\n        if score < 20:\n            ...", "composite_functions": [], "_input_type": "tuple", "_output_type": "NoneType"}
{"snippet": "def f(a, b, c):\n    if a > b:\n        if a > c:\n            return c\n        elif b > c:\n            return b\n        else:\n            return c\n    elif b > c:\n        return a\n    elif a > c:\n        return a\n    else:\n        return c", "input": "1, 2, 3", "output": "3", "imports": [], "original_snippet": "def f(a, b, c):\n    if a > b:\n        if a > c:\n            return c\n        elif b > c:\n            return b\n        else:\n            return c\n    elif b > c:\n        return a\n    elif a > c:\n        return a\n    else:\n        return c", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(n: int, d: str) -> str:\n    if n % 2 == 0:\n        d = '1' + d\n        if d == '111000':\n            return 'No repeat'\n        elif d[len(d) - 1] == '0':\n            return 'Yes'\n        else:\n            return f(n // 2, d)\n    else:\n        return 'No'", "input": "2, '111000'", "output": "'Yes'", "imports": [], "original_snippet": "def f(n: int, d: str) -> str:\n    if n % 2 == 0:\n        d = '1' + d\n        if d == '111000':\n            return 'No repeat'\n        elif d[len(d) - 1] == '0':\n            return 'Yes'\n        else:\n            return f(n // 2, d)\n    else:\n        return 'No'", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(two_liners: dict):\n    text1 = '' if len(two_liners['t1'].split()) != 3 else two_liners['t1']\n    text2 = '' if len(two_liners['t2'].split()) != 3 else two_liners['t1']\n    return text1 + text2", "input": "{'t1': 'Get your pen and paper buzzing for learning Arithmetic, Geometry, Rational Arithmetic, Polynomials, Graph Theory, and Linear Algebra', 't2': 'Join us on Udemy to watch a 20-minute video teaching Turing-style machine which is equivalent to a semi-automated computer next to the following  bill of materials'}", "output": "''", "imports": [], "original_snippet": "def f(two_liners: dict):\n    text1 = '' if len(two_liners['t1'].split()) != 3 else two_liners['t1']\n    text2 = '' if len(two_liners['t2'].split()) != 3 else two_liners['t1']\n    return text1 + text2", "composite_functions": [], "_input_type": "dict", "_output_type": "str"}
{"snippet": "def f(n):\n    if n < 1:\n        return 0\n    s = ''\n    for c in str(n):\n        s = c + s\n    return eval(s)", "input": "123", "output": "321", "imports": [], "original_snippet": "def f(n):\n    if n < 1:\n        return 0\n    s = ''\n    for c in str(n):\n        s = c + s\n    return eval(s)", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(s1, s2, s3):\n    pass", "input": "\"X\", \"W\", \"D\"", "output": "None", "imports": [], "original_snippet": "def f(s1, s2, s3):\n    pass", "composite_functions": [], "_input_type": "tuple", "_output_type": "NoneType"}
{"snippet": "def f(n):\n    x = 1\n    while x <= n:\n        y = x\n        while y < n:\n            z = y\n            while z < n:\n                if z ** 3 + 2 * y ** 2 == n:\n                    break\n                z += 1\n            if z == n:\n                break\n            y += 1\n        if y == n:\n            break\n        x += 1\n    return n - n // 2", "input": "109", "output": "55", "imports": [], "original_snippet": "def f(n):\n    x = 1\n    while x <= n:\n        y = x\n        while y < n:\n            z = y\n            while z < n:\n                if z ** 3 + 2 * y ** 2 == n:\n                    break\n                z += 1\n            if z == n:\n                break\n            y += 1\n        if y == n:\n            break\n        x += 1\n    return n - n // 2", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(s: str):\n    lst = []\n    current = 0\n    index = 0\n    while index != len(s):\n        if s[index] is ']':\n            lst = lst[::-1]\n            index = index + 1\n        else:\n            current = current + 1\n            lst.insert(current, s[index])\n            index = index + 1\n    return lst", "input": "'[1, 2, 3]'", "output": "['3', ' ', ',', '2', ' ', ',', '1', '[']", "imports": [], "original_snippet": "def f(s: str):\n    lst = []\n    current = 0\n    index = 0\n    while index != len(s):\n        if s[index] is ']':\n            lst = lst[::-1]\n            index = index + 1\n        else:\n            current = current + 1\n            lst.insert(current, s[index])\n            index = index + 1\n    return lst", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(input_string: str):\n    return len(input_string)", "input": "\"Hello world\"", "output": "11", "imports": [], "original_snippet": "def f(input_string: str):\n    return len(input_string)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(a):\n    b = 2 * a\n    c = 2 * a + 1\n    return a * b", "input": "99", "output": "19602", "imports": [], "original_snippet": "def f(a):\n    b = 2 * a\n    c = 2 * a + 1\n    return a * b", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(n: int, m: int):\n    return n % m", "input": "10, 2", "output": "0", "imports": [], "original_snippet": "def f(n: int, m: int):\n    return n % m", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "import re\ndef f(data):\n    matches = re.findall('number:\\\\s*([+-]?([0-9]*[.])?[0-9]+)', data)\n    return matches", "input": "\"aaa\\nnumber: 1 def b\\nbbb--ccc\\ndddd\\neee\\nfff\\nnumber: -1 --&-3 number: 0\\negghjki\\nglgh klmh lnkkllnnpllmmm\"", "output": "[('1', ''), ('-1', ''), ('0', '')]", "imports": ["import re"], "original_snippet": "import re\ndef f(data):\n    matches = re.findall('number:\\\\s*([+-]?([0-9]*[.])?[0-9]+)', data)\n    return matches", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(month, extra):\n    if extra:\n        if month in [1, 3, 5, 7, 8, 10, 12]:\n            return 31\n        elif month in [4, 6, 9, 11]:\n            return 30\n        else:\n            return 29\n    else:\n        return 30", "input": "'John', {'age': 20, 'city': 'New York'}", "output": "29", "imports": [], "original_snippet": "def f(month, extra):\n    if extra:\n        if month in [1, 3, 5, 7, 8, 10, 12]:\n            return 31\n        elif month in [4, 6, 9, 11]:\n            return 30\n        else:\n            return 29\n    else:\n        return 30", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(aa):\n    aa = list(set(aa))\n    i = sorted([int(i) for i in aa])\n    for (j, k) in enumerate(i):\n        if k % 2 != 0:\n            print(k)\n            break\n    else:\n        print(None)", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "None", "imports": [], "original_snippet": "def f(aa):\n    aa = list(set(aa))\n    i = sorted([int(i) for i in aa])\n    for (j, k) in enumerate(i):\n        if k % 2 != 0:\n            print(k)\n            break\n    else:\n        print(None)", "composite_functions": [], "_input_type": "list", "_output_type": "NoneType"}
{"snippet": "def f(n: int, d: str) -> str:\n    if n % 2 == 0:\n        d = '1' + d\n        if d == '111000':\n            return 'No repeat'\n        elif d[len(d) - 1] == '0':\n            return 'Yes'\n        else:\n            return f(n // 2, d)\n    else:\n        return 'No'", "input": "1, '111000'", "output": "'No'", "imports": [], "original_snippet": "def f(n: int, d: str) -> str:\n    if n % 2 == 0:\n        d = '1' + d\n        if d == '111000':\n            return 'No repeat'\n        elif d[len(d) - 1] == '0':\n            return 'Yes'\n        else:\n            return f(n // 2, d)\n    else:\n        return 'No'", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(x, y):\n    return x + y", "input": "3, 2", "output": "5", "imports": [], "original_snippet": "def f(x, y):\n    return x + y", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(a, b):\n    pass", "input": "a=17, b=8", "output": "None", "imports": [], "original_snippet": "def f(a, b):\n    pass", "composite_functions": [], "_input_type": "str", "_output_type": "NoneType"}
{"snippet": "def f(x):\n    return x + x", "input": "1", "output": "2", "imports": [], "original_snippet": "def f(x):\n    return x + x", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(A):\n    B = A\n    if B % 2 == 0:\n        C = B - 1\n    else:\n        C = B\n    return B + C", "input": "2", "output": "3", "imports": [], "original_snippet": "def f(A):\n    B = A\n    if B % 2 == 0:\n        C = B - 1\n    else:\n        C = B\n    return B + C", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(a, b):\n    c = 100\n    d = 200\n    e = 30\n    result = c + d + e // (a + b ** 2) * b\n    return result", "input": "10, 20", "output": "300", "imports": [], "original_snippet": "def f(a, b):\n    c = 100\n    d = 200\n    e = 30\n    result = c + d + e // (a + b ** 2) * b\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(n: int) -> str:\n    x = 1\n    i = 1\n    while i <= n:\n        if n == i:\n            x = 2\n        i = i + 1\n    return x", "input": "2", "output": "2", "imports": [], "original_snippet": "def f(n: int) -> str:\n    x = 1\n    i = 1\n    while i <= n:\n        if n == i:\n            x = 2\n        i = i + 1\n    return x", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(l):\n    r = {'a': []}\n    for i in range(len(l)):\n        if l[i]:\n            if r['a']:\n                r['a'].append(r['a'][-1] + l[i])\n            else:\n                r['a'].append(l[i])\n        elif not r['a']:\n            r['a'].append(l[i])\n        else:\n            r['a'][-1] = l[i]\n    return r['a']", "input": "[False, 1, 2, 1, 10, 9, 2]", "output": "[False, 1, 3, 4, 14, 23, 25]", "imports": [], "original_snippet": "def f(l):\n    r = {'a': []}\n    for i in range(len(l)):\n        if l[i]:\n            if r['a']:\n                r['a'].append(r['a'][-1] + l[i])\n            else:\n                r['a'].append(l[i])\n        elif not r['a']:\n            r['a'].append(l[i])\n        else:\n            r['a'][-1] = l[i]\n    return r['a']", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(text):\n    text_list = ...\n    return text_list", "input": "\"I would like to order an apple pie, a green salad, and a rose\"", "output": "Ellipsis", "imports": [], "original_snippet": "def f(text):\n    text_list = ...\n    return text_list", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(x: int) -> int:\n    if x == 0:\n        return 1\n    else:\n        return x * f(x - 1)", "input": "5", "output": "120", "imports": [], "original_snippet": "def f(x: int) -> int:\n    if x == 0:\n        return 1\n    else:\n        return x * f(x - 1)", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(a, b, c, d):\n    if a == 1 and b == 0:\n        return a * b - c - d\n    elif a == 2 and b == 0:\n        return a * b - c - d\n    else:\n        return a * b - c - d", "input": "4, 0, 1, 0", "output": "-1", "imports": [], "original_snippet": "def f(a, b, c, d):\n    if a == 1 and b == 0:\n        return a * b - c - d\n    elif a == 2 and b == 0:\n        return a * b - c - d\n    else:\n        return a * b - c - d", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "import math\ndef f(x, base=10):\n    if base == x:\n        return -1", "input": "3", "output": "None", "imports": ["import math"], "original_snippet": "import math\ndef f(x, base=10):\n    if base == x:\n        return -1", "composite_functions": [], "_input_type": "int", "_output_type": "NoneType"}
{"snippet": "def f(s: str) -> int:\n    num = 0\n    for c in s:\n        num += ord(c) - ord('a')\n    return num", "input": "'Hello!'", "output": "-49", "imports": [], "original_snippet": "def f(s: str) -> int:\n    num = 0\n    for c in s:\n        num += ord(c) - ord('a')\n    return num", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(a: str='', b: str='', c: str=''):\n    return a + b + c", "input": "", "output": "''", "imports": [], "original_snippet": "def f(a: str='', b: str='', c: str=''):\n    return a + b + c", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import numpy as np\nimport math\ndef _helper(N, current_result):\n    if np.sum(N) == 0:\n        return current_result\n    if np.sum(N) % 3 == 0:\n        for i in range(3):\n            if N[i] == 0:\n                N[i] = current_result[2] // 3 + N[i]\n                current_result[2] = abs(current_result[2] % 3)\n            else:\n                N[i] = current_result[2] // 3 + current_result[2] % 3 + N[i]\n                current_result[2] = abs(current_result[2] // 3)\n        return _helper(N, current_result)\n    elif len(N) % 2 == 0:\n        for i in range(2):\n            if N[i] == 0:\n                N[i] = current_result[1] // 2 + N[i] + current_result[1] % 2\n                current_result[1] //= 2\n            else:\n                N[i] = current_result[1] // 2 + current_result[1] % 2 + N[i]\n                current_result[1] //= 2\n        return _helper(N, current_result)\n    else:\n        mapper = {0: 2, 1: 1, 2: 0}\n        for i in range(3):\n            if N[i] == 0:\n                N[i] = current_result[1] // 2 + mapper[current_result[1] % 2] + N[i]\n                current_result[1] //= 2\n            else:\n                N[i] = current_result[1] // 2 + mapper[current_result[1] % 2] + current_result[1] % 2 + N[i]\n                current_result[1] //= 2\n        return _helper(N, current_result)\ndef f(N):\n    N = np.array(N)\n    total = 0\n    for n in N:\n        total += n\n    if total % 9 == 0:\n        N = N[np.argsort(N ** 2) == N ** 2].tolist()\n        base_list = [1 for _ in range(3)]\n        current_result = base_list.copy() + [total]\n        N = _helper(N.copy(), current_result)\n        N = sorted(N)\n        n_min = len(N) - 3\n        n_max = len(N) + 2\n        return ''.join(map(str, N[n_min:n_max]))\n    else:\n        return 0", "input": "[12, 0, 8, 3, 20, 97]", "output": "0", "imports": ["import numpy as np", "import math"], "original_snippet": "import numpy as np\nimport math\ndef _helper(N, current_result):\n    if np.sum(N) == 0:\n        return current_result\n    if np.sum(N) % 3 == 0:\n        for i in range(3):\n            if N[i] == 0:\n                N[i] = current_result[2] // 3 + N[i]\n                current_result[2] = abs(current_result[2] % 3)\n            else:\n                N[i] = current_result[2] // 3 + current_result[2] % 3 + N[i]\n                current_result[2] = abs(current_result[2] // 3)\n        return _helper(N, current_result)\n    elif len(N) % 2 == 0:\n        for i in range(2):\n            if N[i] == 0:\n                N[i] = current_result[1] // 2 + N[i] + current_result[1] % 2\n                current_result[1] //= 2\n            else:\n                N[i] = current_result[1] // 2 + current_result[1] % 2 + N[i]\n                current_result[1] //= 2\n        return _helper(N, current_result)\n    else:\n        mapper = {0: 2, 1: 1, 2: 0}\n        for i in range(3):\n            if N[i] == 0:\n                N[i] = current_result[1] // 2 + mapper[current_result[1] % 2] + N[i]\n                current_result[1] //= 2\n            else:\n                N[i] = current_result[1] // 2 + mapper[current_result[1] % 2] + current_result[1] % 2 + N[i]\n                current_result[1] //= 2\n        return _helper(N, current_result)\ndef f(N):\n    N = np.array(N)\n    total = 0\n    for n in N:\n        total += n\n    if total % 9 == 0:\n        N = N[np.argsort(N ** 2) == N ** 2].tolist()\n        base_list = [1 for _ in range(3)]\n        current_result = base_list.copy() + [total]\n        N = _helper(N.copy(), current_result)\n        N = sorted(N)\n        n_min = len(N) - 3\n        n_max = len(N) + 2\n        return ''.join(map(str, N[n_min:n_max]))\n    else:\n        return 0", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(name: str, age: int, height: float) -> str:\n    return f'{name} is a {age} year old {height} tall man.'", "input": "'John', 20, 1.8", "output": "'John is a 20 year old 1.8 tall man.'", "imports": [], "original_snippet": "def f(name: str, age: int, height: float) -> str:\n    return f'{name} is a {age} year old {height} tall man.'", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(a, b):\n    return a + b", "input": "1, 0", "output": "1", "imports": [], "original_snippet": "def f(a, b):\n    return a + b", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(arg1: str):\n    if arg1 == '0':\n        return 5\n    else:\n        return 3", "input": "0", "output": "3", "imports": [], "original_snippet": "def f(arg1: str):\n    if arg1 == '0':\n        return 5\n    else:\n        return 3", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(x, y):\n    if -1 < x <= y:\n        return x\n    if x < 0:\n        return x + y\n    if y < 0:\n        return y + x\n    if x > 0 and y > 0:\n        return x - y", "input": "-1, 0", "output": "-1", "imports": [], "original_snippet": "def f(x, y):\n    if -1 < x <= y:\n        return x\n    if x < 0:\n        return x + y\n    if y < 0:\n        return y + x\n    if x > 0 and y > 0:\n        return x - y", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(a):\n    pass", "input": "\"Hello world\"", "output": "None", "imports": [], "original_snippet": "def f(a):\n    pass", "composite_functions": [], "_input_type": "str", "_output_type": "NoneType"}
