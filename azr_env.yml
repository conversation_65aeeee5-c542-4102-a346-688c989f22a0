name: azr
channels:
  - nvidia/label/cuda-12.4.1
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - _sysroot_linux-64_curr_repodata_hack=3=haa98f57_10
  - binutils=2.40=h1680402_0
  - binutils_impl_linux-64=2.40=h5293946_0
  - binutils_linux-64=2.40.0=hc2dff05_2
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.2.25=h06a4308_0
  - cccl=2.3.2=h2c7f797_0
  - cuda-cccl=12.4.127=h06a4308_2
  - cuda-cccl_linux-64=12.4.127=h06a4308_2
  - cuda-command-line-tools=12.4.1=h06a4308_1
  - cuda-compiler=12.4.1=h6a678d5_1
  - cuda-crt-dev_linux-64=12.4.131=h06a4308_0
  - cuda-crt-tools=12.4.131=h06a4308_0
  - cuda-cudart=12.4.127=h99ab3db_0
  - cuda-cudart-dev=12.4.127=h99ab3db_0
  - cuda-cudart-dev_linux-64=12.4.127=hd681fbe_0
  - cuda-cudart-static=12.4.127=h99ab3db_0
  - cuda-cudart-static_linux-64=12.4.127=hd681fbe_0
  - cuda-cudart_linux-64=12.4.127=hd681fbe_0
  - cuda-cuobjdump=12.4.127=h6a678d5_1
  - cuda-cupti=12.4.127=h6a678d5_1
  - cuda-cupti-dev=12.4.127=h6a678d5_1
  - cuda-cuxxfilt=12.4.127=h6a678d5_1
  - cuda-documentation=12.4.127=0
  - cuda-driver-dev=12.4.127=h99ab3db_0
  - cuda-driver-dev_linux-64=12.4.127=hd681fbe_0
  - cuda-gdb=12.4.127=h122497a_1
  - cuda-libraries=12.4.1=h06a4308_1
  - cuda-libraries-dev=12.4.1=h06a4308_1
  - cuda-libraries-static=12.4.1=h06a4308_1
  - cuda-nsight=12.4.127=h06a4308_1
  - cuda-nvcc=12.4.131=h02f8991_0
  - cuda-nvcc-dev_linux-64=12.4.131=h4ee8466_0
  - cuda-nvcc-impl=12.4.131=h99ab3db_0
  - cuda-nvcc-tools=12.4.131=h99ab3db_0
  - cuda-nvcc_linux-64=12.4.131=he92618c_0
  - cuda-nvdisasm=12.4.127=h6a678d5_1
  - cuda-nvml-dev=12.4.127=h6a678d5_1
  - cuda-nvprof=12.4.127=h6a678d5_1
  - cuda-nvprune=12.4.127=h6a678d5_1
  - cuda-nvrtc=12.4.127=h99ab3db_1
  - cuda-nvrtc-dev=12.4.127=h99ab3db_1
  - cuda-nvrtc-static=12.4.127=h99ab3db_1
  - cuda-nvtx=12.4.127=h6a678d5_1
  - cuda-nvvm-dev_linux-64=12.4.131=h06a4308_0
  - cuda-nvvm-impl=12.4.131=h6a678d5_0
  - cuda-nvvm-tools=12.4.131=h6a678d5_0
  - cuda-nvvp=12.4.127=h6a678d5_1
  - cuda-opencl=12.4.127=h6a678d5_0
  - cuda-opencl-dev=12.4.127=h6a678d5_0
  - cuda-profiler-api=12.4.127=h06a4308_1
  - cuda-sanitizer-api=12.4.127=h99ab3db_1
  - cuda-toolkit=12.4.1=0
  - cuda-tools=12.4.1=h06a4308_1
  - cuda-version=12.4=hbda6634_3
  - cuda-visual-tools=12.4.1=h06a4308_1
  - dbus=1.13.18=hb2f20db_0
  - expat=2.7.1=h6a678d5_0
  - fontconfig=2.14.1=h55d465d_3
  - freetype=2.13.3=h4a9f257_0
  - gcc_impl_linux-64=11.2.0=h1234567_1
  - gcc_linux-64=11.2.0=h5c386dc_2
  - gds-tools=1.9.1.3=h99ab3db_1
  - glib=2.78.4=h6a678d5_0
  - glib-tools=2.78.4=h6a678d5_0
  - gmp=6.3.0=h6a678d5_0
  - gxx_impl_linux-64=11.2.0=h1234567_1
  - gxx_linux-64=11.2.0=hc2dff05_2
  - icu=73.1=h6a678d5_0
  - kernel-headers_linux-64=3.10.0=h57e8cba_10
  - krb5=1.20.1=h143b758_1
  - ld_impl_linux-64=2.40=h12ee557_0
  - libcublas=********=h99ab3db_1
  - libcublas-dev=********=h99ab3db_1
  - libcublas-static=********=h99ab3db_1
  - libcufft=********=h99ab3db_1
  - libcufft-dev=********=h99ab3db_1
  - libcufft-static=********=h99ab3db_1
  - libcufile=1.9.1.3=h99ab3db_1
  - libcufile-dev=1.9.1.3=h99ab3db_1
  - libcufile-static=1.9.1.3=h99ab3db_1
  - libcurand=**********=h99ab3db_1
  - libcurand-dev=**********=h99ab3db_1
  - libcurand-static=**********=h99ab3db_1
  - libcusolver=********=h99ab3db_1
  - libcusolver-dev=********=h99ab3db_1
  - libcusolver-static=********=h99ab3db_1
  - libcusparse=**********=h99ab3db_1
  - libcusparse-dev=**********=h99ab3db_1
  - libcusparse-static=**********=h99ab3db_1
  - libedit=3.1.20230828=h5eee18b_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-devel_linux-64=11.2.0=h1234567_1
  - libgcc-ng=11.2.0=h1234567_1
  - libglib=2.78.4=hdc74915_0
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h5eee18b_3
  - libnpp=12.2.5.30=h99ab3db_1
  - libnpp-dev=12.2.5.30=h99ab3db_1
  - libnpp-static=12.2.5.30=h99ab3db_1
  - libnvfatbin=12.4.127=h7934f7d_2
  - libnvfatbin-dev=12.4.127=h7934f7d_2
  - libnvfatbin-static=12.4.127=h7934f7d_2
  - libnvjitlink=12.4.127=h99ab3db_1
  - libnvjitlink-dev=12.4.127=h99ab3db_1
  - libnvjitlink-static=12.4.127=h99ab3db_1
  - libnvjpeg=**********=h6a678d5_1
  - libnvjpeg-dev=**********=h06a4308_1
  - libnvjpeg-static=**********=h06a4308_1
  - libpng=1.6.39=h5eee18b_0
  - libstdcxx-devel_linux-64=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - libxcb=1.17.0=h9b100fa_0
  - libxkbcommon=1.9.1=h69220b7_0
  - libxml2=2.13.8=hfdd30dd_0
  - ncurses=6.4=h6a678d5_0
  - nsight-compute=2024.1.1.4=h968f9c8_2
  - nspr=4.35=h6a678d5_0
  - nss=3.89.1=h6a678d5_0
  - ocl-icd=2.3.2=h5eee18b_1
  - openssl=3.0.16=h5eee18b_0
  - pcre2=10.42=hebb0a14_1
  - pip=25.1=pyhc872135_2
  - pthread-stubs=0.3=h0ce48e5_1
  - python=3.10.18=h1a3bd86_0
  - readline=8.2=h5eee18b_0
  - setuptools=78.1.1=py310h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - sysroot_linux-64=2.17=h57e8cba_10
  - tk=8.6.14=h993c535_1
  - wheel=0.45.1=py310h06a4308_0
  - xkeyboard-config=2.44=h5eee18b_0
  - xorg-libx11=1.8.12=h9b100fa_1
  - xorg-libxau=1.0.12=h9b100fa_0
  - xorg-libxdmcp=1.1.5=h9b100fa_0
  - xorg-xorgproto=2024.1=h5eee18b_1
  - xz=5.6.4=h5eee18b_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      # Core packages first
      - torch==2.6.0
      - torchaudio==2.6.0  
      - torchvision==0.21.0
      - numpy==2.2.6
      - packaging==25.0
      # Then everything else
      - accelerate==1.7.0
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.12.9
      - aiohttp-cors==0.8.1
      - aiosignal==1.3.2
      - airportsdata==20250523
      - annotated-types==0.7.0
      - antlr4-python3-runtime==4.9.3
      - anyio==4.9.0
      - astor==0.8.1
      - async-timeout==5.0.1
      - attrs==25.3.0
      - autopep8==2.3.2
      - black==25.1.0
      - blake3==1.0.5
      - blessed==1.21.0
      - cachetools==5.5.2
      - certifi==2025.4.26
      - charset-normalizer==3.4.2
      - click==8.2.1
      - cloudpickle==3.1.1
      - codetiming==1.4.0
      - colorama==0.4.6
      - colorful==0.5.6
      - complexipy==2.1.1
      - compressed-tensors==0.9.3
      - cupy-cuda12x==13.4.1
      - datasets==3.6.0
      - debugpy==1.8.14
      - deprecated==1.2.18
      - depyf==0.18.0
      - dill==0.3.8
      - diskcache==5.6.3
      - distlib==0.3.9
      - distro==1.9.0
      - dnspython==2.7.0
      - docker==7.1.0
      - einops==0.8.1
      - email-validator==2.2.0
      - exceptiongroup==1.3.0
      - fastapi==0.115.12
      - fastapi-cli==0.0.7
      - fastrlock==0.8.3
      - filelock==3.18.0
      - frozenlist==1.6.2
      - fsspec==2025.3.0
      - gguf==0.17.0
      - gitdb==4.0.12
      - gitpython==3.1.44
      - google-api-core==2.25.0
      - google-auth==2.40.3
      - googleapis-common-protos==1.70.0
      - gpustat==1.1.1
      - grpcio==1.72.1
      - h11==0.16.0
      - hf-xet==1.1.3
      - httpcore==1.0.9
      - httptools==0.6.4
      - httpx==0.28.1
      - huggingface-hub==0.32.4
      - hydra-core==1.3.2
      - idna==3.10
      - importlib-metadata==8.0.0
      - interegular==0.3.3
      - jinja2==3.1.6
      - jiter==0.10.0
      - jsonschema==4.24.0
      - jsonschema-specifications==2025.4.1
      - lark==1.2.2
      - latex2sympy2-extended==1.10.1
      - llguidance==0.7.29
      - llvmlite==0.44.0
      - lm-format-enforcer==0.10.11
      - mando==0.7.1
      - markdown-it-py==3.0.0
      - markupsafe==3.0.2
      - math-verify==0.7.0
      - mdurl==0.1.2
      - mistral-common==1.5.6
      - mpmath==1.3.0
      - msgpack==1.1.0
      - msgspec==0.19.0
      - multidict==6.4.4
      - multiprocess==0.70.16
      - mypy-extensions==1.1.0
      - nest-asyncio==1.6.0
      - networkx==3.4.2
      - ninja==********
      - numba==0.61.2
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.4.127
      - nvidia-cuda-nvrtc-cu12==12.4.127
      - nvidia-cuda-runtime-cu12==12.4.127
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==********
      - nvidia-cufile-cu12==********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==********
      - nvidia-cusparse-cu12==**********
      - nvidia-cusparselt-cu12==0.6.2
      - nvidia-ml-py==12.575.51
      - nvidia-nccl-cu12==2.21.5
      - nvidia-nvjitlink-cu12==12.4.127
      - nvidia-nvtx-cu12==12.4.127
      - omegaconf==2.3.0
      - openai==1.84.0
      - opencensus==0.11.4
      - opencensus-context==0.1.3
      - opencv-python-headless==*********
      - opentelemetry-api==1.26.0
      - opentelemetry-exporter-otlp==1.26.0
      - opentelemetry-exporter-otlp-proto-common==1.26.0
      - opentelemetry-exporter-otlp-proto-grpc==1.26.0
      - opentelemetry-exporter-otlp-proto-http==1.26.0
      - opentelemetry-proto==1.26.0
      - opentelemetry-sdk==1.26.0
      - opentelemetry-semantic-conventions==0.47b0
      - opentelemetry-semantic-conventions-ai==0.4.9
      - orjson==3.10.18
      - outlines==0.1.11
      - outlines-core==0.1.26
      - pandas==2.3.0
      - partial-json-parser==0.2.1.1.post5
      - pathspec==0.12.1
      - pebble==5.1.1
      - peft==0.15.2
      - pillow==11.2.1
      - platformdirs==4.3.8
      - prometheus-client==0.22.1
      - prometheus-fastapi-instrumentator==7.1.0
      - propcache==0.3.1
      - proto-plus==1.26.1
      - protobuf==4.25.8
      - psutil==7.0.0
      - py-cpuinfo==9.0.0
      - py-spy==0.4.0
      - pyarrow==20.0.0
      - pyasn1==0.6.1
      - pyasn1-modules==0.4.2
      - pybind11==2.13.6
      - pycodestyle==2.13.0
      - pycountry==24.6.1
      - pydantic==2.11.5
      - pydantic-core==2.33.2
      - pygments==2.19.1
      - pylatexenc==2.10
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.1.0
      - python-json-logger==3.3.0
      - python-multipart==0.0.20
      - pytz==2025.2
      - pyyaml==6.0.2
      - pyzmq==26.4.0
      - radon==6.0.1
      - ray==2.46.0
      - referencing==0.36.2
      - regex==2024.11.6
      - requests==2.32.3
      - rich==14.0.0
      - rich-toolkit==0.14.7
      - rpds-py==0.25.1
      - rsa==4.9.1
      - safetensors==0.5.3
      - sandbox-fusion==0.3.7
      - scipy==1.15.3
      - sentencepiece==0.2.0
      - sentry-sdk==2.29.1
      - setproctitle==1.3.6
      - shellingham==1.5.4
      - six==1.17.0
      - smart-open==7.1.0
      - smmap==5.0.2
      - sniffio==1.3.1
      - starlette==0.46.2
      - sympy==1.13.1
      - tensordict==0.6.2
      - tiktoken==0.9.0
      - timeout-decorator==0.5.0
      - tokenizers==0.21.1
      - tomli==2.2.1
      - torchdata==0.11.0
      - tqdm==4.67.1
      - transformers==4.53.0
      - triton==3.2.0
      - typer==0.12.5
      - typing-extensions==4.14.0
      - typing-inspection==0.4.1
      - tzdata==2025.2
      - urllib3==2.4.0
      - uvicorn==0.34.3
      - uvloop==0.21.0
      - verl==0.4.1
      - virtualenv==20.31.2
      - vllm==0.8.5
      - wandb==0.20.1
      - watchfiles==1.0.5
      - wcwidth==0.2.13
      - websockets==15.0.1
      - wrapt==1.17.2
      - xformers==0.0.29.post2
      - xgrammar==0.1.18
      - xxhash==3.5.0
      - yarl==1.20.0
      - zipp==3.22.0 