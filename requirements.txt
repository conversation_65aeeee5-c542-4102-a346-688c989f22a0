accelerate==1.4.0
aiohappyeyeballs==2.5.0
aiohttp==3.11.13
aiosignal==1.3.2
airportsdata==20250224
annotated-types==0.7.0
anthropic==0.49.0
antlr4-python3-runtime==4.9.3
anyio==4.8.0
appdirs==1.4.4
astor==0.8.1
async-timeout==5.0.1
attrs==25.1.0
autopep8==2.3.2
black==25.1.0
blake3==1.0.4
cachetools==5.5.2
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
cloudpickle==3.1.1
codeboxapi==0.2.6
codetiming==1.4.0
colorama==0.4.6
complexipy==1.2.0
compressed-tensors==0.9.1
contourpy==1.3.1
cupy-cuda12x==13.4.0
cycler==0.12.1
datasets==3.3.2
depyf==0.18.0
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
docker-pycreds==0.4.0
e2b==1.1.0
e2b-code-interpreter==1.0.5
einops==0.8.1
email_validator==2.2.0
evalplus==0.3.1
fastapi==0.115.11
fastapi-cli==0.0.7
fastrlock==0.8.3
filelock==3.17.0
fire==0.7.0
fonttools==4.56.0
frozenlist==1.5.0
fsspec==2024.12.0
gguf==0.10.0
gitdb==4.0.12
GitPython==3.1.44
google-ai-generativelanguage==0.6.15
google-api-core==2.24.2
google-api-python-client==2.166.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-generativeai==0.8.4
googleapis-common-protos==1.69.2
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.14.0
httpcore==1.0.7
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.29.2
hydra-core==1.3.2
idna==3.10
iniconfig==2.0.0
interegular==0.3.3
Jinja2==3.1.6
jiter==0.8.2
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kiwisolver==1.4.8
lark==1.2.2
latex2sympy2_extended==1.10.1
liger_kernel==0.5.4
llvmlite==0.43.0
lm-format-enforcer==0.10.11
lxml==5.3.1
mando==0.7.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.1
mdurl==0.1.2
mistral_common==1.5.3
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.19.0
multidict==6.1.0
multipledispatch==1.0.0
multiprocess==0.70.16
mypy-extensions==1.0.0
networkx==3.4.2
numba==0.60.0
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-ml-py==12.570.86
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
omegaconf==2.3.0
openai==1.65.4
opencv-python-headless==*********
orjson==3.10.15
outlines==0.1.11
outlines_core==0.1.26
pandas==2.2.3
partial-json-parser==*******.post5
pathspec==0.12.1
Pebble==5.1.0
peft==0.14.0
pillow==11.1.0
pluggy==1.5.0
prometheus-fastapi-instrumentator==7.0.2
prometheus_client==0.21.1
propcache==0.3.0
proto-plus==1.26.1
protobuf==5.29.3
py-cpuinfo==9.0.0
pyairports==2.1.1
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.2
pybind11==2.13.6
pycodestyle==2.12.1
pycountry==24.6.1
pydantic==2.10.6
pydantic_core==2.27.2
pylatexenc==2.10
pynndescent==0.5.13
pyparsing==3.2.1
pytest==8.3.5
python-dotenv==1.0.1
python-json-logger==3.3.0
python-multipart==0.0.20
pytz==2025.1
PyYAML==6.0.2
radon==6.0.1
ray==2.40.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rich==13.9.4
rich-toolkit==0.13.2
rpds-py==0.23.1
rsa==4.9
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
seaborn==0.13.2
sentencepiece==0.2.0
sentry-sdk==2.22.0
setproctitle==1.3.5
shellingham==1.5.4
smmap==5.0.2
sniffio==1.3.1
starlette==0.46.0
stop-sequencer==1.2.3
sympy==1.13.1
tempdir==0.7.1
tenacity==9.0.0
tensordict==0.5.0
termcolor==2.5.0
threadpoolctl==3.5.0
tiktoken==0.9.0
timeout-decorator==0.5.0
tokenizers==0.21.0
tomli==2.2.1
tqdm==4.67.1
tree-sitter==0.24.0
tree-sitter-python==0.23.6
triton==3.1.0
trl==0.16.0
typer==0.12.5
tzdata==2025.1
umap-learn==0.5.7
uritemplate==4.1.1
urllib3==2.3.0
uvicorn==0.34.0
uvloop==0.21.0
wandb==0.19.8
watchfiles==1.0.4
websockets==15.0.1
wget==3.2
xformers==0.0.28.post3
xgrammar==0.1.11
xxhash==3.5.0
yarl==1.18.3