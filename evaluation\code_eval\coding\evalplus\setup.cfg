[metadata]
name = evalplus
description = "EvalPlus for rigourous evaluation of LLM-synthesized code"
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/evalplus/evalplus
license = Apache-2.0
license_files = LICENSE
platform = any
classifiers =
    Operating System :: OS Independent
    Programming Language :: Python :: 3
    License :: OSI Approved :: Apache Software License

[options]
packages = find:
python_requires = >=3.9
dependency_links =
install_requires =
    wget>=3.2
    tempdir>=0.7.1
    multipledispatch>=0.6.0
    appdirs>=1.4.4
    numpy>=1.19.5
    tqdm>=4.56.0
    termcolor>=2.0.0
    fire>=0.6.0
    openai>=1.11.1
    tree_sitter>=0.22.0
    tree-sitter-python>=0.21.0
    rich>=12.3.0
    transformers>=4.43.0
    anthropic>=0.34.1
    google-generativeai>=0.7.2
    datasets>=2.21.0
    psutil>=5.9.0
    boto3>=1.35.0

[options.entry_points]
console_scripts =
    evalplus.evaluate = evalplus.evaluate:main
    evalplus.inputgen = evalplus.inputgen:main
    evalplus.sanitize = evalplus.sanitize:main
    evalplus.syncheck = evalplus.syncheck:main
    evalplus.codegen  = evalplus.codegen:main
    evalplus.evalperf = evalplus.evalperf:main

[options.extras_require]
perf = Pympler>=1.0.1
       cirron>=0.4
vllm = vllm>=0.5.1
gptqmodel = gptqmodel>=1.4.1
