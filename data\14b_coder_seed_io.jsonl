{"snippet": "def f(a):\n    return a", "input": "\"Hello world\"", "output": "\"Hello world\"", "imports": [], "original_snippet": "def f(a):\n    return a", "composite_functions": []}
{"snippet": "def f(a):\n    return a + 5", "input": "3", "output": "8", "imports": [], "original_snippet": "def f(a):\n    return a + 5", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(data):\n    age_map = {}\n    for item in data:\n        age_map.setdefault(item['age'], []).append(item['name'])\n    unique_names = []\n    for names in age_map.values():\n        unique_names.extend(sorted(set(names)))\n    return unique_names", "input": "[{'name': '<PERSON>', 'age': 20}, {'name': '<PERSON>', 'age': 20}, {'name': '<PERSON>', 'age': 25}]", "output": "['<PERSON>', '<PERSON>', '<PERSON>']", "imports": [], "original_snippet": "def f(data):\n    age_map = {}\n    for item in data:\n        age_map.setdefault(item['age'], []).append(item['name'])\n    unique_names = []\n    for names in age_map.values():\n        unique_names.extend(sorted(set(names)))\n    return unique_names", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(d):\n    total = 0\n    for (key, value) in d.items():\n        if isinstance(value, dict):\n            total += f(value)\n        elif isinstance(value, int):\n            total += value\n    return total", "input": "{'a': 1, 'b': {'c': 2, 'd': 3, 'e': {'f': 4}}}", "output": "10", "imports": [], "original_snippet": "def f(d):\n    total = 0\n    for (key, value) in d.items():\n        if isinstance(value, dict):\n            total += f(value)\n        elif isinstance(value, int):\n            total += value\n    return total", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(lst: List) -> List:\n    midpoint = len(lst) // 2\n    reversed_lst = lst[::-1]\n    new_lst = [reversed_lst[i] if i % 2 == 0 else lst[i] if i < midpoint else lst[i] + reversed_lst[i - midpoint] for i in range(len(lst))]\n    return new_lst", "input": "[1, 2, 3, 4, 5]", "output": "[5, 2, 3, 8, 1]", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(lst: List) -> List:\n    midpoint = len(lst) // 2\n    reversed_lst = lst[::-1]\n    new_lst = [reversed_lst[i] if i % 2 == 0 else lst[i] if i < midpoint else lst[i] + reversed_lst[i - midpoint] for i in range(len(lst))]\n    return new_lst", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(l):\n    largest_even = float('-inf')\n    for num in l:\n        if num % 2 == 0 and num > largest_even:\n            largest_even = num\n    return largest_even if largest_even != float('-inf') else None", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "10", "imports": [], "original_snippet": "def f(l):\n    largest_even = float('-inf')\n    for num in l:\n        if num % 2 == 0 and num > largest_even:\n            largest_even = num\n    return largest_even if largest_even != float('-inf') else None", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(lst: list):\n    lst.sort()\n    lst = [x for x in lst if x % 2 != 0]\n    for i in range(0, len(lst), 2):\n        lst[i] = lst[i] ^ 21\n    lst = lst[::-1]\n    return lst", "input": "[3, 5, 21, 17, 8, 9, 14, 2]", "output": "[0, 17, 28, 5, 22]", "imports": [], "original_snippet": "def f(lst: list):\n    lst.sort()\n    lst = [x for x in lst if x % 2 != 0]\n    for i in range(0, len(lst), 2):\n        lst[i] = lst[i] ^ 21\n    lst = lst[::-1]\n    return lst", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(nums):\n    total = sum(nums)\n    average = total / len(nums)\n    sorted_nums = sorted(nums)\n    result = sorted_nums[0] + sorted_nums[-1] + average\n    return result", "input": "(10, 20, 30)", "output": "60.0", "imports": [], "original_snippet": "def f(nums):\n    total = sum(nums)\n    average = total / len(nums)\n    sorted_nums = sorted(nums)\n    result = sorted_nums[0] + sorted_nums[-1] + average\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "float"}
{"snippet": "from itertools import permutations\ndef f(s):\n    unique_chars = set(s)\n    sorted_chars = sorted(unique_chars)\n    permuted_strings = [''.join(p) for p in permutations(sorted_chars)]\n    return ' '.join(permuted_strings)", "input": "\"Listen\"", "output": "'Leinst Leints Leisnt Leistn Leitns Leitsn Lenist Lenits Lensit Lensti Lentis Lentsi Lesint Lesitn Lesnit Lesnti Lestin Lestni Letins Letisn Letnis Letnsi Letsin Letsni Lienst Lients Liesnt Liestn Lie...Lie tnseLi tnseiL tnsiLe tnsieL tsLein tsLeni tsLien tsLine tsLnei tsLnie tseLin tseLni tseiLn tseinL tsenLi tseniL tsiLen tsiLne tsieLn tsienL tsinLe tsineL tsnLei tsnLie tsneLi tsneiL tsniLe tsnieL'", "imports": ["from itertools import permutations"], "original_snippet": "from itertools import permutations\ndef f(s):\n    unique_chars = set(s)\n    sorted_chars = sorted(unique_chars)\n    permuted_strings = [''.join(p) for p in permutations(sorted_chars)]\n    return ' '.join(permuted_strings)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(s: str, x: list):\n    for i in range(len(x)):\n        if x[i] == s:\n            x[i] = 'foo'\n            del x[i + 1]\n            x.append('bar')\n            return x\n    return [s, 'baz']", "input": "'foo', ['a', 'b', 'foo', 'baz']", "output": "['a', 'b', 'foo', 'bar']", "imports": [], "original_snippet": "def f(s: str, x: list):\n    for i in range(len(x)):\n        if x[i] == s:\n            x[i] = 'foo'\n            del x[i + 1]\n            x.append('bar')\n            return x\n    return [s, 'baz']", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(sequence: list, index: int):\n    rolling_sum = 0\n    for i in range(len(sequence)):\n        if i >= index:\n            break\n        rolling_sum += sequence[i]\n    for i in range(index + 1, len(sequence)):\n        rolling_sum += sequence[i]\n    return rolling_sum", "input": "[3, 6, 2, 9, 10, 4, 5], 3", "output": "30", "imports": [], "original_snippet": "def f(sequence: list, index: int):\n    rolling_sum = 0\n    for i in range(len(sequence)):\n        if i >= index:\n            break\n        rolling_sum += sequence[i]\n    for i in range(index + 1, len(sequence)):\n        rolling_sum += sequence[i]\n    return rolling_sum", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(a_str: str):\n    result = ''\n    frequency_dict = dict()\n    for char in a_str:\n        frequency_dict[char] = frequency_dict.get(char, 0) + 1\n    sorted_dict = sorted(frequency_dict.items(), key=lambda item: item[1], reverse=True)\n    mapping_dict = dict()\n    for (idx, (char, _)) in enumerate(sorted_dict):\n        mapping_dict[char] = chr(65 + idx)\n    for char in a_str:\n        if char not in mapping_dict:\n            continue\n        result += mapping_dict[char]\n    return result", "input": "\"Hello, world!\"", "output": "'CDAABEFGBHAIJ'", "imports": [], "original_snippet": "def f(a_str: str):\n    result = ''\n    frequency_dict = dict()\n    for char in a_str:\n        frequency_dict[char] = frequency_dict.get(char, 0) + 1\n    sorted_dict = sorted(frequency_dict.items(), key=lambda item: item[1], reverse=True)\n    mapping_dict = dict()\n    for (idx, (char, _)) in enumerate(sorted_dict):\n        mapping_dict[char] = chr(65 + idx)\n    for char in a_str:\n        if char not in mapping_dict:\n            continue\n        result += mapping_dict[char]\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import math\ndef f(vertices):\n    sides = [math.sqrt((vertices[i][0] - vertices[(i + 1) % len(vertices)][0]) ** 2 + (vertices[i][1] - vertices[(i + 1) % len(vertices)][1]) ** 2) for i in range(len(vertices))]\n    return len(set(sides)) == 1", "input": "[(0, 0), (0, 1), (1, 1), (1, 0)]", "output": "True", "imports": ["import math"], "original_snippet": "import math\ndef f(vertices):\n    sides = [math.sqrt((vertices[i][0] - vertices[(i + 1) % len(vertices)][0]) ** 2 + (vertices[i][1] - vertices[(i + 1) % len(vertices)][1]) ** 2) for i in range(len(vertices))]\n    return len(set(sides)) == 1", "composite_functions": [], "_input_type": "list", "_output_type": "bool"}
{"snippet": "def f(a):\n    b = a * 3\n    c = b + 2\n    d = c // 4\n    return d", "input": "13", "output": "10", "imports": [], "original_snippet": "def f(a):\n    b = a * 3\n    c = b + 2\n    d = c // 4\n    return d", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(arr, target):\n    mapping = {}\n    for (i, num) in enumerate(arr):\n        possible_comp = target - num\n        if possible_comp in mapping:\n            return (i, mapping[possible_comp])\n        else:\n            mapping[num] = i\n    return None", "input": "[2, 7, 11, 15], 9", "output": "(1, 0)", "imports": [], "original_snippet": "def f(arr, target):\n    mapping = {}\n    for (i, num) in enumerate(arr):\n        possible_comp = target - num\n        if possible_comp in mapping:\n            return (i, mapping[possible_comp])\n        else:\n            mapping[num] = i\n    return None", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(s, n, i=0):\n    if n == 0:\n        return s\n    result = s + str(n) + str(i)\n    return f(result, n - 1, i + 1)", "input": "\"abc\", 3", "output": "'abc302112'", "imports": [], "original_snippet": "def f(s, n, i=0):\n    if n == 0:\n        return s\n    result = s + str(n) + str(i)\n    return f(result, n - 1, i + 1)", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(input_data):\n    result = []\n    for i in input_data:\n        temp = []\n        for j in range(i):\n            if j % 2 == 0:\n                temp.append(j * 2)\n            else:\n                temp.append(j * 3 + 1)\n        result.extend(temp[::-1])\n    output = set(result)\n    return (len(output), sum(output), tuple(sorted(output)))", "input": "{1, 2, 3, 4, 5}", "output": "(4, 22, (0, 4, 8, 10))", "imports": [], "original_snippet": "def f(input_data):\n    result = []\n    for i in input_data:\n        temp = []\n        for j in range(i):\n            if j % 2 == 0:\n                temp.append(j * 2)\n            else:\n                temp.append(j * 3 + 1)\n        result.extend(temp[::-1])\n    output = set(result)\n    return (len(output), sum(output), tuple(sorted(output)))", "composite_functions": [], "_input_type": "set", "_output_type": "tuple"}
{"snippet": "def f(a):\n    if int(a, 2) % 2:\n        m = int((int(a, 2) - 1) / 2)\n        r = bin(m ^ m << 1)\n    else:\n        m = int(a, 2)\n        r = str(''.join(['0' if elem.isnumeric() else '1' for elem in a]))\n    return r", "input": "'1101101'", "output": "'0b1011010'", "imports": [], "original_snippet": "def f(a):\n    if int(a, 2) % 2:\n        m = int((int(a, 2) - 1) / 2)\n        r = bin(m ^ m << 1)\n    else:\n        m = int(a, 2)\n        r = str(''.join(['0' if elem.isnumeric() else '1' for elem in a]))\n    return r", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(a, b):\n    result = 0\n    while b != 0:\n        result += a\n        b -= 1\n    return result", "input": "3, 4", "output": "12", "imports": [], "original_snippet": "def f(a, b):\n    result = 0\n    while b != 0:\n        result += a\n        b -= 1\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(l1, l2):\n    result = []\n    for item in l1:\n        if item in l2 and item not in result:\n            result.append(item)\n    return result", "input": "[1, 2, 2, 3, 4, 4, 5], [3, 4, 4, 5, 6]", "output": "[3, 4, 5]", "imports": [], "original_snippet": "def f(l1, l2):\n    result = []\n    for item in l1:\n        if item in l2 and item not in result:\n            result.append(item)\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(nodes, edges, src, dest):\n    from collections import defaultdict\n    graph = defaultdict(list)\n    for (weight, u, v) in edges:\n        graph[u].append((v, weight))\n        graph[v].append((u, weight))\n    visited = {node: False for node in nodes}\n    shortest_distances = {node: float('inf') for node in nodes}\n    shortest_distances[src] = 0\n    from queue import Queue\n    q = Queue()\n    q.put((0, src))\n    while not q.empty():\n        (distance, node) = q.get()\n        if not visited[node]:\n            visited[node] = True\n            for (neighbor, weight) in graph[node]:\n                if shortest_distances[neighbor] > distance + weight:\n                    shortest_distances[neighbor] = distance + weight\n                    q.put((distance + weight, neighbor))\n    return shortest_distances[dest]", "input": "['A', 'B', 'C', 'D', 'E'], [(2, 'A', 'B'), (3, 'A', 'C'), (2, 'B', 'C'), (1, 'B', 'D'), (4, 'C', 'D'), (4, 'C', 'E'), (3, 'D', 'E')], 'A', 'E'", "output": "6", "imports": ["from collections import defaultdict", "from queue import Queue"], "original_snippet": "def f(nodes, edges, src, dest):\n    from collections import defaultdict\n    graph = defaultdict(list)\n    for (weight, u, v) in edges:\n        graph[u].append((v, weight))\n        graph[v].append((u, weight))\n    visited = {node: False for node in nodes}\n    shortest_distances = {node: float('inf') for node in nodes}\n    shortest_distances[src] = 0\n    from queue import Queue\n    q = Queue()\n    q.put((0, src))\n    while not q.empty():\n        (distance, node) = q.get()\n        if not visited[node]:\n            visited[node] = True\n            for (neighbor, weight) in graph[node]:\n                if shortest_distances[neighbor] > distance + weight:\n                    shortest_distances[neighbor] = distance + weight\n                    q.put((distance + weight, neighbor))\n    return shortest_distances[dest]", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(arr: list, k: int):\n    sum_dict = {0: 1}\n    sum_so_far = 0\n    count = 0\n    for num in arr:\n        sum_so_far += num\n        if sum_so_far - k in sum_dict:\n            count += sum_dict[sum_so_far - k]\n        if sum_so_far in sum_dict:\n            sum_dict[sum_so_far] += 1\n        else:\n            sum_dict[sum_so_far] = 1\n    return count", "input": "[1, 1, 1, 1, 1, 2, 2], 3", "output": "4", "imports": [], "original_snippet": "def f(arr: list, k: int):\n    sum_dict = {0: 1}\n    sum_so_far = 0\n    count = 0\n    for num in arr:\n        sum_so_far += num\n        if sum_so_far - k in sum_dict:\n            count += sum_dict[sum_so_far - k]\n        if sum_so_far in sum_dict:\n            sum_dict[sum_so_far] += 1\n        else:\n            sum_dict[sum_so_far] = 1\n    return count", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(lst):\n    products = [x * y for (x, y) in lst]\n    return sum(products)", "input": "[(1, 2), (3, 4), (5, 6)]", "output": "44", "imports": [], "original_snippet": "def f(lst):\n    products = [x * y for (x, y) in lst]\n    return sum(products)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(data):\n    result = ''\n    prev_char = None\n    count = 1\n    for char in data:\n        if char == prev_char:\n            count += 1\n        else:\n            if prev_char:\n                result += str(count) + prev_char\n            count = 1\n            prev_char = char\n    if prev_char:\n        result += str(count) + prev_char\n    return result", "input": "\"aaabbbcccdddd\"", "output": "'3a3b3c4d'", "imports": [], "original_snippet": "def f(data):\n    result = ''\n    prev_char = None\n    count = 1\n    for char in data:\n        if char == prev_char:\n            count += 1\n        else:\n            if prev_char:\n                result += str(count) + prev_char\n            count = 1\n            prev_char = char\n    if prev_char:\n        result += str(count) + prev_char\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(lst: list):\n    lst.sort()\n    lst = [x for x in lst if x % 2 != 0]\n    for i in range(len(lst)):\n        lst[i] = lst[i] * 2\n    lst = lst[::-1]\n    return lst", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "[18, 14, 10, 6, 2]", "imports": [], "original_snippet": "def f(lst: list):\n    lst.sort()\n    lst = [x for x in lst if x % 2 != 0]\n    for i in range(len(lst)):\n        lst[i] = lst[i] * 2\n    lst = lst[::-1]\n    return lst", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(nums: list):\n    filtered_nums = [num for num in nums if num % 2 != 0]\n    squared_nums = [num ** 2 for num in filtered_nums]\n    reversed_nums = squared_nums[::-1]\n    total = sum(reversed_nums)\n    return total", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9]", "output": "165", "imports": [], "original_snippet": "def f(nums: list):\n    filtered_nums = [num for num in nums if num % 2 != 0]\n    squared_nums = [num ** 2 for num in filtered_nums]\n    reversed_nums = squared_nums[::-1]\n    total = sum(reversed_nums)\n    return total", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(data, multiplier=1, depth=0):\n    total = 0\n    for (key, value) in data.items():\n        if isinstance(value, dict):\n            total += f(value, multiplier * (depth + 1), depth + 1)\n        elif isinstance(value, (int, float)):\n            total += value * multiplier\n        elif isinstance(value, str):\n            if value == 'string':\n                total += 10 * multiplier\n            else:\n                total += 5 * multiplier\n    return total", "input": "{'a': 1, 'b': {'c': 2, 'd': {'e': 3, 'f': 'string'}}, 'g': 'string'}", "output": "39", "imports": [], "original_snippet": "def f(data, multiplier=1, depth=0):\n    total = 0\n    for (key, value) in data.items():\n        if isinstance(value, dict):\n            total += f(value, multiplier * (depth + 1), depth + 1)\n        elif isinstance(value, (int, float)):\n            total += value * multiplier\n        elif isinstance(value, str):\n            if value == 'string':\n                total += 10 * multiplier\n            else:\n                total += 5 * multiplier\n    return total", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(numbers):\n    distances = []\n    for i in range(len(numbers) - 1):\n        distance = numbers[i + 1] - numbers[i]\n        distances.append((numbers[i], numbers[i + 1], distance))\n    return distances", "input": "[1, 3, 5, 7]", "output": "[(1, 3, 2), (3, 5, 2), (5, 7, 2)]", "imports": [], "original_snippet": "def f(numbers):\n    distances = []\n    for i in range(len(numbers) - 1):\n        distance = numbers[i + 1] - numbers[i]\n        distances.append((numbers[i], numbers[i + 1], distance))\n    return distances", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list: list):\n    total_sum = 0\n    result_list = []\n    for i in range(len(input_list)):\n        repeated_element_list = input_list[0:i] + input_list[i + 1:]\n        total_sum += sum(repeated_element_list)\n        result_list.append(total_sum)\n    return result_list[::-1]", "input": "[3, 7, 9, 12]", "output": "[93, 74, 52, 28]", "imports": [], "original_snippet": "def f(input_list: list):\n    total_sum = 0\n    result_list = []\n    for i in range(len(input_list)):\n        repeated_element_list = input_list[0:i] + input_list[i + 1:]\n        total_sum += sum(repeated_element_list)\n        result_list.append(total_sum)\n    return result_list[::-1]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list):\n    squared_list = [x * x for x in input_list]\n    unique_squares = set(squared_list)\n    sum_of_unique_squares = sum(unique_squares)\n    sorted_squares = sorted(squared_list)\n    even_indexed_sum = sum((i for i in range(len(sorted_squares)) if i % 2 == 0))\n    return (sum_of_unique_squares, even_indexed_sum)", "input": "[2, 3, 5, 4, 6, 3, 2, 1, 2]", "output": "(91, 20)", "imports": [], "original_snippet": "def f(input_list):\n    squared_list = [x * x for x in input_list]\n    unique_squares = set(squared_list)\n    sum_of_unique_squares = sum(unique_squares)\n    sorted_squares = sorted(squared_list)\n    even_indexed_sum = sum((i for i in range(len(sorted_squares)) if i % 2 == 0))\n    return (sum_of_unique_squares, even_indexed_sum)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(str1, str2, lst):\n    from itertools import permutations\n    seen = set()\n    for p in permutations(str1 + str2):\n        p_str = ''.join(p)\n        if p_str not in seen:\n            seen.add(p_str)\n            lst.append(p_str)\n    return sorted(list(set(lst)))", "input": "'We', 'to', ['dog', 'cat', 'I']", "output": "['I', 'Weot', 'Weto', 'Woet', 'Wote', 'Wteo', 'Wtoe', 'cat', 'dog', 'eWot', 'eWto', 'eoWt', 'eotW', 'etWo', 'etoW', 'oWet', 'oWte', 'oeWt', 'oetW', 'otWe', 'oteW', 'tWeo', 'tWoe', 'teWo', 'teoW', 'toWe', 'toeW']", "imports": ["from itertools import permutations"], "original_snippet": "def f(str1, str2, lst):\n    from itertools import permutations\n    seen = set()\n    for p in permutations(str1 + str2):\n        p_str = ''.join(p)\n        if p_str not in seen:\n            seen.add(p_str)\n            lst.append(p_str)\n    return sorted(list(set(lst)))", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(input_lst):\n    result_store = []\n    for element in input_lst:\n        if element not in result_store:\n            result_store.append(element)\n    result_store.sort()\n    return result_store", "input": "[3, 1, 4, 1, 5, 9, 2, 6, 5, 3, 5, 8, 9, 7, 9]", "output": "[1, 2, 3, 4, 5, 6, 7, 8, 9]", "imports": [], "original_snippet": "def f(input_lst):\n    result_store = []\n    for element in input_lst:\n        if element not in result_store:\n            result_store.append(element)\n    result_store.sort()\n    return result_store", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(lst: list):\n    unique_start = set()\n    for d in lst:\n        unique_start.add(d['start'])\n    result = {str(start): [] for start in unique_start}\n    for d in lst:\n        temp_lst = result[str(d['start'])]\n        existing_end_values = [t[0] for t in temp_lst]\n        if d['end'] not in existing_end_values:\n            count = len([t[0] for t in temp_lst if t[0] <= d['end']])\n            temp_lst.append((d['end'], count))\n            result[str(d['start'])] = sorted(temp_lst, key=lambda x: x[0])\n    return result", "input": "[{'start': 1, 'end': 3}, {'start': 1, 'end': 2}, {'start': 2, 'end': 4}, {'start': 2, 'end': 3}, {'start': 1, 'end': 3}]", "output": "{'1': [(2, 0), (3, 0)], '2': [(3, 0), (4, 0)]}", "imports": [], "original_snippet": "def f(lst: list):\n    unique_start = set()\n    for d in lst:\n        unique_start.add(d['start'])\n    result = {str(start): [] for start in unique_start}\n    for d in lst:\n        temp_lst = result[str(d['start'])]\n        existing_end_values = [t[0] for t in temp_lst]\n        if d['end'] not in existing_end_values:\n            count = len([t[0] for t in temp_lst if t[0] <= d['end']])\n            temp_lst.append((d['end'], count))\n            result[str(d['start'])] = sorted(temp_lst, key=lambda x: x[0])\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(x, y=None, l=[1, 'a'], s=0):\n    d = {'a': x + 1, 'b': x + 5}\n    if l[1] == 'a':\n        x_input = x ** 3 - d['a']\n    if x >= 100:\n        for value in range(1, x + 1):\n            a = {value: value * 2}\n            if a[value] % 2 == 0:\n                x_input += value\n    xl = list(str(x_input))\n    for x in xl:\n        s += int(x)\n        if s == l[0] and l[1] == 'a':\n            l[1] = 'b'\n    return (format(d['b'], '0x'), s, l)", "input": "130", "output": "('87', 24, [1, 'a'])", "imports": [], "original_snippet": "def f(x, y=None, l=[1, 'a'], s=0):\n    d = {'a': x + 1, 'b': x + 5}\n    if l[1] == 'a':\n        x_input = x ** 3 - d['a']\n    if x >= 100:\n        for value in range(1, x + 1):\n            a = {value: value * 2}\n            if a[value] % 2 == 0:\n                x_input += value\n    xl = list(str(x_input))\n    for x in xl:\n        s += int(x)\n        if s == l[0] and l[1] == 'a':\n            l[1] = 'b'\n    return (format(d['b'], '0x'), s, l)", "composite_functions": [], "_input_type": "int", "_output_type": "tuple"}
{"snippet": "def f(numbers):\n    evens = sorted((num for num in numbers if num % 2 == 0))\n    odds = sorted((num for num in numbers if num % 2 != 0))\n    modified = []\n    for i in range(max(len(evens), len(odds))):\n        if i < len(evens):\n            modified.append(evens[i])\n        if i < len(odds):\n            modified.append(odds[i])\n    new_numbers = []\n    for num in modified:\n        binary_num = bin(num)[2:]\n        missing_bit = '0' if binary_num.count('1') % 2 == 0 else '1'\n        new_binary_num = missing_bit + binary_num\n        new_numbers.append(int(new_binary_num, 2) * 2)\n    return new_numbers", "input": "[1, 3, 5, 7, 9, 11, 13, 15, 17, 19]", "output": "[6, 6, 10, 30, 18, 54, 58, 30, 34, 102]", "imports": [], "original_snippet": "def f(numbers):\n    evens = sorted((num for num in numbers if num % 2 == 0))\n    odds = sorted((num for num in numbers if num % 2 != 0))\n    modified = []\n    for i in range(max(len(evens), len(odds))):\n        if i < len(evens):\n            modified.append(evens[i])\n        if i < len(odds):\n            modified.append(odds[i])\n    new_numbers = []\n    for num in modified:\n        binary_num = bin(num)[2:]\n        missing_bit = '0' if binary_num.count('1') % 2 == 0 else '1'\n        new_binary_num = missing_bit + binary_num\n        new_numbers.append(int(new_binary_num, 2) * 2)\n    return new_numbers", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_data):\n    (even_sum, odd_sum, uppercase_length, lowercase_length, digit_length) = (0, 0, 0, 0, 0)\n    for item in input_data:\n        if isinstance(item, int):\n            if item % 2 == 0:\n                even_sum += item\n            else:\n                odd_sum += item\n        elif isinstance(item, str):\n            uppercase_length += sum((1 for char in item if char.isupper()))\n            lowercase_length += sum((1 for char in item if char.islower()))\n            digit_length += sum((1 for char in item if char.isdigit()))\n    total_length = uppercase_length + lowercase_length + digit_length\n    return (total_length, uppercase_length, lowercase_length, digit_length, even_sum, odd_sum)", "input": "[10, 'ABCDE123abc', 7, 'hi']", "output": "(13, 5, 5, 3, 10, 7)", "imports": [], "original_snippet": "def f(input_data):\n    (even_sum, odd_sum, uppercase_length, lowercase_length, digit_length) = (0, 0, 0, 0, 0)\n    for item in input_data:\n        if isinstance(item, int):\n            if item % 2 == 0:\n                even_sum += item\n            else:\n                odd_sum += item\n        elif isinstance(item, str):\n            uppercase_length += sum((1 for char in item if char.isupper()))\n            lowercase_length += sum((1 for char in item if char.islower()))\n            digit_length += sum((1 for char in item if char.isdigit()))\n    total_length = uppercase_length + lowercase_length + digit_length\n    return (total_length, uppercase_length, lowercase_length, digit_length, even_sum, odd_sum)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(brackets: str):\n    stack = []\n    for char in brackets:\n        if char in '([{':\n            stack.append(char)\n        elif char in ')]}':\n            if not stack:\n                return False\n            pair = '({[}])'[3 - '({['.index(stack[-1])]\n            if pair != char:\n                return False\n            stack.pop()\n    return not stack", "input": "\"([{}])\"", "output": "False", "imports": [], "original_snippet": "def f(brackets: str):\n    stack = []\n    for char in brackets:\n        if char in '([{':\n            stack.append(char)\n        elif char in ')]}':\n            if not stack:\n                return False\n            pair = '({[}])'[3 - '({['.index(stack[-1])]\n            if pair != char:\n                return False\n            stack.pop()\n    return not stack", "composite_functions": [], "_input_type": "str", "_output_type": "bool"}
{"snippet": "def f(s1: str, s2: str):\n    reflection_map = {'A': 'Z', 'B': 'Y', 'C': 'X', 'D': 'W', 'E': 'V', 'F': 'U', 'G': 'T', 'H': 'S', 'I': 'R', 'J': 'Q', 'K': 'P', 'L': 'O', 'M': 'N', 'N': 'M', 'O': 'L', 'P': 'K', 'Q': 'J', 'R': 'I', 'S': 'H', 'T': 'G', 'U': 'F', 'V': 'E', 'W': 'D', 'X': 'C', 'Y': 'B', 'Z': 'A'}\n    reflected_s1 = ''.join((reflection_map.get(c, c) for c in s1.upper()))\n    return s2 + reflected_s1", "input": "'THE LEADING FIRM', 'TO THE UNDEVELOPED CRIMES'", "output": "'TO THE UNDEVELOPED CRIMESGSV OVZWRMT URIN'", "imports": [], "original_snippet": "def f(s1: str, s2: str):\n    reflection_map = {'A': 'Z', 'B': 'Y', 'C': 'X', 'D': 'W', 'E': 'V', 'F': 'U', 'G': 'T', 'H': 'S', 'I': 'R', 'J': 'Q', 'K': 'P', 'L': 'O', 'M': 'N', 'N': 'M', 'O': 'L', 'P': 'K', 'Q': 'J', 'R': 'I', 'S': 'H', 'T': 'G', 'U': 'F', 'V': 'E', 'W': 'D', 'X': 'C', 'Y': 'B', 'Z': 'A'}\n    reflected_s1 = ''.join((reflection_map.get(c, c) for c in s1.upper()))\n    return s2 + reflected_s1", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(d):\n    total = 0\n    for value in d.values():\n        if isinstance(value, dict):\n            total += f(value)\n        elif isinstance(value, int):\n            total += value\n    return total", "input": "{'a': 1, 'b': 2, 'c': 3, 'd': {'e': 4, 'f': {'g': 5, 'h': 6}}}", "output": "21", "imports": [], "original_snippet": "def f(d):\n    total = 0\n    for value in d.values():\n        if isinstance(value, dict):\n            total += f(value)\n        elif isinstance(value, int):\n            total += value\n    return total", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(prev_str: str, current_str: str):\n    p_str = [c == i for (c, i) in zip(prev_str, current_str)]\n    return len([1 for i in range(len(p_str) - 2) if all(p_str[i:i + 3])])", "input": "'abcbacd', 'abcbcad'", "output": "2", "imports": [], "original_snippet": "def f(prev_str: str, current_str: str):\n    p_str = [c == i for (c, i) in zip(prev_str, current_str)]\n    return len([1 for i in range(len(p_str) - 2) if all(p_str[i:i + 3])])", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(input_list: list):\n    input_list.sort()\n    result_sum = 0\n    for i in range(1, len(input_list) + 1):\n        result_sum += input_list[i - 1] / i\n    return result_sum", "input": "[9, 5, 7, 3, 1]", "output": "7.716666666666667", "imports": [], "original_snippet": "def f(input_list: list):\n    input_list.sort()\n    result_sum = 0\n    for i in range(1, len(input_list) + 1):\n        result_sum += input_list[i - 1] / i\n    return result_sum", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(s: str):\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    count = 0\n    max_length = 0\n    current_length = 0\n    for i in range(len(s)):\n        if s[i].lower() in vowels:\n            count += 1\n            current_length += 1\n            max_length = max(max_length, current_length)\n        else:\n            current_length = 0\n    return (count, max_length)", "input": "\"hello world, this is a test\"", "output": "(7, 1)", "imports": [], "original_snippet": "def f(s: str):\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    count = 0\n    max_length = 0\n    current_length = 0\n    for i in range(len(s)):\n        if s[i].lower() in vowels:\n            count += 1\n            current_length += 1\n            max_length = max(max_length, current_length)\n        else:\n            current_length = 0\n    return (count, max_length)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(numbers):\n    evens = sorted((num for num in numbers if num % 2 == 0))\n    odds = sorted((num for num in numbers if num % 2 != 0))\n    sorted_nums = []\n    for i in range(max(len(evens), len(odds))):\n        if i < len(evens):\n            sorted_nums.append(evens[i])\n        if i < len(odds):\n            sorted_nums.append(odds[i])\n    result = []\n    for (i, num) in enumerate(sorted_nums):\n        binary = bin(num)[2:]\n        padded_binary = binary.zfill(4)\n        int_binary = int(padded_binary, 2)\n        result.append(str(int_binary * (i + 1)))\n    return result", "input": "[9, 8, 7, 6, 5, 4, 3, 2, 1]", "output": "['2', '2', '12', '12', '30', '30', '56', '56', '81']", "imports": [], "original_snippet": "def f(numbers):\n    evens = sorted((num for num in numbers if num % 2 == 0))\n    odds = sorted((num for num in numbers if num % 2 != 0))\n    sorted_nums = []\n    for i in range(max(len(evens), len(odds))):\n        if i < len(evens):\n            sorted_nums.append(evens[i])\n        if i < len(odds):\n            sorted_nums.append(odds[i])\n    result = []\n    for (i, num) in enumerate(sorted_nums):\n        binary = bin(num)[2:]\n        padded_binary = binary.zfill(4)\n        int_binary = int(padded_binary, 2)\n        result.append(str(int_binary * (i + 1)))\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "import heapq\ndef f(arr, k):\n    biggest_max = float('-inf')\n    smallest_k_ones = []\n    for x in arr:\n        biggest_max = max(biggest_max, x)\n        if len(smallest_k_ones) < k:\n            heapq.heappush(smallest_k_ones, x)\n        elif x > smallest_k_ones[0]:\n            heapq.heappop(smallest_k_ones)\n            heapq.heappush(smallest_k_ones, x)\n    return biggest_max * len(smallest_k_ones)", "input": "[1, 9, 8, 4, 3, 7, 5, 6, 2], 4", "output": "36", "imports": ["import heapq"], "original_snippet": "import heapq\ndef f(arr, k):\n    biggest_max = float('-inf')\n    smallest_k_ones = []\n    for x in arr:\n        biggest_max = max(biggest_max, x)\n        if len(smallest_k_ones) < k:\n            heapq.heappush(smallest_k_ones, x)\n        elif x > smallest_k_ones[0]:\n            heapq.heappop(smallest_k_ones)\n            heapq.heappush(smallest_k_ones, x)\n    return biggest_max * len(smallest_k_ones)", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(lst: list):\n    result = []\n    for i in range(len(lst)):\n        count_dict = {}\n        for j in range(i, len(lst)):\n            key = str(lst[j] + lst[j // len(lst)] * (j % len(lst)))\n            count_dict[key] = count_dict.get(key, 0) + 1\n        sorted_dict = dict(sorted(count_dict.items(), key=lambda item: -item[1]))\n        result.append(max(sorted_dict, key=sorted_dict.get) + str(max(sorted_dict.values())))\n    return result[::-1]", "input": "[1, 2, 3, 4, 5]", "output": "['91', '71', '51', '31', '11']", "imports": [], "original_snippet": "def f(lst: list):\n    result = []\n    for i in range(len(lst)):\n        count_dict = {}\n        for j in range(i, len(lst)):\n            key = str(lst[j] + lst[j // len(lst)] * (j % len(lst)))\n            count_dict[key] = count_dict.get(key, 0) + 1\n        sorted_dict = dict(sorted(count_dict.items(), key=lambda item: -item[1]))\n        result.append(max(sorted_dict, key=sorted_dict.get) + str(max(sorted_dict.values())))\n    return result[::-1]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(data):\n    unique_days = set()\n    unique_cities = set()\n    for record in data:\n        unique_days.add(record['date'])\n        unique_cities.add(record['city'])\n    dates = sorted(unique_days)\n    city_freq = {city: dates.count(record['date']) for city in unique_cities}\n    for (city, freq) in city_freq.items():\n        city_freq[city] = freq ** 2\n    return city_freq", "input": "[{'date': '2023-01-01', 'city': 'New York'}, {'date': '2023-01-01', 'city': 'New York'}, {'date': '2023-01-01', 'city': 'Los Angeles'}, {'date': '2023-01-01', 'city': 'Los Angeles'}, {'date': '2023-01-02', 'city': 'Los Angeles'}, {'date': '2023-01-02', 'city': 'Los Angeles'}, {'date': '2023-01-02', 'city': 'Chicago'}, {'date': '2023-01-02', 'city': 'Chicago'}, {'date': '2023-01-03', 'city': 'Chicago'}, {'date': '2023-01-03', 'city': 'Chicago'}, {'date': '2023-01-04', 'city': 'Chicago'}, {'date': '2023-01-04', 'city': 'Chicago'}]", "output": "{'Chicago': 1, 'New York': 1, 'Los Angeles': 1}", "imports": [], "original_snippet": "def f(data):\n    unique_days = set()\n    unique_cities = set()\n    for record in data:\n        unique_days.add(record['date'])\n        unique_cities.add(record['city'])\n    dates = sorted(unique_days)\n    city_freq = {city: dates.count(record['date']) for city in unique_cities}\n    for (city, freq) in city_freq.items():\n        city_freq[city] = freq ** 2\n    return city_freq", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(input_list: list):\n    unique_elements = set()\n    squares_reversed = []\n    for num in input_list:\n        unique_elements.add(num)\n    for num in unique_elements:\n        squares_reversed.append(num ** 2)\n    squares_reversed.reverse()\n    return sorted(squares_reversed)", "input": "[2, 2, 3, 3, 4, 4, 5]", "output": "[4, 9, 16, 25]", "imports": [], "original_snippet": "def f(input_list: list):\n    unique_elements = set()\n    squares_reversed = []\n    for num in input_list:\n        unique_elements.add(num)\n    for num in unique_elements:\n        squares_reversed.append(num ** 2)\n    squares_reversed.reverse()\n    return sorted(squares_reversed)", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(a: list, b: str, index: int):\n    modified_list = [x * 2 for x in a]\n    b = ''.join([char.lower() if i % 2 == 0 else char.upper() for (i, char) in enumerate(b)])\n    modified_list.append(index)\n    for char in b:\n        if modified_list[-1] % 2 == 0:\n            modified_list = [x + 1 for x in modified_list]\n        elif modified_list[-1] % 2 != 0:\n            modified_list = [x - 1 for x in modified_list]\n    filtered_list = [x for x in modified_list if x % 2 == 0]\n    return sum(filtered_list)", "input": "[1, 2, 3, 4, 5], \"Python\", 2", "output": "32", "imports": [], "original_snippet": "def f(a: list, b: str, index: int):\n    modified_list = [x * 2 for x in a]\n    b = ''.join([char.lower() if i % 2 == 0 else char.upper() for (i, char) in enumerate(b)])\n    modified_list.append(index)\n    for char in b:\n        if modified_list[-1] % 2 == 0:\n            modified_list = [x + 1 for x in modified_list]\n        elif modified_list[-1] % 2 != 0:\n            modified_list = [x - 1 for x in modified_list]\n    filtered_list = [x for x in modified_list if x % 2 == 0]\n    return sum(filtered_list)", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(lst: list):\n    sorted_lst = sorted(lst)\n    sorted_lst.append(sorted_lst[0])\n    del sorted_lst[1]\n    return sum(sorted_lst)", "input": "[4, 1, 3, 2]", "output": "9", "imports": [], "original_snippet": "def f(lst: list):\n    sorted_lst = sorted(lst)\n    sorted_lst.append(sorted_lst[0])\n    del sorted_lst[1]\n    return sum(sorted_lst)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers):\n    sorted_squares = sorted((num ** 2 for num in numbers))\n    sum_of_even_indexed_squares = sum((sorted_squares[i] for i in range(0, len(sorted_squares), 2)))\n    average_of_squares = sum(sorted_squares) / len(sorted_squares)\n    return (sum_of_even_indexed_squares, average_of_squares)", "input": "[4, 1, 7, 2, 6, 3, 9]", "output": "(127, 28.0)", "imports": [], "original_snippet": "def f(numbers):\n    sorted_squares = sorted((num ** 2 for num in numbers))\n    sum_of_even_indexed_squares = sum((sorted_squares[i] for i in range(0, len(sorted_squares), 2)))\n    average_of_squares = sum(sorted_squares) / len(sorted_squares)\n    return (sum_of_even_indexed_squares, average_of_squares)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(alias, date):\n    alias_dict = {'adam': ['Seattle', 'Los Angeles'], 'beverly': ['Philly', 'New York']}\n    date_dict = {'2023-03-11': ['Los Angeles', 'San Francisco']}\n    start_pos = None\n    end_pos = None\n    for (name, path) in alias_dict.items():\n        if alias.lower() in name:\n            start_pos = path[0]\n            end_pos = path[1]\n            break\n    for (dat, pos) in date_dict.items():\n        if date == dat:\n            start_pos = pos[0]\n            end_pos = pos[1]\n            break\n    return f'Start from {start_pos} and end at {end_pos}'", "input": "\"bever\", \"2023-03-11\"", "output": "'Start from Los Angeles and end at San Francisco'", "imports": [], "original_snippet": "def f(alias, date):\n    alias_dict = {'adam': ['Seattle', 'Los Angeles'], 'beverly': ['Philly', 'New York']}\n    date_dict = {'2023-03-11': ['Los Angeles', 'San Francisco']}\n    start_pos = None\n    end_pos = None\n    for (name, path) in alias_dict.items():\n        if alias.lower() in name:\n            start_pos = path[0]\n            end_pos = path[1]\n            break\n    for (dat, pos) in date_dict.items():\n        if date == dat:\n            start_pos = pos[0]\n            end_pos = pos[1]\n            break\n    return f'Start from {start_pos} and end at {end_pos}'", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(input_list):\n    sorted_list = sorted(input_list)\n    running_sum = [sum(sorted_list[:i + 1]) for (i, _) in enumerate(sorted_list)]\n    reversed_running_sum = running_sum[::-1]\n    filtered_result = [x for (i, x) in enumerate(reversed_running_sum) if x > i + 1]\n    return filtered_result", "input": "[3, 1, 2]", "output": "[6, 3]", "imports": [], "original_snippet": "def f(input_list):\n    sorted_list = sorted(input_list)\n    running_sum = [sum(sorted_list[:i + 1]) for (i, _) in enumerate(sorted_list)]\n    reversed_running_sum = running_sum[::-1]\n    filtered_result = [x for (i, x) in enumerate(reversed_running_sum) if x > i + 1]\n    return filtered_result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(numbers):\n    result = {}\n    total = 0\n    for num in numbers:\n        if num % 2 == 0:\n            square = num ** 2\n            result[square] = total\n        if num % 2 != 0:\n            total += num\n    return result", "input": "[2, 3, 4, 5, 6]", "output": "{4: 0, 16: 3, 36: 8}", "imports": [], "original_snippet": "def f(numbers):\n    result = {}\n    total = 0\n    for num in numbers:\n        if num % 2 == 0:\n            square = num ** 2\n            result[square] = total\n        if num % 2 != 0:\n            total += num\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(input_dict):\n    (counts, numbers) = (input_dict['counts'], input_dict['numbers'])\n    sorted_numbers = sorted(numbers)\n    largest_count = max(counts.values())\n    char_with_largest_count = max(counts, key=counts.get)\n    if largest_count > len(sorted_numbers) / 2:\n        sorted_numbers = [ord(char_with_largest_count)] * len(sorted_numbers)\n    return sorted_numbers[-1]", "input": "{\"counts\": {\"a\": 10, \"b\": 5, \"c\": 3, \"d\": 15}, \"numbers\": [5, 2, 8, 1, 9]}", "output": "100", "imports": [], "original_snippet": "def f(input_dict):\n    (counts, numbers) = (input_dict['counts'], input_dict['numbers'])\n    sorted_numbers = sorted(numbers)\n    largest_count = max(counts.values())\n    char_with_largest_count = max(counts, key=counts.get)\n    if largest_count > len(sorted_numbers) / 2:\n        sorted_numbers = [ord(char_with_largest_count)] * len(sorted_numbers)\n    return sorted_numbers[-1]", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(numbers):\n    product = 1\n    for num in numbers:\n        if num > 0 and isinstance(num, int):\n            product *= num\n    even_numbers = [num for num in numbers if num > 0 and num % 2 == 0 and isinstance(num, int)]\n    even_numbers.sort(reverse=True)\n    result = even_numbers[0] - even_numbers[-1] if even_numbers else product\n    return result", "input": "[10, -5, 7, 2, 3.5, 4, 5, 20, 3]", "output": "18", "imports": [], "original_snippet": "def f(numbers):\n    product = 1\n    for num in numbers:\n        if num > 0 and isinstance(num, int):\n            product *= num\n    even_numbers = [num for num in numbers if num > 0 and num % 2 == 0 and isinstance(num, int)]\n    even_numbers.sort(reverse=True)\n    result = even_numbers[0] - even_numbers[-1] if even_numbers else product\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(l: list[int]):\n    product = 1\n    for i in range(len(l)):\n        product *= l[i]\n        if product in l and i < len(l) - 1:\n            product //= l[i]\n            continue\n        l[i] = product\n    return l", "input": "[2,3]", "output": "[2, 3]", "imports": [], "original_snippet": "def f(l: list[int]):\n    product = 1\n    for i in range(len(l)):\n        product *= l[i]\n        if product in l and i < len(l) - 1:\n            product //= l[i]\n            continue\n        l[i] = product\n    return l", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(data: dict) -> dict:\n    result = {'below_25_names': [], 'total_below_25': 0, 'above_25_names': [], 'total_above_25': 0, 'count_with_matching_name_total': 0}\n    data = dict(sorted(data.items(), key=lambda x: x[1]['age']))\n    for (key, value) in data.items():\n        age = value['age']\n        if age < 25:\n            result['below_25_names'].append(key)\n            result['total_below_25'] += 1\n        elif age >= 25:\n            result['above_25_names'].append(key)\n            result['total_above_25'] += 1\n    age_mapping = {'below': 25, 'above': result['total_above_25']}\n    for (k, v) in data.items():\n        if v.get('name') in result[f\"{('below' if v['age'] < age_mapping['above'] else 'above')}_25_names\"]:\n            result['count_with_matching_name_total'] += 1\n    return result", "input": "{'John': {'name': 'John', 'age': 20}, 'Alice': {'name': 'Alice', 'age': 24}, 'Bob': {'name': 'Bob', 'age': 30}, 'JoeBob': {'name': 'JoeBob', 'age': 26}, 'Joe': {'name': 'Joe', 'age': 21}}", "output": "{'below_25_names': ['John', 'Joe', 'Alice'], 'total_below_25': 3, 'above_25_names': ['JoeBob', 'Bob'], 'total_above_25': 2, 'count_with_matching_name_total': 2}", "imports": [], "original_snippet": "def f(data: dict) -> dict:\n    result = {'below_25_names': [], 'total_below_25': 0, 'above_25_names': [], 'total_above_25': 0, 'count_with_matching_name_total': 0}\n    data = dict(sorted(data.items(), key=lambda x: x[1]['age']))\n    for (key, value) in data.items():\n        age = value['age']\n        if age < 25:\n            result['below_25_names'].append(key)\n            result['total_below_25'] += 1\n        elif age >= 25:\n            result['above_25_names'].append(key)\n            result['total_above_25'] += 1\n    age_mapping = {'below': 25, 'above': result['total_above_25']}\n    for (k, v) in data.items():\n        if v.get('name') in result[f\"{('below' if v['age'] < age_mapping['above'] else 'above')}_25_names\"]:\n            result['count_with_matching_name_total'] += 1\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(nums: list, Q: list, R: list):\n    num_indices = {num: [] for num in nums}\n    for (i, num) in enumerate(nums):\n        num_indices[num].append(i)\n    for (q, r) in zip(Q, R):\n        if num_indices.get(q):\n            if r - 1 < len(num_indices[q]):\n                nums[num_indices[q][r - 1]] = 0\n    return sum((num for num in nums if num != 0))", "input": "[1, 2, 3, 2, 4, 1, 7, 4, 4], [2, 4], [2, 1]", "output": "22", "imports": [], "original_snippet": "def f(nums: list, Q: list, R: list):\n    num_indices = {num: [] for num in nums}\n    for (i, num) in enumerate(nums):\n        num_indices[num].append(i)\n    for (q, r) in zip(Q, R):\n        if num_indices.get(q):\n            if r - 1 < len(num_indices[q]):\n                nums[num_indices[q][r - 1]] = 0\n    return sum((num for num in nums if num != 0))", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(lst):\n    largest_11_13 = []\n    for num in lst:\n        multiples_of_11 = [x for x in range(num, -1, -1) if x % 11 == 0]\n        multiples_of_13 = [x for x in range(num, -1, -1) if x % 13 == 0]\n        largest_multiple = max(multiples_of_11[-1], multiples_of_13[-1])\n        largest_11_13.append(largest_multiple)\n    result = sum((x * y for (x, y) in zip(largest_11_13, largest_11_13)))\n    return result", "input": "[22, 26, 50, 65]", "output": "0", "imports": [], "original_snippet": "def f(lst):\n    largest_11_13 = []\n    for num in lst:\n        multiples_of_11 = [x for x in range(num, -1, -1) if x % 11 == 0]\n        multiples_of_13 = [x for x in range(num, -1, -1) if x % 13 == 0]\n        largest_multiple = max(multiples_of_11[-1], multiples_of_13[-1])\n        largest_11_13.append(largest_multiple)\n    result = sum((x * y for (x, y) in zip(largest_11_13, largest_11_13)))\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(lst, m):\n    squares = [n ** 2 for n in lst]\n    squares.sort()\n    three_squares = []\n    for (i, a) in enumerate(squares):\n        b = squares[len(squares) - 1 - i]\n        c = -b - a\n        if c < b:\n            three_squares.append([a ** 0.5, b ** 0.5, c ** 0.5])\n    return three_squares", "input": "[1, 2, 3, 4, 5], 1", "output": "[[1.0, 5.0, (3.122248963055649e-16+5.0990195135927845j)], [2.0, 4.0, (2.7383934913210134e-16+4.47213595499958j)], [3.0, 3.0, (2.5978681687064796e-16+4.242640687119285j)], [4.0, 2.0, (2.7383934913210134e-16+4.47213595499958j)], [5.0, 1.0, (3.122248963055649e-16+5.0990195135927845j)]]", "imports": [], "original_snippet": "def f(lst, m):\n    squares = [n ** 2 for n in lst]\n    squares.sort()\n    three_squares = []\n    for (i, a) in enumerate(squares):\n        b = squares[len(squares) - 1 - i]\n        c = -b - a\n        if c < b:\n            three_squares.append([a ** 0.5, b ** 0.5, c ** 0.5])\n    return three_squares", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(ll):\n    sums = []\n    has_odd = False\n    for (i, lst) in enumerate(ll):\n        multiplied = [v * (k + 1) for (k, v) in enumerate(lst)]\n        sums.append(sum((v ** 2 for v in reversed(multiplied))))\n        if len(lst) % 2 != 0:\n            has_odd = True\n            return i\n    if has_odd == False:\n        return sums", "input": "[[1, 2, 3], [4, 5], [6, 7, 8, 9]]", "output": "0", "imports": [], "original_snippet": "def f(ll):\n    sums = []\n    has_odd = False\n    for (i, lst) in enumerate(ll):\n        multiplied = [v * (k + 1) for (k, v) in enumerate(lst)]\n        sums.append(sum((v ** 2 for v in reversed(multiplied))))\n        if len(lst) % 2 != 0:\n            has_odd = True\n            return i\n    if has_odd == False:\n        return sums", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(lst: list):\n    max_values = []\n    for sublist in lst:\n        max_values.append(max(sublist) * 2)\n    return max_values", "input": "[[1, 2, 3], [4, 5, 6], [7, 8, 9]]", "output": "[6, 12, 18]", "imports": [], "original_snippet": "def f(lst: list):\n    max_values = []\n    for sublist in lst:\n        max_values.append(max(sublist) * 2)\n    return max_values", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(lst: list) -> int:\n    lst.sort()\n    subarray_index_0 = lst[::2]\n    subarray_index_1 = lst[1::2]\n    subarray_index_0.reverse()\n    subarray_index_1.reverse()\n    sum_of_even_indexed_elements = sum(subarray_index_0)\n    sum_of_odd_indexed_elements = sum(subarray_index_1)\n    total_sum = sum_of_even_indexed_elements + sum_of_odd_indexed_elements\n    return total_sum", "input": "[2, 3, 1, 4, 5]", "output": "15", "imports": [], "original_snippet": "def f(lst: list) -> int:\n    lst.sort()\n    subarray_index_0 = lst[::2]\n    subarray_index_1 = lst[1::2]\n    subarray_index_0.reverse()\n    subarray_index_1.reverse()\n    sum_of_even_indexed_elements = sum(subarray_index_0)\n    sum_of_odd_indexed_elements = sum(subarray_index_1)\n    total_sum = sum_of_even_indexed_elements + sum_of_odd_indexed_elements\n    return total_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from math import factorial\ndef f(numbers: list):\n    numbers.sort()\n    weighted_sum = sum((num * factorial(i + 1) for (i, num) in enumerate(numbers)))\n    (max_num, min_num) = (max(numbers), min(numbers))\n    digit_sum_max = sum((int(digit) for digit in str(max_num)))\n    digit_sum_min = sum((int(digit) for digit in str(min_num)))\n    return [digit_sum_max, digit_sum_min, weighted_sum]", "input": "[9, 5, 7, 3, 1]", "output": "[9, 1, 1285]", "imports": ["from math import factorial"], "original_snippet": "from math import factorial\ndef f(numbers: list):\n    numbers.sort()\n    weighted_sum = sum((num * factorial(i + 1) for (i, num) in enumerate(numbers)))\n    (max_num, min_num) = (max(numbers), min(numbers))\n    digit_sum_max = sum((int(digit) for digit in str(max_num)))\n    digit_sum_min = sum((int(digit) for digit in str(min_num)))\n    return [digit_sum_max, digit_sum_min, weighted_sum]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(s):\n    res = []\n    (i, c) = (0, 0)\n    for _ in range(len(s)):\n        if s[i] == s[-1 - i]:\n            res.append(c)\n        else:\n            m = s[i // 2 + 1 if i < len(s) // 2 else -1 - (i // 2 + 1)]\n            if s[i] != m:\n                c = 1\n            else:\n                c += 1\n        res.append(c)\n        i += 1\n    return sum(res)", "input": "'abcbcacb'", "output": "13", "imports": [], "original_snippet": "def f(s):\n    res = []\n    (i, c) = (0, 0)\n    for _ in range(len(s)):\n        if s[i] == s[-1 - i]:\n            res.append(c)\n        else:\n            m = s[i // 2 + 1 if i < len(s) // 2 else -1 - (i // 2 + 1)]\n            if s[i] != m:\n                c = 1\n            else:\n                c += 1\n        res.append(c)\n        i += 1\n    return sum(res)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(matrix):\n    matrix = [row for (i, row) in enumerate(matrix) if i % 2 == 0]\n    matrix = list(map(list, zip(*matrix)))\n    matrix = [row for (i, row) in enumerate(zip(*matrix)) if i % 2 != 0]\n    matrix = list(map(list, zip(*matrix)))\n    return matrix", "input": "[[1, 2, 3, 4], [5, 6, 7, 8], [9, 10, 11, 12], [13, 14, 15, 16]]", "output": "[[9], [10], [11], [12]]", "imports": [], "original_snippet": "def f(matrix):\n    matrix = [row for (i, row) in enumerate(matrix) if i % 2 == 0]\n    matrix = list(map(list, zip(*matrix)))\n    matrix = [row for (i, row) in enumerate(zip(*matrix)) if i % 2 != 0]\n    matrix = list(map(list, zip(*matrix)))\n    return matrix", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list1, input_list2):\n    result = []\n    for element in input_list1:\n        if element in input_list2 and element not in result:\n            result.append(element)\n    return result", "input": "[1, 2, 3, 4, 5], [4, 5, 6, 7, 8]", "output": "[4, 5]", "imports": [], "original_snippet": "def f(input_list1, input_list2):\n    result = []\n    for element in input_list1:\n        if element in input_list2 and element not in result:\n            result.append(element)\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "from itertools import permutations\ndef f(coordinates: set):\n    coordinate_dict = {}\n    for (i, path) in enumerate(coordinates):\n        coordinate_dict[path[0] if i > 0 else path[0]] = path[-1] if i < len(coordinates) - 1 else path[0]\n    max_length = -1\n    for path in permutations(coordinate_dict):\n        length = 0\n        for i in range(len(path) - 1):\n            length += abs(coordinate_dict[path[i]] - coordinate_dict[path[i + 1]])\n        max_length = max(max_length, length)\n    return max_length", "input": "{(1, 3), (3, 7), (7, 11), (11, 5)}", "output": "20", "imports": ["from itertools import permutations"], "original_snippet": "from itertools import permutations\ndef f(coordinates: set):\n    coordinate_dict = {}\n    for (i, path) in enumerate(coordinates):\n        coordinate_dict[path[0] if i > 0 else path[0]] = path[-1] if i < len(coordinates) - 1 else path[0]\n    max_length = -1\n    for path in permutations(coordinate_dict):\n        length = 0\n        for i in range(len(path) - 1):\n            length += abs(coordinate_dict[path[i]] - coordinate_dict[path[i + 1]])\n        max_length = max(max_length, length)\n    return max_length", "composite_functions": [], "_input_type": "set", "_output_type": "int"}
{"snippet": "def f(nums: list):\n    filtered_nums = [num for num in nums if num % 2 == 0]\n    reversed_nums = filtered_nums[::-1]\n    total = sum(reversed_nums)\n    return total", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9]", "output": "20", "imports": [], "original_snippet": "def f(nums: list):\n    filtered_nums = [num for num in nums if num % 2 == 0]\n    reversed_nums = filtered_nums[::-1]\n    total = sum(reversed_nums)\n    return total", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "import math\ndef f(arr: list[list]):\n    for outer_index in range(len(arr)):\n        inner_list = arr[outer_index]\n        for inner_index in range(len(inner_list)):\n            num = inner_list[inner_index]\n            if num % 2 != 0:\n                for divisor in range(3, num):\n                    if divisor % 2 != 0 and num % divisor == 0:\n                        while num % divisor == 0:\n                            num /= divisor\n                            inner_list[inner_index] = int(num)\n    max_index = (0, 0)\n    for outer_index in range(len(arr)):\n        inner_list = arr[outer_index]\n        for inner_index in range(len(inner_list)):\n            if inner_list[inner_index] > inner_list[max_index[1]]:\n                max_index = (outer_index, inner_index)\n    return max_index", "input": "[[3, 9, 15], [21, 27, 33], [39, 45, 51]]", "output": "(0, 0)", "imports": ["import math"], "original_snippet": "import math\ndef f(arr: list[list]):\n    for outer_index in range(len(arr)):\n        inner_list = arr[outer_index]\n        for inner_index in range(len(inner_list)):\n            num = inner_list[inner_index]\n            if num % 2 != 0:\n                for divisor in range(3, num):\n                    if divisor % 2 != 0 and num % divisor == 0:\n                        while num % divisor == 0:\n                            num /= divisor\n                            inner_list[inner_index] = int(num)\n    max_index = (0, 0)\n    for outer_index in range(len(arr)):\n        inner_list = arr[outer_index]\n        for inner_index in range(len(inner_list)):\n            if inner_list[inner_index] > inner_list[max_index[1]]:\n                max_index = (outer_index, inner_index)\n    return max_index", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(s: str):\n    ascii_values = sorted([ord(c) for c in s])\n    count_of_values = {}\n    for value in ascii_values:\n        if value in count_of_values:\n            count_of_values[value] += 1\n        else:\n            count_of_values[value] = 1\n    result = ''\n    for (value, count) in count_of_values.items():\n        result += str(value) * count\n    return result", "input": "\"agccddeeeaa\"", "output": "'9797979999100100101101101103'", "imports": [], "original_snippet": "def f(s: str):\n    ascii_values = sorted([ord(c) for c in s])\n    count_of_values = {}\n    for value in ascii_values:\n        if value in count_of_values:\n            count_of_values[value] += 1\n        else:\n            count_of_values[value] = 1\n    result = ''\n    for (value, count) in count_of_values.items():\n        result += str(value) * count\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(lst: list, k: int) -> int:\n    odd_numbers = [x for x in lst if x % 2 != 0]\n    odd_numbers.sort(reverse=True)\n    squared_odd_numbers = [x ** 2 for x in odd_numbers]\n    return sum(squared_odd_numbers[:k])", "input": "[2, 3, 4, 5, 6, 7, 8, 9], 3", "output": "155", "imports": [], "original_snippet": "def f(lst: list, k: int) -> int:\n    odd_numbers = [x for x in lst if x % 2 != 0]\n    odd_numbers.sort(reverse=True)\n    squared_odd_numbers = [x ** 2 for x in odd_numbers]\n    return sum(squared_odd_numbers[:k])", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(s: str):\n    result = ''\n    for c in s:\n        if c >= 'A' and c <= 'Z':\n            state = chr((ord(c) - 64) % 26 + 65)\n            result += state * (ord(state) - 64)\n        elif c >= '0' and c <= '9':\n            state = chr((ord(c) - 47) % 10 + 48)\n            result += state * (ord(state) - 47)\n        else:\n            result += c\n    return result", "input": "'Hello, world! 123'", "output": "'IIIIIIIIIello, world! 222333344444'", "imports": [], "original_snippet": "def f(s: str):\n    result = ''\n    for c in s:\n        if c >= 'A' and c <= 'Z':\n            state = chr((ord(c) - 64) % 26 + 65)\n            result += state * (ord(state) - 64)\n        elif c >= '0' and c <= '9':\n            state = chr((ord(c) - 47) % 10 + 48)\n            result += state * (ord(state) - 47)\n        else:\n            result += c\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(n: int) -> int:\n    binary_representation = bin(n)[2:]\n    count_of_ones = binary_representation.count('1')\n    dividend = count_of_ones / (count_of_ones + len(binary_representation))\n    min_power_of_two = 2\n    while min_power_of_two < dividend:\n        min_power_of_two *= 2\n    return min_power_of_two", "input": "13", "output": "2", "imports": [], "original_snippet": "def f(n: int) -> int:\n    binary_representation = bin(n)[2:]\n    count_of_ones = binary_representation.count('1')\n    dividend = count_of_ones / (count_of_ones + len(binary_representation))\n    min_power_of_two = 2\n    while min_power_of_two < dividend:\n        min_power_of_two *= 2\n    return min_power_of_two", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(s):\n    substrings = set()\n    length = len(s)\n    for i in range(length):\n        for j in range(i + 1, length + 1):\n            substring = s[i:j]\n            substrings.add(substring)\n    sorted_substrings = sorted(substrings)\n    return sorted_substrings", "input": "\"abcde\"", "output": "['a', 'ab', 'abc', 'abcd', 'abcde', 'b', 'bc', 'bcd', 'bcde', 'c', 'cd', 'cde', 'd', 'de', 'e']", "imports": [], "original_snippet": "def f(s):\n    substrings = set()\n    length = len(s)\n    for i in range(length):\n        for j in range(i + 1, length + 1):\n            substring = s[i:j]\n            substrings.add(substring)\n    sorted_substrings = sorted(substrings)\n    return sorted_substrings", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(s: str):\n    s = s.lower()\n    vowel_counts = {}\n    for char in s:\n        if char in 'aeiou' and char not in vowel_counts:\n            vowel_counts[char] = 1\n        elif char in 'aeiou':\n            vowel_counts[char] += 1\n    sorted_vowel_counts = sorted(vowel_counts.items(), key=lambda x: x[1])\n    result = ''\n    for (vowel, count) in sorted_vowel_counts:\n        result += vowel * (len(s) - count)\n    return result", "input": "\"thereoncewasamangotree\"", "output": "'ooooooooooooooooooooaaaaaaaaaaaaaaaaaaaeeeeeeeeeeeeeeeee'", "imports": [], "original_snippet": "def f(s: str):\n    s = s.lower()\n    vowel_counts = {}\n    for char in s:\n        if char in 'aeiou' and char not in vowel_counts:\n            vowel_counts[char] = 1\n        elif char in 'aeiou':\n            vowel_counts[char] += 1\n    sorted_vowel_counts = sorted(vowel_counts.items(), key=lambda x: x[1])\n    result = ''\n    for (vowel, count) in sorted_vowel_counts:\n        result += vowel * (len(s) - count)\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(s, ranges, args):\n    result = ''\n    i = 0\n    for (index, char) in enumerate(s):\n        if index in ranges:\n            result += args[i]\n            i += 1\n        else:\n            result += char\n    return result", "input": "'Hello World', [(0, 4), (6, 11)], ['!', 'I']", "output": "'Hello World'", "imports": [], "original_snippet": "def f(s, ranges, args):\n    result = ''\n    i = 0\n    for (index, char) in enumerate(s):\n        if index in ranges:\n            result += args[i]\n            i += 1\n        else:\n            result += char\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(lst: list):\n    grouped_by_color = {}\n    for d in lst:\n        color = d['color']\n        if color not in grouped_by_color:\n            grouped_by_color[color] = []\n        grouped_by_color[color].append(d)\n    grouped_by_area = [(color, sum((abs(node['x']) + abs(node['y']) for node in nodes)), nodes) for (color, nodes) in grouped_by_color.items()]\n    grouped_by_area.sort(key=lambda x: x[1])\n    sorted_and_grouped = [node for (_, _, group) in grouped_by_area for node in group]\n    num_unique_colors = len(grouped_by_color)\n    return (sorted_and_grouped, num_unique_colors)", "input": "[{'x': 1, 'y': 2, 'color': 'red'}, {'x': 3, 'y': 4, 'color': 'green'}, {'x': -1, 'y': 2, 'color': 'red'}, {'x': 5, 'y': 0, 'color': 'blue'}]", "output": "([{'x': 5, 'y': 0, 'color': 'blue'}, {'x': 1, 'y': 2, 'color': 'red'}, {'x': -1, 'y': 2, 'color': 'red'}, {'x': 3, 'y': 4, 'color': 'green'}], 3)", "imports": [], "original_snippet": "def f(lst: list):\n    grouped_by_color = {}\n    for d in lst:\n        color = d['color']\n        if color not in grouped_by_color:\n            grouped_by_color[color] = []\n        grouped_by_color[color].append(d)\n    grouped_by_area = [(color, sum((abs(node['x']) + abs(node['y']) for node in nodes)), nodes) for (color, nodes) in grouped_by_color.items()]\n    grouped_by_area.sort(key=lambda x: x[1])\n    sorted_and_grouped = [node for (_, _, group) in grouped_by_area for node in group]\n    num_unique_colors = len(grouped_by_color)\n    return (sorted_and_grouped, num_unique_colors)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(num):\n    even = num % 2 == 0\n    increment = 2 if even else -2\n    return num + increment", "input": "6", "output": "8", "imports": [], "original_snippet": "def f(num):\n    even = num % 2 == 0\n    increment = 2 if even else -2\n    return num + increment", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(lst: list) -> int:\n    i = 0\n    while i < len(lst) - 1:\n        if lst[i] == lst[i + 1]:\n            lst.pop(i)\n            lst.pop(i)\n            i -= 1\n        i += 1\n    lst.sort()\n    for x in range(max(lst) + 1):\n        if x >= sum(lst):\n            return x", "input": "[2, 2, 3, 3, 4, 5, 6, 6, 7, 8, 9]", "output": "None", "imports": [], "original_snippet": "def f(lst: list) -> int:\n    i = 0\n    while i < len(lst) - 1:\n        if lst[i] == lst[i + 1]:\n            lst.pop(i)\n            lst.pop(i)\n            i -= 1\n        i += 1\n    lst.sort()\n    for x in range(max(lst) + 1):\n        if x >= sum(lst):\n            return x", "composite_functions": [], "_input_type": "list", "_output_type": "NoneType"}
{"snippet": "def f(coordinates: list):\n    (sum_x, sum_y, area) = (0, 0, 0)\n    for (x, y) in coordinates:\n        sum_x += x\n        sum_y += y\n    for i in range(len(coordinates)):\n        (x1, y1) = coordinates[i]\n        (x2, y2) = coordinates[(i + 1) % len(coordinates)]\n        area += x1 * y2 - x2 * y1\n    area = abs(area) / 2\n    return area", "input": "[(0, 0), (0, 1), (1, 1), (1, 0)]", "output": "1.0", "imports": [], "original_snippet": "def f(coordinates: list):\n    (sum_x, sum_y, area) = (0, 0, 0)\n    for (x, y) in coordinates:\n        sum_x += x\n        sum_y += y\n    for i in range(len(coordinates)):\n        (x1, y1) = coordinates[i]\n        (x2, y2) = coordinates[(i + 1) % len(coordinates)]\n        area += x1 * y2 - x2 * y1\n    area = abs(area) / 2\n    return area", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(nums):\n    (min_val, max_val) = (min(nums), max(nums))\n    product = min_val * max_val * len(nums)\n    even_sum = sum((num for num in nums if num % 2 == 0))\n    odd_count = sum((1 for num in nums if num % 2 != 0))\n    final_sum = even_sum * odd_count\n    return product + final_sum", "input": "[1, 2, 3, 4, 5]", "output": "43", "imports": [], "original_snippet": "def f(nums):\n    (min_val, max_val) = (min(nums), max(nums))\n    product = min_val * max_val * len(nums)\n    even_sum = sum((num for num in nums if num % 2 == 0))\n    odd_count = sum((1 for num in nums if num % 2 != 0))\n    final_sum = even_sum * odd_count\n    return product + final_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers):\n    product_of_odds = 1\n    sum_of_even_digits = 0\n    for num in numbers:\n        if num % 2 == 0:\n            sum_of_even_digits += sum((int(digit) for digit in str(num)))\n        else:\n            product_of_odds *= num\n    return (sum_of_even_digits, product_of_odds)", "input": "[24, 7, 8, 11, 13, 17]", "output": "(14, 17017)", "imports": [], "original_snippet": "def f(numbers):\n    product_of_odds = 1\n    sum_of_even_digits = 0\n    for num in numbers:\n        if num % 2 == 0:\n            sum_of_even_digits += sum((int(digit) for digit in str(num)))\n        else:\n            product_of_odds *= num\n    return (sum_of_even_digits, product_of_odds)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(nums: list, target: int):\n    seen = set()\n    max_product = -1\n    for num in nums:\n        if num in seen:\n            continue\n        complement = target - num\n        if complement in seen:\n            max_product = max(max_product, num * complement * max(seen - {num, complement}))\n        seen.add(num)\n    return max_product", "input": "[1, 6, 3, 22, 10], 36", "output": "-1", "imports": [], "original_snippet": "def f(nums: list, target: int):\n    seen = set()\n    max_product = -1\n    for num in nums:\n        if num in seen:\n            continue\n        complement = target - num\n        if complement in seen:\n            max_product = max(max_product, num * complement * max(seen - {num, complement}))\n        seen.add(num)\n    return max_product", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(s: str):\n    char_dict = {}\n    for c in s:\n        if c in char_dict:\n            char_dict[c] += 1\n        else:\n            char_dict[c] = 1\n    sorted_dict = {k: v for (k, v) in sorted(char_dict.items(), key=lambda item: (-item[1], item[0]))}\n    ascii_sum = sum((ord(c) for c in s))\n    total = sum((ord(char) * (ascii_sum + count) for (char, count) in sorted_dict.items()))\n    return total", "input": "\"algorithm\"", "output": "936056", "imports": [], "original_snippet": "def f(s: str):\n    char_dict = {}\n    for c in s:\n        if c in char_dict:\n            char_dict[c] += 1\n        else:\n            char_dict[c] = 1\n    sorted_dict = {k: v for (k, v) in sorted(char_dict.items(), key=lambda item: (-item[1], item[0]))}\n    ascii_sum = sum((ord(c) for c in s))\n    total = sum((ord(char) * (ascii_sum + count) for (char, count) in sorted_dict.items()))\n    return total", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_list):\n    filtered_list = list(set(input_list))\n    filtered_list.sort()\n    mapping = {v: i for (i, v) in enumerate(filtered_list)}\n    sum1 = sum((v ** 2 for v in filtered_list))\n    reversed_mapping = {v: k for (k, v) in mapping.items()}\n    sum2 = sum((reversed_mapping[i] for i in range(len(filtered_list))))\n    return sum1 - sum2", "input": "[5, 10, 3, 5, 7, 10]", "output": "158", "imports": [], "original_snippet": "def f(input_list):\n    filtered_list = list(set(input_list))\n    filtered_list.sort()\n    mapping = {v: i for (i, v) in enumerate(filtered_list)}\n    sum1 = sum((v ** 2 for v in filtered_list))\n    reversed_mapping = {v: k for (k, v) in mapping.items()}\n    sum2 = sum((reversed_mapping[i] for i in range(len(filtered_list))))\n    return sum1 - sum2", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(dictionaries: list):\n    transformed_dict = {}\n    for d in dictionaries:\n        name = d['name']\n        modifiers = d['modifiers']\n        transformed_dict[name] = sum(modifiers) / len(modifiers)\n    return transformed_dict", "input": "[{'name': 'John', 'modifiers': [10, 20, 30]}, {'name': 'Jane', 'modifiers': [5, 15, 25]}]", "output": "{'John': 20.0, 'Jane': 15.0}", "imports": [], "original_snippet": "def f(dictionaries: list):\n    transformed_dict = {}\n    for d in dictionaries:\n        name = d['name']\n        modifiers = d['modifiers']\n        transformed_dict[name] = sum(modifiers) / len(modifiers)\n    return transformed_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "from itertools import permutations\nfrom math import sqrt\ndef f(block: str):\n    perms = [''.join(p) for p in permutations(block)]\n    rev_perms = [p[::-1] for p in perms]\n    candidates = list(set(perms + rev_perms))\n    for perm in candidates:\n        if perm[0] == '1':\n            continue\n        root = sqrt(int(perm))\n        if root.is_integer():\n            return perm\n    return None", "input": "'1032'", "output": "None", "imports": ["from itertools import permutations", "from math import sqrt"], "original_snippet": "from itertools import permutations\nfrom math import sqrt\ndef f(block: str):\n    perms = [''.join(p) for p in permutations(block)]\n    rev_perms = [p[::-1] for p in perms]\n    candidates = list(set(perms + rev_perms))\n    for perm in candidates:\n        if perm[0] == '1':\n            continue\n        root = sqrt(int(perm))\n        if root.is_integer():\n            return perm\n    return None", "composite_functions": [], "_input_type": "str", "_output_type": "NoneType"}
{"snippet": "def f(n):\n    result = []\n    for i in range(n):\n        if i % 3 == 0 and i % 5 == 0:\n            result.append('foobar')\n        elif i % 3 == 0:\n            result.append('foo')\n        elif i % 5 == 0:\n            result.append('bar')\n        else:\n            result.append(i)\n    return result", "input": "15", "output": "['foobar', 1, 2, 'foo', 4, 'bar', 'foo', 7, 8, 'foo', 'bar', 11, 'foo', 13, 14]", "imports": [], "original_snippet": "def f(n):\n    result = []\n    for i in range(n):\n        if i % 3 == 0 and i % 5 == 0:\n            result.append('foobar')\n        elif i % 3 == 0:\n            result.append('foo')\n        elif i % 5 == 0:\n            result.append('bar')\n        else:\n            result.append(i)\n    return result", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(data: dict, multiplier: float=1.0, depth: int=0):\n    total = 0\n    for (key, value) in data.items():\n        if isinstance(value, dict):\n            multiplier_mod = multiplier * (depth + 1) if '_transform' not in value else multiplier * 0.5 ** depth\n            total += f(value, multiplier_mod, depth + 1)\n        elif isinstance(value, (int, float)):\n            total += value * multiplier\n        elif isinstance(value, str):\n            if value == 'special':\n                total += 10 * multiplier\n            elif value == 'secret':\n                total += 5 * multiplier\n            else:\n                total += 1 * multiplier\n    return total", "input": "{'key1': 2, 'key2': {'subkey1': 3, 'secret': 5, '_transform': 10, 'subkey2': {'subsubkey1': 11, 'subsecret': 15}}, 'special': 10}", "output": "82.0", "imports": [], "original_snippet": "def f(data: dict, multiplier: float=1.0, depth: int=0):\n    total = 0\n    for (key, value) in data.items():\n        if isinstance(value, dict):\n            multiplier_mod = multiplier * (depth + 1) if '_transform' not in value else multiplier * 0.5 ** depth\n            total += f(value, multiplier_mod, depth + 1)\n        elif isinstance(value, (int, float)):\n            total += value * multiplier\n        elif isinstance(value, str):\n            if value == 'special':\n                total += 10 * multiplier\n            elif value == 'secret':\n                total += 5 * multiplier\n            else:\n                total += 1 * multiplier\n    return total", "composite_functions": [], "_input_type": "dict", "_output_type": "float"}
{"snippet": "def f(strings: list):\n    result = []\n    for string in strings:\n        char_list = [char for char in string]\n        char_list.sort()\n        sorted_string = ''.join(char_list)\n        reversed_string = sorted_string[::-1]\n        duplicate_removed_string = ''.join(sorted(list(set(reversed_string))))\n        result.append(duplicate_removed_string)\n    return result", "input": "[\"abc\", \"def\", \"ghi\"]", "output": "['abc', 'def', 'ghi']", "imports": [], "original_snippet": "def f(strings: list):\n    result = []\n    for string in strings:\n        char_list = [char for char in string]\n        char_list.sort()\n        sorted_string = ''.join(char_list)\n        reversed_string = sorted_string[::-1]\n        duplicate_removed_string = ''.join(sorted(list(set(reversed_string))))\n        result.append(duplicate_removed_string)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(lst):\n    lst.sort()\n    running_total = 0\n    separate_list = []\n    for elem in lst:\n        running_total += elem * 2\n        if running_total > elem:\n            separate_list.append(elem)\n    return separate_list", "input": "[3, 1, 4, 1, 5, 9, 2, 6, 5, 3, 5, 8, 9, 7, 9]", "output": "[1, 1, 2, 3, 3, 4, 5, 5, 5, 6, 7, 8, 9, 9, 9]", "imports": [], "original_snippet": "def f(lst):\n    lst.sort()\n    running_total = 0\n    separate_list = []\n    for elem in lst:\n        running_total += elem * 2\n        if running_total > elem:\n            separate_list.append(elem)\n    return separate_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(d):\n    result = []\n    temp = {}\n    for (key, value) in d.items():\n        if key.startswith('a_'):\n            temp_value = temp.get(key[2:], None)\n            if temp_value is None or value > temp_value:\n                temp[key[2:]] = value\n    for (subkey, value) in temp.items():\n        result.append(f'Key: {subkey}, Value: {value}')\n    return result", "input": "{'a_a': 5, 'a_b': 10, 'a_c': 15, 'a_a_value': 8, 'prefix_a': 20}", "output": "['Key: a, Value: 5', 'Key: b, Value: 10', 'Key: c, Value: 15', 'Key: a_value, Value: 8']", "imports": [], "original_snippet": "def f(d):\n    result = []\n    temp = {}\n    for (key, value) in d.items():\n        if key.startswith('a_'):\n            temp_value = temp.get(key[2:], None)\n            if temp_value is None or value > temp_value:\n                temp[key[2:]] = value\n    for (subkey, value) in temp.items():\n        result.append(f'Key: {subkey}, Value: {value}')\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "list"}
{"snippet": "def f(input_list: list):\n    unique_elements = set()\n    for num in input_list:\n        unique_elements.add(num)\n    squared_elements = [num ** 2 for num in unique_elements]\n    squared_elements_with_negatives = [-num ** 2 if num < 0 else num ** 2 for num in unique_elements]\n    final_list = squared_elements[::-1] + squared_elements_with_negatives[::-1]\n    final_list_sorted = sorted(final_list, reverse=True)\n    return final_list_sorted", "input": "[2, -2, 3, -3, 4, 4, 5]", "output": "[25, 25, 16, 16, 9, 9, 9, 4, 4, 4, -4, -9]", "imports": [], "original_snippet": "def f(input_list: list):\n    unique_elements = set()\n    for num in input_list:\n        unique_elements.add(num)\n    squared_elements = [num ** 2 for num in unique_elements]\n    squared_elements_with_negatives = [-num ** 2 if num < 0 else num ** 2 for num in unique_elements]\n    final_list = squared_elements[::-1] + squared_elements_with_negatives[::-1]\n    final_list_sorted = sorted(final_list, reverse=True)\n    return final_list_sorted", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from itertools import combinations_with_replacement, permutations\ndef f(letters: int, numbers: int, symbols: int, min_length: int, allow_repetition: bool) -> int:\n    if min_length < 1 or letters < 0 or numbers < 0 or (symbols < 0):\n        return 0\n    lowercase = [chr(i) for i in range(97, 97 + letters)]\n    uppercase = [chr(i) for i in range(65, 65 + letters)]\n    num = [str(i) for i in range(0, numbers)]\n    sym = chr(33) + chr(34) * min(1, symbols) + chr(35) * min(1, symbols - 1) + chr(36) * min(1, symbols - 2) + chr(37) * min(1, symbols - 3) + chr(38) * min(1, symbols - 4)\n    password_characters = lowercase + uppercase + num + list(sym)\n    password_combinations = 0\n    for length in range(min_length, len(password_characters) + 1):\n        if allow_repetition:\n            passwords = combinations_with_replacement(password_characters, length)\n        else:\n            passwords = permutations(password_characters, length)\n        password_combinations += len(set([tuple(comb) for comb in passwords]))\n    return password_combinations", "input": "1, 2, 3, 2, True", "output": "12861", "imports": ["from itertools import combinations_with_replacement, permutations"], "original_snippet": "from itertools import combinations_with_replacement, permutations\ndef f(letters: int, numbers: int, symbols: int, min_length: int, allow_repetition: bool) -> int:\n    if min_length < 1 or letters < 0 or numbers < 0 or (symbols < 0):\n        return 0\n    lowercase = [chr(i) for i in range(97, 97 + letters)]\n    uppercase = [chr(i) for i in range(65, 65 + letters)]\n    num = [str(i) for i in range(0, numbers)]\n    sym = chr(33) + chr(34) * min(1, symbols) + chr(35) * min(1, symbols - 1) + chr(36) * min(1, symbols - 2) + chr(37) * min(1, symbols - 3) + chr(38) * min(1, symbols - 4)\n    password_characters = lowercase + uppercase + num + list(sym)\n    password_combinations = 0\n    for length in range(min_length, len(password_characters) + 1):\n        if allow_repetition:\n            passwords = combinations_with_replacement(password_characters, length)\n        else:\n            passwords = permutations(password_characters, length)\n        password_combinations += len(set([tuple(comb) for comb in passwords]))\n    return password_combinations", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(intervals):\n    intervals.sort(key=lambda x: (x['start'], x['end']))\n    merged_intervals = []\n    for interval in intervals:\n        if not merged_intervals or interval['start'] > merged_intervals[-1]['end']:\n            merged_intervals.append(interval)\n        else:\n            merged_intervals[-1]['end'] = max(merged_intervals[-1]['end'], interval['end'])\n    return merged_intervals", "input": "[{'start': 1, 'end': 5}, {'start': 3, 'end': 8}, {'start': 10, 'end': 15}, {'start': 20, 'end': 25}]", "output": "[{'start': 1, 'end': 8}, {'start': 10, 'end': 15}, {'start': 20, 'end': 25}]", "imports": [], "original_snippet": "def f(intervals):\n    intervals.sort(key=lambda x: (x['start'], x['end']))\n    merged_intervals = []\n    for interval in intervals:\n        if not merged_intervals or interval['start'] > merged_intervals[-1]['end']:\n            merged_intervals.append(interval)\n        else:\n            merged_intervals[-1]['end'] = max(merged_intervals[-1]['end'], interval['end'])\n    return merged_intervals", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "import math\ndef f(nums):\n    numbers_dict = {num - 5: 0 for num in nums}\n    factorial_sums = [math.factorial(num - 5) for num in nums]\n    sorted_nums = sorted(nums, reverse=True)\n    for i in range(len(sorted_nums)):\n        if i % 2 == 0:\n            numbers_dict[sorted_nums[i] - 5] += factorial_sums[i]\n        else:\n            numbers_dict[sorted_nums[i] - 5] -= factorial_sums[i]\n    return numbers_dict", "input": "[5, 10, 15, 20, 25]", "output": "{0: 2432902008176640000, 5: -1307674368000, 10: 3628800, 15: -120, 20: 1}", "imports": ["import math"], "original_snippet": "import math\ndef f(nums):\n    numbers_dict = {num - 5: 0 for num in nums}\n    factorial_sums = [math.factorial(num - 5) for num in nums]\n    sorted_nums = sorted(nums, reverse=True)\n    for i in range(len(sorted_nums)):\n        if i % 2 == 0:\n            numbers_dict[sorted_nums[i] - 5] += factorial_sums[i]\n        else:\n            numbers_dict[sorted_nums[i] - 5] -= factorial_sums[i]\n    return numbers_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(N):\n    A = sorted(range(1, N + 1), reverse=True)\n    B = sorted(set(A), reverse=True)\n    D = {}\n    ai = bi = 0\n    for _ in range(len(A)):\n        if bi < len(B):\n            D[A[ai]] = B[bi]\n            ai += 1\n            bi += 1\n        elif ai < len(A):\n            D[A[ai]] = B[-1]\n            ai += 1\n    sum_odd = sum([int(d) for (key, val) in D.items() for d in str(val) if int(d) % 2 != 0])\n    return sum_odd ** 2", "input": "57", "output": "48841", "imports": [], "original_snippet": "def f(N):\n    A = sorted(range(1, N + 1), reverse=True)\n    B = sorted(set(A), reverse=True)\n    D = {}\n    ai = bi = 0\n    for _ in range(len(A)):\n        if bi < len(B):\n            D[A[ai]] = B[bi]\n            ai += 1\n            bi += 1\n        elif ai < len(A):\n            D[A[ai]] = B[-1]\n            ai += 1\n    sum_odd = sum([int(d) for (key, val) in D.items() for d in str(val) if int(d) % 2 != 0])\n    return sum_odd ** 2", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(n):\n    results = []\n    digits = [int(digit) for digit in str(n)]\n    digits.sort(reverse=True)\n    for i in range(0, len(digits), 3):\n        group = digits[i:i + 3]\n        if len(group) == 3:\n            results.append(sum(group))\n            results.append(group[0] - group[1] - group[2])\n            group.sort()\n            results.append(group[0] + group[1] * group[2])\n            group.sort()\n            if group[1] == 5:\n                results.append(group[0] % group[1])\n    return results", "input": "1234567890", "output": "[24, -6, 79, 15, -3, 34, 4, 6, 0, 7]", "imports": [], "original_snippet": "def f(n):\n    results = []\n    digits = [int(digit) for digit in str(n)]\n    digits.sort(reverse=True)\n    for i in range(0, len(digits), 3):\n        group = digits[i:i + 3]\n        if len(group) == 3:\n            results.append(sum(group))\n            results.append(group[0] - group[1] - group[2])\n            group.sort()\n            results.append(group[0] + group[1] * group[2])\n            group.sort()\n            if group[1] == 5:\n                results.append(group[0] % group[1])\n    return results", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(N, M):\n    grid = [[True] * (M + 1) for _ in range(N + 1)]\n    for i in range(N + 1):\n        for j in range(M + 1):\n            if i * j == 0:\n                grid[i][j] = False\n            if i % 2 == 0 or j % 2 == 0:\n                continue\n            grid[i][j] = grid[i - 1][j] or grid[i][j - 1]\n    return grid[N][M]", "input": "2, 2", "output": "True", "imports": [], "original_snippet": "def f(N, M):\n    grid = [[True] * (M + 1) for _ in range(N + 1)]\n    for i in range(N + 1):\n        for j in range(M + 1):\n            if i * j == 0:\n                grid[i][j] = False\n            if i % 2 == 0 or j % 2 == 0:\n                continue\n            grid[i][j] = grid[i - 1][j] or grid[i][j - 1]\n    return grid[N][M]", "composite_functions": [], "_input_type": "tuple", "_output_type": "bool"}
{"snippet": "def f(s: str):\n    result = ''\n    ctr = 1\n    for i in range(1, len(s)):\n        if s[i] == s[i - 1]:\n            ctr += 1\n        else:\n            result += s[i - 1] + 'x' if ctr > 1 else s[i - 1]\n            ctr = 1\n    result += s[-1] + 'x' if ctr > 1 else s[-1]\n    return result", "input": "\"oxoxxooxxoxxooxx\"", "output": "'oxoxxoxxxoxxoxxx'", "imports": [], "original_snippet": "def f(s: str):\n    result = ''\n    ctr = 1\n    for i in range(1, len(s)):\n        if s[i] == s[i - 1]:\n            ctr += 1\n        else:\n            result += s[i - 1] + 'x' if ctr > 1 else s[i - 1]\n            ctr = 1\n    result += s[-1] + 'x' if ctr > 1 else s[-1]\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(graph, source):\n    visited = [False] * len(graph)\n    previous = [-1] * len(graph)\n    queue = [source]\n    visited[source] = True\n    while queue:\n        node = queue.pop(0)\n        for neighbor in graph[node]:\n            if not visited[neighbor]:\n                queue.append(neighbor)\n                visited[neighbor] = True\n                previous[neighbor] = node\n    paths = []\n    for i in range(len(graph)):\n        if previous[i] != -1:\n            path = []\n            current = i\n            while current != source:\n                path.insert(0, current)\n                current = previous[current]\n            path.insert(0, source)\n            paths.append(path)\n    max_degree = max([len(path) for path in paths])\n    shortest_paths = [path for path in paths if len(path) == max_degree]\n    return shortest_paths", "input": "{0: [1, 2], 1: [0, 2, 3], 2: [0, 1, 3], 3: [1, 2]}, 0", "output": "[[0, 1, 3]]", "imports": [], "original_snippet": "def f(graph, source):\n    visited = [False] * len(graph)\n    previous = [-1] * len(graph)\n    queue = [source]\n    visited[source] = True\n    while queue:\n        node = queue.pop(0)\n        for neighbor in graph[node]:\n            if not visited[neighbor]:\n                queue.append(neighbor)\n                visited[neighbor] = True\n                previous[neighbor] = node\n    paths = []\n    for i in range(len(graph)):\n        if previous[i] != -1:\n            path = []\n            current = i\n            while current != source:\n                path.insert(0, current)\n                current = previous[current]\n            path.insert(0, source)\n            paths.append(path)\n    max_degree = max([len(path) for path in paths])\n    shortest_paths = [path for path in paths if len(path) == max_degree]\n    return shortest_paths", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(input_list):\n    (even, odd) = ([num for num in input_list if num % 2 == 0], [num for num in input_list if num % 2 != 0])\n    (even, odd) = (sorted(even), sorted(odd, reverse=True))\n    grouped_elements = []\n    while len(even) and len(odd):\n        replacement = odd.pop() if len(odd) < len(even) else even.pop()\n        grouped_elements += [replacement] * (replacement % 10)\n    grouped_elements += even + odd\n    grouped_elements = sorted(grouped_elements)\n    prefix_count = 0\n    while prefix_count < (len(grouped_elements) - 1) // 2:\n        if grouped_elements[prefix_count] == odd[prefix_count]:\n            prefix_count = prefix_count + 1\n        else:\n            break\n    result = grouped_elements[:prefix_count * 2]\n    return result", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]", "output": "[]", "imports": [], "original_snippet": "def f(input_list):\n    (even, odd) = ([num for num in input_list if num % 2 == 0], [num for num in input_list if num % 2 != 0])\n    (even, odd) = (sorted(even), sorted(odd, reverse=True))\n    grouped_elements = []\n    while len(even) and len(odd):\n        replacement = odd.pop() if len(odd) < len(even) else even.pop()\n        grouped_elements += [replacement] * (replacement % 10)\n    grouped_elements += even + odd\n    grouped_elements = sorted(grouped_elements)\n    prefix_count = 0\n    while prefix_count < (len(grouped_elements) - 1) // 2:\n        if grouped_elements[prefix_count] == odd[prefix_count]:\n            prefix_count = prefix_count + 1\n        else:\n            break\n    result = grouped_elements[:prefix_count * 2]\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(data: list):\n    even_numbers = [x for x in data if x % 2 == 0]\n    max_even = max(even_numbers)\n    return list(reversed(data[:max_even]))", "input": "[1, 2, 3, 4, 7, 5, 2]", "output": "[4, 3, 2, 1]", "imports": [], "original_snippet": "def f(data: list):\n    even_numbers = [x for x in data if x % 2 == 0]\n    max_even = max(even_numbers)\n    return list(reversed(data[:max_even]))", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(text: str, words: set):\n    words_to_numbers = {'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5, 'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10}\n    filtered_words = [word for word in words if word in text]\n    result_sum = sum((words_to_numbers[word] for word in filtered_words))\n    return result_sum", "input": "'My age is nine years old', {'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten'}", "output": "9", "imports": [], "original_snippet": "def f(text: str, words: set):\n    words_to_numbers = {'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5, 'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10}\n    filtered_words = [word for word in words if word in text]\n    result_sum = sum((words_to_numbers[word] for word in filtered_words))\n    return result_sum", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(strings: list):\n    result = {}\n    for string in strings:\n        char_list = [char for char in string]\n        char_list.reverse()\n        deduped_list = list(dict.fromkeys(char_list))\n        unique_reversed_string = ''.join(deduped_list)\n        result[string] = unique_reversed_string\n    return list(result.values())", "input": "[\"hello\", \"world\", \"apple\"]", "output": "['oleh', 'dlrow', 'elpa']", "imports": [], "original_snippet": "def f(strings: list):\n    result = {}\n    for string in strings:\n        char_list = [char for char in string]\n        char_list.reverse()\n        deduped_list = list(dict.fromkeys(char_list))\n        unique_reversed_string = ''.join(deduped_list)\n        result[string] = unique_reversed_string\n    return list(result.values())", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(employees):\n    employees.sort(key=lambda x: x['salary'])\n    (low_salary, mid_salary, high_salary) = ([], [], [])\n    for employee in employees:\n        bonus = 0\n        if employee['salary'] < 30000:\n            bonus = employee['salary'] * 0.02\n        elif 30000 <= employee['salary'] <= 50000:\n            bonus = employee['salary'] * 0.03\n        else:\n            bonus = employee['salary'] * 0.05\n        if bonus == 0:\n            low_salary.append((employee['name'], bonus))\n        elif 0 < bonus <= 5000:\n            mid_salary.append((employee['name'], bonus))\n        else:\n            high_salary.append((employee['name'], bonus))\n    result = [low_salary[0][0], mid_salary[0][0], high_salary[0][0]] if low_salary and mid_salary and high_salary else []\n    return result", "input": "[\n    {'name': 'Alice', 'salary': 40000, 'department': 'Marketing'},\n    {'name': 'Bob', 'salary': 35000, 'department': 'Engineering'},\n    {'name': 'Carol', 'salary': 55000, 'department': 'Sales'},\n    {'name': 'Dave', 'salary': 25000, 'department': 'Administration'},\n    {'name': 'Eva', 'salary': 50000, 'department': 'IT'},\n    {'name': 'Frank', 'salary': 60000, 'department': 'HR'},\n    {'name': 'Grace', 'salary': 28000, 'department': 'Finance'}\n]", "output": "[]", "imports": [], "original_snippet": "def f(employees):\n    employees.sort(key=lambda x: x['salary'])\n    (low_salary, mid_salary, high_salary) = ([], [], [])\n    for employee in employees:\n        bonus = 0\n        if employee['salary'] < 30000:\n            bonus = employee['salary'] * 0.02\n        elif 30000 <= employee['salary'] <= 50000:\n            bonus = employee['salary'] * 0.03\n        else:\n            bonus = employee['salary'] * 0.05\n        if bonus == 0:\n            low_salary.append((employee['name'], bonus))\n        elif 0 < bonus <= 5000:\n            mid_salary.append((employee['name'], bonus))\n        else:\n            high_salary.append((employee['name'], bonus))\n    result = [low_salary[0][0], mid_salary[0][0], high_salary[0][0]] if low_salary and mid_salary and high_salary else []\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(lst):\n    lap_times = lst[::2]\n    speeds = lst[1::2]\n    lap_time_sums = [sum(lap_times[:i]) for i in range(1, len(lap_times) + 1)]\n    speed_log_avgs = [sum((spd_log for spd_log in speeds[:i])) / i for i in range(1, len(speeds) + 1)]\n    result = sum((bit ^ 1 for (bit, lap_time_sum, speed_log_avg) in zip(lst, lap_time_sums, speed_log_avgs) if int(lap_time_sum) & 1 == 0 and speed_log_avg != 1))\n    return result", "input": "[1, 2, 3, 4, 5, 6]", "output": "3", "imports": [], "original_snippet": "def f(lst):\n    lap_times = lst[::2]\n    speeds = lst[1::2]\n    lap_time_sums = [sum(lap_times[:i]) for i in range(1, len(lap_times) + 1)]\n    speed_log_avgs = [sum((spd_log for spd_log in speeds[:i])) / i for i in range(1, len(speeds) + 1)]\n    result = sum((bit ^ 1 for (bit, lap_time_sum, speed_log_avg) in zip(lst, lap_time_sums, speed_log_avgs) if int(lap_time_sum) & 1 == 0 and speed_log_avg != 1))\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_string):\n    first_substring = input_string[0]\n    second_substring = input_string[1]\n    target_substring = 'target'\n    if target_substring in input_string:\n        index = input_string.find(target_substring)\n        new_string = first_substring + second_substring + input_string[index:]\n        reversed_new_string = new_string[::-1]\n        ascii_string = ''.join((str(ord(c)) for c in reversed_new_string))\n        found_substring = ''.join((str(ord(c)) for c in target_substring[::-1]))\n        result = reversed_new_string + found_substring\n        return result\n    else:\n        return None", "input": "'anchortargetsubstring'", "output": "'gnirtsbustegratna11610110311497116'", "imports": [], "original_snippet": "def f(input_string):\n    first_substring = input_string[0]\n    second_substring = input_string[1]\n    target_substring = 'target'\n    if target_substring in input_string:\n        index = input_string.find(target_substring)\n        new_string = first_substring + second_substring + input_string[index:]\n        reversed_new_string = new_string[::-1]\n        ascii_string = ''.join((str(ord(c)) for c in reversed_new_string))\n        found_substring = ''.join((str(ord(c)) for c in target_substring[::-1]))\n        result = reversed_new_string + found_substring\n        return result\n    else:\n        return None", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_list):\n    results = []\n    for d in input_list:\n        nums = d['nums']\n        target = d['target']\n        for i in range(len(nums)):\n            total = 0\n            for j in range(i, len(nums)):\n                total += nums[j]\n                if total == target:\n                    results.append(nums[i:j + 1])\n                    break\n                elif total > target:\n                    break\n    return sorted(results, key=lambda x: (-sum(x), -len(x)))[0]", "input": "[{'nums': [1, 2, 3, 4, 5], 'target': 6}, {'nums': [5, 4, 3, 2, 1], 'target': 5}]", "output": "[1, 2, 3]", "imports": [], "original_snippet": "def f(input_list):\n    results = []\n    for d in input_list:\n        nums = d['nums']\n        target = d['target']\n        for i in range(len(nums)):\n            total = 0\n            for j in range(i, len(nums)):\n                total += nums[j]\n                if total == target:\n                    results.append(nums[i:j + 1])\n                    break\n                elif total > target:\n                    break\n    return sorted(results, key=lambda x: (-sum(x), -len(x)))[0]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(num_list):\n    unique_nums = sorted(set(num_list))\n    sum_of_squares = sum((num ** 2 for num in unique_nums))\n    first_occurrences = {num: num_list.index(num) for num in unique_nums}\n    sum_of_indices = sum(first_occurrences.values())\n    return sum_of_squares - sum_of_indices", "input": "[5, 9, 3, 5, 7, 9, 10, 3]", "output": "251", "imports": [], "original_snippet": "def f(num_list):\n    unique_nums = sorted(set(num_list))\n    sum_of_squares = sum((num ** 2 for num in unique_nums))\n    first_occurrences = {num: num_list.index(num) for num in unique_nums}\n    sum_of_indices = sum(first_occurrences.values())\n    return sum_of_squares - sum_of_indices", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from collections import Counter\nfrom operator import itemgetter\ndef f(s):\n    char_freq = Counter(s)\n    sorted_chars = sorted(char_freq.items(), key=itemgetter(1))\n    distinct_chars = len(sorted_chars)\n    min_freq = sorted_chars[0][1]\n    max_freq = sorted_chars[-1][1]\n    return (distinct_chars, min_freq, max_freq)", "input": "'aabbccddeeffgghhii'", "output": "(9, 2, 2)", "imports": ["from collections import Counter", "from operator import itemgetter"], "original_snippet": "from collections import Counter\nfrom operator import itemgetter\ndef f(s):\n    char_freq = Counter(s)\n    sorted_chars = sorted(char_freq.items(), key=itemgetter(1))\n    distinct_chars = len(sorted_chars)\n    min_freq = sorted_chars[0][1]\n    max_freq = sorted_chars[-1][1]\n    return (distinct_chars, min_freq, max_freq)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(lst: list):\n    unique_values = []\n    for element in lst:\n        if isinstance(element, list):\n            unique_values.append(element)\n        elif element not in unique_values:\n            unique_values.append(element)\n    return [sublist + [int] for sublist in unique_values]", "input": "[[1, 2], [3, 4], [1, 2], [4, 5], [1, 2]]", "output": "[[1, 2, <class 'int'>], [3, 4, <class 'int'>], [1, 2, <class 'int'>], [4, 5, <class 'int'>], [1, 2, <class 'int'>]]", "imports": [], "original_snippet": "def f(lst: list):\n    unique_values = []\n    for element in lst:\n        if isinstance(element, list):\n            unique_values.append(element)\n        elif element not in unique_values:\n            unique_values.append(element)\n    return [sublist + [int] for sublist in unique_values]", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(num_list: list, n: int):\n    unique_nums = list(set(num_list))\n    unique_nums.sort()\n    squares = [x * x for x in unique_nums]\n    filtered_squares = [x for x in squares if x % n != 0]\n    return sum(filtered_squares)", "input": "[1,2,2,3,3,3,4,4,4,4,5,5,6,6,7], 2", "output": "84", "imports": [], "original_snippet": "def f(num_list: list, n: int):\n    unique_nums = list(set(num_list))\n    unique_nums.sort()\n    squares = [x * x for x in unique_nums]\n    filtered_squares = [x for x in squares if x % n != 0]\n    return sum(filtered_squares)", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(numbers):\n    nums_counted = {}\n    average_sum = 0\n    prev_pair_comparator = None\n    for num in numbers:\n        if num in nums_counted:\n            nums_counted[num] += 1\n        else:\n            nums_counted[num] = 1\n        average_sum += num / len(numbers)\n        if prev_pair_comparator is not None:\n            if num > prev_pair_comparator:\n                return 'greater'\n            elif num < prev_pair_comparator:\n                return 'less'\n        prev_pair_comparator = num\n    return (max(nums_counted, key=nums_counted.get), round(average_sum, 2), nums_counted)", "input": "[5, 9, 2, 5, 2, 7, 5, 7, 5, 8]", "output": "'greater'", "imports": [], "original_snippet": "def f(numbers):\n    nums_counted = {}\n    average_sum = 0\n    prev_pair_comparator = None\n    for num in numbers:\n        if num in nums_counted:\n            nums_counted[num] += 1\n        else:\n            nums_counted[num] = 1\n        average_sum += num / len(numbers)\n        if prev_pair_comparator is not None:\n            if num > prev_pair_comparator:\n                return 'greater'\n            elif num < prev_pair_comparator:\n                return 'less'\n        prev_pair_comparator = num\n    return (max(nums_counted, key=nums_counted.get), round(average_sum, 2), nums_counted)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(lst):\n    lst.sort()\n    lst = [x for x in lst if x % 2 != 0]\n    for i in range(len(lst)):\n        lst[i] = lst[i] * 2\n    lst = lst[::-1]\n    return lst", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "[18, 14, 10, 6, 2]", "imports": [], "original_snippet": "def f(lst):\n    lst.sort()\n    lst = [x for x in lst if x % 2 != 0]\n    for i in range(len(lst)):\n        lst[i] = lst[i] * 2\n    lst = lst[::-1]\n    return lst", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(lst):\n    min_val = min(lst)\n    lst = [elem // 2 for elem in lst if elem % 2 == 0]\n    lst = [elem + min_val for elem in lst]\n    lst = [elem * 2 for elem in lst]\n    return lst", "input": "[2, 4, 6, 8, 10]", "output": "[6, 8, 10, 12, 14]", "imports": [], "original_snippet": "def f(lst):\n    min_val = min(lst)\n    lst = [elem // 2 for elem in lst if elem % 2 == 0]\n    lst = [elem + min_val for elem in lst]\n    lst = [elem * 2 for elem in lst]\n    return lst", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(nums: list):\n    if not nums:\n        return None\n    nums.sort()\n    nums.pop(0)\n    new_nums = [num * (num - i // 2) for (i, num) in enumerate(nums)]\n    max_num = max(new_nums)\n    return max_num / len(nums)", "input": "[1, 2, 3, 4, 5]", "output": "5.0", "imports": [], "original_snippet": "def f(nums: list):\n    if not nums:\n        return None\n    nums.sort()\n    nums.pop(0)\n    new_nums = [num * (num - i // 2) for (i, num) in enumerate(nums)]\n    max_num = max(new_nums)\n    return max_num / len(nums)", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "import heapq\ndef f(matrix, target_sum):\n    row_sum = [sum(row) for row in matrix]\n    col_sum = [0] * len(matrix[0])\n    for row in matrix:\n        for (i, col) in enumerate(row):\n            col_sum[i] += col\n    h = []\n    for (i, col) in enumerate(col_sum):\n        if col >= target_sum:\n            h.append(-col)\n    heapq.heapify(h)\n    count = 0\n    while len(h) >= 2:\n        larger = -heapq.heappop(h)\n        smaller = -heapq.heappop(h)\n        new_sum = larger + smaller\n        if new_sum >= target_sum:\n            count += 1\n            heapq.heappush(h, -new_sum)\n    return count", "input": "[[10, 9, 8], [7, 6, 5], [4, 3, 2], [1, 100, 6]], 15", "output": "2", "imports": ["import heapq"], "original_snippet": "import heapq\ndef f(matrix, target_sum):\n    row_sum = [sum(row) for row in matrix]\n    col_sum = [0] * len(matrix[0])\n    for row in matrix:\n        for (i, col) in enumerate(row):\n            col_sum[i] += col\n    h = []\n    for (i, col) in enumerate(col_sum):\n        if col >= target_sum:\n            h.append(-col)\n    heapq.heapify(h)\n    count = 0\n    while len(h) >= 2:\n        larger = -heapq.heappop(h)\n        smaller = -heapq.heappop(h)\n        new_sum = larger + smaller\n        if new_sum >= target_sum:\n            count += 1\n            heapq.heappush(h, -new_sum)\n    return count", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(numbers: list, num: int) -> int:\n    if not numbers or num <= 0:\n        return 0\n    numbers.sort()\n    result = 0\n    for i in range(0, len(numbers), num):\n        group = numbers[i:i + num]\n        result += max(group) - min(group)\n    return result", "input": "[4, 7, 2, 9, 5, 1, 6, 3, 8], 3", "output": "6", "imports": [], "original_snippet": "def f(numbers: list, num: int) -> int:\n    if not numbers or num <= 0:\n        return 0\n    numbers.sort()\n    result = 0\n    for i in range(0, len(numbers), num):\n        group = numbers[i:i + num]\n        result += max(group) - min(group)\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(nums: list):\n    odd_squares = [num ** 2 for num in nums if num % 2 == 1]\n    result = odd_squares[0]\n    for num in odd_squares[1:]:\n        result &= num\n    return result", "input": "[1, 2, 3, 4, 5]", "output": "1", "imports": [], "original_snippet": "def f(nums: list):\n    odd_squares = [num ** 2 for num in nums if num % 2 == 1]\n    result = odd_squares[0]\n    for num in odd_squares[1:]:\n        result &= num\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(data: dict):\n    processed_data = {}\n    processed_keys = []\n    for (key, value) in data.items():\n        if isinstance(key, str) and isinstance(value, (int, float)):\n            processed_key = key[::-1].strip()\n            if processed_key in processed_data:\n                if len(processed_key) % 2 == 0:\n                    processed_data[processed_key] += value * 2\n                else:\n                    processed_data[processed_key] += value + 1\n            elif len(processed_key) % 2 == 0:\n                processed_data[processed_key] = value * 2\n            else:\n                processed_data[processed_key] = value + 1\n            processed_keys.append(processed_key)\n    even_values_count = 0\n    for key in processed_data:\n        if key not in processed_keys:\n            processed_data[key] = 0\n        for processed_key in processed_keys:\n            if len(processed_key) % 2 == 0:\n                processed_data[key] += 1\n        if processed_data[key] % 2 == 0:\n            even_values_count += 1\n    return even_values_count", "input": "{'abc': 2, 'def': 3, 'xyz': 4}", "output": "1", "imports": [], "original_snippet": "def f(data: dict):\n    processed_data = {}\n    processed_keys = []\n    for (key, value) in data.items():\n        if isinstance(key, str) and isinstance(value, (int, float)):\n            processed_key = key[::-1].strip()\n            if processed_key in processed_data:\n                if len(processed_key) % 2 == 0:\n                    processed_data[processed_key] += value * 2\n                else:\n                    processed_data[processed_key] += value + 1\n            elif len(processed_key) % 2 == 0:\n                processed_data[processed_key] = value * 2\n            else:\n                processed_data[processed_key] = value + 1\n            processed_keys.append(processed_key)\n    even_values_count = 0\n    for key in processed_data:\n        if key not in processed_keys:\n            processed_data[key] = 0\n        for processed_key in processed_keys:\n            if len(processed_key) % 2 == 0:\n                processed_data[key] += 1\n        if processed_data[key] % 2 == 0:\n            even_values_count += 1\n    return even_values_count", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(lst):\n    return sum((-x if lst.count(x) % 2 == 0 else x for x in lst))", "input": "[1, 2, 3, 2, 1, 4, 5, 4, 5]", "output": "-21", "imports": [], "original_snippet": "def f(lst):\n    return sum((-x if lst.count(x) % 2 == 0 else x for x in lst))", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_list):\n    flat_list = [item for sublist in input_list for item in (sublist if isinstance(sublist, list) else [sublist])]\n    numeric_only = [x for x in flat_list if isinstance(x, int)]\n    string_only = [x for x in flat_list if isinstance(x, str)]\n    numeric_only.sort(key=lambda x: str(x))\n    string_only.sort()\n    unique_numeric = [str(x) for x in numeric_only]\n    unique_string = list(set(string_only))\n    combined_unique = unique_numeric + unique_string\n    backwards_combined = combined_unique[::-1]\n    final_string = ''.join(backwards_combined)\n    return final_string", "input": "[[9, 2, 6], ['apple', 3, 'orange', 8], [5, 'banana']]", "output": "'bananaorangeapple986532'", "imports": [], "original_snippet": "def f(input_list):\n    flat_list = [item for sublist in input_list for item in (sublist if isinstance(sublist, list) else [sublist])]\n    numeric_only = [x for x in flat_list if isinstance(x, int)]\n    string_only = [x for x in flat_list if isinstance(x, str)]\n    numeric_only.sort(key=lambda x: str(x))\n    string_only.sort()\n    unique_numeric = [str(x) for x in numeric_only]\n    unique_string = list(set(string_only))\n    combined_unique = unique_numeric + unique_string\n    backwards_combined = combined_unique[::-1]\n    final_string = ''.join(backwards_combined)\n    return final_string", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(s: str):\n    if not s:\n        return None\n    char_counts = {}\n    for char in s:\n        if char.isnumeric():\n            char_counts[char] = char_counts.get(char, 0) + 1\n    sorted_chars = sorted(char_counts.items(), key=lambda x: x[1], reverse=True)\n    (most_frequent_char, count) = sorted_chars[0]\n    new_count = count * (count - count // 2)\n    indices = [index for (index, char) in enumerate(s) if char == most_frequent_char]\n    result_string = ''\n    for (index, char) in enumerate(s):\n        if index in indices:\n            result_string += chr((ord(char) - ord('0') + new_count) % 10 + ord('0'))\n        else:\n            result_string += char\n    return result_string", "input": "\"1234567890\"", "output": "'2234567890'", "imports": [], "original_snippet": "def f(s: str):\n    if not s:\n        return None\n    char_counts = {}\n    for char in s:\n        if char.isnumeric():\n            char_counts[char] = char_counts.get(char, 0) + 1\n    sorted_chars = sorted(char_counts.items(), key=lambda x: x[1], reverse=True)\n    (most_frequent_char, count) = sorted_chars[0]\n    new_count = count * (count - count // 2)\n    indices = [index for (index, char) in enumerate(s) if char == most_frequent_char]\n    result_string = ''\n    for (index, char) in enumerate(s):\n        if index in indices:\n            result_string += chr((ord(char) - ord('0') + new_count) % 10 + ord('0'))\n        else:\n            result_string += char\n    return result_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(arr: list, k: int):\n    min_len = float('inf')\n    (left, total) = (0, 0)\n    for right in range(len(arr)):\n        total += arr[right]\n        while total >= k:\n            min_len = min(min_len, right - left + 1)\n            total -= arr[left]\n            left += 1\n    return min_len if min_len != float('inf') else -1", "input": "[23, 2, 4, 3, 5, 7, 8, 1], 21", "output": "1", "imports": [], "original_snippet": "def f(arr: list, k: int):\n    min_len = float('inf')\n    (left, total) = (0, 0)\n    for right in range(len(arr)):\n        total += arr[right]\n        while total >= k:\n            min_len = min(min_len, right - left + 1)\n            total -= arr[left]\n            left += 1\n    return min_len if min_len != float('inf') else -1", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(num_list: list, target_sum: int) -> list:\n    num_list.sort()\n    current_sum = 0\n    current_sublist = []\n    best_sum = 0\n    best_sublist = []\n    for num in num_list:\n        if current_sum + num <= target_sum:\n            current_sum += num\n            current_sublist.append(num)\n        else:\n            if abs(target_sum - current_sum) < abs(target_sum - best_sum):\n                best_sum = current_sum\n                best_sublist = current_sublist.copy()\n            current_sum = 0\n            current_sublist = [num]\n    if abs(target_sum - current_sum) < abs(target_sum - best_sum):\n        best_sum = current_sum\n        best_sublist = current_sublist.copy()\n    return best_sublist", "input": "[10, 15, 20, 25, 30, 35, 40], 100", "output": "[10, 15, 20, 25, 30]", "imports": [], "original_snippet": "def f(num_list: list, target_sum: int) -> list:\n    num_list.sort()\n    current_sum = 0\n    current_sublist = []\n    best_sum = 0\n    best_sublist = []\n    for num in num_list:\n        if current_sum + num <= target_sum:\n            current_sum += num\n            current_sublist.append(num)\n        else:\n            if abs(target_sum - current_sum) < abs(target_sum - best_sum):\n                best_sum = current_sum\n                best_sublist = current_sublist.copy()\n            current_sum = 0\n            current_sublist = [num]\n    if abs(target_sum - current_sum) < abs(target_sum - best_sum):\n        best_sum = current_sum\n        best_sublist = current_sublist.copy()\n    return best_sublist", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(nums: list, sorted_ranges: list=list()):\n    for (start, end) in sorted_ranges:\n        nums[start:end] = sorted(nums[start:end])\n    sorted_start = None\n    for i in range(len(nums) - 1):\n        if nums[i] <= nums[i + 1]:\n            sorted_start = i\n            break\n    return sorted_start", "input": "[0, 4, 2, 1, 3, 7, 9], [[1, 5]]", "output": "0", "imports": [], "original_snippet": "def f(nums: list, sorted_ranges: list=list()):\n    for (start, end) in sorted_ranges:\n        nums[start:end] = sorted(nums[start:end])\n    sorted_start = None\n    for i in range(len(nums) - 1):\n        if nums[i] <= nums[i + 1]:\n            sorted_start = i\n            break\n    return sorted_start", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(lst):\n    sum_result = 0\n    for i in lst:\n        mod_i = i % 17\n        if mod_i == 0:\n            continue\n        reverse_i = int(str(i)[::-1])\n        mod_reverse_i = reverse_i % 17\n        if mod_reverse_i == 0:\n            continue\n        sum_result += mod_i * mod_reverse_i\n    return sum_result", "input": "[5278, 321, 994, 235, 696, 31317]", "output": "511", "imports": [], "original_snippet": "def f(lst):\n    sum_result = 0\n    for i in lst:\n        mod_i = i % 17\n        if mod_i == 0:\n            continue\n        reverse_i = int(str(i)[::-1])\n        mod_reverse_i = reverse_i % 17\n        if mod_reverse_i == 0:\n            continue\n        sum_result += mod_i * mod_reverse_i\n    return sum_result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(lst1, lst2):\n    final = sorted(list(set(lst1 + lst2)))\n    original_indexes = {x: min(lst1.index(x) if x in lst1 else float('inf'), lst2.index(x) if x in lst2 else float('inf')) for x in final}\n    return [(x, original_indexes[x]) for x in final]", "input": "[1, 2, 3, 4, 5], [3, 4, 5, 6, 7]", "output": "[(1, 0), (2, 1), (3, 0), (4, 1), (5, 2), (6, 3), (7, 4)]", "imports": [], "original_snippet": "def f(lst1, lst2):\n    final = sorted(list(set(lst1 + lst2)))\n    original_indexes = {x: min(lst1.index(x) if x in lst1 else float('inf'), lst2.index(x) if x in lst2 else float('inf')) for x in final}\n    return [(x, original_indexes[x]) for x in final]", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(input_list: list):\n    n = len(input_list)\n    result = [input_list[i] + n for i in range(n)]\n    return result", "input": "[3, 1, 4, 1, 5, 9]", "output": "[9, 7, 10, 7, 11, 15]", "imports": [], "original_snippet": "def f(input_list: list):\n    n = len(input_list)\n    result = [input_list[i] + n for i in range(n)]\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(relationships: dict):\n    graph = {}\n    metric = 0\n    for (u, details) in relationships.items():\n        graph[u] = {'age': details['age'], 'job': details['job'], 'connections': details['connections'], 'counter': sum(details['connections'].values()), 'sort_key': (details['age'], details['job'])}\n    sorted_entities = sorted(graph, key=lambda u: (graph[u]['counter'], -graph[u]['age'], graph[u]['job']))\n    central_node = sorted_entities[0]\n    metric = graph[central_node]['counter']\n    return (central_node, metric)", "input": "{\n    'Alice': {'age': 30, 'job': 'Engineer', 'connections': {'Bob': 2, 'Charlie': 1, 'David': 3}},\n    'Bob': {'age': 35, 'job': 'Doctor', 'connections': {'Alice': 2, 'Eve': 1}},\n    'Charlie': {'age': 40, 'job': 'Lawyer', 'connections': {'Alice': 1, 'David': 2}},\n    'David': {'age': 31, 'job': 'Teacher', 'connections': {'Alice': 3, 'Charlie': 2}},\n    'Eve': {'age': 32, 'job': 'Architect', 'connections': {'Bob': 1}},\n}", "output": "('Eve', 1)", "imports": [], "original_snippet": "def f(relationships: dict):\n    graph = {}\n    metric = 0\n    for (u, details) in relationships.items():\n        graph[u] = {'age': details['age'], 'job': details['job'], 'connections': details['connections'], 'counter': sum(details['connections'].values()), 'sort_key': (details['age'], details['job'])}\n    sorted_entities = sorted(graph, key=lambda u: (graph[u]['counter'], -graph[u]['age'], graph[u]['job']))\n    central_node = sorted_entities[0]\n    metric = graph[central_node]['counter']\n    return (central_node, metric)", "composite_functions": [], "_input_type": "dict", "_output_type": "tuple"}
{"snippet": "def f(operation_lists: list):\n    result = 0\n    for operation_list in operation_lists:\n        for operation in operation_list:\n            (verb, number) = operation\n            if verb == 'add':\n                result += number\n            elif verb == 'subtract':\n                result -= number\n            elif verb == 'multiply':\n                result *= number\n            elif verb == 'divide':\n                result /= number\n    return result", "input": "[[('add', 1), ('subtract', 2)], [('multiply', 3), ('divide', 2)]]", "output": "-1.5", "imports": [], "original_snippet": "def f(operation_lists: list):\n    result = 0\n    for operation_list in operation_lists:\n        for operation in operation_list:\n            (verb, number) = operation\n            if verb == 'add':\n                result += number\n            elif verb == 'subtract':\n                result -= number\n            elif verb == 'multiply':\n                result *= number\n            elif verb == 'divide':\n                result /= number\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(lst: list) -> dict:\n    sorted_lst = sorted(lst)\n    sums = {}\n    found = False\n    for i in range(len(sorted_lst)):\n        if sorted_lst[i] % 2 != 0 and sorted_lst[i] not in sums:\n            sums[sorted_lst[i]] = sum((x for x in sorted_lst[i - 1:0:-2] if x % 2 == 0))\n        for j in range(i, len(sorted_lst)):\n            if sorted_lst[j] >= sorted_lst[i] and sorted_lst[j] % 3 == 0:\n                found = True\n                break\n        if found:\n            break\n    return {'orig_list': lst, 'sorted_list': sorted_lst, 'sums': sums}", "input": "[4, 2, 1, 6, 7, 8, 3, 5, 9]", "output": "{'orig_list': [4, 2, 1, 6, 7, 8, 3, 5, 9], 'sorted_list': [1, 2, 3, 4, 5, 6, 7, 8, 9], 'sums': {1: 0}}", "imports": [], "original_snippet": "def f(lst: list) -> dict:\n    sorted_lst = sorted(lst)\n    sums = {}\n    found = False\n    for i in range(len(sorted_lst)):\n        if sorted_lst[i] % 2 != 0 and sorted_lst[i] not in sums:\n            sums[sorted_lst[i]] = sum((x for x in sorted_lst[i - 1:0:-2] if x % 2 == 0))\n        for j in range(i, len(sorted_lst)):\n            if sorted_lst[j] >= sorted_lst[i] and sorted_lst[j] % 3 == 0:\n                found = True\n                break\n        if found:\n            break\n    return {'orig_list': lst, 'sorted_list': sorted_lst, 'sums': sums}", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(lst):\n    lst.sort()\n    lst = [x for x in lst if x % 2 != 0]\n    for i in range(len(lst)):\n        lst[i] = lst[i] * 3\n    lst = lst[::-1]\n    return lst", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "[27, 21, 15, 9, 3]", "imports": [], "original_snippet": "def f(lst):\n    lst.sort()\n    lst = [x for x in lst if x % 2 != 0]\n    for i in range(len(lst)):\n        lst[i] = lst[i] * 3\n    lst = lst[::-1]\n    return lst", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(nums: list):\n    is_increasing = True\n    is_decreasing = True\n    for i in range(1, len(nums)):\n        if nums[i] < nums[i - 1]:\n            is_increasing = False\n        if nums[i] > nums[i - 1]:\n            is_decreasing = False\n    return is_increasing or is_decreasing", "input": "[1, 2, 3, 4, 5]", "output": "True", "imports": [], "original_snippet": "def f(nums: list):\n    is_increasing = True\n    is_decreasing = True\n    for i in range(1, len(nums)):\n        if nums[i] < nums[i - 1]:\n            is_increasing = False\n        if nums[i] > nums[i - 1]:\n            is_decreasing = False\n    return is_increasing or is_decreasing", "composite_functions": [], "_input_type": "list", "_output_type": "bool"}
{"snippet": "def f(numbers: list):\n    transformed_values = [x * (i + 1) for (i, x) in enumerate(numbers)]\n    squared_values = [x ** 2 for x in transformed_values]\n    sorted_values = sorted(squared_values, reverse=True)\n    rounded_sum = round(sum(sorted_values) / 2)\n    return rounded_sum", "input": "[1, 2, 3, 4, 5]", "output": "490", "imports": [], "original_snippet": "def f(numbers: list):\n    transformed_values = [x * (i + 1) for (i, x) in enumerate(numbers)]\n    squared_values = [x ** 2 for x in transformed_values]\n    sorted_values = sorted(squared_values, reverse=True)\n    rounded_sum = round(sum(sorted_values) / 2)\n    return rounded_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(matrix: list) -> tuple:\n    if len(matrix) == 0 or len(matrix[0]) == 0:\n        return (-1, -1, -1)\n    n = len(matrix)\n    m = len(matrix[0])\n    diag_sum = 0\n    (row, col) = (0, 0)\n    while row < n and col < m:\n        diag_sum += matrix[row][col]\n        row += 1\n        col += 1\n    col0_product = 1\n    for i in range(n):\n        col0_product *= matrix[i][0]\n    max_len = 0\n    for row in matrix:\n        inc_subseq = [row[0]]\n        for num in row[1:]:\n            if num > inc_subseq[-1]:\n                inc_subseq.append(num)\n            else:\n                inc_subseq[:] = [x for x in inc_subseq if x <= num]\n                inc_subseq.append(num)\n        max_len = max(max_len, len(inc_subseq))\n    return (diag_sum, col0_product, max_len)", "input": "[[1, 2, 3], [4, 3, 2], [2, 2, 2]]", "output": "(6, 8, 3)", "imports": [], "original_snippet": "def f(matrix: list) -> tuple:\n    if len(matrix) == 0 or len(matrix[0]) == 0:\n        return (-1, -1, -1)\n    n = len(matrix)\n    m = len(matrix[0])\n    diag_sum = 0\n    (row, col) = (0, 0)\n    while row < n and col < m:\n        diag_sum += matrix[row][col]\n        row += 1\n        col += 1\n    col0_product = 1\n    for i in range(n):\n        col0_product *= matrix[i][0]\n    max_len = 0\n    for row in matrix:\n        inc_subseq = [row[0]]\n        for num in row[1:]:\n            if num > inc_subseq[-1]:\n                inc_subseq.append(num)\n            else:\n                inc_subseq[:] = [x for x in inc_subseq if x <= num]\n                inc_subseq.append(num)\n        max_len = max(max_len, len(inc_subseq))\n    return (diag_sum, col0_product, max_len)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(input_list):\n    modified_list = []\n    for i in input_list:\n        modified_element = i\n        if i % 2 == 0 and i > 0:\n            modified_element *= i % 5\n        elif i % 3 == 0 and i > 0:\n            modified_element = i ** (i % 4)\n        elif i % 5 == 0 and i > 0:\n            modified_element = 0\n        modified_list.append(modified_element)\n    return modified_list", "input": "[20, 9, 18, 23, 15]", "output": "[0, 9, 54, 23, 3375]", "imports": [], "original_snippet": "def f(input_list):\n    modified_list = []\n    for i in input_list:\n        modified_element = i\n        if i % 2 == 0 and i > 0:\n            modified_element *= i % 5\n        elif i % 3 == 0 and i > 0:\n            modified_element = i ** (i % 4)\n        elif i % 5 == 0 and i > 0:\n            modified_element = 0\n        modified_list.append(modified_element)\n    return modified_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(words: list, k: int):\n    word_count = {}\n    for word in words:\n        if word in word_count:\n            word_count[word] += 1\n        else:\n            word_count[word] = 1\n    sorted_words = sorted(word_count.items(), key=lambda x: (-x[1], x[0]))\n    top_k_words = [word for (word, count) in sorted_words[:k]]\n    return top_k_words", "input": "['apple', 'orange', 'banana', 'apple', 'orange', 'apple'], 3", "output": "['apple', 'orange', 'banana']", "imports": [], "original_snippet": "def f(words: list, k: int):\n    word_count = {}\n    for word in words:\n        if word in word_count:\n            word_count[word] += 1\n        else:\n            word_count[word] = 1\n    sorted_words = sorted(word_count.items(), key=lambda x: (-x[1], x[0]))\n    top_k_words = [word for (word, count) in sorted_words[:k]]\n    return top_k_words", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(names):\n    total = 0\n    palindromic_substrings = {}\n    for name in names:\n        reversed_name = name[::-1]\n        potential_palindrome = reversed_name + name\n        if potential_palindrome == potential_palindrome[::-1]:\n            current_total = sum([ord(char) for char in potential_palindrome])\n            palindromic_substrings[potential_palindrome] = current_total\n        else:\n            palindrome_candidate = potential_palindrome + potential_palindrome[::-1]\n            if palindrome_candidate == palindrome_candidate[::-1]:\n                current_total = sum([ord(char) for char in palindrome_candidate])\n                palindromic_substrings[palindrome_candidate] = current_total\n    if len(palindromic_substrings) > 0:\n        total = max(palindromic_substrings.values())\n    return total", "input": "['adddcas', 'ababasa', 'bobabba']", "output": "1416", "imports": [], "original_snippet": "def f(names):\n    total = 0\n    palindromic_substrings = {}\n    for name in names:\n        reversed_name = name[::-1]\n        potential_palindrome = reversed_name + name\n        if potential_palindrome == potential_palindrome[::-1]:\n            current_total = sum([ord(char) for char in potential_palindrome])\n            palindromic_substrings[potential_palindrome] = current_total\n        else:\n            palindrome_candidate = potential_palindrome + potential_palindrome[::-1]\n            if palindrome_candidate == palindrome_candidate[::-1]:\n                current_total = sum([ord(char) for char in palindrome_candidate])\n                palindromic_substrings[palindrome_candidate] = current_total\n    if len(palindromic_substrings) > 0:\n        total = max(palindromic_substrings.values())\n    return total", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(strings):\n    result = []\n    for s in strings:\n        if s.isalpha() and s != s.lower():\n            chars = list(map(str.lower, s))\n            (chars[0], chars[-1]) = (chars[-1].upper(), chars[0].upper())\n            result.append(''.join(chars))\n    return sorted(result, key=lambda x: (x.isalpha(), x))", "input": "['Alice', 'bob', 'carol', 'DEF', 'GHI', 'jkl']", "output": "['ElicA', 'FeD', 'IhG']", "imports": [], "original_snippet": "def f(strings):\n    result = []\n    for s in strings:\n        if s.isalpha() and s != s.lower():\n            chars = list(map(str.lower, s))\n            (chars[0], chars[-1]) = (chars[-1].upper(), chars[0].upper())\n            result.append(''.join(chars))\n    return sorted(result, key=lambda x: (x.isalpha(), x))", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(data: dict):\n    visited_nodes = set('')\n    parent_nodes = set()\n    current_node = ''\n    while current_node != None:\n        child_nodes = data.get(current_node, [])\n        if len(child_nodes) == 0:\n            if current_node != '':\n                return True\n            else:\n                return False\n        if current_node != '':\n            parent_nodes.add(current_node)\n            child_nodes = [node for node in child_nodes if node not in visited_nodes]\n        if len(child_nodes) == 0:\n            current_node = None\n        else:\n            current_node = child_nodes[0]\n            visited_nodes.add(current_node)", "input": "{\"\": [\"A\", \"B\", \"C\"], \"A\": [\"D\", \"E\"], \"B\": [\"F\"], \"C\": [], \"D\": [], \"E\": [\"H\"], \"F\": [\"I\"], \"H\": [], \"I\": [\"J\"], \"J\": []}", "output": "True", "imports": [], "original_snippet": "def f(data: dict):\n    visited_nodes = set('')\n    parent_nodes = set()\n    current_node = ''\n    while current_node != None:\n        child_nodes = data.get(current_node, [])\n        if len(child_nodes) == 0:\n            if current_node != '':\n                return True\n            else:\n                return False\n        if current_node != '':\n            parent_nodes.add(current_node)\n            child_nodes = [node for node in child_nodes if node not in visited_nodes]\n        if len(child_nodes) == 0:\n            current_node = None\n        else:\n            current_node = child_nodes[0]\n            visited_nodes.add(current_node)", "composite_functions": [], "_input_type": "dict", "_output_type": "bool"}
{"snippet": "def f(input_dict):\n    counts = input_dict['counts']\n    sorted_names = sorted(input_dict['names'])\n    largest_count = max(counts.values())\n    threshold = int(largest_count / 3)\n    low_count = []\n    medium_count = []\n    high_count = []\n    for (name, count) in counts.items():\n        if count < threshold:\n            low_count.append(name)\n        elif threshold <= count <= largest_count - threshold:\n            medium_count.append(name)\n        else:\n            high_count.append(name)\n    result = [min(low_count, default=''), min(medium_count, default=''), min(high_count, default='')]\n    return result", "input": "{\n    \"counts\": {\"Alice\": 10, \"Bob\": 8, \"Carol\": 12, \"Dave\": 5, \"Eva\": 7},\n    \"names\": [\"Dave\", \"Carol\", \"Alice\", \"Bob\"]\n}", "output": "['', 'Bob', 'Alice']", "imports": [], "original_snippet": "def f(input_dict):\n    counts = input_dict['counts']\n    sorted_names = sorted(input_dict['names'])\n    largest_count = max(counts.values())\n    threshold = int(largest_count / 3)\n    low_count = []\n    medium_count = []\n    high_count = []\n    for (name, count) in counts.items():\n        if count < threshold:\n            low_count.append(name)\n        elif threshold <= count <= largest_count - threshold:\n            medium_count.append(name)\n        else:\n            high_count.append(name)\n    result = [min(low_count, default=''), min(medium_count, default=''), min(high_count, default='')]\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "list"}
{"snippet": "def f(numbers, target):\n    triples = set()\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            for k in range(j + 1, len(numbers)):\n                if numbers[i] + numbers[j] + numbers[k] == target:\n                    triple = (min(numbers[i], numbers[j], numbers[k]), max(numbers[i], numbers[j], numbers[k]))\n                    triples.add(triple)\n    return len(triples)", "input": "[10, 4, 1, 3, 12, 7, 5], 15", "output": "2", "imports": [], "original_snippet": "def f(numbers, target):\n    triples = set()\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            for k in range(j + 1, len(numbers)):\n                if numbers[i] + numbers[j] + numbers[k] == target:\n                    triple = (min(numbers[i], numbers[j], numbers[k]), max(numbers[i], numbers[j], numbers[k]))\n                    triples.add(triple)\n    return len(triples)", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "from collections import Counter\ndef f(nums: list):\n    freq_dict = Counter(nums)\n    weighted_sum = sum((num ** 2 * freq_dict[num] ** num for num in freq_dict))\n    total_frequency = sum(freq_dict.values())\n    return weighted_sum / total_frequency", "input": "[1, 2, 2, 3, 3, 3]", "output": "43.333333333333336", "imports": ["from collections import Counter"], "original_snippet": "from collections import Counter\ndef f(nums: list):\n    freq_dict = Counter(nums)\n    weighted_sum = sum((num ** 2 * freq_dict[num] ** num for num in freq_dict))\n    total_frequency = sum(freq_dict.values())\n    return weighted_sum / total_frequency", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(history):\n    result = []\n    for (series, entry) in history.items():\n        if series == 'Odd':\n            total_sum = sum([n for n in entry['numbers'] if n % 4 != 0 and n % 3 != 0])\n            result.append({'series': series, 'sum': total_sum})\n        elif series == 'Even':\n            total_sum = sum([n for n in entry['numbers'] if n % 4 == 0 and n % 3 == 0])\n            result.append({'series': series, 'sum': total_sum})\n    return result", "input": "{'Odd': {'numbers': [1, 2, 3, 4, 5]}, 'Even': {'numbers': [6, 7, 8, 9, 10]}}", "output": "[{'series': 'Odd', 'sum': 8}, {'series': 'Even', 'sum': 0}]", "imports": [], "original_snippet": "def f(history):\n    result = []\n    for (series, entry) in history.items():\n        if series == 'Odd':\n            total_sum = sum([n for n in entry['numbers'] if n % 4 != 0 and n % 3 != 0])\n            result.append({'series': series, 'sum': total_sum})\n        elif series == 'Even':\n            total_sum = sum([n for n in entry['numbers'] if n % 4 == 0 and n % 3 == 0])\n            result.append({'series': series, 'sum': total_sum})\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "list"}
{"snippet": "def f(lst1, lst2):\n    for i in range(len(lst1)):\n        if i % 2 == 0:\n            lst1[i] *= 3\n        elif lst1[i] % 3 == 0:\n            product_even_pos = 1\n            for j in range(0, len(lst2), 2):\n                product_even_pos *= lst2[j]\n            lst1[i] += product_even_pos\n    even_sum_set = set()\n    for i in range(len(lst2) - 1):\n        if (i + 1) % 2 == 0:\n            even_sum_set.add(lst2[i] + lst2[i + 1])\n        else:\n            even_sum_set.add(lst2[i] + lst2[i + 1])\n    total_sum = 0\n    for val in even_sum_set:\n        total_sum += val\n    return [x for x in lst1 if x > total_sum]", "input": "[2, 4, 6, 8, 10], [3, 6, 9, 12]", "output": "[]", "imports": [], "original_snippet": "def f(lst1, lst2):\n    for i in range(len(lst1)):\n        if i % 2 == 0:\n            lst1[i] *= 3\n        elif lst1[i] % 3 == 0:\n            product_even_pos = 1\n            for j in range(0, len(lst2), 2):\n                product_even_pos *= lst2[j]\n            lst1[i] += product_even_pos\n    even_sum_set = set()\n    for i in range(len(lst2) - 1):\n        if (i + 1) % 2 == 0:\n            even_sum_set.add(lst2[i] + lst2[i + 1])\n        else:\n            even_sum_set.add(lst2[i] + lst2[i + 1])\n    total_sum = 0\n    for val in even_sum_set:\n        total_sum += val\n    return [x for x in lst1 if x > total_sum]", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(input_list):\n    unique_distances = set()\n    seen_dists = set()\n    for sublist in input_list:\n        for i in range(len(sublist)):\n            for j in range(i + 1, len(sublist)):\n                dist = abs(sublist[i] - sublist[j])\n                if dist not in seen_dists:\n                    seen_dists.add(dist)\n                    unique_distances.add(dist)\n    (min_dist, max_dist) = (min(unique_distances), max(unique_distances))\n    result = [dist * 2 for dist in unique_distances if min_dist <= dist <= max_dist]\n    return sorted(result)", "input": "[[1, 6, 9], [2, 5, 10, 15], [3, 7, 12], [4, 8]]", "output": "[6, 8, 10, 16, 18, 20, 26]", "imports": [], "original_snippet": "def f(input_list):\n    unique_distances = set()\n    seen_dists = set()\n    for sublist in input_list:\n        for i in range(len(sublist)):\n            for j in range(i + 1, len(sublist)):\n                dist = abs(sublist[i] - sublist[j])\n                if dist not in seen_dists:\n                    seen_dists.add(dist)\n                    unique_distances.add(dist)\n    (min_dist, max_dist) = (min(unique_distances), max(unique_distances))\n    result = [dist * 2 for dist in unique_distances if min_dist <= dist <= max_dist]\n    return sorted(result)", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(numbers: list):\n    min_value = float('inf')\n    for (i, num) in enumerate(numbers):\n        if i % 2 == 0:\n            transformed_num = num + i\n        else:\n            transformed_num = num * i\n        min_value = min(min_value, transformed_num)\n    return min_value", "input": "[4, 1, 2, 3, 8, 5]", "output": "1", "imports": [], "original_snippet": "def f(numbers: list):\n    min_value = float('inf')\n    for (i, num) in enumerate(numbers):\n        if i % 2 == 0:\n            transformed_num = num + i\n        else:\n            transformed_num = num * i\n        min_value = min(min_value, transformed_num)\n    return min_value", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(lst: list):\n    grouped_by_key = {}\n    for d in lst:\n        key = d['key']\n        grouped_by_key.setdefault(key, []).append(d['value'])\n    max_values_in_groups = {key: max(values) for (key, values) in grouped_by_key.items()}\n    sorted_keys = sorted(max_values_in_groups.items(), key=lambda x: x[1])\n    formatted_output = [{'key': key, 'max_value': value} for (key, value) in sorted_keys]\n    total_keys = len(formatted_output)\n    return (formatted_output, total_keys)", "input": "[{'key': 'apple', 'value': 10}, {'key': 'banana', 'value': 20}, {'key': 'cherry', 'value': 30}, {'key': 'apricot', 'value': 25}, {'key': 'blueberry', 'value': 15}]", "output": "([{'key': 'apple', 'max_value': 10}, {'key': 'blueberry', 'max_value': 15}, {'key': 'banana', 'max_value': 20}, {'key': 'apricot', 'max_value': 25}, {'key': 'cherry', 'max_value': 30}], 5)", "imports": [], "original_snippet": "def f(lst: list):\n    grouped_by_key = {}\n    for d in lst:\n        key = d['key']\n        grouped_by_key.setdefault(key, []).append(d['value'])\n    max_values_in_groups = {key: max(values) for (key, values) in grouped_by_key.items()}\n    sorted_keys = sorted(max_values_in_groups.items(), key=lambda x: x[1])\n    formatted_output = [{'key': key, 'max_value': value} for (key, value) in sorted_keys]\n    total_keys = len(formatted_output)\n    return (formatted_output, total_keys)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(sequence, max_occurrences, min_length):\n    occurrences = 0\n    length = 0\n    for char in sequence:\n        if char == 'O':\n            occurrences += 1\n        length += 1\n    if occurrences > max_occurrences or length < min_length:\n        return False\n    for char in sequence:\n        if char == 'O' or len(sequence) == min_length:\n            return True\n        return False", "input": "[\"GREEN\", \"RED\", \"YELLOW\", \"YELLOW\"], 2, 5", "output": "False", "imports": [], "original_snippet": "def f(sequence, max_occurrences, min_length):\n    occurrences = 0\n    length = 0\n    for char in sequence:\n        if char == 'O':\n            occurrences += 1\n        length += 1\n    if occurrences > max_occurrences or length < min_length:\n        return False\n    for char in sequence:\n        if char == 'O' or len(sequence) == min_length:\n            return True\n        return False", "composite_functions": [], "_input_type": "tuple", "_output_type": "bool"}
{"snippet": "def f(s: str, stack_len):\n    stack = []\n    for c in s:\n        if c == '(':\n            stack.append(c)\n        elif c == ')':\n            if stack and stack[-1] == '(':\n                stack.pop()\n            else:\n                stack.append(c)\n    return len(stack) <= stack_len", "input": "\"(()())\", 2", "output": "True", "imports": [], "original_snippet": "def f(s: str, stack_len):\n    stack = []\n    for c in s:\n        if c == '(':\n            stack.append(c)\n        elif c == ')':\n            if stack and stack[-1] == '(':\n                stack.pop()\n            else:\n                stack.append(c)\n    return len(stack) <= stack_len", "composite_functions": [], "_input_type": "tuple", "_output_type": "bool"}
{"snippet": "def f(lst: list, k: int):\n    modified_values = [idx * num for (idx, num) in enumerate(lst, start=1)]\n    grouped_even_greater_than_modified = [num for (idx, num) in enumerate(lst) if idx % 2 == 0 and num > modified_values[idx - 1]]\n    grouped_odd_greater_than_modified = [num for (idx, num) in enumerate(lst) if idx % 2 == 1 and num > modified_values[idx - 1]]\n    grouped_even_greater_than_modified.sort()\n    grouped_odd_greater_than_modified.sort()\n    return sum((num * modified_values[idx - 1] for (idx, num) in enumerate(grouped_even_greater_than_modified + grouped_odd_greater_than_modified)))", "input": "[2, 3, 4, 5, 6, 7, 8, 9, 10], 3", "output": "270", "imports": [], "original_snippet": "def f(lst: list, k: int):\n    modified_values = [idx * num for (idx, num) in enumerate(lst, start=1)]\n    grouped_even_greater_than_modified = [num for (idx, num) in enumerate(lst) if idx % 2 == 0 and num > modified_values[idx - 1]]\n    grouped_odd_greater_than_modified = [num for (idx, num) in enumerate(lst) if idx % 2 == 1 and num > modified_values[idx - 1]]\n    grouped_even_greater_than_modified.sort()\n    grouped_odd_greater_than_modified.sort()\n    return sum((num * modified_values[idx - 1] for (idx, num) in enumerate(grouped_even_greater_than_modified + grouped_odd_greater_than_modified)))", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(students):\n    return_data = []\n    students.sort(key=lambda x: x['gpa'], reverse=True)\n    length = len(students)\n    avg = sum((student['gpa'] for student in students[:int(length * 0.5)])) / (length * 0.5)\n    for student in students[int(length * 0.5):]:\n        if student['gpa'] > avg:\n            return_data.append(student['name'])\n    return return_data", "input": "[\n    {'name': 'Alice', 'gpa': 3.1},\n    {'name': 'Bob', 'gpa': 3.7},\n    {'name': 'Carol', 'gpa': 3.3},\n    {'name': 'Dave', 'gpa': 4.0},\n    {'name': 'Eva', 'gpa': 3.0},\n    {'name': 'Frank', 'gpa': 3.9},\n    {'name': 'Grace', 'gpa': 3.8},\n    {'name': 'Henry', 'gpa': 3.6},\n    {'name': 'Ivy', 'gpa': 3.2},\n    {'name': 'Jack', 'gpa': 3.5},\n    {'name': 'Kate', 'gpa': 3.4},\n    {'name': 'Liam', 'gpa': 3.95},\n    {'name': 'Mia', 'gpa': 3.75},\n    {'name': 'Noah', 'gpa': 3.1},\n    {'name': 'Olivia', 'gpa': 3.55},\n    {'name': 'Pete', 'gpa': 3.15}\n]", "output": "[]", "imports": [], "original_snippet": "def f(students):\n    return_data = []\n    students.sort(key=lambda x: x['gpa'], reverse=True)\n    length = len(students)\n    avg = sum((student['gpa'] for student in students[:int(length * 0.5)])) / (length * 0.5)\n    for student in students[int(length * 0.5):]:\n        if student['gpa'] > avg:\n            return_data.append(student['name'])\n    return return_data", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(num_list: list, n: int):\n    unique_numbers = list(set(num_list))\n    unique_numbers.sort()\n    doubles = [num * 2 for num in unique_numbers]\n    filtered_doubles = [num for num in doubles if num % n != 0]\n    triples = [num * 3 for num in unique_numbers]\n    return [[num, triples[i]] for (i, num) in enumerate(filtered_doubles)]", "input": "[1,2,3,4,5,6,7,8,9], 3", "output": "[[2, 3], [4, 6], [8, 9], [10, 12], [14, 15], [16, 18]]", "imports": [], "original_snippet": "def f(num_list: list, n: int):\n    unique_numbers = list(set(num_list))\n    unique_numbers.sort()\n    doubles = [num * 2 for num in unique_numbers]\n    filtered_doubles = [num for num in doubles if num % n != 0]\n    triples = [num * 3 for num in unique_numbers]\n    return [[num, triples[i]] for (i, num) in enumerate(filtered_doubles)]", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(data: list):\n    sums = []\n    for i in range(len(data)):\n        sub_list = data[:i] + data[i + 1:]\n        sums.append(sum(sub_list))\n    return max(sums) - min(sums)", "input": "[5, 10, 15, 20]", "output": "15", "imports": [], "original_snippet": "def f(data: list):\n    sums = []\n    for i in range(len(data)):\n        sub_list = data[:i] + data[i + 1:]\n        sums.append(sum(sub_list))\n    return max(sums) - min(sums)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(ranges: list[dict]):\n    sorted_ranges = sorted(ranges, key=lambda k: k['start'])\n    i = 0\n    while i < len(sorted_ranges) - 1:\n        if sorted_ranges[i]['end'] <= sorted_ranges[i + 1]['start']:\n            i += 1\n            continue\n        if sorted_ranges[i]['is_included'] and sorted_ranges[i]['end'] <= sorted_ranges[i + 1]['end']:\n            del sorted_ranges[i]\n            continue\n        i += 1\n    for i in range(len(sorted_ranges)):\n        if sorted_ranges[i]['is_included']:\n            for j in range(i + 1, len(sorted_ranges)):\n                if sorted_ranges[j]['is_included'] and sorted_ranges[j]['start'] <= sorted_ranges[i]['end']:\n                    sorted_ranges[i]['end'] = sorted_ranges[j]['end']\n                    sorted_ranges[i]['is_included'] = False\n                    sorted_ranges[j]['start'] = sorted_ranges[j]['end'] + 1\n                elif not sorted_ranges[j]['is_included'] and sorted_ranges[j]['start'] <= sorted_ranges[i]['end']:\n                    sorted_ranges[i]['end'] = max(sorted_ranges[i]['end'], sorted_ranges[j]['end'])\n        elif not sorted_ranges[i]['is_included']:\n            for j in range(i + 1, len(sorted_ranges)):\n                if sorted_ranges[j]['is_included'] and sorted_ranges[j]['start'] <= sorted_ranges[i]['end']:\n                    sorted_ranges[i]['is_included'] = True\n                    sorted_ranges[j]['start'] = sorted_ranges[j]['end'] + 1\n    return sorted_ranges", "input": "[\n    {'start': 5, 'end': 10, 'is_included': True},\n    {'start': 3, 'end': 8, 'is_included': False},\n    {'start': 7, 'end': 14, 'is_included': True},\n    {'start': 11, 'end': 15, 'is_included': False},\n    {'start': 18, 'end': 25, 'is_included': True},\n    {'start': 22, 'end': 25, 'is_included': False}\n]", "output": "[{'start': 3, 'end': 8, 'is_included': False}, {'start': 11, 'end': 15, 'is_included': False}, {'start': 22, 'end': 25, 'is_included': False}]", "imports": [], "original_snippet": "def f(ranges: list[dict]):\n    sorted_ranges = sorted(ranges, key=lambda k: k['start'])\n    i = 0\n    while i < len(sorted_ranges) - 1:\n        if sorted_ranges[i]['end'] <= sorted_ranges[i + 1]['start']:\n            i += 1\n            continue\n        if sorted_ranges[i]['is_included'] and sorted_ranges[i]['end'] <= sorted_ranges[i + 1]['end']:\n            del sorted_ranges[i]\n            continue\n        i += 1\n    for i in range(len(sorted_ranges)):\n        if sorted_ranges[i]['is_included']:\n            for j in range(i + 1, len(sorted_ranges)):\n                if sorted_ranges[j]['is_included'] and sorted_ranges[j]['start'] <= sorted_ranges[i]['end']:\n                    sorted_ranges[i]['end'] = sorted_ranges[j]['end']\n                    sorted_ranges[i]['is_included'] = False\n                    sorted_ranges[j]['start'] = sorted_ranges[j]['end'] + 1\n                elif not sorted_ranges[j]['is_included'] and sorted_ranges[j]['start'] <= sorted_ranges[i]['end']:\n                    sorted_ranges[i]['end'] = max(sorted_ranges[i]['end'], sorted_ranges[j]['end'])\n        elif not sorted_ranges[i]['is_included']:\n            for j in range(i + 1, len(sorted_ranges)):\n                if sorted_ranges[j]['is_included'] and sorted_ranges[j]['start'] <= sorted_ranges[i]['end']:\n                    sorted_ranges[i]['is_included'] = True\n                    sorted_ranges[j]['start'] = sorted_ranges[j]['end'] + 1\n    return sorted_ranges", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(string):\n    stack = []\n    result = []\n    for i in range(len(string) * 2):\n        if i % 2 == 0:\n            stack.append(string[i // 2])\n        elif stack and stack[-1] == '(' and (string[i // 2] == ')'):\n            stack.pop()\n        else:\n            result.append(string[i // 2])\n    return ''.join(result)", "input": "'()(())()((())'", "output": "'()(())()((())'", "imports": [], "original_snippet": "def f(string):\n    stack = []\n    result = []\n    for i in range(len(string) * 2):\n        if i % 2 == 0:\n            stack.append(string[i // 2])\n        elif stack and stack[-1] == '(' and (string[i // 2] == ')'):\n            stack.pop()\n        else:\n            result.append(string[i // 2])\n    return ''.join(result)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_string):\n    first_substring = input_string[0:5]\n    second_substring = input_string[5:]\n    target_substring = 'target'\n    if target_substring in input_string:\n        index = input_string.find(target_substring)\n        new_string = first_substring + second_substring\n        reversed_new_string = new_string[::-1]\n        ascii_string = ''.join((str(ord(c)) for c in reversed_new_string))\n        found_substring = ''.join((str(ord(c)) for c in target_substring[::-1]))\n        result = reversed_new_string + found_substring\n        return result\n    else:\n        return None", "input": "'anchortargetsubstring'", "output": "'gnirtsbustegratrohcna11610110311497116'", "imports": [], "original_snippet": "def f(input_string):\n    first_substring = input_string[0:5]\n    second_substring = input_string[5:]\n    target_substring = 'target'\n    if target_substring in input_string:\n        index = input_string.find(target_substring)\n        new_string = first_substring + second_substring\n        reversed_new_string = new_string[::-1]\n        ascii_string = ''.join((str(ord(c)) for c in reversed_new_string))\n        found_substring = ''.join((str(ord(c)) for c in target_substring[::-1]))\n        result = reversed_new_string + found_substring\n        return result\n    else:\n        return None", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(dict1: dict, dict2: dict) -> dict:\n    merged_dict = {**dict1, **dict2}\n    dict1_sorted = {k: v for (k, v) in sorted(dict1.items(), key=lambda item: item[1])}\n    dict2_sorted = {k: v for (k, v) in sorted(dict2.items(), key=lambda item: item[1])}\n    for key in dict1_sorted:\n        if key in dict2_sorted:\n            merged_dict[key] = dict1_sorted[key] + dict2_sorted[key]\n    return merged_dict", "input": "{\n    'hamburger': 5,\n    'pizza': 3,\n    'pepsi': 3,\n    'nuggets': 6\n},\n{\n    'coffee': 5,\n    'piyunay': 3,\n    'cake': 3,\n    'waterbottle': 6\n}", "output": "{'hamburger': 5, 'pizza': 3, 'pepsi': 3, 'nuggets': 6, 'coffee': 5, 'piyunay': 3, 'cake': 3, 'waterbottle': 6}", "imports": [], "original_snippet": "def f(dict1: dict, dict2: dict) -> dict:\n    merged_dict = {**dict1, **dict2}\n    dict1_sorted = {k: v for (k, v) in sorted(dict1.items(), key=lambda item: item[1])}\n    dict2_sorted = {k: v for (k, v) in sorted(dict2.items(), key=lambda item: item[1])}\n    for key in dict1_sorted:\n        if key in dict2_sorted:\n            merged_dict[key] = dict1_sorted[key] + dict2_sorted[key]\n    return merged_dict", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(s):\n    a = s[::2]\n    b = s[1::2]\n    result = ''\n    for (i, j) in zip(a, b):\n        if i < j:\n            result += i\n        elif i > j:\n            result += j\n    return result", "input": "'hello'", "output": "'e'", "imports": [], "original_snippet": "def f(s):\n    a = s[::2]\n    b = s[1::2]\n    result = ''\n    for (i, j) in zip(a, b):\n        if i < j:\n            result += i\n        elif i > j:\n            result += j\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(lst):\n    sorted_lst = sorted(set(lst))\n    return sum(sorted_lst[::3])", "input": "[7, 3, 6, 3, 9, 6, 2, 2, 5, 9, 6, 4, 1, 8, 1, 5, 6, 8, 2, 10, 5, 7, 4, 1, 8, 7, 10, 4, 2, 7, 9, 10, 7, 10, 8, 10, 8, 4, 10, 1, 10, 6, 8, 10, 10, 9, 10, 9, 5, 9, 1, 4, 3, 4, 10, 9, 10, 10, 10, 2, 5, 7, 6, 6, 10, 6, 1, 5, 4, 8, 8, 1, 6, 7, 2, 10, 9, 6, 3, 4, 1, 3, 10, 1, 1, 4, 5, 8, 7, 5, 8, 7, 5, 3, 4, 8, 8, 6, 4, 2, 9, 6, 2, 8, 4, 3, 3, 4, 6, 6, 2, 2, 3, 10, 10, 10, 3, 4, 2, 7, 3, 8, 9, 8, 10, 9, 1, 7, 6, 2, 6, 3, 10, 10, 10, 10, 1, 2, 10, 6, 1, 6, 4, 7, 7, 9, 10, 7, 2, 8, 2, 1]", "output": "22", "imports": [], "original_snippet": "def f(lst):\n    sorted_lst = sorted(set(lst))\n    return sum(sorted_lst[::3])", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(lst: list):\n    even_reversed = lst[0::2][::-1]\n    odd_preserved = lst[1::2]\n    output = [even_reversed[i] - sum(odd_preserved[:i]) for i in range(len(even_reversed))]\n    return output", "input": "[10, 20, 30, 40, 50, 60, 70]", "output": "[70, 30, -30, -110]", "imports": [], "original_snippet": "def f(lst: list):\n    even_reversed = lst[0::2][::-1]\n    odd_preserved = lst[1::2]\n    output = [even_reversed[i] - sum(odd_preserved[:i]) for i in range(len(even_reversed))]\n    return output", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(string: str) -> str:\n    reversed_string = list(string[::-1])\n    result = []\n    idx = 0\n    while idx < len(reversed_string):\n        if reversed_string[idx].isupper():\n            next_upper = next((i for i in range(idx + 1, len(reversed_string)) if reversed_string[i].isupper()), None)\n            if next_upper is not None:\n                (reversed_string[idx], reversed_string[next_upper]) = (reversed_string[next_upper], reversed_string[idx])\n        idx += 1\n    final_string = ''.join(reversed_string)\n    filtered_string = final_string.splitlines()\n    final_string = ''.join((line for line in filtered_string if not line.startswith('#')))\n    return final_string", "input": "'Hello, world! # This is a comment'", "output": "'tnemmoc a si sihH # !dlrow ,olleT'", "imports": [], "original_snippet": "def f(string: str) -> str:\n    reversed_string = list(string[::-1])\n    result = []\n    idx = 0\n    while idx < len(reversed_string):\n        if reversed_string[idx].isupper():\n            next_upper = next((i for i in range(idx + 1, len(reversed_string)) if reversed_string[i].isupper()), None)\n            if next_upper is not None:\n                (reversed_string[idx], reversed_string[next_upper]) = (reversed_string[next_upper], reversed_string[idx])\n        idx += 1\n    final_string = ''.join(reversed_string)\n    filtered_string = final_string.splitlines()\n    final_string = ''.join((line for line in filtered_string if not line.startswith('#')))\n    return final_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from itertools import permutations\ndef f(sequence: list, target: str):\n    found = False\n    max_idx = len(sequence) - 1\n    for perm in permutations(sequence):\n        for (idx, item) in enumerate(perm):\n            if item == target:\n                if idx == max_idx:\n                    found = True\n                    break\n                next_item = perm[idx + 1]\n                if next_item == target:\n                    found = True\n                    break\n        if found:\n            return True\n    return False", "input": "['a', 'b', 'c'], 'b'", "output": "True", "imports": ["from itertools import permutations"], "original_snippet": "from itertools import permutations\ndef f(sequence: list, target: str):\n    found = False\n    max_idx = len(sequence) - 1\n    for perm in permutations(sequence):\n        for (idx, item) in enumerate(perm):\n            if item == target:\n                if idx == max_idx:\n                    found = True\n                    break\n                next_item = perm[idx + 1]\n                if next_item == target:\n                    found = True\n                    break\n        if found:\n            return True\n    return False", "composite_functions": [], "_input_type": "tuple", "_output_type": "bool"}
{"snippet": "def f(students: list):\n    age_category_mapping = {'below': 25, 'above': sum([1 for student in students if student['age'] >= 25])}\n    student_count = 0\n    for student in students:\n        name = student['name']\n        age_category = 'below' if student['age'] < age_category_mapping['above'] else 'above'\n        matching_students = [s for s in students if s['name'] == name and age_category_mapping[age_category] == (s['age'] < age_category_mapping['above'])]\n        if len(matching_students) > 1:\n            student_count += 1\n    return {'student_count_with_matching_name_and_age': student_count}", "input": "[\n    {'name': 'John', 'age': 20},\n    {'name': 'Alice', 'age': 24},\n    {'name': 'Bob', 'age': 30},\n    {'name': 'Joe', 'age': 21},\n    {'name': 'John', 'age': 25}\n]", "output": "{'student_count_with_matching_name_and_age': 0}", "imports": [], "original_snippet": "def f(students: list):\n    age_category_mapping = {'below': 25, 'above': sum([1 for student in students if student['age'] >= 25])}\n    student_count = 0\n    for student in students:\n        name = student['name']\n        age_category = 'below' if student['age'] < age_category_mapping['above'] else 'above'\n        matching_students = [s for s in students if s['name'] == name and age_category_mapping[age_category] == (s['age'] < age_category_mapping['above'])]\n        if len(matching_students) > 1:\n            student_count += 1\n    return {'student_count_with_matching_name_and_age': student_count}", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "from typing import List\nimport heapq\ndef f(people: List[dict], weight: int) -> str:\n    sorted_people = sorted(people, key=lambda x: x['height'], reverse=True)\n    group = []\n    total_weight = 0\n    for person in sorted_people:\n        if total_weight + person['age'] <= weight:\n            group.append(person['name'])\n            total_weight += person['age']\n    return f\"Teleport {' & '.join(group)}\" if len(group) > 1 else 'No eligible group'", "input": "[{'name': 'Alice', 'age': 25, 'height': 160}, {'name': 'Bob', 'age': 30, 'height': 175}, {'name': 'Charlie', 'age': 22, 'height': 180}, {'name': 'Dave', 'age': 27, 'height': 165}], 55", "output": "'Teleport Charlie & Bob'", "imports": ["from typing import List", "import heapq"], "original_snippet": "from typing import List\nimport heapq\ndef f(people: List[dict], weight: int) -> str:\n    sorted_people = sorted(people, key=lambda x: x['height'], reverse=True)\n    group = []\n    total_weight = 0\n    for person in sorted_people:\n        if total_weight + person['age'] <= weight:\n            group.append(person['name'])\n            total_weight += person['age']\n    return f\"Teleport {' & '.join(group)}\" if len(group) > 1 else 'No eligible group'", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(lst):\n    if len(lst) < 2:\n        return None\n    max_product = float('-inf')\n    max_num1 = max_num2 = None\n    for i in range(len(lst) - 1):\n        for j in range(i + 1, len(lst)):\n            product = lst[i] * lst[j]\n            if product > max_product:\n                max_product = product\n                max_num1 = lst[i]\n                max_num2 = lst[j]\n    return (max_product, max_num1, max_num2)", "input": "[1, 2, 3, 4, 5]", "output": "(20, 4, 5)", "imports": [], "original_snippet": "def f(lst):\n    if len(lst) < 2:\n        return None\n    max_product = float('-inf')\n    max_num1 = max_num2 = None\n    for i in range(len(lst) - 1):\n        for j in range(i + 1, len(lst)):\n            product = lst[i] * lst[j]\n            if product > max_product:\n                max_product = product\n                max_num1 = lst[i]\n                max_num2 = lst[j]\n    return (max_product, max_num1, max_num2)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(numbers: list):\n    numbers.sort()\n    table1 = [[0] * len(numbers) for _ in range(len(numbers))]\n    table2 = [[0] * len(numbers) for _ in range(len(numbers))]\n    for i in range(len(numbers)):\n        table1[i][i] = 1\n    for i in range(len(numbers) - 1, -1, -1):\n        for j in range(len(numbers) - 1, i, -1):\n            if numbers[j] - numbers[i] == 1 or numbers[j] - numbers[i] == -1:\n                table1[i][j] = max(table1[i][j], 1 + table1[i][j - 1])\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            if numbers[j] - numbers[i] == 1 or numbers[j] - numbers[i] == -1:\n                table2[j][j] = max(table2[j][j], 1 + table2[i + 1][j])\n    get1 = lambda delta: table1[delta][len(numbers) - 1]\n    get2 = lambda delta: table2[0][delta]\n    size = lambda delta: (min(get1(delta), get2(delta)), delta)\n    return min((size(i) for i in range(len(numbers))))[0]", "input": "[1, 2, 3, 4, 5, 8, 9, 10, 11]", "output": "0", "imports": [], "original_snippet": "def f(numbers: list):\n    numbers.sort()\n    table1 = [[0] * len(numbers) for _ in range(len(numbers))]\n    table2 = [[0] * len(numbers) for _ in range(len(numbers))]\n    for i in range(len(numbers)):\n        table1[i][i] = 1\n    for i in range(len(numbers) - 1, -1, -1):\n        for j in range(len(numbers) - 1, i, -1):\n            if numbers[j] - numbers[i] == 1 or numbers[j] - numbers[i] == -1:\n                table1[i][j] = max(table1[i][j], 1 + table1[i][j - 1])\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            if numbers[j] - numbers[i] == 1 or numbers[j] - numbers[i] == -1:\n                table2[j][j] = max(table2[j][j], 1 + table2[i + 1][j])\n    get1 = lambda delta: table1[delta][len(numbers) - 1]\n    get2 = lambda delta: table2[0][delta]\n    size = lambda delta: (min(get1(delta), get2(delta)), delta)\n    return min((size(i) for i in range(len(numbers))))[0]", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(grid: list, start: tuple, end: tuple) -> dict:\n    (rows, cols) = (len(grid), len(grid[0]))\n    dp = [[float('inf')] * cols for _ in range(rows)]\n    dp[start[0]][start[1]] = 0\n    for i in range(rows):\n        for j in range(cols):\n            if grid[i][j] == 1:\n                continue\n            if i > 0:\n                dp[i][j] = min(dp[i][j], dp[i - 1][j] + grid[i - 1][j])\n            if j > 0:\n                dp[i][j] = min(dp[i][j], dp[i][j - 1] + grid[i][j - 1])\n    return min(dp[end[0]][end[1]], float('inf'))", "input": "[[1, 0, 0, 1], [0, 0, 0, 0], [0, 0, 0, 0], [1, 0, 0, 1]], (0, 0), (3, 3)", "output": "inf", "imports": [], "original_snippet": "def f(grid: list, start: tuple, end: tuple) -> dict:\n    (rows, cols) = (len(grid), len(grid[0]))\n    dp = [[float('inf')] * cols for _ in range(rows)]\n    dp[start[0]][start[1]] = 0\n    for i in range(rows):\n        for j in range(cols):\n            if grid[i][j] == 1:\n                continue\n            if i > 0:\n                dp[i][j] = min(dp[i][j], dp[i - 1][j] + grid[i - 1][j])\n            if j > 0:\n                dp[i][j] = min(dp[i][j], dp[i][j - 1] + grid[i][j - 1])\n    return min(dp[end[0]][end[1]], float('inf'))", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "from collections import defaultdict\ndef f(numbers):\n    result = []\n    seen = set()\n    for num in numbers:\n        if num % 2 == 0:\n            if num % 5 == 0:\n                num += sum(seen)\n            else:\n                num *= num % 3\n        result.append(num)\n        seen.add(num)\n    return result", "input": "[6, 10, 23, 4, 11, 15, 1, 8, 13, 3]", "output": "[0, 10, 23, 4, 11, 15, 1, 16, 13, 3]", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(numbers):\n    result = []\n    seen = set()\n    for num in numbers:\n        if num % 2 == 0:\n            if num % 5 == 0:\n                num += sum(seen)\n            else:\n                num *= num % 3\n        result.append(num)\n        seen.add(num)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(graph):\n    nodes = set(graph.keys())\n    visited = set()\n    result = []\n    for node in nodes:\n        if node not in visited:\n            stack = [node]\n            while stack:\n                current = stack.pop()\n                if current not in visited:\n                    visited.add(current)\n                    result.append(current)\n                    stack.extend(graph[current] - visited)\n    return result", "input": "{1: {2, 3}, 2: {1, 4}, 3: {1, 5}, 4: {2}, 5: {3}}", "output": "[1, 3, 5, 2, 4]", "imports": [], "original_snippet": "def f(graph):\n    nodes = set(graph.keys())\n    visited = set()\n    result = []\n    for node in nodes:\n        if node not in visited:\n            stack = [node]\n            while stack:\n                current = stack.pop()\n                if current not in visited:\n                    visited.add(current)\n                    result.append(current)\n                    stack.extend(graph[current] - visited)\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "list"}
{"snippet": "def f(n: int):\n    fib_seq = [1, 1]\n    for i in range(2, n):\n        fib_seq.append(fib_seq[i - 1] + fib_seq[i - 2])\n    if n % 2 == 0:\n        fib_seq = [-x for x in fib_seq]\n    else:\n        fib_seq = [fib_seq[-i] for i in range(1, n + 1)]\n    for i in range(n):\n        if i % 2 == 0:\n            fib_seq[i] *= i\n        else:\n            fib_seq[i] *= -i\n    return fib_seq", "input": "14", "output": "[0, 1, -4, 9, -20, 40, -78, 147, -272, 495, -890, 1584, -2796, 4901]", "imports": [], "original_snippet": "def f(n: int):\n    fib_seq = [1, 1]\n    for i in range(2, n):\n        fib_seq.append(fib_seq[i - 1] + fib_seq[i - 2])\n    if n % 2 == 0:\n        fib_seq = [-x for x in fib_seq]\n    else:\n        fib_seq = [fib_seq[-i] for i in range(1, n + 1)]\n    for i in range(n):\n        if i % 2 == 0:\n            fib_seq[i] *= i\n        else:\n            fib_seq[i] *= -i\n    return fib_seq", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(numbers):\n    unique_numbers = list(set(numbers))\n    even_numbers = [num for num in unique_numbers if num % 2 == 0]\n    even_numbers.sort()\n    even_sum = sum(even_numbers)\n    odd_numbers = [num for num in unique_numbers if num % 2 != 0]\n    odd_numbers.sort()\n    odd_sum = sum(odd_numbers)\n    total_sum = even_sum + odd_sum\n    num_transformations = len(numbers)\n    result = {'total_sum': total_sum, 'num_transformations': num_transformations}\n    return result", "input": "[1, 2, 3, 4, 5, 2, 4, 6, 7, 8]", "output": "{'total_sum': 36, 'num_transformations': 10}", "imports": [], "original_snippet": "def f(numbers):\n    unique_numbers = list(set(numbers))\n    even_numbers = [num for num in unique_numbers if num % 2 == 0]\n    even_numbers.sort()\n    even_sum = sum(even_numbers)\n    odd_numbers = [num for num in unique_numbers if num % 2 != 0]\n    odd_numbers.sort()\n    odd_sum = sum(odd_numbers)\n    total_sum = even_sum + odd_sum\n    num_transformations = len(numbers)\n    result = {'total_sum': total_sum, 'num_transformations': num_transformations}\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(message: str, key: int):\n    state = 0\n    for i in range(len(message)):\n        state ^= ord(message[i]) << i % 4 * 8\n    encoded_message = [chr(state >> shift & 255) for shift in range(0, 32, 8)]\n    for i in range(len(encoded_message)):\n        state ^= ord(encoded_message[i]) << i % 4 * 8 ^ key\n    decoded_message = [chr(state >> shift & 255) for shift in range(0, 32, 8)]\n    return ''.join(decoded_message)", "input": "'Hello, world!', 0x1234", "output": "'\\x00\\x00\\x00\\x00'", "imports": [], "original_snippet": "def f(message: str, key: int):\n    state = 0\n    for i in range(len(message)):\n        state ^= ord(message[i]) << i % 4 * 8\n    encoded_message = [chr(state >> shift & 255) for shift in range(0, 32, 8)]\n    for i in range(len(encoded_message)):\n        state ^= ord(encoded_message[i]) << i % 4 * 8 ^ key\n    decoded_message = [chr(state >> shift & 255) for shift in range(0, 32, 8)]\n    return ''.join(decoded_message)", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "from typing import List\ndef f(lst: List[List[int]], target: int) -> List[int]:\n    first = 0\n    last = len(lst) - 1\n    while first <= last:\n        mid = (first + last) // 2\n        if target > lst[mid][-1]:\n            first = mid + 1\n        elif target < lst[mid][0]:\n            last = mid - 1\n        else:\n            break\n    if first > last:\n        return False\n    row = lst[mid]\n    first = 0\n    last = len(row) - 1\n    while first <= last:\n        mid = (first + last) // 2\n        if target > row[mid]:\n            first = mid + 1\n        elif target < row[mid]:\n            last = mid - 1\n        else:\n            return True\n    return False", "input": "[[10, 15, 20], [40, 51, 52], [67, 68, 75]], 40", "output": "True", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(lst: List[List[int]], target: int) -> List[int]:\n    first = 0\n    last = len(lst) - 1\n    while first <= last:\n        mid = (first + last) // 2\n        if target > lst[mid][-1]:\n            first = mid + 1\n        elif target < lst[mid][0]:\n            last = mid - 1\n        else:\n            break\n    if first > last:\n        return False\n    row = lst[mid]\n    first = 0\n    last = len(row) - 1\n    while first <= last:\n        mid = (first + last) // 2\n        if target > row[mid]:\n            first = mid + 1\n        elif target < row[mid]:\n            last = mid - 1\n        else:\n            return True\n    return False", "composite_functions": [], "_input_type": "tuple", "_output_type": "bool"}
{"snippet": "def f(string: str):\n    vowels = ['a', 'e', 'i', 'o', 'u']\n    vowel_count = 0\n    for char in string:\n        if char.lower() in vowels:\n            vowel_count += 1\n    doubled_vowel_count = vowel_count * 2\n    reversed_string = ''.join(reversed(string))\n    output = reversed_string + 'A' * doubled_vowel_count\n    return output", "input": "\"apple\"", "output": "'elppaAAAA'", "imports": [], "original_snippet": "def f(string: str):\n    vowels = ['a', 'e', 'i', 'o', 'u']\n    vowel_count = 0\n    for char in string:\n        if char.lower() in vowels:\n            vowel_count += 1\n    doubled_vowel_count = vowel_count * 2\n    reversed_string = ''.join(reversed(string))\n    output = reversed_string + 'A' * doubled_vowel_count\n    return output", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(lst):\n    if len(lst) < 2:\n        return None\n    while True:\n        lst.sort()\n        second_small = lst[1]\n        lst = [x for x in lst if x != second_small]\n        if len(lst) == 2:\n            return second_small", "input": "[153, 220, 112, 293, 372, 980, 365, 102]", "output": "372", "imports": [], "original_snippet": "def f(lst):\n    if len(lst) < 2:\n        return None\n    while True:\n        lst.sort()\n        second_small = lst[1]\n        lst = [x for x in lst if x != second_small]\n        if len(lst) == 2:\n            return second_small", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(s):\n    mod_s = ''\n    for i in range(len(s)):\n        mod_s += s[i] + s[::-1][i]\n    mod_s = ''.join(set(mod_s))\n    return mod_s", "input": "'abc'", "output": "'cab'", "imports": [], "original_snippet": "def f(s):\n    mod_s = ''\n    for i in range(len(s)):\n        mod_s += s[i] + s[::-1][i]\n    mod_s = ''.join(set(mod_s))\n    return mod_s", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(numbers):\n    evens = sorted([num for num in numbers if num % 2 == 0])\n    unique_evens = [num for (i, num) in enumerate(evens) if i == 0 or num != evens[i - 1]]\n    unique_evens.sort()\n    result = [num for num in unique_evens if num < sum(numbers) // len(numbers)]\n    return result", "input": "[9, 8, 7, 6, 5, 4, 3, 2, 1]", "output": "[2, 4]", "imports": [], "original_snippet": "def f(numbers):\n    evens = sorted([num for num in numbers if num % 2 == 0])\n    unique_evens = [num for (i, num) in enumerate(evens) if i == 0 or num != evens[i - 1]]\n    unique_evens.sort()\n    result = [num for num in unique_evens if num < sum(numbers) // len(numbers)]\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list: list, target_age: int) -> str:\n    closest_age = None\n    closest_name = None\n    for dictionary in input_list:\n        age = dictionary.get('age')\n        if age is not None:\n            if closest_age is None or abs(age - target_age) < abs(closest_age - target_age):\n                closest_age = age\n                closest_name = dictionary.get('name')\n    if closest_name:\n        return closest_name\n    else:\n        return 'No match found'", "input": "[\n    {\"name\": \"Alice\", \"age\": 20},\n    {\"name\": \"Bob\", \"age\": 25},\n    {\"name\": \"Carol\", \"age\": 30},\n    {\"name\": \"Dave\", \"age\": 35},\n    {\"name\": \"Eve\", \"age\": 25}\n],\n22", "output": "'Alice'", "imports": [], "original_snippet": "def f(input_list: list, target_age: int) -> str:\n    closest_age = None\n    closest_name = None\n    for dictionary in input_list:\n        age = dictionary.get('age')\n        if age is not None:\n            if closest_age is None or abs(age - target_age) < abs(closest_age - target_age):\n                closest_age = age\n                closest_name = dictionary.get('name')\n    if closest_name:\n        return closest_name\n    else:\n        return 'No match found'", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(binary_strings):\n    ints = [int(b, 2) for b in binary_strings]\n    result = 0\n    for (i, num) in enumerate(ints):\n        shifted_num = num << i\n        modified_num = shifted_num | num\n        result |= modified_num\n    result_bin = bin(result)[2:].zfill(len(binary_strings[0]))\n    return result_bin", "input": "['10101', '11011']", "output": "'111111'", "imports": [], "original_snippet": "def f(binary_strings):\n    ints = [int(b, 2) for b in binary_strings]\n    result = 0\n    for (i, num) in enumerate(ints):\n        shifted_num = num << i\n        modified_num = shifted_num | num\n        result |= modified_num\n    result_bin = bin(result)[2:].zfill(len(binary_strings[0]))\n    return result_bin", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(numbers: list):\n    odd_numbers = [n for n in numbers if n % 2 != 0]\n    even_numbers = [n for n in numbers if n % 2 == 0]\n    return sum(even_numbers) * len(odd_numbers)", "input": "[1, 2, 3, 4, 5]", "output": "18", "imports": [], "original_snippet": "def f(numbers: list):\n    odd_numbers = [n for n in numbers if n % 2 != 0]\n    even_numbers = [n for n in numbers if n % 2 == 0]\n    return sum(even_numbers) * len(odd_numbers)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from collections import defaultdict\ndef f(s, k):\n    counts = defaultdict(int)\n    for char in s:\n        counts[char] += 1\n    least_frequent_chars = sorted(counts.items(), key=lambda x: (x[1], x[0]))\n    ordered_chars = [char for (char, _) in least_frequent_chars]\n    result = ''\n    for char in s:\n        result += ordered_chars[ordered_chars.index(char) - k]\n    return result", "input": "'abcaacbab', 2", "output": "'cabccbaca'", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(s, k):\n    counts = defaultdict(int)\n    for char in s:\n        counts[char] += 1\n    least_frequent_chars = sorted(counts.items(), key=lambda x: (x[1], x[0]))\n    ordered_chars = [char for (char, _) in least_frequent_chars]\n    result = ''\n    for char in s:\n        result += ordered_chars[ordered_chars.index(char) - k]\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(lst: list) -> int:\n    even_indexed_elements = lst[::2]\n    odd_indexed_elements = lst[1::2]\n    even_sum = sum(sorted(even_indexed_elements))\n    odd_sum = sum(sorted(odd_indexed_elements))\n    return abs(even_sum - odd_sum)", "input": "[10, 20, 30, 40, 50, 60]", "output": "30", "imports": [], "original_snippet": "def f(lst: list) -> int:\n    even_indexed_elements = lst[::2]\n    odd_indexed_elements = lst[1::2]\n    even_sum = sum(sorted(even_indexed_elements))\n    odd_sum = sum(sorted(odd_indexed_elements))\n    return abs(even_sum - odd_sum)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers):\n    count_dict = {}\n    position_dict = {}\n    for (pos, num) in enumerate(numbers):\n        if num in count_dict:\n            count_dict[num] += 1\n            position_dict[num].append(pos)\n        else:\n            count_dict[num] = 1\n            position_dict[num] = [pos]\n    even_count_elements = [k for (k, v) in count_dict.items() if v % 2 == 0]\n    sorted_even_count_elements = sorted(even_count_elements)\n    result = 0\n    for element in sorted_even_count_elements:\n        result += sum(position_dict[element]) * element\n    return result", "input": "[3, 1, 4, 1, 5, 9, 3, 5]", "output": "77", "imports": [], "original_snippet": "def f(numbers):\n    count_dict = {}\n    position_dict = {}\n    for (pos, num) in enumerate(numbers):\n        if num in count_dict:\n            count_dict[num] += 1\n            position_dict[num].append(pos)\n        else:\n            count_dict[num] = 1\n            position_dict[num] = [pos]\n    even_count_elements = [k for (k, v) in count_dict.items() if v % 2 == 0]\n    sorted_even_count_elements = sorted(even_count_elements)\n    result = 0\n    for element in sorted_even_count_elements:\n        result += sum(position_dict[element]) * element\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "import math\ndef f(input_list: list):\n    freq_dict = {}\n    parity_dict = {}\n    product = 1\n    for num in input_list:\n        freq_dict[num] = freq_dict.setdefault(num, 0) + 1\n        parity_dict[num] = freq_dict[num] % 2\n        if parity_dict[num] == 1:\n            product *= num\n        else:\n            product //= num\n    return product", "input": "[4, 2, 5, 2, 5, 2, 3, 4, 3, 4]", "output": "8", "imports": ["import math"], "original_snippet": "import math\ndef f(input_list: list):\n    freq_dict = {}\n    parity_dict = {}\n    product = 1\n    for num in input_list:\n        freq_dict[num] = freq_dict.setdefault(num, 0) + 1\n        parity_dict[num] = freq_dict[num] % 2\n        if parity_dict[num] == 1:\n            product *= num\n        else:\n            product //= num\n    return product", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(lst):\n    cubes = []\n    for num in lst:\n        if num >= 0:\n            cubes.append(num ** 3)\n    return sum(cubes)", "input": "[1, 2, -3, 4, -5, 6]", "output": "289", "imports": [], "original_snippet": "def f(lst):\n    cubes = []\n    for num in lst:\n        if num >= 0:\n            cubes.append(num ** 3)\n    return sum(cubes)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(data: dict):\n    sorted_keys = sorted(data.keys())\n    total = 0\n    for key in sorted_keys:\n        values = data[key]\n        if len(values) > 0:\n            min_val = min(values)\n            max_val = max(values)\n            product = min_val * max_val\n            if product < 0:\n                product *= -1\n            total += product\n    return total", "input": "{1: [2, 4, 6], 2: [3, 9, -1], 3: [5]}", "output": "46", "imports": [], "original_snippet": "def f(data: dict):\n    sorted_keys = sorted(data.keys())\n    total = 0\n    for key in sorted_keys:\n        values = data[key]\n        if len(values) > 0:\n            min_val = min(values)\n            max_val = max(values)\n            product = min_val * max_val\n            if product < 0:\n                product *= -1\n            total += product\n    return total", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(s: str, shift: int) -> str:\n    char_list = list(s)\n    length = len(char_list)\n    shifted_chars = []\n    for char in char_list:\n        new_pos = (char_list.index(char) + shift) % length\n        shifted_chars.append(char_list[new_pos])\n    return ''.join(shifted_chars)", "input": "\"Hello, World!\", 5", "output": "', WWrld!reWlo'", "imports": [], "original_snippet": "def f(s: str, shift: int) -> str:\n    char_list = list(s)\n    length = len(char_list)\n    shifted_chars = []\n    for char in char_list:\n        new_pos = (char_list.index(char) + shift) % length\n        shifted_chars.append(char_list[new_pos])\n    return ''.join(shifted_chars)", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(words: list):\n    pattern1_count = 0\n    pattern2_count = 0\n    for word in words:\n        for i in range(len(word) - 1):\n            if word[i] == 'a' and word[i + 1] == 'b':\n                pattern1_count += 1\n            elif word[i] == 'c' and word[i + 1] == 'd':\n                pattern2_count += 1\n    return pattern1_count % 2 == 0 and pattern2_count % 3 == 0", "input": "['abc', 'bab', 'cde', 'cbd', 'abc', 'dbd']", "output": "False", "imports": [], "original_snippet": "def f(words: list):\n    pattern1_count = 0\n    pattern2_count = 0\n    for word in words:\n        for i in range(len(word) - 1):\n            if word[i] == 'a' and word[i + 1] == 'b':\n                pattern1_count += 1\n            elif word[i] == 'c' and word[i + 1] == 'd':\n                pattern2_count += 1\n    return pattern1_count % 2 == 0 and pattern2_count % 3 == 0", "composite_functions": [], "_input_type": "list", "_output_type": "bool"}
{"snippet": "def f(seq):\n    magic_hash = lambda x: x * 17 % 500 + 500\n    result = []\n    hash_set = set()\n    for num in seq:\n        num_hash = magic_hash(num)\n        if num_hash > 500 and num_hash > max(hash_set) if hash_set else True:\n            hash_set.add(num_hash)\n            result.append(max(hash_set))\n        elif num_hash in hash_set:\n            hash_set.remove(num_hash)\n    return result", "input": "[2, 1, 3, 4, 5, 6, 7]", "output": "[534, 551, 568, 585, 602, 619]", "imports": [], "original_snippet": "def f(seq):\n    magic_hash = lambda x: x * 17 % 500 + 500\n    result = []\n    hash_set = set()\n    for num in seq:\n        num_hash = magic_hash(num)\n        if num_hash > 500 and num_hash > max(hash_set) if hash_set else True:\n            hash_set.add(num_hash)\n            result.append(max(hash_set))\n        elif num_hash in hash_set:\n            hash_set.remove(num_hash)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(strings: list):\n    result = []\n    for s in strings:\n        if s.startswith('a') and s.endswith('z') or (s.startswith('z') and s.endswith('a')):\n            transformed = s[::-1].replace('a', 'end').replace('z', 'start')\n            result.append(transformed)\n    return ' '.join(result)", "input": "['abz', 'azz', 'zya', 'azx', 'aayz']", "output": "'startbend startstartend endystart startyendend'", "imports": [], "original_snippet": "def f(strings: list):\n    result = []\n    for s in strings:\n        if s.startswith('a') and s.endswith('z') or (s.startswith('z') and s.endswith('a')):\n            transformed = s[::-1].replace('a', 'end').replace('z', 'start')\n            result.append(transformed)\n    return ' '.join(result)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(text: str) -> list:\n    seen = {}\n    result = []\n    for char in text:\n        if char not in seen:\n            seen[char] = 1\n        else:\n            seen[char] += 1\n        if seen[char] % 2 == 0:\n            result.append(seen[char])\n    return result", "input": "\"programming\"", "output": "[2, 2, 2]", "imports": [], "original_snippet": "def f(text: str) -> list:\n    seen = {}\n    result = []\n    for char in text:\n        if char not in seen:\n            seen[char] = 1\n        else:\n            seen[char] += 1\n        if seen[char] % 2 == 0:\n            result.append(seen[char])\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(strings):\n    extracted = [sorted([c for c in s if c.isalnum()]) for s in strings]\n    return ''.join([''.join(e) for e in extracted])", "input": "['Alice123', 'Bob456', 'Charlie789', 'Dave0!.']", "output": "'123Aceil456Bbo789Caehilr0Daev'", "imports": [], "original_snippet": "def f(strings):\n    extracted = [sorted([c for c in s if c.isalnum()]) for s in strings]\n    return ''.join([''.join(e) for e in extracted])", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(nums):\n    sorted_nums = sorted(nums, key=lambda num: bin(num).count('1'))\n    return sum(sorted_nums[-3:])", "input": "[13, 7, 11, 3, 5, 9]", "output": "31", "imports": [], "original_snippet": "def f(nums):\n    sorted_nums = sorted(nums, key=lambda num: bin(num).count('1'))\n    return sum(sorted_nums[-3:])", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from collections import defaultdict\ndef f(string: str):\n    length = len(string)\n    counter = defaultdict(int)\n    for i in range(length):\n        for j in range(i + 1, length):\n            substring = string[i:j + 1]\n            if len(set(substring)) == len(substring):\n                counter[substring] += 1\n    max_count = max(counter.values()) if counter else 0\n    return max_count", "input": "'abca'", "output": "1", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(string: str):\n    length = len(string)\n    counter = defaultdict(int)\n    for i in range(length):\n        for j in range(i + 1, length):\n            substring = string[i:j + 1]\n            if len(set(substring)) == len(substring):\n                counter[substring] += 1\n    max_count = max(counter.values()) if counter else 0\n    return max_count", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "import re\ndef f(sentence):\n    words = re.split('\\\\W+', sentence) if sentence else []\n    transformed_words = [''.join(reversed(word)) for word in words]\n    word_count = {}\n    for word in transformed_words:\n        if len(word) % 2 == 0:\n            key = f'e_{word}'\n        else:\n            key = f'o_{word}'\n        word_count[key] = word_count.get(key, 0) + len(word)\n    sorted_words = sorted(word_count.items())\n    summed_values = sum((value for (key, value) in sorted_words if key.startswith('e_')))\n    return summed_values", "input": "'The quick brown fox jumps over the lazy dog'", "output": "8", "imports": ["import re"], "original_snippet": "import re\ndef f(sentence):\n    words = re.split('\\\\W+', sentence) if sentence else []\n    transformed_words = [''.join(reversed(word)) for word in words]\n    word_count = {}\n    for word in transformed_words:\n        if len(word) % 2 == 0:\n            key = f'e_{word}'\n        else:\n            key = f'o_{word}'\n        word_count[key] = word_count.get(key, 0) + len(word)\n    sorted_words = sorted(word_count.items())\n    summed_values = sum((value for (key, value) in sorted_words if key.startswith('e_')))\n    return summed_values", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(numbers: list[int]) -> int:\n    if not numbers:\n        return 'No valid numbers found'\n    even_numbers = [num for num in numbers if num % 2 == 0]\n    odd_numbers = [num for num in numbers if num % 2 != 0]\n    if not even_numbers:\n        return 'No even numbers found'\n    if not odd_numbers:\n        return 'No odd numbers found'\n    largest_even = max(even_numbers)\n    smallest_odd = min(odd_numbers)\n    return largest_even * smallest_odd", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "10", "imports": [], "original_snippet": "def f(numbers: list[int]) -> int:\n    if not numbers:\n        return 'No valid numbers found'\n    even_numbers = [num for num in numbers if num % 2 == 0]\n    odd_numbers = [num for num in numbers if num % 2 != 0]\n    if not even_numbers:\n        return 'No even numbers found'\n    if not odd_numbers:\n        return 'No odd numbers found'\n    largest_even = max(even_numbers)\n    smallest_odd = min(odd_numbers)\n    return largest_even * smallest_odd", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "import bisect\ndef f(nums: list, target: int) -> list:\n    original_nums = nums.copy()\n    nums = list(set(nums))\n    nums.sort()\n    for i in range(len(nums)):\n        remainder = target - nums[i]\n        if remainder in nums[i + 1:]:\n            j = original_nums.index(nums[i])\n            k = original_nums.index(remainder)\n            if j != k:\n                return [j, k]\n    return []", "input": "[1, 3, 5, 6, 7, 8, 2], 10", "output": "[6, 5]", "imports": ["import bisect"], "original_snippet": "import bisect\ndef f(nums: list, target: int) -> list:\n    original_nums = nums.copy()\n    nums = list(set(nums))\n    nums.sort()\n    for i in range(len(nums)):\n        remainder = target - nums[i]\n        if remainder in nums[i + 1:]:\n            j = original_nums.index(nums[i])\n            k = original_nums.index(remainder)\n            if j != k:\n                return [j, k]\n    return []", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(string: str) -> str:\n    word = ''.join((char for char in string.upper() if char.isalpha()))\n    length = len(word)\n    response = []\n    for i in range(1, length + 1):\n        letters = list(word[:i].lower())\n        letters = letters[::-1]\n        letters = [letter.upper() for letter in letters]\n        letters = [letter * 2 for letter in letters]\n        letters = [letter + '!' + '?' for letter in letters]\n        response.append(' and '.join(letters))\n    return '\\n'.join(response)", "input": "\"Hello, World\"", "output": "'HH!?\\nEE!? and HH!?\\nLL!? and EE!? and HH!?\\nLL!? and LL!? and EE!? and HH!?\\nOO!? and LL!? and LL!? and EE!? and HH!?\\nWW!? and OO!? and LL!? and LL!? and EE!? and HH!?\\nOO!? and WW!? and OO!? and L...nd LL!? and LL!? and EE!? and HH!?\\nLL!? and RR!? and OO!? and WW!? and OO!? and LL!? and LL!? and EE!? and HH!?\\nDD!? and LL!? and RR!? and OO!? and WW!? and OO!? and LL!? and LL!? and EE!? and HH!?'", "imports": [], "original_snippet": "def f(string: str) -> str:\n    word = ''.join((char for char in string.upper() if char.isalpha()))\n    length = len(word)\n    response = []\n    for i in range(1, length + 1):\n        letters = list(word[:i].lower())\n        letters = letters[::-1]\n        letters = [letter.upper() for letter in letters]\n        letters = [letter * 2 for letter in letters]\n        letters = [letter + '!' + '?' for letter in letters]\n        response.append(' and '.join(letters))\n    return '\\n'.join(response)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(l):\n    sorted_l = sorted(l)\n    reversed_l = sorted_l[::-1]\n    sliced_l = reversed_l[1:-1]\n    sum_l = sum(sliced_l)\n    frequency_dict = {}\n    for num in sliced_l:\n        frequency_dict[num] = frequency_dict.get(num, 0) + 1\n    product = 1\n    for value in frequency_dict.values():\n        product *= value\n    result = sum_l * product\n    return result", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "44", "imports": [], "original_snippet": "def f(l):\n    sorted_l = sorted(l)\n    reversed_l = sorted_l[::-1]\n    sliced_l = reversed_l[1:-1]\n    sum_l = sum(sliced_l)\n    frequency_dict = {}\n    for num in sliced_l:\n        frequency_dict[num] = frequency_dict.get(num, 0) + 1\n    product = 1\n    for value in frequency_dict.values():\n        product *= value\n    result = sum_l * product\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(s: str) -> int:\n    ascii_product = 1\n    for c in s:\n        ascii_product *= ord(c)\n    char_counts = {}\n    for c in s:\n        char_counts[ord(c)] = char_counts.get(ord(c), 0) + 1\n    most_common_char_value = max(char_counts.keys(), key=char_counts.get)\n    difference = ascii_product - most_common_char_value\n    return difference", "input": "'abc'", "output": "940997", "imports": [], "original_snippet": "def f(s: str) -> int:\n    ascii_product = 1\n    for c in s:\n        ascii_product *= ord(c)\n    char_counts = {}\n    for c in s:\n        char_counts[ord(c)] = char_counts.get(ord(c), 0) + 1\n    most_common_char_value = max(char_counts.keys(), key=char_counts.get)\n    difference = ascii_product - most_common_char_value\n    return difference", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(lst):\n    if len(lst) < 2:\n        return None\n    lst.sort()\n    count_25 = 0\n    for ele in lst:\n        if ele < 25:\n            count_25 += 1\n    for i in range(len(lst) - 2, -1, -1):\n        if lst[i] not in range(25, 26):\n            return lst[i]", "input": "[25, 15, 30, 24, 10, 26, 23, 22]", "output": "26", "imports": [], "original_snippet": "def f(lst):\n    if len(lst) < 2:\n        return None\n    lst.sort()\n    count_25 = 0\n    for ele in lst:\n        if ele < 25:\n            count_25 += 1\n    for i in range(len(lst) - 2, -1, -1):\n        if lst[i] not in range(25, 26):\n            return lst[i]", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_list):\n    if len(input_list) == 0:\n        return 0\n    result = input_list[0]\n    for i in range(1, len(input_list)):\n        current_element = input_list[i]\n        if i % 2 == 0:\n            result = result + current_element\n        else:\n            result = result * current_element\n    return result", "input": "[1, 2, 3, 4, 5]", "output": "25", "imports": [], "original_snippet": "def f(input_list):\n    if len(input_list) == 0:\n        return 0\n    result = input_list[0]\n    for i in range(1, len(input_list)):\n        current_element = input_list[i]\n        if i % 2 == 0:\n            result = result + current_element\n        else:\n            result = result * current_element\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers):\n    squares = [x ** 2 for x in numbers]\n    sum_squares = sum(squares)\n    avg_squares = sum_squares / len(numbers)\n    return round(avg_squares, 3)", "input": "[8, 23, 49, 64, 91]", "output": "3074.2", "imports": [], "original_snippet": "def f(numbers):\n    squares = [x ** 2 for x in numbers]\n    sum_squares = sum(squares)\n    avg_squares = sum_squares / len(numbers)\n    return round(avg_squares, 3)", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(word):\n    alphabet_weight = {c: ord(c) - 96 for c in 'abcdefghijklmnopqrstuvwxyz'}\n    sorted_word = sorted(word, key=lambda x: alphabet_weight[x])\n    running_sum = [sum((ord(c) for c in sorted_word[:i + 1])) for (i, _) in enumerate(sorted_word)]\n    result = []\n    for i in range(1, len(running_sum) + 1):\n        if i < len(running_sum) and running_sum[len(running_sum) - i] > i * 2:\n            result.append((chr(96 + i), chr(96 + running_sum[len(running_sum) - i - 1])))\n    return sorted(result, key=lambda x: (x[0], x[1]))", "input": "'abcde'", "output": "[('a', '\u01ea'), ('b', '\u0186'), ('c', '\u0123'), ('d', '\u00c1')]", "imports": [], "original_snippet": "def f(word):\n    alphabet_weight = {c: ord(c) - 96 for c in 'abcdefghijklmnopqrstuvwxyz'}\n    sorted_word = sorted(word, key=lambda x: alphabet_weight[x])\n    running_sum = [sum((ord(c) for c in sorted_word[:i + 1])) for (i, _) in enumerate(sorted_word)]\n    result = []\n    for i in range(1, len(running_sum) + 1):\n        if i < len(running_sum) and running_sum[len(running_sum) - i] > i * 2:\n            result.append((chr(96 + i), chr(96 + running_sum[len(running_sum) - i - 1])))\n    return sorted(result, key=lambda x: (x[0], x[1]))", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(arr: list, n: int):\n    max_num = max(arr)\n    if n > max_num:\n        less_than_n = [-1]\n        while less_than_n[-1] == -1:\n            less_than_n = [num for num in arr if num < n]\n            if len(less_than_n) == 0:\n                return -1\n            n = max(less_than_n)\n        return n\n    elif n <= max_num and n in arr:\n        return arr.index(n)\n    else:\n        return -1", "input": "[1, 2, 3, 4, 5], 6", "output": "5", "imports": [], "original_snippet": "def f(arr: list, n: int):\n    max_num = max(arr)\n    if n > max_num:\n        less_than_n = [-1]\n        while less_than_n[-1] == -1:\n            less_than_n = [num for num in arr if num < n]\n            if len(less_than_n) == 0:\n                return -1\n            n = max(less_than_n)\n        return n\n    elif n <= max_num and n in arr:\n        return arr.index(n)\n    else:\n        return -1", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(l):\n    o = {}\n    m = min(l)\n    M = max(l)\n    b = '_'.join((str(x) for x in l if x % 2 == 0))\n    i = ''.join((str(x) for x in l if x % 2 != 0))\n    if b == '':\n        b = i[::-1]\n    if i == '':\n        i = b.count(str(len(i)))\n    for x in range(m, M + 1):\n        o[x] = x * b.count(str(x)) + x * i.count(str(x))\n    return o", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "{1: 2, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, 10: 10}", "imports": [], "original_snippet": "def f(l):\n    o = {}\n    m = min(l)\n    M = max(l)\n    b = '_'.join((str(x) for x in l if x % 2 == 0))\n    i = ''.join((str(x) for x in l if x % 2 != 0))\n    if b == '':\n        b = i[::-1]\n    if i == '':\n        i = b.count(str(len(i)))\n    for x in range(m, M + 1):\n        o[x] = x * b.count(str(x)) + x * i.count(str(x))\n    return o", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(n):\n    sequence = [0] * n\n    for i in range(n):\n        if i == 0:\n            sequence[i] = 1\n        else:\n            sequence[i] = sequence[i - 1] + i\n    for i in range(n):\n        if i % 2 == 0:\n            sequence[i] *= -1\n        else:\n            sequence[i] *= 2\n    return sequence", "input": "10", "output": "[-1, 4, -4, 14, -11, 32, -22, 58, -37, 92]", "imports": [], "original_snippet": "def f(n):\n    sequence = [0] * n\n    for i in range(n):\n        if i == 0:\n            sequence[i] = 1\n        else:\n            sequence[i] = sequence[i - 1] + i\n    for i in range(n):\n        if i % 2 == 0:\n            sequence[i] *= -1\n        else:\n            sequence[i] *= 2\n    return sequence", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(compressed_string):\n    decompressed_string = ''\n    current_count = ''\n    for char in compressed_string:\n        if char.isdigit():\n            current_count += char\n        else:\n            decompressed_string += char * int(current_count) if current_count else char * 1\n            current_count = ''\n    return decompressed_string", "input": "'A4B3C2D1'", "output": "'ABBBBCCCDD'", "imports": [], "original_snippet": "def f(compressed_string):\n    decompressed_string = ''\n    current_count = ''\n    for char in compressed_string:\n        if char.isdigit():\n            current_count += char\n        else:\n            decompressed_string += char * int(current_count) if current_count else char * 1\n            current_count = ''\n    return decompressed_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_string: str) -> str:\n    mixed_string = ''.join([c.upper() if (i + 1) % 4 == 0 else c.lower() for (i, c) in enumerate(input_string)])\n    ascii_values = [ord(c) for c in mixed_string]\n    reversed_segment = ascii_values[len(ascii_values) // 2:]\n    reversed_segment.reverse()\n    for i in range(len(ascii_values) // 2):\n        ascii_values[len(ascii_values) // 2 + i] = reversed_segment[i]\n    modified_string = ''.join([chr(v) for v in ascii_values])\n    return modified_string", "input": "\"python\"", "output": "'pytnoH'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    mixed_string = ''.join([c.upper() if (i + 1) % 4 == 0 else c.lower() for (i, c) in enumerate(input_string)])\n    ascii_values = [ord(c) for c in mixed_string]\n    reversed_segment = ascii_values[len(ascii_values) // 2:]\n    reversed_segment.reverse()\n    for i in range(len(ascii_values) // 2):\n        ascii_values[len(ascii_values) // 2 + i] = reversed_segment[i]\n    modified_string = ''.join([chr(v) for v in ascii_values])\n    return modified_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(word_list: list, target_length: int):\n    word_pairs = set()\n    result = []\n    for i in range(len(word_list)):\n        for j in range(i + 1, len(word_list)):\n            product = len(word_list[i]) * len(word_list[j])\n            if product == target_length:\n                pair = tuple(sorted([word_list[i], word_list[j]]))\n                if pair not in word_pairs:\n                    result.append(pair)\n                    word_pairs.add(pair)\n    return result", "input": "['cat', 'dog', 'elephant', 'lion'], 24", "output": "[('cat', 'elephant'), ('dog', 'elephant')]", "imports": [], "original_snippet": "def f(word_list: list, target_length: int):\n    word_pairs = set()\n    result = []\n    for i in range(len(word_list)):\n        for j in range(i + 1, len(word_list)):\n            product = len(word_list[i]) * len(word_list[j])\n            if product == target_length:\n                pair = tuple(sorted([word_list[i], word_list[j]]))\n                if pair not in word_pairs:\n                    result.append(pair)\n                    word_pairs.add(pair)\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "from itertools import combinations, permutations\ndef f(word: str):\n    word = word.lower()\n    char_count = {}\n    for char in word:\n        if char.isalpha() and char != ' ':\n            char_count[char] = char_count.get(char, 0) + 1\n    distinct_letters = set(char_count.keys())\n    num_steps = len(word) * len(distinct_letters)\n    combs = list(combinations(char_count.values(), len(distinct_letters)))\n    perms = list(permutations(char_count.values(), len(distinct_letters)))\n    combs_result = list(map(lambda tup: sum((1 / x for x in tup)), combs))\n    perms_result = list(map(lambda tup: sum((x for x in tup)), perms))\n    return num_steps * sum(combs_result) - sum(perms_result) == 0", "input": "\"beaver\"", "output": "False", "imports": ["from itertools import combinations, permutations"], "original_snippet": "from itertools import combinations, permutations\ndef f(word: str):\n    word = word.lower()\n    char_count = {}\n    for char in word:\n        if char.isalpha() and char != ' ':\n            char_count[char] = char_count.get(char, 0) + 1\n    distinct_letters = set(char_count.keys())\n    num_steps = len(word) * len(distinct_letters)\n    combs = list(combinations(char_count.values(), len(distinct_letters)))\n    perms = list(permutations(char_count.values(), len(distinct_letters)))\n    combs_result = list(map(lambda tup: sum((1 / x for x in tup)), combs))\n    perms_result = list(map(lambda tup: sum((x for x in tup)), perms))\n    return num_steps * sum(combs_result) - sum(perms_result) == 0", "composite_functions": [], "_input_type": "str", "_output_type": "bool"}
{"snippet": "def f(input_string: str):\n    modified_string = input_string[3:12:7] + input_string[4:13:7] + input_string[2:11:7] + input_string[1:10:7] + input_string[0:9:7] + input_string[5:14:7]\n    adjusted_count = 0\n    for (idx, char) in enumerate(modified_string):\n        if char in ('A', 'C', 'E', 'G'):\n            adjusted_count += 1\n    return (idx + 1) * (adjusted_count + 1) + 2 * (modified_string.find('A') + 1)", "input": "\"ABCDEFGHIJKLMN\"", "output": "66", "imports": [], "original_snippet": "def f(input_string: str):\n    modified_string = input_string[3:12:7] + input_string[4:13:7] + input_string[2:11:7] + input_string[1:10:7] + input_string[0:9:7] + input_string[5:14:7]\n    adjusted_count = 0\n    for (idx, char) in enumerate(modified_string):\n        if char in ('A', 'C', 'E', 'G'):\n            adjusted_count += 1\n    return (idx + 1) * (adjusted_count + 1) + 2 * (modified_string.find('A') + 1)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_list):\n    even_list = [num for num in input_list if num % 2 == 0]\n    odd_list = [num for num in input_list if num % 2 != 0]\n    even_list.sort(reverse=True)\n    odd_list.sort()\n    combined_list = even_list + odd_list\n    combined_set = set(combined_list)\n    result_sum = sum(combined_set)\n    return result_sum", "input": "[5, 9, 5, 3, 1, 7, 5, 4, 3]", "output": "29", "imports": [], "original_snippet": "def f(input_list):\n    even_list = [num for num in input_list if num % 2 == 0]\n    odd_list = [num for num in input_list if num % 2 != 0]\n    even_list.sort(reverse=True)\n    odd_list.sort()\n    combined_list = even_list + odd_list\n    combined_set = set(combined_list)\n    result_sum = sum(combined_set)\n    return result_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(binary_str: str):\n    num = int(binary_str, 2)\n    result = 1\n    for i in range(2, num):\n        if num % i == 0:\n            result = 0\n            break\n    for digit in binary_str:\n        if digit == '0':\n            result *= 2\n        else:\n            result += 3\n    return result", "input": "'1011'", "output": "14", "imports": [], "original_snippet": "def f(binary_str: str):\n    num = int(binary_str, 2)\n    result = 1\n    for i in range(2, num):\n        if num % i == 0:\n            result = 0\n            break\n    for digit in binary_str:\n        if digit == '0':\n            result *= 2\n        else:\n            result += 3\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(numbers: list) -> dict:\n    max_num = max(numbers)\n    count_dict = {i: 0 for i in range(1, max_num + 1)}\n    power_of_2_count = {2 ** i: 0 for i in range(max_num.bit_length())}\n    consecutive_count = 1\n    consecutive_start_index = 0\n    for (current_index, current_number) in enumerate(numbers):\n        if current_index == 0 or current_number - numbers[current_index - 1] == 1:\n            consecutive_count += 1\n        else:\n            consecutive_group = numbers[consecutive_start_index:current_index]\n            for number in consecutive_group:\n                if (number - 1) % 2 == 0 and ((number - 1) // 2 % 2 == 0 or number % 2 == 0):\n                    power_of_2_count[number] += consecutive_count\n                    count_dict[number] += consecutive_count\n            consecutive_count = 1\n            consecutive_start_index = current_index\n    return power_of_2_count", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "{1: 0, 2: 0, 4: 0, 8: 0}", "imports": [], "original_snippet": "def f(numbers: list) -> dict:\n    max_num = max(numbers)\n    count_dict = {i: 0 for i in range(1, max_num + 1)}\n    power_of_2_count = {2 ** i: 0 for i in range(max_num.bit_length())}\n    consecutive_count = 1\n    consecutive_start_index = 0\n    for (current_index, current_number) in enumerate(numbers):\n        if current_index == 0 or current_number - numbers[current_index - 1] == 1:\n            consecutive_count += 1\n        else:\n            consecutive_group = numbers[consecutive_start_index:current_index]\n            for number in consecutive_group:\n                if (number - 1) % 2 == 0 and ((number - 1) // 2 % 2 == 0 or number % 2 == 0):\n                    power_of_2_count[number] += consecutive_count\n                    count_dict[number] += consecutive_count\n            consecutive_count = 1\n            consecutive_start_index = current_index\n    return power_of_2_count", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(arr):\n    from collections import Counter\n    counts = Counter(arr)\n    (most_common_num, _) = counts.most_common(1)[0]\n    new_arr = [-num if num == most_common_num else num for num in arr]\n    average = sum(new_arr) / len(new_arr)\n    return average", "input": "[1, 2, 3, 2, 4, 2, 5]", "output": "1.0", "imports": ["from collections import Counter"], "original_snippet": "def f(arr):\n    from collections import Counter\n    counts = Counter(arr)\n    (most_common_num, _) = counts.most_common(1)[0]\n    new_arr = [-num if num == most_common_num else num for num in arr]\n    average = sum(new_arr) / len(new_arr)\n    return average", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(initial_price: int):\n    price_history = {}\n    price = initial_price\n    day_count = 0\n    while day_count <= 365:\n        price_history[day_count] = price\n        if price % 2 == 0:\n            if price % 3 == 0:\n                price *= 0.8\n            else:\n                price *= 0.9\n        elif price > 100:\n            price *= 1.05\n        else:\n            price *= 1.1\n        day_count += 1\n    return sum(price_history.values()) / len(price_history)", "input": "100", "output": "204600770.4976374", "imports": [], "original_snippet": "def f(initial_price: int):\n    price_history = {}\n    price = initial_price\n    day_count = 0\n    while day_count <= 365:\n        price_history[day_count] = price\n        if price % 2 == 0:\n            if price % 3 == 0:\n                price *= 0.8\n            else:\n                price *= 0.9\n        elif price > 100:\n            price *= 1.05\n        else:\n            price *= 1.1\n        day_count += 1\n    return sum(price_history.values()) / len(price_history)", "composite_functions": [], "_input_type": "int", "_output_type": "float"}
{"snippet": "def f(input_list):\n    flat_list = [str(item) for sublist in input_list for item in (sublist if isinstance(sublist, list) else [sublist])]\n    flat_list.sort()\n    str_count = {ele: flat_list.count(ele) for ele in set(flat_list)}\n    new_list = [(key, value) for (key, value) in str_count.items()]\n    new_list.reverse()\n    return new_list", "input": "['a', [3, 2], 'b', [3, 'c'], [5, 'b', 'a']]", "output": "[('b', 2), ('a', 2), ('2', 1), ('3', 2), ('c', 1), ('5', 1)]", "imports": [], "original_snippet": "def f(input_list):\n    flat_list = [str(item) for sublist in input_list for item in (sublist if isinstance(sublist, list) else [sublist])]\n    flat_list.sort()\n    str_count = {ele: flat_list.count(ele) for ele in set(flat_list)}\n    new_list = [(key, value) for (key, value) in str_count.items()]\n    new_list.reverse()\n    return new_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(x: int) -> int:\n    bit_count = bin(x).count('1')\n    reverse_bits = int(bin(x)[2:][::-1], 2)\n    sum_of_digits = sum((int(digit) for digit in str(x)))\n    result = x + bit_count + reverse_bits + sum_of_digits\n    return result", "input": "34", "output": "60", "imports": [], "original_snippet": "def f(x: int) -> int:\n    bit_count = bin(x).count('1')\n    reverse_bits = int(bin(x)[2:][::-1], 2)\n    sum_of_digits = sum((int(digit) for digit in str(x)))\n    result = x + bit_count + reverse_bits + sum_of_digits\n    return result", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(numbers):\n    sorted_nums = sorted(numbers)\n    result = []\n    unique_elements = set(sorted_nums)\n    for num in sorted_nums:\n        if num % 2 == 0:\n            square = num ** 2\n            if square not in result:\n                result.append(square)\n        else:\n            result.append(num * len(unique_elements) - 1)\n    result.sort()\n    return result", "input": "[2, 3, 4, 5]", "output": "[4, 11, 16, 19]", "imports": [], "original_snippet": "def f(numbers):\n    sorted_nums = sorted(numbers)\n    result = []\n    unique_elements = set(sorted_nums)\n    for num in sorted_nums:\n        if num % 2 == 0:\n            square = num ** 2\n            if square not in result:\n                result.append(square)\n        else:\n            result.append(num * len(unique_elements) - 1)\n    result.sort()\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(string: str):\n    words = string.split(' ')\n    reversed_words = [''.join(reversed(word)) for word in words]\n    new_string = ' '.join(reversed_words)\n    unique_chars = set()\n    for word in reversed_words:\n        if word == word[::-1]:\n            for char in word:\n                unique_chars.discard(char)\n        else:\n            for char in word:\n                unique_chars.add(char)\n    count_unique_chars = len(unique_chars)\n    return count_unique_chars", "input": "\"Hello worldmadam I am doing well\"", "output": "12", "imports": [], "original_snippet": "def f(string: str):\n    words = string.split(' ')\n    reversed_words = [''.join(reversed(word)) for word in words]\n    new_string = ' '.join(reversed_words)\n    unique_chars = set()\n    for word in reversed_words:\n        if word == word[::-1]:\n            for char in word:\n                unique_chars.discard(char)\n        else:\n            for char in word:\n                unique_chars.add(char)\n    count_unique_chars = len(unique_chars)\n    return count_unique_chars", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(string: str):\n    reversed_string = string[::-1]\n    common_letters = [char for char in set(reversed_string) if string.count(char) > 1]\n    return sorted(common_letters, reverse=True)", "input": "\"abracadabra\"", "output": "['r', 'b', 'a']", "imports": [], "original_snippet": "def f(string: str):\n    reversed_string = string[::-1]\n    common_letters = [char for char in set(reversed_string) if string.count(char) > 1]\n    return sorted(common_letters, reverse=True)", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(arr, query):\n    indices = [i for (i, x) in enumerate(arr) if x == query]\n    return indices[-1] if indices else -1", "input": "[3, 1, 1.2, 2, 4, 1, 5, 1, 6, 3, 7, 1, 8, 9, 1], 1", "output": "14", "imports": [], "original_snippet": "def f(arr, query):\n    indices = [i for (i, x) in enumerate(arr) if x == query]\n    return indices[-1] if indices else -1", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(lst):\n    stack = []\n    total = 0\n    for i in range(len(lst)):\n        if lst[i] != 0:\n            if lst[i - 1] == 0:\n                stack.append(total)\n            total += lst[i]\n        elif stack:\n            total -= stack.pop()\n    return total", "input": "[5, 3, 2, 0, 6, 3, 2, 2, 0, 3, 7, 0, 0, 0, 3]", "output": "13", "imports": [], "original_snippet": "def f(lst):\n    stack = []\n    total = 0\n    for i in range(len(lst)):\n        if lst[i] != 0:\n            if lst[i - 1] == 0:\n                stack.append(total)\n            total += lst[i]\n        elif stack:\n            total -= stack.pop()\n    return total", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(words):\n    vowels = 'aeiou'\n    longest_word = max(words, key=len)\n    shortest_word = min(words, key=len)\n    concatenated_string = shortest_word + longest_word\n    char_list = list(concatenated_string)\n    char_type = ['vowel' if char.lower() in vowels else 'consonant' for char in char_list]\n    vowel_dist = {}\n    consonant_dist = {}\n    for (i, char) in enumerate(char_list):\n        char_type = ['vowel' if char.lower() in vowels else 'consonant']\n        if char_type == 'vowel':\n            vowel_dist[char] = vowel_dist.get(char, 0) + 1\n        else:\n            consonant_dist[char] = consonant_dist.get(char, 0) + 1\n    return (vowel_dist, consonant_dist, concatenated_string)", "input": "[\"python\", \"code\", \"challenge\", \"noisette\"]", "output": "({}, {'c': 2, 'o': 1, 'd': 1, 'e': 3, 'h': 1, 'a': 1, 'l': 2, 'n': 1, 'g': 1}, 'codechallenge')", "imports": [], "original_snippet": "def f(words):\n    vowels = 'aeiou'\n    longest_word = max(words, key=len)\n    shortest_word = min(words, key=len)\n    concatenated_string = shortest_word + longest_word\n    char_list = list(concatenated_string)\n    char_type = ['vowel' if char.lower() in vowels else 'consonant' for char in char_list]\n    vowel_dist = {}\n    consonant_dist = {}\n    for (i, char) in enumerate(char_list):\n        char_type = ['vowel' if char.lower() in vowels else 'consonant']\n        if char_type == 'vowel':\n            vowel_dist[char] = vowel_dist.get(char, 0) + 1\n        else:\n            consonant_dist[char] = consonant_dist.get(char, 0) + 1\n    return (vowel_dist, consonant_dist, concatenated_string)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(nums: list):\n    from collections import Counter\n    unique_nums = set(nums)\n    unique_count = Counter(unique_nums)\n    if all((count == 1 for count in unique_count.values())):\n        return []\n    removed_duplicates = list(unique_nums)\n    a = sorted(removed_duplicates)\n    dict_a = {val: i for (i, val) in enumerate(a)}\n    b = [(0, k) if v < 3 else dict_a[k] for (k, v) in unique_count.items() if v < 3]\n    return b", "input": "[1, 2, 2, 3, 3, 3, 4, 5]", "output": "[]", "imports": ["from collections import Counter"], "original_snippet": "def f(nums: list):\n    from collections import Counter\n    unique_nums = set(nums)\n    unique_count = Counter(unique_nums)\n    if all((count == 1 for count in unique_count.values())):\n        return []\n    removed_duplicates = list(unique_nums)\n    a = sorted(removed_duplicates)\n    dict_a = {val: i for (i, val) in enumerate(a)}\n    b = [(0, k) if v < 3 else dict_a[k] for (k, v) in unique_count.items() if v < 3]\n    return b", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_groups):\n    group_count = len(input_groups)\n    people = [None] * group_count\n    for i in range(group_count):\n        input_group = input_groups[i]\n        has_homeless = input_group['name'] == 'homeless'\n        totals = {'self_count': input_group['self_count'], 'food': input_group['food'], 'climates': [c.lower() for c in input_group['location_found'].split(',')], 'species': input_group['about']}\n        people[i] = {'has_homeless': has_homeless, 'count': input_group['count'], 'totals': totals}\n    homeless_count = sum((g['totals']['self_count'] for g in people if g['has_homeless']))\n    animals_count = sum((g['totals']['food'] for g in people if not g['has_homeless']))\n    joint_climates = set.intersection(*[set(d['totals']['climates']) for d in people])\n    animal_founder = set.union(*[set(d['totals']['species'].split(', ')) for d in people])\n    return (homeless_count, animals_count, list(sorted(joint_climates)), list(animal_founder))", "input": "[\n    {'name': 'homeless', 'count': 2, 'self_count': 1, 'food': 0, 'location_found': 'North, East', 'about': 'dog, cat'},\n    {'name': 'homeless', 'count': 3, 'self_count': 2, 'food': 1, 'location_found': 'South, East', 'about': 'rabbit'},\n    {'name': 'homeless', 'count': 4, 'self_count': 3, 'food': 2, 'location_found': 'West, East', 'about': 'horse'}\n]", "output": "(6, 0, [' east'], ['rabbit', 'cat', 'dog', 'horse'])", "imports": [], "original_snippet": "def f(input_groups):\n    group_count = len(input_groups)\n    people = [None] * group_count\n    for i in range(group_count):\n        input_group = input_groups[i]\n        has_homeless = input_group['name'] == 'homeless'\n        totals = {'self_count': input_group['self_count'], 'food': input_group['food'], 'climates': [c.lower() for c in input_group['location_found'].split(',')], 'species': input_group['about']}\n        people[i] = {'has_homeless': has_homeless, 'count': input_group['count'], 'totals': totals}\n    homeless_count = sum((g['totals']['self_count'] for g in people if g['has_homeless']))\n    animals_count = sum((g['totals']['food'] for g in people if not g['has_homeless']))\n    joint_climates = set.intersection(*[set(d['totals']['climates']) for d in people])\n    animal_founder = set.union(*[set(d['totals']['species'].split(', ')) for d in people])\n    return (homeless_count, animals_count, list(sorted(joint_climates)), list(animal_founder))", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(s):\n    unique_chars = ''.join(set(s.replace(' ', '')))\n    return f'{len(unique_chars)} {unique_chars}'", "input": "\"hello world\"", "output": "'7 doehwlr'", "imports": [], "original_snippet": "def f(s):\n    unique_chars = ''.join(set(s.replace(' ', '')))\n    return f'{len(unique_chars)} {unique_chars}'", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(transactions):\n    customer_totals = {}\n    highest_amount = float('-inf')\n    lowest_amount = float('inf')\n    for transaction in transactions:\n        customer = transaction['customer']\n        amount = transaction['amount']\n        if customer not in customer_totals:\n            customer_totals[customer] = 0\n        customer_totals[customer] += amount\n        highest_amount = max(highest_amount, amount)\n        lowest_amount = min(lowest_amount, amount)\n    summary = {'customer_totals': customer_totals, 'highest_amount': highest_amount, 'lowest_amount': lowest_amount}\n    return summary", "input": "[{'customer': 'Alice', 'amount': 10},\n{'customer': 'Bob', 'amount': 5},\n{'customer': 'Alice', 'amount': 15},\n{'customer': 'Alice', 'amount': 20},\n{'customer': 'Bob', 'amount': 25}]", "output": "{'customer_totals': {'Alice': 45, 'Bob': 30}, 'highest_amount': 25, 'lowest_amount': 5}", "imports": [], "original_snippet": "def f(transactions):\n    customer_totals = {}\n    highest_amount = float('-inf')\n    lowest_amount = float('inf')\n    for transaction in transactions:\n        customer = transaction['customer']\n        amount = transaction['amount']\n        if customer not in customer_totals:\n            customer_totals[customer] = 0\n        customer_totals[customer] += amount\n        highest_amount = max(highest_amount, amount)\n        lowest_amount = min(lowest_amount, amount)\n    summary = {'customer_totals': customer_totals, 'highest_amount': highest_amount, 'lowest_amount': lowest_amount}\n    return summary", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(numbers):\n    if not numbers:\n        return []\n    odd_product = 1\n    for num in numbers:\n        if num % 2 != 0:\n            odd_product *= num\n    for i in range(len(numbers)):\n        if i % 2 == 0:\n            numbers[i] = odd_product\n    return numbers", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "[945, 2, 945, 4, 945, 6, 945, 8, 945, 10]", "imports": [], "original_snippet": "def f(numbers):\n    if not numbers:\n        return []\n    odd_product = 1\n    for num in numbers:\n        if num % 2 != 0:\n            odd_product *= num\n    for i in range(len(numbers)):\n        if i % 2 == 0:\n            numbers[i] = odd_product\n    return numbers", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list: list):\n    stack = []\n    result = []\n    for num in input_list:\n        while stack and stack[-1] > num:\n            result.append(stack.pop())\n        stack.append(num)\n    while stack:\n        result.append(stack.pop())\n    return result", "input": "[15, 12, 5, 20, 10, 7, 25, 9]", "output": "[15, 12, 20, 10, 25, 9, 7, 5]", "imports": [], "original_snippet": "def f(input_list: list):\n    stack = []\n    result = []\n    for num in input_list:\n        while stack and stack[-1] > num:\n            result.append(stack.pop())\n        stack.append(num)\n    while stack:\n        result.append(stack.pop())\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(num: int):\n    curr = str(num)\n    total_sum = sum((int(digit) for digit in curr))\n    while '9' in curr:\n        new_sum = 0\n        for i in range(len(curr)):\n            num = int(curr[i])\n            if num == 9:\n                curr = curr[:i] + '8' + curr[i + 1:]\n            else:\n                new_sum += num\n        total_sum += new_sum\n    return total_sum", "input": "24839", "output": "43", "imports": [], "original_snippet": "def f(num: int):\n    curr = str(num)\n    total_sum = sum((int(digit) for digit in curr))\n    while '9' in curr:\n        new_sum = 0\n        for i in range(len(curr)):\n            num = int(curr[i])\n            if num == 9:\n                curr = curr[:i] + '8' + curr[i + 1:]\n            else:\n                new_sum += num\n        total_sum += new_sum\n    return total_sum", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(s: str) -> int:\n    vowels = ['a', 'e', 'i', 'o', 'u']\n    s = s.lower()\n    s = s.replace(' ', '')\n    s = ''.join((i for i in s if i not in vowels))\n    s = s.strip()\n    count = 0\n    while len(s) > 0:\n        if s[0] == s[-1]:\n            count += len(s)\n            s = s[1:-1]\n        else:\n            s = s[1:]\n    return count", "input": "'Let me solve this intriguing question effortlessly'", "output": "1", "imports": [], "original_snippet": "def f(s: str) -> int:\n    vowels = ['a', 'e', 'i', 'o', 'u']\n    s = s.lower()\n    s = s.replace(' ', '')\n    s = ''.join((i for i in s if i not in vowels))\n    s = s.strip()\n    count = 0\n    while len(s) > 0:\n        if s[0] == s[-1]:\n            count += len(s)\n            s = s[1:-1]\n        else:\n            s = s[1:]\n    return count", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(strings: list):\n    results = []\n    buffer = []\n    for s in strings:\n        buffer.append(s)\n        if len(buffer) >= 3:\n            current_length = len(set([c for b in buffer for c in b]))\n            buffer.pop(0)\n            if current_length == 5:\n                results.append(''.join(buffer))\n                buffer = []\n            elif current_length > 5:\n                buffer = []\n    return results", "input": "['abc', 'def', '123', '456', '789', 'xyz']", "output": "[]", "imports": [], "original_snippet": "def f(strings: list):\n    results = []\n    buffer = []\n    for s in strings:\n        buffer.append(s)\n        if len(buffer) >= 3:\n            current_length = len(set([c for b in buffer for c in b]))\n            buffer.pop(0)\n            if current_length == 5:\n                results.append(''.join(buffer))\n                buffer = []\n            elif current_length > 5:\n                buffer = []\n    return results", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(nums):\n    evens = sorted((n for n in nums if n % 2 == 0))\n    odds = sorted((n for n in nums if n % 2 != 0))\n    new_list = []\n    for i in range(max(len(evens), len(odds))):\n        if i < len(evens):\n            new_list.append(evens[i])\n        if i < len(odds):\n            new_list.append(odds[i])\n    result_list = []\n    for (i, num) in enumerate(new_list):\n        binary_num = bin(num)[2:]\n        if len(binary_num) % 2 == 0:\n            new_binary_num = '1' + binary_num\n        else:\n            new_binary_num = '0' + binary_num\n        result_num = int(new_binary_num, 2) * 2\n        result_list.append(result_num)\n    return result_list", "input": "[4, 2, 1, 17, 56, 70, 3, 10, 15, 20]", "output": "[12, 2, 8, 14, 52, 62, 40, 34, 240, 140]", "imports": [], "original_snippet": "def f(nums):\n    evens = sorted((n for n in nums if n % 2 == 0))\n    odds = sorted((n for n in nums if n % 2 != 0))\n    new_list = []\n    for i in range(max(len(evens), len(odds))):\n        if i < len(evens):\n            new_list.append(evens[i])\n        if i < len(odds):\n            new_list.append(odds[i])\n    result_list = []\n    for (i, num) in enumerate(new_list):\n        binary_num = bin(num)[2:]\n        if len(binary_num) % 2 == 0:\n            new_binary_num = '1' + binary_num\n        else:\n            new_binary_num = '0' + binary_num\n        result_num = int(new_binary_num, 2) * 2\n        result_list.append(result_num)\n    return result_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import List\ndef f(numbers: List[float]):\n    unique_elements = set()\n    for num in numbers:\n        decimal_place = abs(num) - int(abs(num))\n        if not unique_elements or decimal_place > max((decimal_place for num in unique_elements)):\n            unique_elements.add(num)\n    return len(unique_elements)", "input": "[2.1, 3.3, 4.0, 3.4, 2.2, 4.5, 5.6, 4.1, 3.3]", "output": "1", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(numbers: List[float]):\n    unique_elements = set()\n    for num in numbers:\n        decimal_place = abs(num) - int(abs(num))\n        if not unique_elements or decimal_place > max((decimal_place for num in unique_elements)):\n            unique_elements.add(num)\n    return len(unique_elements)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_str: str):\n    result = ''\n    for (i, char) in enumerate(input_str):\n        if 'A' <= char <= 'Z':\n            new_char = chr((ord(char) - ord('A') + 3) % 26 + ord('A'))\n            result += new_char\n        elif 'a' <= char <= 'z':\n            new_char = chr((ord(char) - ord('a') + 19) % 26 + ord('a'))\n            result += new_char\n        elif char.isdigit():\n            result += str((int(char) - 1) % 10)\n        else:\n            result += char\n    return result", "input": "'A1b2Z'", "output": "'D0u1C'", "imports": [], "original_snippet": "def f(input_str: str):\n    result = ''\n    for (i, char) in enumerate(input_str):\n        if 'A' <= char <= 'Z':\n            new_char = chr((ord(char) - ord('A') + 3) % 26 + ord('A'))\n            result += new_char\n        elif 'a' <= char <= 'z':\n            new_char = chr((ord(char) - ord('a') + 19) % 26 + ord('a'))\n            result += new_char\n        elif char.isdigit():\n            result += str((int(char) - 1) % 10)\n        else:\n            result += char\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(arr):\n    dic = {}\n    for elem in arr:\n        (x, y) = elem\n        if x in dic:\n            dic[x].append(y)\n        else:\n            dic[x] = [y]\n    return {k: sorted(v) for (k, v) in dic.items()}", "input": "[(1, 2), (3, 4), (1, 3), (2, 1), (3, 5), (2, 4)]", "output": "{1: [2, 3], 3: [4, 5], 2: [1, 4]}", "imports": [], "original_snippet": "def f(arr):\n    dic = {}\n    for elem in arr:\n        (x, y) = elem\n        if x in dic:\n            dic[x].append(y)\n        else:\n            dic[x] = [y]\n    return {k: sorted(v) for (k, v) in dic.items()}", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(data: dict) -> dict:\n    result = {'even_keys': [], 'odd_values': [], 'zero_values': []}\n    for (key, value) in sorted(data.items()):\n        if key % 2 == 0:\n            if value == 0:\n                result['even_keys'].append(key)\n            elif value % 2 == 1:\n                result['odd_values'].append(value)\n            else:\n                result['even_keys'].append(key)\n        elif value == 0:\n            result['zero_values'].append(value)\n    return result", "input": "{0: 0, 1: 0, 2: 0, 3: 1, 4: 0, 5: 1, 6: 0, 7: 1, 8: 0, 9: 1}", "output": "{'even_keys': [0, 2, 4, 6, 8], 'odd_values': [], 'zero_values': [0]}", "imports": [], "original_snippet": "def f(data: dict) -> dict:\n    result = {'even_keys': [], 'odd_values': [], 'zero_values': []}\n    for (key, value) in sorted(data.items()):\n        if key % 2 == 0:\n            if value == 0:\n                result['even_keys'].append(key)\n            elif value % 2 == 1:\n                result['odd_values'].append(value)\n            else:\n                result['even_keys'].append(key)\n        elif value == 0:\n            result['zero_values'].append(value)\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(s: str):\n    res = ''\n    index = 0\n    for i in range(0, len(s)):\n        if s[i] != ' ':\n            if i + 1 < len(s) and s[i + 1] == ' ':\n                res = s[i] + res\n            elif i == 0:\n                res = s[i] + res\n            elif i == len(s) - 1:\n                index += 1\n    return (res, index)", "input": "\"Hello World\"", "output": "('oH', 1)", "imports": [], "original_snippet": "def f(s: str):\n    res = ''\n    index = 0\n    for i in range(0, len(s)):\n        if s[i] != ' ':\n            if i + 1 < len(s) and s[i + 1] == ' ':\n                res = s[i] + res\n            elif i == 0:\n                res = s[i] + res\n            elif i == len(s) - 1:\n                index += 1\n    return (res, index)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(lst):\n    longest_sequence = 1\n    current_sequence = 1\n    for i in range(1, len(lst)):\n        if lst[i] == lst[i - 1] + 1:\n            current_sequence += 1\n            longest_sequence = max(longest_sequence, current_sequence)\n        else:\n            current_sequence = 1\n    return longest_sequence", "input": "[1, 2, 3, 5, 6, 7, 9, 10, 11, 12]", "output": "4", "imports": [], "original_snippet": "def f(lst):\n    longest_sequence = 1\n    current_sequence = 1\n    for i in range(1, len(lst)):\n        if lst[i] == lst[i - 1] + 1:\n            current_sequence += 1\n            longest_sequence = max(longest_sequence, current_sequence)\n        else:\n            current_sequence = 1\n    return longest_sequence", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_list):\n    results = []\n    for d in input_list:\n        nums = d['nums']\n        target = d['target']\n        for i in range(len(nums)):\n            total = 0\n            for j in range(i, len(nums)):\n                total += nums[j]\n                if total == target:\n                    results.append(nums[i:j + 1])\n                    break\n                elif total > target:\n                    break\n    return sorted(results, key=lambda x: (-sum(x), -len(x)))[0]", "input": "[{'nums': [1, 2, 3, 4, 5], 'target': 6}]", "output": "[1, 2, 3]", "imports": [], "original_snippet": "def f(input_list):\n    results = []\n    for d in input_list:\n        nums = d['nums']\n        target = d['target']\n        for i in range(len(nums)):\n            total = 0\n            for j in range(i, len(nums)):\n                total += nums[j]\n                if total == target:\n                    results.append(nums[i:j + 1])\n                    break\n                elif total > target:\n                    break\n    return sorted(results, key=lambda x: (-sum(x), -len(x)))[0]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(n: int) -> int:\n    (a, b) = (0, 1)\n    for _ in range(n):\n        (a, b) = (b, a + b)\n    return a", "input": "151", "output": "16130531424904581415797907386349", "imports": [], "original_snippet": "def f(n: int) -> int:\n    (a, b) = (0, 1)\n    for _ in range(n):\n        (a, b) = (b, a + b)\n    return a", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(sentence: str):\n    vowels = 'aeiouAEIOU'\n    longest_substring = ''\n    for i in range(len(sentence)):\n        curr_substring = sentence[i]\n        curr_len = 0\n        prev_char_vowel = sentence[i] in vowels\n        for j in range(i + 1, len(sentence)):\n            char_vowel = sentence[j] in vowels\n            if char_vowel != prev_char_vowel:\n                curr_substring += sentence[j]\n                curr_len += 1\n                prev_char_vowel = not prev_char_vowel\n            else:\n                break\n        if len(curr_substring) > len(longest_substring):\n            longest_substring = curr_substring\n    return longest_substring", "input": "\"Hello, I want to test this sentence with some alternating vowels and consonants\"", "output": "'some al'", "imports": [], "original_snippet": "def f(sentence: str):\n    vowels = 'aeiouAEIOU'\n    longest_substring = ''\n    for i in range(len(sentence)):\n        curr_substring = sentence[i]\n        curr_len = 0\n        prev_char_vowel = sentence[i] in vowels\n        for j in range(i + 1, len(sentence)):\n            char_vowel = sentence[j] in vowels\n            if char_vowel != prev_char_vowel:\n                curr_substring += sentence[j]\n                curr_len += 1\n                prev_char_vowel = not prev_char_vowel\n            else:\n                break\n        if len(curr_substring) > len(longest_substring):\n            longest_substring = curr_substring\n    return longest_substring", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import math\ndef f(strings):\n    L = max((len(s) for s in strings))\n    transformed_list = []\n    for (idx, orig_string) in enumerate(strings, start=1):\n        new_length = math.ceil(idx * len(orig_string) / L)\n        if new_length % 2 == 0:\n            transformed_str = orig_string[::-1]\n        else:\n            transformed_str = orig_string.upper()\n        if len(transformed_str) > new_length:\n            transformed_str = transformed_str[:new_length]\n        elif len(transformed_str) < new_length:\n            transformed_str += 'X' * (new_length - len(transformed_str))\n        transformed_list.append(transformed_str)\n    return transformed_list", "input": "[\"hello\", \"world\", \"challenge\", \"noisette\"]", "output": "['H', 'dl', 'CHA', 'ette']", "imports": ["import math"], "original_snippet": "import math\ndef f(strings):\n    L = max((len(s) for s in strings))\n    transformed_list = []\n    for (idx, orig_string) in enumerate(strings, start=1):\n        new_length = math.ceil(idx * len(orig_string) / L)\n        if new_length % 2 == 0:\n            transformed_str = orig_string[::-1]\n        else:\n            transformed_str = orig_string.upper()\n        if len(transformed_str) > new_length:\n            transformed_str = transformed_str[:new_length]\n        elif len(transformed_str) < new_length:\n            transformed_str += 'X' * (new_length - len(transformed_str))\n        transformed_list.append(transformed_str)\n    return transformed_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import *\ndef f(data: Union[List[str], List[int], Dict[str, int]], key: str) -> Union[int, str]:\n    if isinstance(data, list):\n        transformed_data = [x[::-1] if isinstance(x, str) else x for x in data]\n        result = sum(transformed_data) if key == 'sum' else len(transformed_data)\n    else:\n        grouped_data = {k: v for (k, v) in data.items() if v > 0}\n        result = ''.join(sorted(grouped_data.keys())) if key == 'sorted_keys' else sum(grouped_data.values())\n    return result", "input": "{'1': 10, '2': 20, '3': -5}, 'sum'", "output": "30", "imports": ["from typing import *"], "original_snippet": "from typing import *\ndef f(data: Union[List[str], List[int], Dict[str, int]], key: str) -> Union[int, str]:\n    if isinstance(data, list):\n        transformed_data = [x[::-1] if isinstance(x, str) else x for x in data]\n        result = sum(transformed_data) if key == 'sum' else len(transformed_data)\n    else:\n        grouped_data = {k: v for (k, v) in data.items() if v > 0}\n        result = ''.join(sorted(grouped_data.keys())) if key == 'sorted_keys' else sum(grouped_data.values())\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(x: int) -> int:\n    x_minus_one = x - 1\n    x_multiplied = x * x_minus_one\n    x_multiplied_div_2a = x_multiplied // 2\n    x_multiplied_div_2a_multiplied = x_multiplied_div_2a * x\n    x_div_3b = x_multiplied_div_2a_multiplied // 3\n    x_div_3b_multiplied = x_div_3b * x\n    return x_div_3b_multiplied", "input": "10", "output": "1500", "imports": [], "original_snippet": "def f(x: int) -> int:\n    x_minus_one = x - 1\n    x_multiplied = x * x_minus_one\n    x_multiplied_div_2a = x_multiplied // 2\n    x_multiplied_div_2a_multiplied = x_multiplied_div_2a * x\n    x_div_3b = x_multiplied_div_2a_multiplied // 3\n    x_div_3b_multiplied = x_div_3b * x\n    return x_div_3b_multiplied", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(lst):\n    transformed = [val ** 2 if i % 2 == 0 else val for (i, val) in enumerate(lst)]\n    sum_of_transformed = sum(transformed)\n    modified_lst = [val * sum_of_transformed if i % 2 != 0 else val for (i, val) in enumerate(transformed)]\n    total_sum = sum(modified_lst)\n    return total_sum", "input": "[1, 2, 3, 4, 5]", "output": "281", "imports": [], "original_snippet": "def f(lst):\n    transformed = [val ** 2 if i % 2 == 0 else val for (i, val) in enumerate(lst)]\n    sum_of_transformed = sum(transformed)\n    modified_lst = [val * sum_of_transformed if i % 2 != 0 else val for (i, val) in enumerate(transformed)]\n    total_sum = sum(modified_lst)\n    return total_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(data):\n    sum_of_squares = {}\n    sum_of_negatives = {}\n    for (key, value) in data.items():\n        sum_of_squares[key] = sum([x ** 2 for x in value if x > 0])\n        sum_of_negatives[key] = sum([x for x in value if x < 0])\n    min_sum_of_squares = min(sum_of_squares.values())\n    total_sum = sum([min_sum_of_squares, sum([sum_of_negatives[key] for key in data])])\n    return total_sum", "input": "{'A': [2, -3, 4, 5, -6], 'B': [1, -2, 3, -4, 5], 'C': [-1, -2, 3, -4, 5]}", "output": "12", "imports": [], "original_snippet": "def f(data):\n    sum_of_squares = {}\n    sum_of_negatives = {}\n    for (key, value) in data.items():\n        sum_of_squares[key] = sum([x ** 2 for x in value if x > 0])\n        sum_of_negatives[key] = sum([x for x in value if x < 0])\n    min_sum_of_squares = min(sum_of_squares.values())\n    total_sum = sum([min_sum_of_squares, sum([sum_of_negatives[key] for key in data])])\n    return total_sum", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(word_list):\n    result = []\n    seen_palindromes = set()\n    filtered_words = [word.lower() for word in word_list if len(word) == 5]\n    for word in filtered_words:\n        if word not in seen_palindromes and word == word[::-1]:\n            seen_palindromes.add(word)\n            result.append(word)\n    return result", "input": "['radar', 'level', 'Level', 'hello', 'world', 'eye', 'madam']", "output": "['radar', 'level', 'madam']", "imports": [], "original_snippet": "def f(word_list):\n    result = []\n    seen_palindromes = set()\n    filtered_words = [word.lower() for word in word_list if len(word) == 5]\n    for word in filtered_words:\n        if word not in seen_palindromes and word == word[::-1]:\n            seen_palindromes.add(word)\n            result.append(word)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(data: dict):\n    target_range = range(1, 10)\n    values = dict()\n    for (key, lst) in data.items():\n        filtered_list = [value for value in lst if value in target_range]\n        values[key] = sum(filtered_list)\n    for (key, value) in values.items():\n        if value > 0:\n            values[key] = value * 2\n        else:\n            values[key] = value - 1\n    even_keys = [key for (key, value) in values.items() if value % 2 == 0]\n    total_sum = sum([values[key] for key in even_keys])\n    return total_sum", "input": "{'A': [1, 2, 3, -4, 7, 8], 'B': [3, 5, 7, -8, 9], 'C': [1, 5, 7, -9, 10]}", "output": "116", "imports": [], "original_snippet": "def f(data: dict):\n    target_range = range(1, 10)\n    values = dict()\n    for (key, lst) in data.items():\n        filtered_list = [value for value in lst if value in target_range]\n        values[key] = sum(filtered_list)\n    for (key, value) in values.items():\n        if value > 0:\n            values[key] = value * 2\n        else:\n            values[key] = value - 1\n    even_keys = [key for (key, value) in values.items() if value % 2 == 0]\n    total_sum = sum([values[key] for key in even_keys])\n    return total_sum", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(numbers):\n    unique_evens = list(set([x for x in numbers if x % 2 == 0]))\n    sum_of_evens = sum(unique_evens)\n    modified_evens = [x * sum_of_evens for x in unique_evens]\n    product = 1\n    for x in modified_evens:\n        product *= x\n    return product", "input": "[2, 5, 9, 12, 15]", "output": "4704", "imports": [], "original_snippet": "def f(numbers):\n    unique_evens = list(set([x for x in numbers if x % 2 == 0]))\n    sum_of_evens = sum(unique_evens)\n    modified_evens = [x * sum_of_evens for x in unique_evens]\n    product = 1\n    for x in modified_evens:\n        product *= x\n    return product", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
