"""
Input validation and sanitization for Absolute Zero Reasoner.

This module provides comprehensive validation for configurations, parameters,
and user inputs to ensure system security and stability.
"""

import re
import logging
from typing import Any, Dict, List, Optional
from pathlib import Path
from absolute_zero_reasoner.exceptions import ValidationError, SecurityError

logger = logging.getLogger(__name__)


class ValidationRule:
    """Base class for validation rules."""

    def __init__(self, name: str, error_message: str = None):
        self.name = name
        self.error_message = error_message or f"Validation failed for rule: {name}"

    def validate(self, value: Any) -> bool:
        """
        Validate a value against this rule.

        Args:
            value: Value to validate

        Returns:
            True if valid, False otherwise
        """
        raise NotImplementedError

    def __call__(self, value: Any) -> bool:
        return self.validate(value)


class TypeRule(ValidationRule):
    """Validate value type."""

    def __init__(self, expected_type: type, name: str = None):
        self.expected_type = expected_type
        super().__init__(
            name or f"type_{expected_type.__name__}",
            f"Expected type {expected_type.__name__}"
        )

    def validate(self, value: Any) -> bool:
        return isinstance(value, self.expected_type)


class RangeRule(ValidationRule):
    """Validate numeric range."""

    def __init__(self, min_val: Optional[float] = None, max_val: Optional[float] = None,
                 name: str = None):
        self.min_val = min_val
        self.max_val = max_val
        super().__init__(
            name or f"range_{min_val}_{max_val}",
            f"Value must be between {min_val} and {max_val}"
        )

    def validate(self, value: Any) -> bool:
        if not isinstance(value, (int, float)):
            return False

        if self.min_val is not None and value < self.min_val:
            return False

        if self.max_val is not None and value > self.max_val:
            return False

        return True


class RegexRule(ValidationRule):
    """Validate string against regex pattern."""

    def __init__(self, pattern: str, name: str = None):
        self.pattern = re.compile(pattern)
        super().__init__(
            name or f"regex_{pattern}",
            f"Value must match pattern: {pattern}"
        )

    def validate(self, value: Any) -> bool:
        if not isinstance(value, str):
            return False
        return bool(self.pattern.match(value))


class LengthRule(ValidationRule):
    """Validate string/list length."""

    def __init__(self, min_len: Optional[int] = None, max_len: Optional[int] = None,
                 name: str = None):
        self.min_len = min_len
        self.max_len = max_len
        super().__init__(
            name or f"length_{min_len}_{max_len}",
            f"Length must be between {min_len} and {max_len}"
        )

    def validate(self, value: Any) -> bool:
        if not hasattr(value, '__len__'):
            return False

        length = len(value)

        if self.min_len is not None and length < self.min_len:
            return False

        if self.max_len is not None and length > self.max_len:
            return False

        return True


class ChoiceRule(ValidationRule):
    """Validate value is in allowed choices."""

    def __init__(self, choices: List[Any], name: str = None):
        self.choices = choices
        super().__init__(
            name or f"choice_{len(choices)}",
            f"Value must be one of: {choices}"
        )

    def validate(self, value: Any) -> bool:
        return value in self.choices


class PathRule(ValidationRule):
    """Validate file/directory path."""

    def __init__(self, must_exist: bool = False, must_be_file: bool = False,
                 must_be_dir: bool = False, name: str = None):
        self.must_exist = must_exist
        self.must_be_file = must_be_file
        self.must_be_dir = must_be_dir
        super().__init__(
            name or "path_validation",
            "Invalid path"
        )

    def validate(self, value: Any) -> bool:
        if not isinstance(value, (str, Path)):
            return False

        path = Path(value)

        if self.must_exist and not path.exists():
            return False

        if self.must_be_file and path.exists() and not path.is_file():
            return False

        if self.must_be_dir and path.exists() and not path.is_dir():
            return False

        return True


class SecurityRule(ValidationRule):
    """Validate input for security issues."""

    DANGEROUS_PATTERNS = [
        r'__import__',
        r'exec\s*\(',
        r'eval\s*\(',
        r'open\s*\(',
        r'file\s*\(',
        r'input\s*\(',
        r'raw_input\s*\(',
        r'compile\s*\(',
        r'reload\s*\(',
        r'\.\./',
        r'subprocess',
        r'os\.',
        r'sys\.',
        r'shutil\.',
        r'pickle\.',
        r'marshal\.',
        r'imp\.',
        r'importlib\.',
    ]

    def __init__(self, name: str = None):
        super().__init__(
            name or "security_check",
            "Input contains potentially dangerous content"
        )

    def validate(self, value: Any) -> bool:
        if not isinstance(value, str):
            return True  # Only validate strings

        value_lower = value.lower()

        for pattern in self.DANGEROUS_PATTERNS:
            if re.search(pattern, value_lower):
                logger.warning(f"Security rule triggered by pattern: {pattern}")
                return False

        return True


class Validator:
    """Main validator class that applies multiple rules."""

    def __init__(self, rules: List[ValidationRule] = None):
        self.rules = rules or []

    def add_rule(self, rule: ValidationRule) -> None:
        """Add a validation rule."""
        self.rules.append(rule)

    def validate(self, value: Any, field_name: str = "value") -> None:
        """
        Validate a value against all rules.

        Args:
            value: Value to validate
            field_name: Name of the field being validated

        Raises:
            ValidationError: If validation fails
            SecurityError: If security validation fails
        """
        for rule in self.rules:
            if not rule.validate(value):
                error_msg = f"Validation failed for field '{field_name}': {rule.error_message}"

                if isinstance(rule, SecurityRule):
                    raise SecurityError(error_msg, violation_type="input_validation")
                else:
                    raise ValidationError(error_msg, field=field_name, value=value)

    def is_valid(self, value: Any) -> bool:
        """
        Check if a value is valid without raising exceptions.

        Args:
            value: Value to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            self.validate(value)
            return True
        except (ValidationError, SecurityError):
            return False


class ConfigValidator:
    """Validator for configuration dictionaries."""

    def __init__(self):
        self.field_validators: Dict[str, Validator] = {}
        self.required_fields: List[str] = []

    def add_field(self, field_name: str, validator: Validator, required: bool = False) -> None:
        """
        Add a field validator.

        Args:
            field_name: Name of the configuration field
            validator: Validator for this field
            required: Whether this field is required
        """
        self.field_validators[field_name] = validator
        if required:
            self.required_fields.append(field_name)

    def validate_config(self, config: Dict[str, Any]) -> None:
        """
        Validate a configuration dictionary.

        Args:
            config: Configuration to validate

        Raises:
            ValidationError: If validation fails
        """
        # Check required fields
        for field in self.required_fields:
            if field not in config:
                raise ValidationError(f"Required field '{field}' is missing", field=field)

        # Validate each field
        for field_name, value in config.items():
            if field_name in self.field_validators:
                self.field_validators[field_name].validate(value, field_name)

    def is_valid_config(self, config: Dict[str, Any]) -> bool:
        """
        Check if configuration is valid without raising exceptions.

        Args:
            config: Configuration to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            self.validate_config(config)
            return True
        except (ValidationError, SecurityError):
            return False


def create_training_config_validator() -> ConfigValidator:
    """Create a validator for training configuration."""
    validator = ConfigValidator()

    # Learning rate validation
    lr_validator = Validator([
        TypeRule(float),
        RangeRule(1e-8, 1.0)
    ])
    validator.add_field("learning_rate", lr_validator, required=True)

    # Batch size validation
    batch_validator = Validator([
        TypeRule(int),
        RangeRule(1, 1024)
    ])
    validator.add_field("batch_size", batch_validator, required=True)

    # Epochs validation
    epochs_validator = Validator([
        TypeRule(int),
        RangeRule(1, 10000)
    ])
    validator.add_field("epochs", epochs_validator, required=True)

    # Model path validation
    model_path_validator = Validator([
        TypeRule(str),
        LengthRule(1, 500),
        SecurityRule()
    ])
    validator.add_field("model_path", model_path_validator, required=True)

    # Optional fields
    optimizer_validator = Validator([
        TypeRule(str),
        ChoiceRule(["adam", "sgd", "adamw", "rmsprop"])
    ])
    validator.add_field("optimizer", optimizer_validator, required=False)

    return validator


def create_code_execution_validator() -> Validator:
    """Create a validator for code execution inputs."""
    return Validator([
        TypeRule(str),
        LengthRule(1, 50000),  # Max 50k characters
        SecurityRule()
    ])


def create_file_path_validator(must_exist: bool = False) -> Validator:
    """Create a validator for file paths."""
    return Validator([
        TypeRule(str),
        LengthRule(1, 1000),
        PathRule(must_exist=must_exist),
        SecurityRule()
    ])


def sanitize_string(input_str: str, max_length: int = 1000) -> str:
    """
    Sanitize a string input by removing dangerous content.

    Args:
        input_str: String to sanitize
        max_length: Maximum allowed length

    Returns:
        Sanitized string
    """
    if not isinstance(input_str, str):
        return str(input_str)

    # Truncate if too long
    if len(input_str) > max_length:
        input_str = input_str[:max_length]

    # Remove null bytes and control characters
    input_str = ''.join(char for char in input_str if ord(char) >= 32 or char in '\t\n\r')

    # Remove potentially dangerous patterns
    dangerous_patterns = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'vbscript:',
        r'onload\s*=',
        r'onerror\s*=',
        r'onclick\s*=',
    ]

    for pattern in dangerous_patterns:
        input_str = re.sub(pattern, '', input_str, flags=re.IGNORECASE | re.DOTALL)

    return input_str


def validate_and_sanitize_config(config: Dict[str, Any], validator: ConfigValidator) -> Dict[str, Any]:
    """
    Validate and sanitize a configuration dictionary.

    Args:
        config: Configuration to validate and sanitize
        validator: Validator to use

    Returns:
        Sanitized configuration

    Raises:
        ValidationError: If validation fails
    """
    # First validate the config
    validator.validate_config(config)

    # Then sanitize string values
    sanitized_config = {}
    for key, value in config.items():
        if isinstance(value, str):
            sanitized_config[key] = sanitize_string(value)
        elif isinstance(value, dict):
            # Recursively sanitize nested dictionaries
            sanitized_config[key] = {
                k: sanitize_string(v) if isinstance(v, str) else v
                for k, v in value.items()
            }
        else:
            sanitized_config[key] = value

    return sanitized_config