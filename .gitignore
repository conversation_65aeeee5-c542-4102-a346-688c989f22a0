# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
# models/
models/
latex2sympy/

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.settings/

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Distribution / packaging
.Python
*.manifest
*.spec

# Logs and databases
*.log
*.sqlite
*.db

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Environments
.env
.env.*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
wandb/
outputs/
checkpoints/
logs/ 
data/math/
eval_logs/*
data/math/*
data/orz*/*
data/simplerl/*
data/big_math/*
data/deepscaler/*
data/cruxeval/*
data/code_reason/*
upload_model_hf.py
error*.json

slurm_outputs/*
*.out
code_generation_lite/*
v*_*/*
data/dapo*/*
uploads/*
evalplus_results/*
evaluation/code_eval/coding/LiveCodeBench/*/lcb