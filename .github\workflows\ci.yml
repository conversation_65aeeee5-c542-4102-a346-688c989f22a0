name: Absolute Zero CI

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  test-installation:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install torch transformers==4.47.1 vllm==0.7.3 debugpy
      - name: Run minimal installation test (CPU only)
        run: |
          python test_installation_minimal.py
        env:
          CUDA_VISIBLE_DEVICES: ''
      - name: Show Python version
        run: python --version
      - name: Show pip freeze
        run: pip freeze
