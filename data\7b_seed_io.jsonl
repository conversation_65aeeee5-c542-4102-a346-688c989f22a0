{"snippet": "def f(a):\n    return a", "input": "\"Hello world\"", "output": "\"Hello world\"", "imports": [], "original_snippet": "def f(a):\n    return a", "composite_functions": []}
{"snippet": "def f(experience: str):\n    applicable_experience = experience.replace(' ', '').lower()\n    if applicable_experience == 'instructor':\n        return 'instructor'\n    elif applicable_experience == 'juniordeveloper':\n        return 'junior developer'\n    elif applicable_experience == 'intern':\n        return 'intern'\n    else:\n        return 'we cant decide'", "input": "\" I am an JUNIOR developer\"", "output": "'we cant decide'", "imports": [], "original_snippet": "def f(experience: str):\n    applicable_experience = experience.replace(' ', '').lower()\n    if applicable_experience == 'instructor':\n        return 'instructor'\n    elif applicable_experience == 'juniordeveloper':\n        return 'junior developer'\n    elif applicable_experience == 'intern':\n        return 'intern'\n    else:\n        return 'we cant decide'", "composite_functions": []}
{"snippet": "def f(start_state):\n    depth = 3\n    state = [start_state]\n    for i in range(depth):\n        state = [state] * len(state) * depth\n    return str(state)", "input": "[1, 2, 3]", "output": "'[[[[[1, 2, 3]], [[1, 2, 3]], [[1, 2, 3]]], [[[1, 2, 3]], [[1, 2, 3]], [[1, 2, 3]]], [[[1, 2, 3]], [[1, 2, 3]], [[1, 2, 3]]], [[[1, 2, 3]], [[1, 2, 3]], [[1, 2, 3]]], [[[1, 2, 3]], [[1, 2, 3]], [[1, 2...2, 3]], [[1, 2, 3]], [[1, 2, 3]]], [[[1, 2, 3]], [[1, 2, 3]], [[1, 2, 3]]], [[[1, 2, 3]], [[1, 2, 3]], [[1, 2, 3]]], [[[1, 2, 3]], [[1, 2, 3]], [[1, 2, 3]]], [[[1, 2, 3]], [[1, 2, 3]], [[1, 2, 3]]]]]'", "imports": [], "original_snippet": "def f(start_state):\n    depth = 3\n    state = [start_state]\n    for i in range(depth):\n        state = [state] * len(state) * depth\n    return str(state)", "composite_functions": []}
{"snippet": "def f(binary_list):\n    output = []\n    for i in range(len(binary_list)):\n        b = binary_list[i]\n        output_bit = 1 - b\n        output.append(output_bit)\n    return output", "input": "[0, 1, 1, 1, 1, 1, 1, 1]", "output": "[1, 0, 0, 0, 0, 0, 0, 0]", "imports": [], "original_snippet": "def f(binary_list):\n    output = []\n    for i in range(len(binary_list)):\n        b = binary_list[i]\n        output_bit = 1 - b\n        output.append(output_bit)\n    return output", "composite_functions": []}
{"snippet": "def f(string1: str, string2: str):\n    words = string1.split()\n    target_words = ['are', 'happy', 'worth', 'unique']\n    i = 0\n    while i < len(words):\n        if words[i] in target_words:\n            return string1\n        i += 1\n    return string2", "input": "\"Hello, we are happy people\", \"I am worth having unique sentences.\"", "output": "'Hello, we are happy people'", "imports": [], "original_snippet": "def f(string1: str, string2: str):\n    words = string1.split()\n    target_words = ['are', 'happy', 'worth', 'unique']\n    i = 0\n    while i < len(words):\n        if words[i] in target_words:\n            return string1\n        i += 1\n    return string2", "composite_functions": []}
{"snippet": "def f(a):\n    num = int(a, 2)\n    n = len(a)\n    fib_seq = []\n    (a, b) = (0, 1)\n    for _ in range(n):\n        fib_seq.append(a)\n        (a, b) = (b, a + b)\n    result = num ^ fib_seq[-1]\n    return result", "input": "\"10101\"", "output": "22", "imports": [], "original_snippet": "def f(a):\n    num = int(a, 2)\n    n = len(a)\n    fib_seq = []\n    (a, b) = (0, 1)\n    for _ in range(n):\n        fib_seq.append(a)\n        (a, b) = (b, a + b)\n    result = num ^ fib_seq[-1]\n    return result", "composite_functions": []}
{"snippet": "def f(nums: list):\n    largest_even = None\n    for num in nums:\n        if num % 2 == 0 and (largest_even is None or num > largest_even):\n            largest_even = num\n    return largest_even", "input": "[3, 5, 2, 9, 6]", "output": "6", "imports": [], "original_snippet": "def f(nums: list):\n    largest_even = None\n    for num in nums:\n        if num % 2 == 0 and (largest_even is None or num > largest_even):\n            largest_even = num\n    return largest_even", "composite_functions": []}
{"snippet": "def f(dict_list: list):\n    main_list = []\n    for elem in dict_list:\n        sub_list = []\n        for value in elem.values():\n            if isinstance(value, dict):\n                for sub_value in value.values():\n                    sub_list.append(sub_value)\n            else:\n                sub_list.append(value)\n        main_list.append(sub_list)\n    return main_list", "input": "[{'a': {'b': {'c': 1}}}, {'d': {'e': {'f': 2}}}]", "output": "[[{'c': 1}], [{'f': 2}]]", "imports": [], "original_snippet": "def f(dict_list: list):\n    main_list = []\n    for elem in dict_list:\n        sub_list = []\n        for value in elem.values():\n            if isinstance(value, dict):\n                for sub_value in value.values():\n                    sub_list.append(sub_value)\n            else:\n                sub_list.append(value)\n        main_list.append(sub_list)\n    return main_list", "composite_functions": []}
{"snippet": "def f(text: str):\n    dict1 = {'P1': text}\n    dict2 = {'P2': 'Hello World!'}\n    dict3 = {'P3': dict1, 'P4': dict2}\n    dict3['P3'] = dict(dict3['P4'])\n    del dict3['P4']\n    dict3['P3']['P2'] = text\n    res = []\n    for value in dict3.values():\n        if isinstance(value, str):\n            res.append(value)\n        elif isinstance(value, dict) and 'P2' in value:\n            res.append(value['P2'])\n    formatted_text = ''.join(res)\n    if len(dict1['P1']) % 3 == 0:\n        formatted_text = formatted_text[::2]\n    elif len(dict1['P1']) % 3 == 1:\n        formatted_text = ''.join(reversed(formatted_text))\n    else:\n        formatted_text = formatted_text[::-2]\n    return formatted_text", "input": "\"Hello!\"", "output": "'Hlo'", "imports": [], "original_snippet": "def f(text: str):\n    dict1 = {'P1': text}\n    dict2 = {'P2': 'Hello World!'}\n    dict3 = {'P3': dict1, 'P4': dict2}\n    dict3['P3'] = dict(dict3['P4'])\n    del dict3['P4']\n    dict3['P3']['P2'] = text\n    res = []\n    for value in dict3.values():\n        if isinstance(value, str):\n            res.append(value)\n        elif isinstance(value, dict) and 'P2' in value:\n            res.append(value['P2'])\n    formatted_text = ''.join(res)\n    if len(dict1['P1']) % 3 == 0:\n        formatted_text = formatted_text[::2]\n    elif len(dict1['P1']) % 3 == 1:\n        formatted_text = ''.join(reversed(formatted_text))\n    else:\n        formatted_text = formatted_text[::-2]\n    return formatted_text", "composite_functions": []}
{"snippet": "def f(s):\n    reversed_str = ''\n    len_s = len(s)\n    for i in range(len_s - 1, -1, -1):\n        reversed_str += s[i]\n    return reversed_str", "input": "\"challengeString\"", "output": "'gnirtSegnellahc'", "imports": [], "original_snippet": "def f(s):\n    reversed_str = ''\n    len_s = len(s)\n    for i in range(len_s - 1, -1, -1):\n        reversed_str += s[i]\n    return reversed_str", "composite_functions": []}
{"snippet": "def f(num: int):\n    num_squared = num ** 2\n    num_squared_root = num_squared * num\n    result = num_squared_root * 2\n    return result", "input": "10", "output": "2000", "imports": [], "original_snippet": "def f(num: int):\n    num_squared = num ** 2\n    num_squared_root = num_squared * num\n    result = num_squared_root * 2\n    return result", "composite_functions": []}
{"snippet": "def f(input_string: str):\n    alphabet = {'a': 'b', 'b': 'c', 'c': 'd', 'd': 'e', 'e': 'f', 'f': 'g', 'g': 'h', 'h': 'i', 'i': 'j', 'j': 'k', 'k': 'l', 'l': 'm', 'm': 'n', 'n': 'o', 'o': 'p', 'p': 'q', 'q': 'r', 'r': 's', 's': 't', 't': 'u', 'u': 'v', 'v': 'w', 'w': 'x', 'x': 'y', 'y': 'z', 'z': 'a'}\n    output_string = []\n    for word in input_string.split():\n        new_word = ''\n        for char in word:\n            if char.lower() in alphabet:\n                new_char = alphabet[char.lower()]\n            else:\n                new_char = char\n            if char.isupper():\n                new_word += new_char.upper()\n            else:\n                new_word += new_char\n        output_string.append(new_word)\n    return ' '.join(output_string)", "input": "\"The quick brown fox jumps over the lazy dog.\"", "output": "'Uif rvjdl cspxo gpy kvnqt pwfs uif mbaz eph.'", "imports": [], "original_snippet": "def f(input_string: str):\n    alphabet = {'a': 'b', 'b': 'c', 'c': 'd', 'd': 'e', 'e': 'f', 'f': 'g', 'g': 'h', 'h': 'i', 'i': 'j', 'j': 'k', 'k': 'l', 'l': 'm', 'm': 'n', 'n': 'o', 'o': 'p', 'p': 'q', 'q': 'r', 'r': 's', 's': 't', 't': 'u', 'u': 'v', 'v': 'w', 'w': 'x', 'x': 'y', 'y': 'z', 'z': 'a'}\n    output_string = []\n    for word in input_string.split():\n        new_word = ''\n        for char in word:\n            if char.lower() in alphabet:\n                new_char = alphabet[char.lower()]\n            else:\n                new_char = char\n            if char.isupper():\n                new_word += new_char.upper()\n            else:\n                new_word += new_char\n        output_string.append(new_word)\n    return ' '.join(output_string)", "composite_functions": []}
{"snippet": "def f(digits_list: list):\n    count = 0\n    i = 0\n    while i < len(digits_list):\n        digit = digits_list[i]\n        is_prime = all((digit % n != 0 for n in range(2, int(digit ** 0.5) + 1)))\n        is_perfect_square = int(digit ** 0.5) ** 2 == digit\n        if digit == 2 and is_prime and is_perfect_square:\n            count += 1\n        i += 1\n    return count", "input": "[2, 3, 4, 5, 6, 7, 8]", "output": "0", "imports": [], "original_snippet": "def f(digits_list: list):\n    count = 0\n    i = 0\n    while i < len(digits_list):\n        digit = digits_list[i]\n        is_prime = all((digit % n != 0 for n in range(2, int(digit ** 0.5) + 1)))\n        is_perfect_square = int(digit ** 0.5) ** 2 == digit\n        if digit == 2 and is_prime and is_perfect_square:\n            count += 1\n        i += 1\n    return count", "composite_functions": []}
{"snippet": "def f(nested_dicts):\n    sum = 0\n    for emp in nested_dicts:\n        if emp.get('salary', 0) > 150000:\n            sum += emp.get('salary', 0)\n    return sum", "input": "[{'name': 'John', 'salary': 160000, 'department': 'Engineering'},\n{'name': 'Alice', 'salary': 140000, 'department': 'Marketing'},\n{'name': 'Bob', 'salary': 180000, 'department': 'Sales'},\n{'name': 'David', 'salary': 120000, 'department': 'Human Resources'},\n{'name': 'Sara', 'salary': 200000, 'department': 'Management'}]", "output": "540000", "imports": [], "original_snippet": "def f(nested_dicts):\n    sum = 0\n    for emp in nested_dicts:\n        if emp.get('salary', 0) > 150000:\n            sum += emp.get('salary', 0)\n    return sum", "composite_functions": []}
{"snippet": "def f(a: set):\n    prime_numbers = [2, 3, 5, 7, 11]\n    count = 0\n    for num in a:\n        if num in prime_numbers and num % 2 != 0:\n            count += 1\n    return 'Even' if count % 2 == 0 else 'Odd'", "input": "{7, 11, 17, 19, 23, 3}", "output": "'Odd'", "imports": [], "original_snippet": "def f(a: set):\n    prime_numbers = [2, 3, 5, 7, 11]\n    count = 0\n    for num in a:\n        if num in prime_numbers and num % 2 != 0:\n            count += 1\n    return 'Even' if count % 2 == 0 else 'Odd'", "composite_functions": []}
{"snippet": "def f(numbers):\n    result = []\n    for number in numbers:\n        result.append(number ** 2)\n    return result", "input": "[1, 2, 3, 4, 5]", "output": "[1, 4, 9, 16, 25]", "imports": [], "original_snippet": "def f(numbers):\n    result = []\n    for number in numbers:\n        result.append(number ** 2)\n    return result", "composite_functions": []}
{"snippet": "def f(x: int, y: int):\n    result = []\n    y_factorial = 1\n    if y != 0:\n        for i in range(1, y + 1):\n            y_factorial *= i\n        result.append(x ** y_factorial)\n        result.append(y_factorial * x)\n    elif y == 0:\n        result.append(x ** 0)\n    return result", "input": "5, 3", "output": "[15625, 30]", "imports": [], "original_snippet": "def f(x: int, y: int):\n    result = []\n    y_factorial = 1\n    if y != 0:\n        for i in range(1, y + 1):\n            y_factorial *= i\n        result.append(x ** y_factorial)\n        result.append(y_factorial * x)\n    elif y == 0:\n        result.append(x ** 0)\n    return result", "composite_functions": []}
{"snippet": "def f(numbers: list, divisor: int=5):\n    result = []\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            if (numbers[i] + numbers[j]) % divisor == 0:\n                result.append([numbers[i], numbers[j]])\n    return result", "input": "[10, 12, 15, 17, 20]", "output": "[[10, 15], [10, 20], [15, 20]]", "imports": [], "original_snippet": "def f(numbers: list, divisor: int=5):\n    result = []\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            if (numbers[i] + numbers[j]) % divisor == 0:\n                result.append([numbers[i], numbers[j]])\n    return result", "composite_functions": []}
{"snippet": "def f(place_list: list):\n    unique_places = set()\n    for place in place_list:\n        if len(place) != 0:\n            unique_places.add(place)\n    return len(unique_places)", "input": "['home', 'home', 'work', '', 'work', 'home']", "output": "2", "imports": [], "original_snippet": "def f(place_list: list):\n    unique_places = set()\n    for place in place_list:\n        if len(place) != 0:\n            unique_places.add(place)\n    return len(unique_places)", "composite_functions": []}
{"snippet": "def f(lists: list):\n    total = 1\n    for outer_list in lists:\n        for inner_list in outer_list:\n            for num in inner_list:\n                if isinstance(num, int):\n                    total *= num\n    return total", "input": "[[[1,2,3], [4,5,6]], [[7,8,9], [10,11,12]]]", "output": "479001600", "imports": [], "original_snippet": "def f(lists: list):\n    total = 1\n    for outer_list in lists:\n        for inner_list in outer_list:\n            for num in inner_list:\n                if isinstance(num, int):\n                    total *= num\n    return total", "composite_functions": []}
{"snippet": "def f(start_state):\n    depth = 3\n    state = [start_state]\n    for i in range(depth):\n        state = [[[target] for j in range(len(state) * 2)] for target in state]\n    state = state[0][0]\n    state_as_string = str(state).replace('[', '{').replace(']', '}').replace(\"'\", '').replace(' ', '')\n    return state_as_string", "input": "[5]", "output": "'{{{{{{5}},{{5}}}},{{{{5}},{{5}}}}}}'", "imports": [], "original_snippet": "def f(start_state):\n    depth = 3\n    state = [start_state]\n    for i in range(depth):\n        state = [[[target] for j in range(len(state) * 2)] for target in state]\n    state = state[0][0]\n    state_as_string = str(state).replace('[', '{').replace(']', '}').replace(\"'\", '').replace(' ', '')\n    return state_as_string", "composite_functions": []}
{"snippet": "def f(s: str, k: int):\n    if len(s) < k or k <= 0:\n        return ''\n    substring = s[:k]\n    processed_substring = [ord(char) for char in substring]\n    for i in range(len(processed_substring)):\n        processed_substring[i] = processed_substring[i] + (i + 1) // 2\n    return ''.join([chr(num) if num < 256 else '' for num in processed_substring])", "input": "'abcdefg', 3", "output": "'acd'", "imports": [], "original_snippet": "def f(s: str, k: int):\n    if len(s) < k or k <= 0:\n        return ''\n    substring = s[:k]\n    processed_substring = [ord(char) for char in substring]\n    for i in range(len(processed_substring)):\n        processed_substring[i] = processed_substring[i] + (i + 1) // 2\n    return ''.join([chr(num) if num < 256 else '' for num in processed_substring])", "composite_functions": []}
{"snippet": "def f(my_string):\n    my_string_encoded = my_string.encode('utf-8')\n    my_string_encoded = my_string_encoded * len(my_string_encoded)\n    my_string_decode = my_string_encoded.decode('utf-8')\n    return my_string_decode", "input": "'Howdy! How are you doing?'", "output": "'Howdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing...owdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing?Howdy! How are you doing?'", "imports": [], "original_snippet": "def f(my_string):\n    my_string_encoded = my_string.encode('utf-8')\n    my_string_encoded = my_string_encoded * len(my_string_encoded)\n    my_string_decode = my_string_encoded.decode('utf-8')\n    return my_string_decode", "composite_functions": []}
{"snippet": "def f(a):\n    if a <= 1:\n        return\n    numbers = set(range(1, a + 1))\n    count = 0\n    i = 2\n    while i <= a / 2:\n        if i not in numbers:\n            i += 1\n            continue\n        j = 2 * i\n        while j <= a:\n            if j in numbers:\n                numbers.remove(j)\n            j += i\n        i += 1\n    for number in numbers:\n        if number > 13:\n            count += 1\n    if count % 2 == 0:\n        return True\n    else:\n        return False", "input": "12", "output": "True", "imports": [], "original_snippet": "def f(a):\n    if a <= 1:\n        return\n    numbers = set(range(1, a + 1))\n    count = 0\n    i = 2\n    while i <= a / 2:\n        if i not in numbers:\n            i += 1\n            continue\n        j = 2 * i\n        while j <= a:\n            if j in numbers:\n                numbers.remove(j)\n            j += i\n        i += 1\n    for number in numbers:\n        if number > 13:\n            count += 1\n    if count % 2 == 0:\n        return True\n    else:\n        return False", "composite_functions": []}
{"snippet": "import numpy as np\ndef f(diameter):\n    radius = float(diameter) * 0.5\n    circle_area = round(np.pi * radius ** 2, 4)\n    return f'The area of the circle with diameter {diameter} is {circle_area}.'", "input": "20.5", "output": "'The area of the circle with diameter 20.5 is 330.0636.'", "imports": ["import numpy as np"], "original_snippet": "import numpy as np\ndef f(diameter):\n    radius = float(diameter) * 0.5\n    circle_area = round(np.pi * radius ** 2, 4)\n    return f'The area of the circle with diameter {diameter} is {circle_area}.'", "composite_functions": []}
{"snippet": "def f(input_list: list):\n    even_numbers = [num for num in input_list if num % 2 == 0]\n    return sorted(set(sorted(even_numbers, reverse=True)))", "input": "[2, 4, 6, 8]", "output": "[2, 4, 6, 8]", "imports": [], "original_snippet": "def f(input_list: list):\n    even_numbers = [num for num in input_list if num % 2 == 0]\n    return sorted(set(sorted(even_numbers, reverse=True)))", "composite_functions": []}
{"snippet": "def f(input_str: str) -> str:\n    altered_str = ''\n    input_len = len(input_str)\n    for (idx, char) in enumerate(input_str):\n        if idx % 2 != 0 and idx / input_len % 2 == 0:\n            altered_str += input_str[idx].upper()\n        else:\n            altered_str += input_str[idx]\n    return altered_str", "input": "'TestStringWithAMixedCaseAndPrioritiesOfAlterations'", "output": "'TestStringWithAMixedCaseAndPrioritiesOfAlterations'", "imports": [], "original_snippet": "def f(input_str: str) -> str:\n    altered_str = ''\n    input_len = len(input_str)\n    for (idx, char) in enumerate(input_str):\n        if idx % 2 != 0 and idx / input_len % 2 == 0:\n            altered_str += input_str[idx].upper()\n        else:\n            altered_str += input_str[idx]\n    return altered_str", "composite_functions": []}
{"snippet": "def f(encrypted_message: str):\n    decoded_message = ''\n    repeat_count = 0\n    shift_direction = 1\n    shift_value = 0\n    for char in encrypted_message:\n        if char.isalpha():\n            if repeat_count == 0:\n                decoded_char_position = ord(char) - 65\n                decoded_char_position += shift_value\n                decoded_char_position %= 26\n                decoded_char = chr(decoded_char_position + 65)\n            else:\n                decoded_message += decoded_message[-1]\n                repeat_count -= 1\n                continue\n        else:\n            decoded_message += char\n            repeat_count += 1\n            continue\n        decoded_message += decoded_char\n        shift_value += 1\n        if repeat_count == 0:\n            shift_value %= 26\n    return decoded_message", "input": "'Front Udohiv Sbpodjk'", "output": "'FYWWD  OAUWK  RGGWDF'", "imports": [], "original_snippet": "def f(encrypted_message: str):\n    decoded_message = ''\n    repeat_count = 0\n    shift_direction = 1\n    shift_value = 0\n    for char in encrypted_message:\n        if char.isalpha():\n            if repeat_count == 0:\n                decoded_char_position = ord(char) - 65\n                decoded_char_position += shift_value\n                decoded_char_position %= 26\n                decoded_char = chr(decoded_char_position + 65)\n            else:\n                decoded_message += decoded_message[-1]\n                repeat_count -= 1\n                continue\n        else:\n            decoded_message += char\n            repeat_count += 1\n            continue\n        decoded_message += decoded_char\n        shift_value += 1\n        if repeat_count == 0:\n            shift_value %= 26\n    return decoded_message", "composite_functions": []}
{"snippet": "def f(dividend: int) -> int:\n    count = 0\n    for potential_divisor in range(1, dividend + 1):\n        if dividend % potential_divisor == 0:\n            count += 1\n    return count", "input": "6", "output": "4", "imports": [], "original_snippet": "def f(dividend: int) -> int:\n    count = 0\n    for potential_divisor in range(1, dividend + 1):\n        if dividend % potential_divisor == 0:\n            count += 1\n    return count", "composite_functions": []}
{"snippet": "def f(list1, list2):\n    result = []\n    i = 0\n    while i < len(list1):\n        if i % 2 == 0:\n            result.append(list1[i] ** 3)\n        else:\n            result.append(list1[i] ** 2)\n        i += 1\n    return result", "input": "[2, 3, 4, 5], [6, 7, 8, 9]", "output": "[8, 9, 64, 25]", "imports": [], "original_snippet": "def f(list1, list2):\n    result = []\n    i = 0\n    while i < len(list1):\n        if i % 2 == 0:\n            result.append(list1[i] ** 3)\n        else:\n            result.append(list1[i] ** 2)\n        i += 1\n    return result", "composite_functions": []}
{"snippet": "def f(lst):\n    mid = len(lst) // 2\n    mid_num = None\n    for i in range(len(lst)):\n        if lst[i] % 2 != 0:\n            return lst[i] if mid_num == None else mid_num if mid_num % 2 == 0 else lst[i]\n        elif i == mid and lst[mid] % 2 == 0:\n            mid_num = lst[mid]", "input": "[5, 6, 9, 8, 10]", "output": "5", "imports": [], "original_snippet": "def f(lst):\n    mid = len(lst) // 2\n    mid_num = None\n    for i in range(len(lst)):\n        if lst[i] % 2 != 0:\n            return lst[i] if mid_num == None else mid_num if mid_num % 2 == 0 else lst[i]\n        elif i == mid and lst[mid] % 2 == 0:\n            mid_num = lst[mid]", "composite_functions": []}
{"snippet": "def f(numbers):\n    numbers_stack = []\n    target_sum_dict = {0: -1}\n    for (index, num) in enumerate(numbers):\n        target_sum = num - index\n        before_index = target_sum_dict.get(target_sum)\n        if before_index is not None:\n            return f'{before_index}, {index}'\n        target_sum_dict[num] = index", "input": "[5, 2, 5, 7, 6, 3, 1]", "output": "'1, 4'", "imports": [], "original_snippet": "def f(numbers):\n    numbers_stack = []\n    target_sum_dict = {0: -1}\n    for (index, num) in enumerate(numbers):\n        target_sum = num - index\n        before_index = target_sum_dict.get(target_sum)\n        if before_index is not None:\n            return f'{before_index}, {index}'\n        target_sum_dict[num] = index", "composite_functions": []}
{"snippet": "def f(matrix):\n    flat_list = []\n    for sub_list in matrix:\n        flat_list.extend(sub_list)\n    if not flat_list:\n        return []\n    if not all((isinstance(item, (int, float)) for item in flat_list)):\n        raise ValueError('List must contain only integers or floats.')\n    flat_list.sort()\n    return flat_list", "input": "[[1, 2, 3], [4, 5], [6]]", "output": "[1, 2, 3, 4, 5, 6]", "imports": [], "original_snippet": "def f(matrix):\n    flat_list = []\n    for sub_list in matrix:\n        flat_list.extend(sub_list)\n    if not flat_list:\n        return []\n    if not all((isinstance(item, (int, float)) for item in flat_list)):\n        raise ValueError('List must contain only integers or floats.')\n    flat_list.sort()\n    return flat_list", "composite_functions": []}
{"snippet": "def f(lst):\n    output = []\n    for dict in lst:\n        if dict.get('integer', 0) > 100:\n            output.append(dict.get('string', ''))\n    return output", "input": "[{'string': 'John', 'integer': 150}, {'string': 'Alice', 'integer': 50}, {'string': 'Bob', 'integer': 250}]", "output": "['John', 'Bob']", "imports": [], "original_snippet": "def f(lst):\n    output = []\n    for dict in lst:\n        if dict.get('integer', 0) > 100:\n            output.append(dict.get('string', ''))\n    return output", "composite_functions": []}
{"snippet": "def f(list_of_strings: list[str]):\n    cleaned_string = ''.join(list_of_strings)\n    string_count = dict()\n    current_char = cleaned_string[0]\n    current_count = 1\n    for char in cleaned_string[1:]:\n        if current_char == char:\n            current_count += 1\n        else:\n            string_count[current_char] = current_count\n            current_char = char\n            current_count = 1\n    string_count[current_char] = current_count\n    return string_count", "input": "list_of_strings = ['a', 'a', 'b', 'c', 'b', 'd', 'e', 'c', 'b', 'a']", "output": "{'a': 1, 'b': 1, 'c': 1, 'd': 1, 'e': 1}", "imports": [], "original_snippet": "def f(list_of_strings: list[str]):\n    cleaned_string = ''.join(list_of_strings)\n    string_count = dict()\n    current_char = cleaned_string[0]\n    current_count = 1\n    for char in cleaned_string[1:]:\n        if current_char == char:\n            current_count += 1\n        else:\n            string_count[current_char] = current_count\n            current_char = char\n            current_count = 1\n    string_count[current_char] = current_count\n    return string_count", "composite_functions": []}
{"snippet": "from collections import Counter\ndef f(lst: list) -> tuple:\n    item_counter = Counter(lst)\n    common_items = {}\n    uncommon_items = {}\n    common_items = {k: v for (k, v) in item_counter.items() if item_counter[k] > 1}\n    for (item, count) in item_counter.items():\n        if item not in common_items:\n            for num in item_counter:\n                if count + item_counter[num] != 0 and item != num:\n                    uncommon_items[item] = item_counter[num]\n                    break\n    uncommon_items = {k: v for (k, v) in uncommon_items.items() if v != 0}\n    return (common_items, uncommon_items)", "input": "[17, 17, 12, 14, 14, 15, 15, 19, 20, 21, 21]", "output": "({17: 2, 14: 2, 15: 2, 21: 2}, {12: 2, 19: 2, 20: 2})", "imports": ["from collections import Counter"], "original_snippet": "from collections import Counter\ndef f(lst: list) -> tuple:\n    item_counter = Counter(lst)\n    common_items = {}\n    uncommon_items = {}\n    common_items = {k: v for (k, v) in item_counter.items() if item_counter[k] > 1}\n    for (item, count) in item_counter.items():\n        if item not in common_items:\n            for num in item_counter:\n                if count + item_counter[num] != 0 and item != num:\n                    uncommon_items[item] = item_counter[num]\n                    break\n    uncommon_items = {k: v for (k, v) in uncommon_items.items() if v != 0}\n    return (common_items, uncommon_items)", "composite_functions": []}
{"snippet": "from collections import deque\ndef f(start_sequence, stop_sequence):\n    deq = deque()\n    for num in range(start_sequence, stop_sequence + 1):\n        deq.appendleft(num)\n    while len(deq) > 1:\n        deq.popleft()\n        deq.appendleft(deq[-1])\n        deq.pop()\n    return str(deq[0])", "input": "11, 20", "output": "'19'", "imports": ["from collections import deque"], "original_snippet": "from collections import deque\ndef f(start_sequence, stop_sequence):\n    deq = deque()\n    for num in range(start_sequence, stop_sequence + 1):\n        deq.appendleft(num)\n    while len(deq) > 1:\n        deq.popleft()\n        deq.appendleft(deq[-1])\n        deq.pop()\n    return str(deq[0])", "composite_functions": []}
{"snippet": "def f(input_array: list):\n    product = 1\n    for item in input_array:\n        if isinstance(item, int):\n            product *= item\n        elif isinstance(item, str):\n            product += len(item)\n    return product", "input": "[2, 'hello', 3, 'world']", "output": "26", "imports": [], "original_snippet": "def f(input_array: list):\n    product = 1\n    for item in input_array:\n        if isinstance(item, int):\n            product *= item\n        elif isinstance(item, str):\n            product += len(item)\n    return product", "composite_functions": []}
{"snippet": "def f(dividend: int):\n    total_count = 0\n    for current_divisor in range(2, dividend + 1):\n        is_divide = dividend % current_divisor == 0\n        if is_divide:\n            prime_count = 0\n            for prime_candidate_number in range(2, current_divisor):\n                if current_divisor % prime_candidate_number == 0:\n                    break\n            else:\n                prime_count = 1\n            if prime_count == 1 and current_divisor % 2 == 0:\n                total_count += 1\n    return total_count", "input": "6", "output": "1", "imports": [], "original_snippet": "def f(dividend: int):\n    total_count = 0\n    for current_divisor in range(2, dividend + 1):\n        is_divide = dividend % current_divisor == 0\n        if is_divide:\n            prime_count = 0\n            for prime_candidate_number in range(2, current_divisor):\n                if current_divisor % prime_candidate_number == 0:\n                    break\n            else:\n                prime_count = 1\n            if prime_count == 1 and current_divisor % 2 == 0:\n                total_count += 1\n    return total_count", "composite_functions": []}
{"snippet": "def f(lst: list) -> dict:\n    num_dict = {num: lst.count(num) for num in lst}\n    sorted_nums = sorted(num_dict.items(), key=lambda x: x[1], reverse=True)\n    result = {}\n    for num in sorted_nums:\n        if num[1] % 2 == 0:\n            result[num[0]] = num[1] ** 2\n        else:\n            result[num[0]] = num[1] ** 1\n    return result", "input": "[-2, -2, -2, -2, 0, 0, 0, 5, 7, 7]", "output": "{-2: 16, 0: 3, 7: 4, 5: 1}", "imports": [], "original_snippet": "def f(lst: list) -> dict:\n    num_dict = {num: lst.count(num) for num in lst}\n    sorted_nums = sorted(num_dict.items(), key=lambda x: x[1], reverse=True)\n    result = {}\n    for num in sorted_nums:\n        if num[1] % 2 == 0:\n            result[num[0]] = num[1] ** 2\n        else:\n            result[num[0]] = num[1] ** 1\n    return result", "composite_functions": []}
{"snippet": "def f(text: str):\n    length = len(text)\n    new_text = ''\n    for (i, char) in enumerate(text):\n        new_text += chr(ord(char) << 2 ^ i + 1)\n    return new_text", "input": "'TestString'", "output": "'\u0151\u0196\u01cf\u01d4\u0149\u01d6\u01cf\u01ac\u01b1\u0196'", "imports": [], "original_snippet": "def f(text: str):\n    length = len(text)\n    new_text = ''\n    for (i, char) in enumerate(text):\n        new_text += chr(ord(char) << 2 ^ i + 1)\n    return new_text", "composite_functions": []}
{"snippet": "def f(input_list_of_string: list):\n    filtered_lengths_dict = {}\n    for i in range(0, len(input_list_of_string), 2):\n        string = input_list_of_string[i]\n        if len(string) % 2 == 0:\n            filtered_lengths_dict[string] = len(string)\n    return tuple(filtered_lengths_dict.values())", "input": "['even', 'string', 'list', 'of', 'even', '', 'list', 'of', 'strings']", "output": "(4, 4)", "imports": [], "original_snippet": "def f(input_list_of_string: list):\n    filtered_lengths_dict = {}\n    for i in range(0, len(input_list_of_string), 2):\n        string = input_list_of_string[i]\n        if len(string) % 2 == 0:\n            filtered_lengths_dict[string] = len(string)\n    return tuple(filtered_lengths_dict.values())", "composite_functions": []}
{"snippet": "def f(data_list):\n    for (index, sub_list) in enumerate(data_list):\n        for i in range(len(sub_list) - 2):\n            if sub_list[i] + 1 == sub_list[i + 1] and sub_list[i + 1] - 1 == sub_list[i + 2]:\n                return index\n    return -1", "input": "[[9, 7, 8], [6, 4, 5], [2, 1, 3]]", "output": "-1", "imports": [], "original_snippet": "def f(data_list):\n    for (index, sub_list) in enumerate(data_list):\n        for i in range(len(sub_list) - 2):\n            if sub_list[i] + 1 == sub_list[i + 1] and sub_list[i + 1] - 1 == sub_list[i + 2]:\n                return index\n    return -1", "composite_functions": []}
{"snippet": "def f(numbers):\n    result = []\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            multiplication = numbers[i] * numbers[j]\n            addition = numbers[i] + numbers[j]\n            result.append([numbers[i], numbers[j], multiplication, addition])\n    return result", "input": "[1, 2, 3, 4]", "output": "[[1, 2, 2, 3], [1, 3, 3, 4], [1, 4, 4, 5], [2, 3, 6, 5], [2, 4, 8, 6], [3, 4, 12, 7]]", "imports": [], "original_snippet": "def f(numbers):\n    result = []\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            multiplication = numbers[i] * numbers[j]\n            addition = numbers[i] + numbers[j]\n            result.append([numbers[i], numbers[j], multiplication, addition])\n    return result", "composite_functions": []}
{"snippet": "def f(numbers: list) -> list:\n    numbers = numbers[::-1]\n    results = []\n    for i in range(len(numbers)):\n        if numbers[i] % 2 == 1:\n            for j in range(i + 1, len(numbers)):\n                if numbers[j] % 2 == 0 and (numbers[i] + numbers[j]) % 3 == 0:\n                    results.append((numbers[i], numbers[j]))\n                    numbers[j] = 0\n                    break\n        else:\n            for j in range(i + 1, len(numbers)):\n                if numbers[j] % 2 == 1 and numbers[i] + numbers[j] > 100:\n                    results.append((numbers[i], numbers[j]))\n                    numbers[j] = 0\n                    break\n    results = [tuple(i) for i in results if i != (0,)]\n    return results", "input": "[15, 50, 12, 90, 70, 99, 42, 100]", "output": "[(100, 99), (90, 15)]", "imports": [], "original_snippet": "def f(numbers: list) -> list:\n    numbers = numbers[::-1]\n    results = []\n    for i in range(len(numbers)):\n        if numbers[i] % 2 == 1:\n            for j in range(i + 1, len(numbers)):\n                if numbers[j] % 2 == 0 and (numbers[i] + numbers[j]) % 3 == 0:\n                    results.append((numbers[i], numbers[j]))\n                    numbers[j] = 0\n                    break\n        else:\n            for j in range(i + 1, len(numbers)):\n                if numbers[j] % 2 == 1 and numbers[i] + numbers[j] > 100:\n                    results.append((numbers[i], numbers[j]))\n                    numbers[j] = 0\n                    break\n    results = [tuple(i) for i in results if i != (0,)]\n    return results", "composite_functions": []}
{"snippet": "import re\ndef f(string: str):\n    pattern = '([a-z0-9A-Z]+[_]?[a-z]+)_([a-z0-9A-Z][_]?[a-z0-9]*)\\\\\\\\1\\\\\\\\2'\n    match = re.search(pattern, string)\n    if match:\n        return True\n    else:\n        return False", "input": "'FormatString_ExampleFormatter'", "output": "False", "imports": ["import re"], "original_snippet": "import re\ndef f(string: str):\n    pattern = '([a-z0-9A-Z]+[_]?[a-z]+)_([a-z0-9A-Z][_]?[a-z0-9]*)\\\\\\\\1\\\\\\\\2'\n    match = re.search(pattern, string)\n    if match:\n        return True\n    else:\n        return False", "composite_functions": []}
{"snippet": "def f(value):\n    result = 0\n    for i in range(0, value + 1):\n        result += 10 ** i\n    return result", "input": "4", "output": "11111", "imports": [], "original_snippet": "def f(value):\n    result = 0\n    for i in range(0, value + 1):\n        result += 10 ** i\n    return result", "composite_functions": []}
{"snippet": "def f(n: int, parent: int, sequence: list) -> int:\n    if n == 0:\n        return sequence[parent]\n    sequence.append(f(n - 1, n - 1, sequence) + f(n - 1, 0, sequence))\n    return sequence[n]", "input": "4, 2, [1,2,3,4,5]", "output": "5", "imports": [], "original_snippet": "def f(n: int, parent: int, sequence: list) -> int:\n    if n == 0:\n        return sequence[parent]\n    sequence.append(f(n - 1, n - 1, sequence) + f(n - 1, 0, sequence))\n    return sequence[n]", "composite_functions": []}
{"snippet": "import numpy as np\ndef f(numbers):\n    prev_result = 1\n    for num in numbers:\n        prev_result *= num\n    transformed_output = prev_result % numbers[-1]\n    return transformed_output", "input": "[2, 3, 5]", "output": "0", "imports": ["import numpy as np"], "original_snippet": "import numpy as np\ndef f(numbers):\n    prev_result = 1\n    for num in numbers:\n        prev_result *= num\n    transformed_output = prev_result % numbers[-1]\n    return transformed_output", "composite_functions": []}
{"snippet": "def f(lst):\n    result = []\n    for i in range(len(lst)):\n        for j in range(i + 1, len(lst)):\n            result.append(lst[i] + lst[j])\n    return result", "input": "[3, 4, 7, 12, 18, 21]", "output": "[7, 10, 15, 21, 24, 11, 16, 22, 25, 19, 25, 28, 30, 33, 39]", "imports": [], "original_snippet": "def f(lst):\n    result = []\n    for i in range(len(lst)):\n        for j in range(i + 1, len(lst)):\n            result.append(lst[i] + lst[j])\n    return result", "composite_functions": []}
{"snippet": "def f(freqs):\n    result = []\n    result += [char + str(freqs[char]) for char in freqs if freqs[char] > 1]\n    result += [char for char in freqs if freqs[char] == 1]\n    return ''.join(result)", "input": "{'o': 4, 'w': 3, 'm': 3, ' ' : 1, 'g': 1, 't': 1}", "output": "'o4w3m3 gt'", "imports": [], "original_snippet": "def f(freqs):\n    result = []\n    result += [char + str(freqs[char]) for char in freqs if freqs[char] > 1]\n    result += [char for char in freqs if freqs[char] == 1]\n    return ''.join(result)", "composite_functions": []}
{"snippet": "def f(numbers):\n    sum_of_evens = 0\n    product_of_odds = 1\n    for num in numbers:\n        if num % 2 == 0:\n            sum_of_evens += num\n        else:\n            product_of_odds *= num\n    return (sum_of_evens, product_of_odds)", "input": "[2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "(30, 945)", "imports": [], "original_snippet": "def f(numbers):\n    sum_of_evens = 0\n    product_of_odds = 1\n    for num in numbers:\n        if num % 2 == 0:\n            sum_of_evens += num\n        else:\n            product_of_odds *= num\n    return (sum_of_evens, product_of_odds)", "composite_functions": []}
{"snippet": "def f(input_sequence):\n    if len(input_sequence) % 2 == 0:\n        return [x for (idx, x) in enumerate(input_sequence[::-1]) if idx % 2 == 0]\n    else:\n        return [x for (idx, x) in enumerate(input_sequence) if idx % 2 == 0]", "input": "[45, 58, 22, 88, 14, 38, 59, 3]", "output": "[3, 38, 88, 58]", "imports": [], "original_snippet": "def f(input_sequence):\n    if len(input_sequence) % 2 == 0:\n        return [x for (idx, x) in enumerate(input_sequence[::-1]) if idx % 2 == 0]\n    else:\n        return [x for (idx, x) in enumerate(input_sequence) if idx % 2 == 0]", "composite_functions": []}
{"snippet": "def f(arg1):\n    chars = list(arg1)\n    indexes = []\n    for i in range(len(chars)):\n        indexes.append([])\n        j = 0\n        while j < len(chars):\n            if chars[j] != chars[i]:\n                indexes[i].append(j)\n                break\n            j += 1\n    return indexes", "input": "'abra'", "output": "[[1], [0], [0], [1]]", "imports": [], "original_snippet": "def f(arg1):\n    chars = list(arg1)\n    indexes = []\n    for i in range(len(chars)):\n        indexes.append([])\n        j = 0\n        while j < len(chars):\n            if chars[j] != chars[i]:\n                indexes[i].append(j)\n                break\n            j += 1\n    return indexes", "composite_functions": []}
{"snippet": "def f(name: str, info: dict):\n    output = []\n    words = name.split()\n    for word in words:\n        word = word.lower()\n        output.append(len(word))\n    info['length'] = sum(output) / len(output)\n    return info", "input": "'Johns mother', {'children': 2, 'hobbies': ['reading', 'art']}", "output": "{'children': 2, 'hobbies': ['reading', 'art'], 'length': 5.5}", "imports": [], "original_snippet": "def f(name: str, info: dict):\n    output = []\n    words = name.split()\n    for word in words:\n        word = word.lower()\n        output.append(len(word))\n    info['length'] = sum(output) / len(output)\n    return info", "composite_functions": []}
{"snippet": "def f(graph: dict, target_point: tuple) -> int:\n    min_distance = float('inf')\n    for (vertex, point) in graph.items():\n        distance = abs(point[0] - target_point[0]) + abs(point[1] - target_point[1])\n        if distance < min_distance:\n            min_distance = distance\n    return min_distance", "input": "{'a': (0, 0), 'b': (3, 4), 'c': (2, 1)}, (2, 2)", "output": "1", "imports": [], "original_snippet": "def f(graph: dict, target_point: tuple) -> int:\n    min_distance = float('inf')\n    for (vertex, point) in graph.items():\n        distance = abs(point[0] - target_point[0]) + abs(point[1] - target_point[1])\n        if distance < min_distance:\n            min_distance = distance\n    return min_distance", "composite_functions": []}
{"snippet": "def f(binary: str, n: int):\n    sum_result = 0\n    if binary.isdigit():\n        binary = str(bin(int(binary)))[2:]\n    for num in binary:\n        sum_result += int(num) * n\n    return sum_result", "input": "'12', 6", "output": "12", "imports": [], "original_snippet": "def f(binary: str, n: int):\n    sum_result = 0\n    if binary.isdigit():\n        binary = str(bin(int(binary)))[2:]\n    for num in binary:\n        sum_result += int(num) * n\n    return sum_result", "composite_functions": []}
{"snippet": "def f(people_info: list):\n    names = []\n    age_totals = 0\n    for person in people_info:\n        if 'salary' in person and person['salary'] > 150000:\n            age_totals += person['age']\n            names.append(person['name'])\n    if names:\n        average_age = age_totals / len(names)\n        return {'average_age': average_age, 'rich_names': names}\n    else:\n        return 'No one fits the search criteria.'", "input": "[\n    {'name': 'Oscar', 'age': 25, 'salary': 250000 },\n    {'name': 'Raul', 'age': 42, 'salary': 124000 },\n    {'name': 'Priscilla', 'age': 36, 'salary': 200000}\n]", "output": "{'average_age': 30.5, 'rich_names': ['Oscar', 'Priscilla']}", "imports": [], "original_snippet": "def f(people_info: list):\n    names = []\n    age_totals = 0\n    for person in people_info:\n        if 'salary' in person and person['salary'] > 150000:\n            age_totals += person['age']\n            names.append(person['name'])\n    if names:\n        average_age = age_totals / len(names)\n        return {'average_age': average_age, 'rich_names': names}\n    else:\n        return 'No one fits the search criteria.'", "composite_functions": []}
{"snippet": "def f(seq: tuple):\n    stack = []\n    result = []\n    for i in range(len(seq)):\n        if seq[i] == '(':\n            stack.append(i)\n        elif seq[i] == ')':\n            start = stack.pop()\n            result.append((start, i))\n    return [x for x in result if len(x) % 2 == 0]", "input": "'(())()(((())))'", "output": "[(1, 2), (0, 3), (4, 5), (9, 10), (8, 11), (7, 12), (6, 13)]", "imports": [], "original_snippet": "def f(seq: tuple):\n    stack = []\n    result = []\n    for i in range(len(seq)):\n        if seq[i] == '(':\n            stack.append(i)\n        elif seq[i] == ')':\n            start = stack.pop()\n            result.append((start, i))\n    return [x for x in result if len(x) % 2 == 0]", "composite_functions": []}
{"snippet": "def f(items: dict):\n    total_usage_seconds = 0\n    max_end_time = 0\n    for (equipment, duration) in items.items():\n        current_time = max_end_time\n        total_usage_seconds += duration\n        max_end_time = current_time + duration\n    return total_usage_seconds", "input": "{'pen': 5, 'notebook': 15, 'paper': 10}", "output": "30", "imports": [], "original_snippet": "def f(items: dict):\n    total_usage_seconds = 0\n    max_end_time = 0\n    for (equipment, duration) in items.items():\n        current_time = max_end_time\n        total_usage_seconds += duration\n        max_end_time = current_time + duration\n    return total_usage_seconds", "composite_functions": []}
{"snippet": "def f(numbers: list[int], sum_divisors: list[int]=[3, 5]):\n    total_sum = 0\n    for number in numbers:\n        if any((number % divisor == 0 for divisor in sum_divisors)):\n            digit_sum = sum((int(digit) for digit in str(number) if digit.isdigit()))\n            total_sum += digit_sum\n    return total_sum", "input": "numbers = [23, 45, 11, 59, 74]", "output": "9", "imports": [], "original_snippet": "def f(numbers: list[int], sum_divisors: list[int]=[3, 5]):\n    total_sum = 0\n    for number in numbers:\n        if any((number % divisor == 0 for divisor in sum_divisors)):\n            digit_sum = sum((int(digit) for digit in str(number) if digit.isdigit()))\n            total_sum += digit_sum\n    return total_sum", "composite_functions": []}
{"snippet": "def f(name: str, info: dict):\n    if info.get('age') and info.get('city'):\n        return name + ': ' + str(info['age']) + ', ' + info['city']\n    elif info.get('age'):\n        return name + ': ' + str(info['age'])\n    elif info.get('city'):\n        return name + ': ' + info['city']\n    else:\n        return 'No info'", "input": "'Mark', {'age': 22}", "output": "'Mark: 22'", "imports": [], "original_snippet": "def f(name: str, info: dict):\n    if info.get('age') and info.get('city'):\n        return name + ': ' + str(info['age']) + ', ' + info['city']\n    elif info.get('age'):\n        return name + ': ' + str(info['age'])\n    elif info.get('city'):\n        return name + ': ' + info['city']\n    else:\n        return 'No info'", "composite_functions": []}
{"snippet": "def f(binary_rep):\n    binary_rep = int(binary_rep, 2)\n    result = binary_rep ^ 42\n    return result", "input": "\"10101010\"", "output": "128", "imports": [], "original_snippet": "def f(binary_rep):\n    binary_rep = int(binary_rep, 2)\n    result = binary_rep ^ 42\n    return result", "composite_functions": []}
{"snippet": "def f(s: str, n: int) -> str:\n    length = len(s)\n    index = length // n\n    if 0 <= index < length:\n        return s[index]\n    else:\n        return ''", "input": "\"Hello World!\", 3", "output": "'o'", "imports": [], "original_snippet": "def f(s: str, n: int) -> str:\n    length = len(s)\n    index = length // n\n    if 0 <= index < length:\n        return s[index]\n    else:\n        return ''", "composite_functions": []}
{"snippet": "def f(n: int, m: int, k: int):\n    arr = [[i * j % 10 for j in range(m)] for i in range(n)]\n    product = 1\n    for i in range(n):\n        for j in range(m):\n            if arr[i][j] in [1, 2, 3, 4, 6, 7, 8, 9]:\n                if 0 <= i + k < n and 0 <= j - k < m:\n                    arr[i + k][j - k] += 1\n                if 0 <= i - k < n and 0 <= j + k < m:\n                    arr[i - k][j + k] += 2\n            product *= arr[i][j] % 10 * 3\n    return product % 1000", "input": "2, 3, 1", "output": "0", "imports": [], "original_snippet": "def f(n: int, m: int, k: int):\n    arr = [[i * j % 10 for j in range(m)] for i in range(n)]\n    product = 1\n    for i in range(n):\n        for j in range(m):\n            if arr[i][j] in [1, 2, 3, 4, 6, 7, 8, 9]:\n                if 0 <= i + k < n and 0 <= j - k < m:\n                    arr[i + k][j - k] += 1\n                if 0 <= i - k < n and 0 <= j + k < m:\n                    arr[i - k][j + k] += 2\n            product *= arr[i][j] % 10 * 3\n    return product % 1000", "composite_functions": []}
{"snippet": "def f(my_list: list) -> int:\n    try:\n        return my_list[len(my_list) + +1]\n    except IndexError:\n        return -1", "input": "[0, 1, 2, 'three', 'four']", "output": "-1", "imports": [], "original_snippet": "def f(my_list: list) -> int:\n    try:\n        return my_list[len(my_list) + +1]\n    except IndexError:\n        return -1", "composite_functions": []}
{"snippet": "def f(custom_list: list):\n    custom_list.append(10)\n    custom_list.extend([20, 30, 40])\n    for i in range(len(custom_list)):\n        if custom_list[i] % 2 == 0:\n            custom_list[i] *= 10\n        else:\n            custom_list[i] += 5\n    custom_list.sort(reverse=True)\n    return custom_list", "input": "[1, 2, 3, 4, 5, 6]", "output": "[400, 300, 200, 100, 60, 40, 20, 10, 8, 6]", "imports": [], "original_snippet": "def f(custom_list: list):\n    custom_list.append(10)\n    custom_list.extend([20, 30, 40])\n    for i in range(len(custom_list)):\n        if custom_list[i] % 2 == 0:\n            custom_list[i] *= 10\n        else:\n            custom_list[i] += 5\n    custom_list.sort(reverse=True)\n    return custom_list", "composite_functions": []}
{"snippet": "from typing import List, Tuple\ndef f(nested_tuple_list: List[Tuple[int]]) -> List[int]:\n    flat_list = []\n    for (index, tuple_value) in enumerate(nested_tuple_list, start=0):\n        flat_list.extend(list(range(tuple_value[0], tuple_value[1] + 1)))\n    return sorted(flat_list)", "input": "[(2, 3), (5, 7), (8, 9)]", "output": "[2, 3, 5, 6, 7, 8, 9]", "imports": ["from typing import List, Tuple"], "original_snippet": "from typing import List, Tuple\ndef f(nested_tuple_list: List[Tuple[int]]) -> List[int]:\n    flat_list = []\n    for (index, tuple_value) in enumerate(nested_tuple_list, start=0):\n        flat_list.extend(list(range(tuple_value[0], tuple_value[1] + 1)))\n    return sorted(flat_list)", "composite_functions": []}
{"snippet": "def f(numbers: list) -> list:\n    result = []\n    current_product = 1\n    max_len = 0\n    max_product = 0\n    for i in range(len(numbers)):\n        current_product *= numbers[i]\n        result.append(current_product)\n        if max_len < len(result):\n            max_len = len(result)\n            max_product = current_product\n    return result[max_len - 1] if max_len == len(numbers) else max(result)", "input": "[2, 3, 0, 1, 6, 5]", "output": "0", "imports": [], "original_snippet": "def f(numbers: list) -> list:\n    result = []\n    current_product = 1\n    max_len = 0\n    max_product = 0\n    for i in range(len(numbers)):\n        current_product *= numbers[i]\n        result.append(current_product)\n        if max_len < len(result):\n            max_len = len(result)\n            max_product = current_product\n    return result[max_len - 1] if max_len == len(numbers) else max(result)", "composite_functions": []}
{"snippet": "def f(strng):\n    even_chars = [chr((ord(char) + (2 if idx % 2 == 0 else 0)) % 128) if idx % 2 == 0 else char for (idx, char) in enumerate(strng)]\n    return ''.join(even_chars)", "input": "'preprocessing'", "output": "'rrgptoeeuskni'", "imports": [], "original_snippet": "def f(strng):\n    even_chars = [chr((ord(char) + (2 if idx % 2 == 0 else 0)) % 128) if idx % 2 == 0 else char for (idx, char) in enumerate(strng)]\n    return ''.join(even_chars)", "composite_functions": []}
{"snippet": "def f(word_list: list[str]):\n    char_counts = {}\n    for word in word_list:\n        for char in word:\n            char_counts[char] = char_counts.get(char, 0) + 1\n    return [(char, count_list) for (char, count_list) in char_counts.items()]", "input": "['hello', 'world']", "output": "[('h', 1), ('e', 1), ('l', 3), ('o', 2), ('w', 1), ('r', 1), ('d', 1)]", "imports": [], "original_snippet": "def f(word_list: list[str]):\n    char_counts = {}\n    for word in word_list:\n        for char in word:\n            char_counts[char] = char_counts.get(char, 0) + 1\n    return [(char, count_list) for (char, count_list) in char_counts.items()]", "composite_functions": []}
{"snippet": "def f(a, b, c=10, d=20, e=30, f=40):\n    x = []\n    for i in range(a, b + 1):\n        if i % 2 == 0:\n            y = i\n            for j in range(c, d + 1):\n                if j % 2 == 1:\n                    y += j * e\n                else:\n                    y += j * f\n            x.append(y)\n    return sum(x) / d - c + a", "input": "2, 10, 1, 9, 5, 5", "output": "129.33333333333334", "imports": [], "original_snippet": "def f(a, b, c=10, d=20, e=30, f=40):\n    x = []\n    for i in range(a, b + 1):\n        if i % 2 == 0:\n            y = i\n            for j in range(c, d + 1):\n                if j % 2 == 1:\n                    y += j * e\n                else:\n                    y += j * f\n            x.append(y)\n    return sum(x) / d - c + a", "composite_functions": []}
{"snippet": "def f(base: float, exponent: int):\n    num = 1\n    sequence = [1]\n    for i in range(exponent):\n        num = round(num * base, 5)\n        sequence.append(num)\n        sequence.append(num)\n    sequence.append(exponent)\n    return sequence", "input": "2.5, 3", "output": "[1, 2.5, 2.5, 6.25, 6.25, 15.625, 15.625, 3]", "imports": [], "original_snippet": "def f(base: float, exponent: int):\n    num = 1\n    sequence = [1]\n    for i in range(exponent):\n        num = round(num * base, 5)\n        sequence.append(num)\n        sequence.append(num)\n    sequence.append(exponent)\n    return sequence", "composite_functions": []}
{"snippet": "def f(users: list, threshold: int, reverse: bool=False):\n    users = sorted(users, key=lambda user: user['age'])\n    users = sorted(users, key=lambda user: user['age'])\n    users = [user for user in users if user['age'] >= threshold]\n    users = sorted(users, key=lambda user: user['age'])\n    if reverse:\n        return users[::-1]\n    else:\n        return users", "input": "[\n    {'name': 'David', 'age': 20},\n    {'name': 'Rosa', 'age': 30},\n    {'name': 'Hans', 'age': 25}\n], 20", "output": "[{'name': 'David', 'age': 20}, {'name': 'Hans', 'age': 25}, {'name': 'Rosa', 'age': 30}]", "imports": [], "original_snippet": "def f(users: list, threshold: int, reverse: bool=False):\n    users = sorted(users, key=lambda user: user['age'])\n    users = sorted(users, key=lambda user: user['age'])\n    users = [user for user in users if user['age'] >= threshold]\n    users = sorted(users, key=lambda user: user['age'])\n    if reverse:\n        return users[::-1]\n    else:\n        return users", "composite_functions": []}
{"snippet": "def f(input_string):\n    output = input_string\n    output = output.replace(' --', '--').upper()\n    kept = True\n    output = ''\n    for char in input_string:\n        if char == '--' and kept:\n            kept = False\n        output += char\n    return output", "input": "'lower -- case'", "output": "'lower -- case'", "imports": [], "original_snippet": "def f(input_string):\n    output = input_string\n    output = output.replace(' --', '--').upper()\n    kept = True\n    output = ''\n    for char in input_string:\n        if char == '--' and kept:\n            kept = False\n        output += char\n    return output", "composite_functions": []}
{"snippet": "def f(string: str, count: int):\n    transformed_string = ''\n    for _ in range(count):\n        transformed_string += string * count\n    return transformed_string", "input": "'ab', 3", "output": "'ababababababababab'", "imports": [], "original_snippet": "def f(string: str, count: int):\n    transformed_string = ''\n    for _ in range(count):\n        transformed_string += string * count\n    return transformed_string", "composite_functions": []}
{"snippet": "def f(binary_string: str):\n    counter = 0\n    reversed_bits = []\n    for bit in reversed(binary_string):\n        zeros = counter * '0'\n        ones = zeros + '1' * (counter - 1)\n        reversed_bits.extend([zeros, ones, bit])\n        counter += 1\n    return ''.join(reversed_bits)", "input": "'1011'", "output": "'1001000010000000111'", "imports": [], "original_snippet": "def f(binary_string: str):\n    counter = 0\n    reversed_bits = []\n    for bit in reversed(binary_string):\n        zeros = counter * '0'\n        ones = zeros + '1' * (counter - 1)\n        reversed_bits.extend([zeros, ones, bit])\n        counter += 1\n    return ''.join(reversed_bits)", "composite_functions": []}
{"snippet": "def f(relations: dict) -> dict:\n    return {child: [parent for parent in parents[::-1]] for (child, parents) in relations.items()}", "input": "{'A': ['B', 'C'], 'B': ['D', 'E'], 'C': ['F', 'G'], 'D': ['H', 'I']}", "output": "{'A': ['C', 'B'], 'B': ['E', 'D'], 'C': ['G', 'F'], 'D': ['I', 'H']}", "imports": [], "original_snippet": "def f(relations: dict) -> dict:\n    return {child: [parent for parent in parents[::-1]] for (child, parents) in relations.items()}", "composite_functions": []}
{"snippet": "import math\ndef f(x: float) -> float:\n    if x <= 0:\n        raise ValueError('Input must be positive')\n    y = math.sqrt(x) + math.log(x) - x\n    return y", "input": "25", "output": "-16.7811241751318", "imports": ["import math"], "original_snippet": "import math\ndef f(x: float) -> float:\n    if x <= 0:\n        raise ValueError('Input must be positive')\n    y = math.sqrt(x) + math.log(x) - x\n    return y", "composite_functions": []}
{"snippet": "from math import sqrt\ndef f(points: list):\n    dist = float('inf')\n    input_len = len(points)\n    for i in range(input_len):\n        for j in range(i + 1, input_len):\n            dist_squared = (points[i][0] - points[j][0]) ** 2 + (points[i][1] - points[j][1]) ** 2\n            min_dist = sqrt(dist_squared)\n            if min_dist < dist:\n                dist = min_dist\n    return dist", "input": "[(5, 6), (7,9),(11, 13)]", "output": "3.605551275463989", "imports": ["from math import sqrt"], "original_snippet": "from math import sqrt\ndef f(points: list):\n    dist = float('inf')\n    input_len = len(points)\n    for i in range(input_len):\n        for j in range(i + 1, input_len):\n            dist_squared = (points[i][0] - points[j][0]) ** 2 + (points[i][1] - points[j][1]) ** 2\n            min_dist = sqrt(dist_squared)\n            if min_dist < dist:\n                dist = min_dist\n    return dist", "composite_functions": []}
{"snippet": "def f(str1: str, str2: str) -> str:\n    dp = [[0] * (len(str2) + 1) for _ in range(len(str1) + 1)]\n    max_length = 0\n    end_index = 0\n    for (i, c1) in enumerate(str1, start=1):\n        for (j, c2) in enumerate(str2, start=1):\n            if c1 == c2:\n                dp[i][j] = dp[i - 1][j - 1] + 1\n                if dp[i][j] > max_length:\n                    max_length = dp[i][j]\n                    end_index = i\n    start_index = end_index - max_length\n    return str1[start_index:end_index]", "input": "\"Hello World\", \"woW ellhodisto\"", "output": "'ell'", "imports": [], "original_snippet": "def f(str1: str, str2: str) -> str:\n    dp = [[0] * (len(str2) + 1) for _ in range(len(str1) + 1)]\n    max_length = 0\n    end_index = 0\n    for (i, c1) in enumerate(str1, start=1):\n        for (j, c2) in enumerate(str2, start=1):\n            if c1 == c2:\n                dp[i][j] = dp[i - 1][j - 1] + 1\n                if dp[i][j] > max_length:\n                    max_length = dp[i][j]\n                    end_index = i\n    start_index = end_index - max_length\n    return str1[start_index:end_index]", "composite_functions": []}
{"snippet": "def f(numbers):\n    moved_numbers = [number << 1 for number in numbers]\n    return [moved_number * i + moved_number for (i, moved_number) in enumerate(moved_numbers)]", "input": "[2, 3, 5]", "output": "[4, 12, 30]", "imports": [], "original_snippet": "def f(numbers):\n    moved_numbers = [number << 1 for number in numbers]\n    return [moved_number * i + moved_number for (i, moved_number) in enumerate(moved_numbers)]", "composite_functions": []}
{"snippet": "def f(input_list: list[int], target: int) -> tuple[int, int]:\n    if len(input_list) < 2:\n        raise ValueError('The length of the list must be greater than 1.')\n    min_diff = float('inf')\n    result = (0, 0)\n    for i in range(len(input_list) - 1):\n        for j in range(i + 1, len(input_list)):\n            sum_ = input_list[i] + input_list[j]\n            diff = abs(sum_ - target)\n            if diff < min_diff:\n                min_diff = diff\n                result = (input_list[i], input_list[j])\n    return result", "input": "[17, 27, 25, 30], 45", "output": "(17, 27)", "imports": [], "original_snippet": "def f(input_list: list[int], target: int) -> tuple[int, int]:\n    if len(input_list) < 2:\n        raise ValueError('The length of the list must be greater than 1.')\n    min_diff = float('inf')\n    result = (0, 0)\n    for i in range(len(input_list) - 1):\n        for j in range(i + 1, len(input_list)):\n            sum_ = input_list[i] + input_list[j]\n            diff = abs(sum_ - target)\n            if diff < min_diff:\n                min_diff = diff\n                result = (input_list[i], input_list[j])\n    return result", "composite_functions": []}
{"snippet": "def f(first_name: str, last_name: str):\n    first_name = ''.join(filter(str.isalpha, first_name))\n    last_name = ''.join(filter(str.isalpha, last_name))\n    (first_name, last_name) = (last_name, first_name)\n    return f'{first_name} {last_name}'", "input": "' Last Name John', ' First Name Doe'", "output": "'FirstNameDoe LastNameJohn'", "imports": [], "original_snippet": "def f(first_name: str, last_name: str):\n    first_name = ''.join(filter(str.isalpha, first_name))\n    last_name = ''.join(filter(str.isalpha, last_name))\n    (first_name, last_name) = (last_name, first_name)\n    return f'{first_name} {last_name}'", "composite_functions": []}
{"snippet": "def f(name: str, info: dict) -> str:\n    most_common_city = max(info['city'], key=info['city'].count)\n    return f\"{name}'s elder parents' age sum: {len(name) * info['age']}, city: {most_common_city}\"", "input": "'John', {'age': 35, 'city': 'New York'}", "output": "\"John's elder parents' age sum: 140, city: N\"", "imports": [], "original_snippet": "def f(name: str, info: dict) -> str:\n    most_common_city = max(info['city'], key=info['city'].count)\n    return f\"{name}'s elder parents' age sum: {len(name) * info['age']}, city: {most_common_city}\"", "composite_functions": []}
{"snippet": "def f(n: int):\n    if n <= 1:\n        return n\n    else:\n        return f(n - 1) + f(n - 2)", "input": "5", "output": "5", "imports": [], "original_snippet": "def f(n: int):\n    if n <= 1:\n        return n\n    else:\n        return f(n - 1) + f(n - 2)", "composite_functions": []}
{"snippet": "def f(title):\n    title_new = title.split()\n    title_word_count = len(title_new)\n    name_new = title_new[0:title_word_count - 2]\n    name_new = ' '.join(name_new)\n    title_word_new = title_new[title_word_count - 2:title_word_count]\n    title_word_new = ' '.join(title_word_new)\n    a = title_new[:1]\n    a = ' '.join(a)\n    for i in a.title():\n        if a in title_word_new and a.title() != title_word_new.title():\n            name_new = a.title()\n            title_word_new = title_word_new.title()\n        else:\n            name_new = name_new.title()\n    return (name_new, title_word_new.title())", "input": "'austin m.d.'", "output": "('Austin', 'Austin M.D.')", "imports": [], "original_snippet": "def f(title):\n    title_new = title.split()\n    title_word_count = len(title_new)\n    name_new = title_new[0:title_word_count - 2]\n    name_new = ' '.join(name_new)\n    title_word_new = title_new[title_word_count - 2:title_word_count]\n    title_word_new = ' '.join(title_word_new)\n    a = title_new[:1]\n    a = ' '.join(a)\n    for i in a.title():\n        if a in title_word_new and a.title() != title_word_new.title():\n            name_new = a.title()\n            title_word_new = title_word_new.title()\n        else:\n            name_new = name_new.title()\n    return (name_new, title_word_new.title())", "composite_functions": []}
{"snippet": "def f(emoji_list: list) -> str:\n    unique_emojis = []\n    unique_count = 0\n    emoji_count = len(emoji_list)\n    for e in emoji_list:\n        if e in unique_emojis:\n            unique_count += 1\n        else:\n            unique_emojis.append(e)\n            unique_count += 1\n    return str(unique_count)", "input": "['\ud83d\ude01', '\ud83d\ude02', '\ud83d\ude02', '\ud83d\udc4d', '\ud83d\ude01', '\ud83d\ude0d', '\ud83d\ude0d']", "output": "'7'", "imports": [], "original_snippet": "def f(emoji_list: list) -> str:\n    unique_emojis = []\n    unique_count = 0\n    emoji_count = len(emoji_list)\n    for e in emoji_list:\n        if e in unique_emojis:\n            unique_count += 1\n        else:\n            unique_emojis.append(e)\n            unique_count += 1\n    return str(unique_count)", "composite_functions": []}
{"snippet": "def f(starting_sequence, n):\n    result = []\n    for i in range(n):\n        x = sum([starting_sequence[j] * (len(starting_sequence) - j) for j in range(len(starting_sequence))])\n        result.append(x % 100)\n        starting_sequence.append(len(starting_sequence))\n    return result[-1]", "input": "[9, 8, 7, 6, 5], 10", "output": "75", "imports": [], "original_snippet": "def f(starting_sequence, n):\n    result = []\n    for i in range(n):\n        x = sum([starting_sequence[j] * (len(starting_sequence) - j) for j in range(len(starting_sequence))])\n        result.append(x % 100)\n        starting_sequence.append(len(starting_sequence))\n    return result[-1]", "composite_functions": []}
{"snippet": "def f(str_input: str) -> list:\n    processed_words = []\n    count = 0\n    last_word_ends_with_vowel = False\n    for character in str_input:\n        if character in 'aeiou':\n            processed_words.append(character)\n        elif count % 3 == 0 and last_word_ends_with_vowel is False:\n            processed_words.append(character)\n        last_word_ends_with_vowel = character in 'aeiou' if character != ' ' else True\n        count = (count + 1) // 3\n    return processed_words", "input": "'aikuingredients'", "output": "['a', 'i', 'u', 'i', 'g', 'r', 'e', 'i', 'e', 't', 's']", "imports": [], "original_snippet": "def f(str_input: str) -> list:\n    processed_words = []\n    count = 0\n    last_word_ends_with_vowel = False\n    for character in str_input:\n        if character in 'aeiou':\n            processed_words.append(character)\n        elif count % 3 == 0 and last_word_ends_with_vowel is False:\n            processed_words.append(character)\n        last_word_ends_with_vowel = character in 'aeiou' if character != ' ' else True\n        count = (count + 1) // 3\n    return processed_words", "composite_functions": []}
{"snippet": "def f(s):\n    groups = list(filter(lambda x: len(x) >= 5, [s[i:i + 5] for i in range(0, len(s), 5)]))\n    product = 1\n    for group in groups:\n        product *= int(group)\n    return str(product)", "input": "'123456789012345567890123456789012345'", "output": "'50809715023009578487964901000'", "imports": [], "original_snippet": "def f(s):\n    groups = list(filter(lambda x: len(x) >= 5, [s[i:i + 5] for i in range(0, len(s), 5)]))\n    product = 1\n    for group in groups:\n        product *= int(group)\n    return str(product)", "composite_functions": []}
{"snippet": "def f(lst: list) -> list:\n    peak_valley_count = 0\n    result = []\n    for i in range(len(lst) - 1):\n        current = lst[i]\n        next = lst[i + 1]\n        if current < next:\n            peak_valley_count += 1\n        result.append(peak_valley_count)\n        result.append((lst[i + 1] - current) // 2)\n    return result", "input": "[0, 2, 1, 5, 6, 4, 7]", "output": "[1, 1, 1, -1, 2, 2, 3, 0, 3, -1, 4, 1]", "imports": [], "original_snippet": "def f(lst: list) -> list:\n    peak_valley_count = 0\n    result = []\n    for i in range(len(lst) - 1):\n        current = lst[i]\n        next = lst[i + 1]\n        if current < next:\n            peak_valley_count += 1\n        result.append(peak_valley_count)\n        result.append((lst[i + 1] - current) // 2)\n    return result", "composite_functions": []}
{"snippet": "from collections import Counter\nfrom itertools import islice\ndef f(input_list: list, remove_count: int):\n    counter = Counter(input_list)\n    sorted_values = sorted(counter, key=counter.get)\n    smallest_numbers = sorted_values[:remove_count]\n    result_list = list(input_list)\n    for (i, num) in enumerate(input_list):\n        if num in smallest_numbers:\n            result_list.remove(num)\n    return result_list", "input": "[8, 12, 4, 21, 11, 38, 33, 17, 18, 24], 3", "output": "[21, 11, 38, 33, 17, 18, 24]", "imports": ["from collections import Counter", "from itertools import islice"], "original_snippet": "from collections import Counter\nfrom itertools import islice\ndef f(input_list: list, remove_count: int):\n    counter = Counter(input_list)\n    sorted_values = sorted(counter, key=counter.get)\n    smallest_numbers = sorted_values[:remove_count]\n    result_list = list(input_list)\n    for (i, num) in enumerate(input_list):\n        if num in smallest_numbers:\n            result_list.remove(num)\n    return result_list", "composite_functions": []}
{"snippet": "def f(nums):\n    if not all((isinstance(num, int) for num in nums)):\n        raise ValueError('All input values must be integers')\n    multiplier = 3\n    results = [num * multiplier for num in nums]\n    for (num, result) in zip(nums, results):\n        print(f'The result of {num} multiplied by {multiplier} is {result}')\n    return results", "input": "nums = [2, 20, 7, 50]", "output": "[6, 60, 21, 150]", "imports": [], "original_snippet": "def f(nums):\n    if not all((isinstance(num, int) for num in nums)):\n        raise ValueError('All input values must be integers')\n    multiplier = 3\n    results = [num * multiplier for num in nums]\n    for (num, result) in zip(nums, results):\n        print(f'The result of {num} multiplied by {multiplier} is {result}')\n    return results", "composite_functions": []}
{"snippet": "def f(names_scores: list):\n    names = []\n    scores = []\n    for (name, score) in names_scores:\n        names.append(name)\n        scores.append(score)\n    for i in range(len(scores)):\n        for j in range(i + 1, len(scores)):\n            if scores[i] < scores[j]:\n                (names[i], names[j]) = (names[j], names[i])\n                (scores[i], scores[j]) = (scores[j], scores[i])\n    return names", "input": "[('Alice', 85), ('Bob', 90), ('Charlie', 80)]", "output": "['Bob', 'Alice', 'Charlie']", "imports": [], "original_snippet": "def f(names_scores: list):\n    names = []\n    scores = []\n    for (name, score) in names_scores:\n        names.append(name)\n        scores.append(score)\n    for i in range(len(scores)):\n        for j in range(i + 1, len(scores)):\n            if scores[i] < scores[j]:\n                (names[i], names[j]) = (names[j], names[i])\n                (scores[i], scores[j]) = (scores[j], scores[i])\n    return names", "composite_functions": []}
{"snippet": "def f(input_number: float, list_nums: list):\n    total = 0\n    for (pos, num) in enumerate(list_nums):\n        total += (num + 1 + 2) * (pos + 1) - input_number - input_number / 3\n    return total + round(input_number / 2) + input_number * 4", "input": "2.5, [1, 2, 3]", "output": "33.0", "imports": [], "original_snippet": "def f(input_number: float, list_nums: list):\n    total = 0\n    for (pos, num) in enumerate(list_nums):\n        total += (num + 1 + 2) * (pos + 1) - input_number - input_number / 3\n    return total + round(input_number / 2) + input_number * 4", "composite_functions": []}
{"snippet": "def f(array: list[list[int]]) -> list[int]:\n    result = []\n    for _ in range(10):\n        modified_list = array[0].copy()\n        (modified_list[0], modified_list[-1]) = (modified_list[-1], modified_list[0])\n        result.append(modified_list)\n    return result", "input": "[[1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [11, 12, 13, 14], [15, 16, 17, 18, 19, 20]]", "output": "[[10, 2, 3, 4, 5, 6, 7, 8, 9, 1], [10, 2, 3, 4, 5, 6, 7, 8, 9, 1], [10, 2, 3, 4, 5, 6, 7, 8, 9, 1], [10, 2, 3, 4, 5, 6, 7, 8, 9, 1], [10, 2, 3, 4, 5, 6, 7, 8, 9, 1], [10, 2, 3, 4, 5, 6, 7, 8, 9, 1], [10, 2, 3, 4, 5, 6, 7, 8, 9, 1], [10, 2, 3, 4, 5, 6, 7, 8, 9, 1], [10, 2, 3, 4, 5, 6, 7, 8, 9, 1], [10, 2, 3, 4, 5, 6, 7, 8, 9, 1]]", "imports": [], "original_snippet": "def f(array: list[list[int]]) -> list[int]:\n    result = []\n    for _ in range(10):\n        modified_list = array[0].copy()\n        (modified_list[0], modified_list[-1]) = (modified_list[-1], modified_list[0])\n        result.append(modified_list)\n    return result", "composite_functions": []}
{"snippet": "def f(intervals):\n    intervals.sort(key=lambda x: x[0])\n    start_mem = {}\n    max_overlap = 0\n    max_intervals = []\n    current_max = None\n    for interval in intervals:\n        start_mem.setdefault(interval[0], 0)\n        start_mem[interval[0]] += 1\n        overlaps = start_mem.get(interval[0], 0)\n        if current_max is None or overlaps > current_max:\n            current_max = overlaps\n            max_intervals = [interval]\n        elif overlaps == current_max:\n            max_intervals.append(interval)\n    return (current_max, max_intervals)", "input": "[(1, 3), (2, 4), (3, 5), (4, 6), (5, 7), (6, 8)]", "output": "(1, [(1, 3), (2, 4), (3, 5), (4, 6), (5, 7), (6, 8)])", "imports": [], "original_snippet": "def f(intervals):\n    intervals.sort(key=lambda x: x[0])\n    start_mem = {}\n    max_overlap = 0\n    max_intervals = []\n    current_max = None\n    for interval in intervals:\n        start_mem.setdefault(interval[0], 0)\n        start_mem[interval[0]] += 1\n        overlaps = start_mem.get(interval[0], 0)\n        if current_max is None or overlaps > current_max:\n            current_max = overlaps\n            max_intervals = [interval]\n        elif overlaps == current_max:\n            max_intervals.append(interval)\n    return (current_max, max_intervals)", "composite_functions": []}
{"snippet": "def f(numbers):\n    prev_result = 1\n    mod_result = []\n    for num in numbers:\n        prev_result *= num\n        mod_result.append(prev_result % 10007)\n    transformed_output = sum((i ** 2 for i in mod_result))\n    return transformed_output", "input": "[2, 3, 5]", "output": "940", "imports": [], "original_snippet": "def f(numbers):\n    prev_result = 1\n    mod_result = []\n    for num in numbers:\n        prev_result *= num\n        mod_result.append(prev_result % 10007)\n    transformed_output = sum((i ** 2 for i in mod_result))\n    return transformed_output", "composite_functions": []}
{"snippet": "def f(lst):\n    max_product = 0\n    max_legend = []\n    for i in range(len(lst)):\n        temp_product = 0\n        for j in range(i, len(lst)):\n            if lst[i] + lst[j] == 2:\n                temp_product += 1\n            if temp_product > max_product:\n                max_product = temp_product\n                max_legend.append((i, j))\n    return (max_product, max_legend[0] if max_product > 0 else 'None')", "input": "[1, 1, 0, 1, 0]", "output": "(3, (0, 0))", "imports": [], "original_snippet": "def f(lst):\n    max_product = 0\n    max_legend = []\n    for i in range(len(lst)):\n        temp_product = 0\n        for j in range(i, len(lst)):\n            if lst[i] + lst[j] == 2:\n                temp_product += 1\n            if temp_product > max_product:\n                max_product = temp_product\n                max_legend.append((i, j))\n    return (max_product, max_legend[0] if max_product > 0 else 'None')", "composite_functions": []}
{"snippet": "def f(messages):\n    total_messages = len(messages)\n    seen_send_times = set()\n    for message in messages:\n        seen_send_times.add(message['send']['time'])\n    delivery_rate = 0\n    for send_time in seen_send_times:\n        delivered_messages = 0\n        for message in messages:\n            if message['send']['time'] == send_time and message['send']['date'] != message['deliver']['date']:\n                delivered_messages += 1\n        delivery_rate = max(delivery_rate, delivered_messages / (total_messages / len(seen_send_times)))\n    return delivery_rate", "input": "[\n    {\n        'send': {'time': 1234, 'date': '2022-01-01'},\n        'deliver': {'time': 1235, 'date': '2022-01-01'}\n    },\n    {\n        'send': {'time': 2345, 'date': '2022-01-01'},\n        'deliver': {'time': 3456, 'date': '2022-01-02'}\n    },\n    {\n        'send': {'time': 4567, 'date': '2022-01-01'},\n        'deliver': {'time': 6789, 'date': '2022-01-02'}\n    }\n]", "output": "1.0", "imports": [], "original_snippet": "def f(messages):\n    total_messages = len(messages)\n    seen_send_times = set()\n    for message in messages:\n        seen_send_times.add(message['send']['time'])\n    delivery_rate = 0\n    for send_time in seen_send_times:\n        delivered_messages = 0\n        for message in messages:\n            if message['send']['time'] == send_time and message['send']['date'] != message['deliver']['date']:\n                delivered_messages += 1\n        delivery_rate = max(delivery_rate, delivered_messages / (total_messages / len(seen_send_times)))\n    return delivery_rate", "composite_functions": []}
{"snippet": "def f(nums, target):\n    index = 0\n    for i in range(len(nums)):\n        if nums[i] == target:\n            index = i\n            break\n    else:\n        raise ValueError('Target not found.')\n    nums.insert(index + 1, nums.pop(index))\n    nums.remove(nums[index])\n    return nums", "input": "[1, 2, 4, 5, 6], 5", "output": "[1, 2, 4, 5]", "imports": [], "original_snippet": "def f(nums, target):\n    index = 0\n    for i in range(len(nums)):\n        if nums[i] == target:\n            index = i\n            break\n    else:\n        raise ValueError('Target not found.')\n    nums.insert(index + 1, nums.pop(index))\n    nums.remove(nums[index])\n    return nums", "composite_functions": []}
{"snippet": "def f(stream):\n    passed = [0 for _ in range(len(stream))]\n    not_passed = [0 for _ in range(len(stream))]\n    for (i, token) in enumerate(stream):\n        if token.startswith('a'):\n            passed[i] += 1\n        elif token.endswith('b'):\n            not_passed[i] += 1\n        elif i > 0:\n            passed[i] = passed[i - 1] if stream[i - 1].startswith('a') else 0\n            not_passed[i] = not_passed[i - 1] if stream[i - 1].endswith('b') else 0\n    return (passed[-1], not_passed[-1])", "input": "['abc', 'aef', 'hjb', 'az', 'def']", "output": "(1, 0)", "imports": [], "original_snippet": "def f(stream):\n    passed = [0 for _ in range(len(stream))]\n    not_passed = [0 for _ in range(len(stream))]\n    for (i, token) in enumerate(stream):\n        if token.startswith('a'):\n            passed[i] += 1\n        elif token.endswith('b'):\n            not_passed[i] += 1\n        elif i > 0:\n            passed[i] = passed[i - 1] if stream[i - 1].startswith('a') else 0\n            not_passed[i] = not_passed[i - 1] if stream[i - 1].endswith('b') else 0\n    return (passed[-1], not_passed[-1])", "composite_functions": []}
{"snippet": "def f(input_matrix):\n    output = [row[:] for row in input_matrix]\n    for i in range(len(output)):\n        for j in range(len(output[0])):\n            output[i][j] = input_matrix[i][j] - (max(0, i - 1) + max(0, j - 1) + min(len(output) - 1, i + 1) + min(len(output[0]) - 1, j + 1))\n    return output", "input": "[[0, 1, 2, 1],\n [2, 3, 5, 4],\n [0, 2, 3, 0],\n [0, 1, 0, 0]]", "output": "[[-2, -2, -3, -5], [-1, -1, -1, -3], [-5, -4, -5, -9], [-6, -6, -9, -10]]", "imports": [], "original_snippet": "def f(input_matrix):\n    output = [row[:] for row in input_matrix]\n    for i in range(len(output)):\n        for j in range(len(output[0])):\n            output[i][j] = input_matrix[i][j] - (max(0, i - 1) + max(0, j - 1) + min(len(output) - 1, i + 1) + min(len(output[0]) - 1, j + 1))\n    return output", "composite_functions": []}
{"snippet": "def f(words):\n    result = []\n    for word in words:\n        if len(word.strip()) % 2 != 0:\n            if len(word) > 1:\n                result.append(word[1])\n            else:\n                result.append('X')\n    return result", "input": "['a', 'ba', 'cb', 'cd', '', ' ', 'z', 'abc', 'def', 'X', 'abcde', 's']", "output": "['X', 'X', 'b', 'e', 'X', 'b', 'X']", "imports": [], "original_snippet": "def f(words):\n    result = []\n    for word in words:\n        if len(word.strip()) % 2 != 0:\n            if len(word) > 1:\n                result.append(word[1])\n            else:\n                result.append('X')\n    return result", "composite_functions": []}
{"snippet": "def f(lists):\n    total = 1\n    for outer_list in lists:\n        for inner_list in outer_list:\n            total *= sum(inner_list)\n    return total * len(lists)", "input": "[[[1, 2, 3], [4, 5, 6]], [[7, 8, 9], [10, 11, 12]]]", "output": "142560", "imports": [], "original_snippet": "def f(lists):\n    total = 1\n    for outer_list in lists:\n        for inner_list in outer_list:\n            total *= sum(inner_list)\n    return total * len(lists)", "composite_functions": []}
{"snippet": "def f(matrix=None):\n    if matrix is None:\n        matrix = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]\n    transposed = list(zip(*matrix))\n    encoded = [row[::-1] for row in transposed]\n    flat_list = [num for row in encoded for num in str(row)]\n    result = ''.join(flat_list)\n    return result", "input": "\"1,2,3,4,5,6,7,8,9\"", "output": "\"('9', ',', '8', ',', '7', ',', '6', ',', '5', ',', '4', ',', '3', ',', '2', ',', '1')\"", "imports": [], "original_snippet": "def f(matrix=None):\n    if matrix is None:\n        matrix = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]\n    transposed = list(zip(*matrix))\n    encoded = [row[::-1] for row in transposed]\n    flat_list = [num for row in encoded for num in str(row)]\n    result = ''.join(flat_list)\n    return result", "composite_functions": []}
{"snippet": "def f(target_string: str, num: int, base: int) -> int:\n    return int(target_string, base)", "input": "'101010', 6, 2", "output": "42", "imports": [], "original_snippet": "def f(target_string: str, num: int, base: int) -> int:\n    return int(target_string, base)", "composite_functions": []}
{"snippet": "def f(input_list: list, remove_count: int):\n    if len(input_list) < remove_count:\n        raise ValueError('Remove count exceeds list length')\n    sorted_mem = {}\n    freq = {}\n    result_list = []\n    for num in input_list:\n        freq[num] = freq.get(num, 0) + 1\n        if num not in sorted_mem:\n            sorted_mem[num] = 0\n        sorted_mem[num] += 1\n    sorted_keys = sorted(freq, key=freq.get, reverse=True)\n    for num in sorted_keys:\n        if sorted_mem[num] > remove_count:\n            remove = sorted_mem[num] - remove_count\n            result_list = [i for i in input_list if i != num] * remove\n            break\n    return result_list", "input": "[50, 10, 40, 30, 20, 60, 70, 40, 60, 70], 4", "output": "[]", "imports": [], "original_snippet": "def f(input_list: list, remove_count: int):\n    if len(input_list) < remove_count:\n        raise ValueError('Remove count exceeds list length')\n    sorted_mem = {}\n    freq = {}\n    result_list = []\n    for num in input_list:\n        freq[num] = freq.get(num, 0) + 1\n        if num not in sorted_mem:\n            sorted_mem[num] = 0\n        sorted_mem[num] += 1\n    sorted_keys = sorted(freq, key=freq.get, reverse=True)\n    for num in sorted_keys:\n        if sorted_mem[num] > remove_count:\n            remove = sorted_mem[num] - remove_count\n            result_list = [i for i in input_list if i != num] * remove\n            break\n    return result_list", "composite_functions": []}
{"snippet": "def f(lst: list[float]) -> str:\n    sorted_list = sorted(lst, reverse=True)\n    top_two = sorted_list[:2]\n    top_two_avg = sum(top_two) / 2\n    return str(top_two_avg)", "input": "[2.5, 7.2, 3.9, 6.4, 8.1]", "output": "'7.65'", "imports": [], "original_snippet": "def f(lst: list[float]) -> str:\n    sorted_list = sorted(lst, reverse=True)\n    top_two = sorted_list[:2]\n    top_two_avg = sum(top_two) / 2\n    return str(top_two_avg)", "composite_functions": []}
{"snippet": "def f(stocks_data):\n    stocks_prices = []\n    for stock in stocks_data:\n        stocks_prices.extend(stock['closing_prices'])\n    unique_prices = list(set(stocks_prices))\n    sorted_prices = sorted(unique_prices, reverse=True)\n    return sorted_prices", "input": "[{'closing_prices': [10.0, 9.5, 8.0, 9.5]},\n{'closing_prices': [5.0, 7.0, 8.0, 10.0]},\n{'closing_prices': [10.0, 9.5, 8.0, 9.0, 10.5, 11.0]}\n]", "output": "[11.0, 10.5, 10.0, 9.5, 9.0, 8.0, 7.0, 5.0]", "imports": [], "original_snippet": "def f(stocks_data):\n    stocks_prices = []\n    for stock in stocks_data:\n        stocks_prices.extend(stock['closing_prices'])\n    unique_prices = list(set(stocks_prices))\n    sorted_prices = sorted(unique_prices, reverse=True)\n    return sorted_prices", "composite_functions": []}
{"snippet": "def f(num: int):\n    primal_input = [d for d in range(2, num // 2 + 1) if num % d == 0]\n    second_intrinsic = primal_input[1]\n    transformed_output = [num % (d if d % second_intrinsic else second_intrinsic) for d in primal_input]\n    return transformed_output", "input": "48", "output": "[0, 0, 0, 0, 0, 0, 0, 0]", "imports": [], "original_snippet": "def f(num: int):\n    primal_input = [d for d in range(2, num // 2 + 1) if num % d == 0]\n    second_intrinsic = primal_input[1]\n    transformed_output = [num % (d if d % second_intrinsic else second_intrinsic) for d in primal_input]\n    return transformed_output", "composite_functions": []}
{"snippet": "def f(nums):\n    n = len(nums)\n    count = 0\n    stack = []\n    for i in range(n):\n        if not stack or nums[stack[-1]] <= nums[i]:\n            stack.append(i)\n            count += 1\n            nums[i] = float('inf')\n        elif nums[stack[-1]] > nums[i]:\n            while stack and nums[stack[-1]] > nums[i]:\n                stack.pop()\n                count -= 1\n    return nums", "input": "[6, 5, 4, 3, 2, 1, 8, 7, 9, 10, 12]", "output": "[inf, 5, inf, 3, inf, 1, inf, 7, inf, 10, inf]", "imports": [], "original_snippet": "def f(nums):\n    n = len(nums)\n    count = 0\n    stack = []\n    for i in range(n):\n        if not stack or nums[stack[-1]] <= nums[i]:\n            stack.append(i)\n            count += 1\n            nums[i] = float('inf')\n        elif nums[stack[-1]] > nums[i]:\n            while stack and nums[stack[-1]] > nums[i]:\n                stack.pop()\n                count -= 1\n    return nums", "composite_functions": []}
{"snippet": "def f(temperature, humidity, pressure, windspeed):\n    heat_index = round((temperature + humidity) * (100 - pressure) / (windspeed / 14.5 + 1) * 0.33)\n    is_hot = heat_index >= 90\n    extreme_cases = [h for h in range(450, 600)]\n    is_extreme = heat_index in extreme_cases\n    return (is_hot, is_extreme)", "input": "60, 8 , 50, 3", "output": "(True, False)", "imports": [], "original_snippet": "def f(temperature, humidity, pressure, windspeed):\n    heat_index = round((temperature + humidity) * (100 - pressure) / (windspeed / 14.5 + 1) * 0.33)\n    is_hot = heat_index >= 90\n    extreme_cases = [h for h in range(450, 600)]\n    is_extreme = heat_index in extreme_cases\n    return (is_hot, is_extreme)", "composite_functions": []}
{"snippet": "def f(numbers, boolean_list):\n    if len(numbers) != len(boolean_list):\n        raise ValueError('Numbers and boolean_list must be of the same length.')\n    interim_list = sorted([n for (b, n) in zip(boolean_list, numbers) if b])\n    return [n for (b, n) in zip(boolean_list, interim_list) if not b]", "input": "[5, 1, 9, 9, 2], [True, True, True, False, False]", "output": "[]", "imports": [], "original_snippet": "def f(numbers, boolean_list):\n    if len(numbers) != len(boolean_list):\n        raise ValueError('Numbers and boolean_list must be of the same length.')\n    interim_list = sorted([n for (b, n) in zip(boolean_list, numbers) if b])\n    return [n for (b, n) in zip(boolean_list, interim_list) if not b]", "composite_functions": []}
{"snippet": "def f(start: int, end: int) -> int:\n    total_sum = 0\n    for i in range(start, end + 1):\n        sum_of_digits = sum((int(digit) ** 2 for digit in str(i)))\n        cubic_root_sum = sum((j ** 3 for j in range(i, end + 1)))\n        total_sum += sum_of_digits + cubic_root_sum\n    return total_sum", "input": "100, 200", "output": "25281096634", "imports": [], "original_snippet": "def f(start: int, end: int) -> int:\n    total_sum = 0\n    for i in range(start, end + 1):\n        sum_of_digits = sum((int(digit) ** 2 for digit in str(i)))\n        cubic_root_sum = sum((j ** 3 for j in range(i, end + 1)))\n        total_sum += sum_of_digits + cubic_root_sum\n    return total_sum", "composite_functions": []}
{"snippet": "def f(string):\n    string = string.lower()\n    reversed_string = string[::-1]\n    xored_string = ''.join((chr(ord(a) ^ ord(b)) for (a, b) in zip(string, reversed_string)))\n    return xored_string", "input": "\"Since;1947, 445Continue\"", "output": "'\\x16\\x1c\\x00\\n\\x11U^Z\\x01\\x03\\x18\\x00\\x18\\x03\\x01Z^U\\x11\\n\\x00\\x1c\\x16'", "imports": [], "original_snippet": "def f(string):\n    string = string.lower()\n    reversed_string = string[::-1]\n    xored_string = ''.join((chr(ord(a) ^ ord(b)) for (a, b) in zip(string, reversed_string)))\n    return xored_string", "composite_functions": []}
{"snippet": "def f(temperatures: list[float]):\n    converted_temperatures = [round(temp * 9 / 5 + 32, 2) for temp in temperatures]\n    average_fahrenheit = sum(converted_temperatures) / len(converted_temperatures)\n    return (average_fahrenheit, converted_temperatures)", "input": "[0, 10, 20, 30, 40, 50, 60]", "output": "(86.0, [32.0, 50.0, 68.0, 86.0, 104.0, 122.0, 140.0])", "imports": [], "original_snippet": "def f(temperatures: list[float]):\n    converted_temperatures = [round(temp * 9 / 5 + 32, 2) for temp in temperatures]\n    average_fahrenheit = sum(converted_temperatures) / len(converted_temperatures)\n    return (average_fahrenheit, converted_temperatures)", "composite_functions": []}
{"snippet": "def f(my_array: list) -> int:\n    try:\n        my_array = sorted(my_array)\n        (largest_1, largest_2) = my_array[-2:]\n        (smallest_1, smallest_2) = my_array[:2]\n        return sum(largest_1) + sum(largest_2) - (smallest_1 + smallest_2)\n    except Exception as e:\n        return -1", "input": "my_array = [1, 3, 2, 8, 5, -5, -2]", "output": "-1", "imports": [], "original_snippet": "def f(my_array: list) -> int:\n    try:\n        my_array = sorted(my_array)\n        (largest_1, largest_2) = my_array[-2:]\n        (smallest_1, smallest_2) = my_array[:2]\n        return sum(largest_1) + sum(largest_2) - (smallest_1 + smallest_2)\n    except Exception as e:\n        return -1", "composite_functions": []}
{"snippet": "from collections import deque\ndef f(nodes: dict) -> dict:\n    node_set = {'nodes': nodes, 'depths': {node: 0 for node in nodes}, 'parent': {}}\n    queue = deque()\n    for root in node_set['nodes']:\n        node_set['depths'][root] = 0\n        node_set['parent'][root] = None\n    for leaf in node_set['nodes']:\n        node_set['depths'][leaf] = 1\n    while queue:\n        for i in range(len(queue)):\n            node = queue.popleft()\n            if node_set['parent'].get(node):\n                node_set['depths'][node_set['parent'][node]] += node_set['depths'][node]\n            if node_set['nodes'].get(node):\n                for child in node_set['nodes'][node]:\n                    queue.append(child)\n                    node_set['parent'][child] = node\n    return node_set", "input": "{'A': ['B', 'C'], 'B': ['D', 'E'], 'C': ['F'], 'D': [], 'E': [], 'F': []}", "output": "{'nodes': {'A': ['B', 'C'], 'B': ['D', 'E'], 'C': ['F'], 'D': [], 'E': [], 'F': []}, 'depths': {'A': 1, 'B': 1, 'C': 1, 'D': 1, 'E': 1, 'F': 1}, 'parent': {'A': None, 'B': None, 'C': None, 'D': None, 'E': None, 'F': None}}", "imports": ["from collections import deque"], "original_snippet": "from collections import deque\ndef f(nodes: dict) -> dict:\n    node_set = {'nodes': nodes, 'depths': {node: 0 for node in nodes}, 'parent': {}}\n    queue = deque()\n    for root in node_set['nodes']:\n        node_set['depths'][root] = 0\n        node_set['parent'][root] = None\n    for leaf in node_set['nodes']:\n        node_set['depths'][leaf] = 1\n    while queue:\n        for i in range(len(queue)):\n            node = queue.popleft()\n            if node_set['parent'].get(node):\n                node_set['depths'][node_set['parent'][node]] += node_set['depths'][node]\n            if node_set['nodes'].get(node):\n                for child in node_set['nodes'][node]:\n                    queue.append(child)\n                    node_set['parent'][child] = node\n    return node_set", "composite_functions": []}
{"snippet": "from collections import defaultdict, deque\ndef f(text):\n    special_chars = {'!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '=', '+', '-', ',', '.', '/', ';', ':', '_', '*', '?', '>', '<', '|', '[', ']', '{', '}', '\\\\', '`', '~', '\\r', '\\n', '\\t'}\n    special_counts = defaultdict(int)\n    char_queue = deque()\n    for char in text:\n        if char in special_chars:\n            special_counts[char] += 1\n            char_queue.append(char)\n    for i in range(len(text)):\n        if text[i] in special_chars:\n            special_counts[text[i]] += 1\n            char_queue.append(text[i])\n            if len(char_queue) > 10:\n                first_char = char_queue.popleft()\n                special_counts[first_char] -= 1\n    chars_with_counts = list(zip(*sorted(special_counts.items(), key=lambda item: item[1], reverse=True)))\n    res = ''\n    for (char, count) in zip(chars_with_counts[0], chars_with_counts[1]):\n        if count > 0:\n            res += f'{char}{count}'\n    return res", "input": "'Hello!@#$&'", "output": "'!2@2#2$2&2'", "imports": ["from collections import defaultdict, deque"], "original_snippet": "from collections import defaultdict, deque\ndef f(text):\n    special_chars = {'!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '=', '+', '-', ',', '.', '/', ';', ':', '_', '*', '?', '>', '<', '|', '[', ']', '{', '}', '\\\\', '`', '~', '\\r', '\\n', '\\t'}\n    special_counts = defaultdict(int)\n    char_queue = deque()\n    for char in text:\n        if char in special_chars:\n            special_counts[char] += 1\n            char_queue.append(char)\n    for i in range(len(text)):\n        if text[i] in special_chars:\n            special_counts[text[i]] += 1\n            char_queue.append(text[i])\n            if len(char_queue) > 10:\n                first_char = char_queue.popleft()\n                special_counts[first_char] -= 1\n    chars_with_counts = list(zip(*sorted(special_counts.items(), key=lambda item: item[1], reverse=True)))\n    res = ''\n    for (char, count) in zip(chars_with_counts[0], chars_with_counts[1]):\n        if count > 0:\n            res += f'{char}{count}'\n    return res", "composite_functions": []}
{"snippet": "def f(number: float, power: int, multiplier: int):\n    result = 1\n    for _ in range(power):\n        result = result * number\n    result = result * multiplier\n    return result", "input": "10, 2, 3", "output": "300", "imports": [], "original_snippet": "def f(number: float, power: int, multiplier: int):\n    result = 1\n    for _ in range(power):\n        result = result * number\n    result = result * multiplier\n    return result", "composite_functions": []}
{"snippet": "def f(array):\n    state = {}\n    for (current, previous) in zip(array[1:], array):\n        if current < previous:\n            if 'ctr' not in state:\n                state['ctr'] = 0\n            state['ctr'] += 1\n        else:\n            state['ctr'] = 0\n        if 'best' not in state:\n            state['best'] = state['ctr']\n        else:\n            state['best'] = max(state['best'], state['ctr'])\n    return state['best']", "input": "[10, 8, 7, 5, 6, 7, 7, 6, 4, 5, 6, 5, 3, 4, 3, 2, 12]", "output": "3", "imports": [], "original_snippet": "def f(array):\n    state = {}\n    for (current, previous) in zip(array[1:], array):\n        if current < previous:\n            if 'ctr' not in state:\n                state['ctr'] = 0\n            state['ctr'] += 1\n        else:\n            state['ctr'] = 0\n        if 'best' not in state:\n            state['best'] = state['ctr']\n        else:\n            state['best'] = max(state['best'], state['ctr'])\n    return state['best']", "composite_functions": []}
{"snippet": "def f(input: int) -> int:\n    min = 1\n    max = 4\n    increment = 3\n    if input > 9 or input <= 0:\n        raise ValueError('Invalid input.')\n    if input > max:\n        max += increment\n    result = (input + min + max) / 3\n    return result", "input": "3", "output": "2.6666666666666665", "imports": [], "original_snippet": "def f(input: int) -> int:\n    min = 1\n    max = 4\n    increment = 3\n    if input > 9 or input <= 0:\n        raise ValueError('Invalid input.')\n    if input > max:\n        max += increment\n    result = (input + min + max) / 3\n    return result", "composite_functions": []}
{"snippet": "def f(lst: list, find_primes: bool=True):\n    is_prime = lambda n: all((n % d != 0 for d in range(2, int(n ** 0.5) + 1)))\n    is_palindrome = lambda n: str(n) == str(n)[::-1]\n    prime_palindromes = []\n    numbers = iter(lst)\n    sum_of_numbers = 0\n    count = 0\n    while count < 10:\n        n = next(numbers)\n        if is_prime(n) and is_palindrome(n):\n            prime_palindromes.append(n)\n            count += 1\n    return prime_palindromes", "input": "[11, 101, 121, 131, 141, 151, 161, 171, 181, 191, 192, 193, 194, 195]", "output": "", "imports": [], "original_snippet": "def f(lst: list, find_primes: bool=True):\n    is_prime = lambda n: all((n % d != 0 for d in range(2, int(n ** 0.5) + 1)))\n    is_palindrome = lambda n: str(n) == str(n)[::-1]\n    prime_palindromes = []\n    numbers = iter(lst)\n    sum_of_numbers = 0\n    count = 0\n    while count < 10:\n        n = next(numbers)\n        if is_prime(n) and is_palindrome(n):\n            prime_palindromes.append(n)\n            count += 1\n    return prime_palindromes", "composite_functions": []}
{"snippet": "def f(lst: list, n: int) -> int:\n    result = []\n    for i in range(len(lst)):\n        result.append(lst[i])\n        result[i] = lst[i] * (i + 1) if lst[i] % 2 == 1 else lst[i]\n    result = list(set(result))\n    result.sort()\n    return result[n - 1]", "input": "[3, 7, 8, 12, 2, 6, 1, 9, 5, 4], 3", "output": "4", "imports": [], "original_snippet": "def f(lst: list, n: int) -> int:\n    result = []\n    for i in range(len(lst)):\n        result.append(lst[i])\n        result[i] = lst[i] * (i + 1) if lst[i] % 2 == 1 else lst[i]\n    result = list(set(result))\n    result.sort()\n    return result[n - 1]", "composite_functions": []}
{"snippet": "def f(input_dicts):\n    output = []\n    for d in input_dicts:\n        if not 'value' in d:\n            continue\n        if d['value'] >= 0:\n            output.append(d['value'])\n    return output", "input": "[{'value': 10}, {'value': 20}, {'value': -5}]", "output": "[10, 20]", "imports": [], "original_snippet": "def f(input_dicts):\n    output = []\n    for d in input_dicts:\n        if not 'value' in d:\n            continue\n        if d['value'] >= 0:\n            output.append(d['value'])\n    return output", "composite_functions": []}
{"snippet": "def f(input_string: str):\n    result = ''\n    uppercase = True\n    numeric = False\n    for char in input_string:\n        if char.isupper():\n            print('UPPER')\n            result += char.lower()\n        elif char.islower():\n            print('LOWER')\n            result += char.upper()\n        elif char.isdigit():\n            print('NUMBER')\n            result += char\n        else:\n            result += char\n        (uppercase, numeric) = (True, False)\n    return result", "input": "'Hello2World!'", "output": "'hELLO2wORLD!'", "imports": [], "original_snippet": "def f(input_string: str):\n    result = ''\n    uppercase = True\n    numeric = False\n    for char in input_string:\n        if char.isupper():\n            print('UPPER')\n            result += char.lower()\n        elif char.islower():\n            print('LOWER')\n            result += char.upper()\n        elif char.isdigit():\n            print('NUMBER')\n            result += char\n        else:\n            result += char\n        (uppercase, numeric) = (True, False)\n    return result", "composite_functions": []}
{"snippet": "def f(message_list: list, keyword: str, substring: str):\n    count = 0\n    for message in message_list:\n        if message.get('sender') is None:\n            message['sender'] = {'id': 0, 'name': 'Unknown'}\n        if message.get('sender').get('name') == keyword and message.get('content') is not None and (substring in message.get('content')):\n            count += 1\n    return count", "input": "[\n    {\n        'sender': {'id': 0, 'name': 'Alice'},\n        'content': 'Hi, this is an example for testing.'\n    },\n    {\n        'sender': {'id': 1, 'name': 'Bob'},\n        'content': 'Hi, you have an example for testing.'\n    },\n    {\n        'sender': {'id': 0, 'name': 'Alice'},\n        'content': 'Hi, please test this.'\n    }\n], 'Alice', 'example'", "output": "1", "imports": [], "original_snippet": "def f(message_list: list, keyword: str, substring: str):\n    count = 0\n    for message in message_list:\n        if message.get('sender') is None:\n            message['sender'] = {'id': 0, 'name': 'Unknown'}\n        if message.get('sender').get('name') == keyword and message.get('content') is not None and (substring in message.get('content')):\n            count += 1\n    return count", "composite_functions": []}
{"snippet": "def f(numbers: list[int]) -> list[tuple[int, int]]:\n    special_pairs = []\n    for i in range(len(numbers) - 1):\n        for j in range(i + 1, len(numbers)):\n            if (numbers[i] + numbers[j]) % 4 == 0:\n                special_pairs.append((numbers[i], numbers[j]))\n    return special_pairs", "input": "[4, 8, 12, 16, 20]", "output": "[(4, 8), (4, 12), (4, 16), (4, 20), (8, 12), (8, 16), (8, 20), (12, 16), (12, 20), (16, 20)]", "imports": [], "original_snippet": "def f(numbers: list[int]) -> list[tuple[int, int]]:\n    special_pairs = []\n    for i in range(len(numbers) - 1):\n        for j in range(i + 1, len(numbers)):\n            if (numbers[i] + numbers[j]) % 4 == 0:\n                special_pairs.append((numbers[i], numbers[j]))\n    return special_pairs", "composite_functions": []}
{"snippet": "from typing import List\ndef f(input_list: List[str]) -> List[float]:\n    split_list = input_list.split(', ')\n    reciprocals_list = [1 / float(num) for num in split_list]\n    return reciprocals_list", "input": "\"2, 0.5, 1.5\"", "output": "[0.5, 2.0, 0.6666666666666666]", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(input_list: List[str]) -> List[float]:\n    split_list = input_list.split(', ')\n    reciprocals_list = [1 / float(num) for num in split_list]\n    return reciprocals_list", "composite_functions": []}
{"snippet": "def f(x: int):\n    lst = [i for i in range(100, 1000)]\n    map_lst = list(map(lambda e: str(e), lst))\n    new_lst = [int(i) for i in map_lst if '8' in i]\n    return new_lst[x]", "input": "2", "output": "128", "imports": [], "original_snippet": "def f(x: int):\n    lst = [i for i in range(100, 1000)]\n    map_lst = list(map(lambda e: str(e), lst))\n    new_lst = [int(i) for i in map_lst if '8' in i]\n    return new_lst[x]", "composite_functions": []}
{"snippet": "def f(range_num: range, target_sum: int):\n    loop_multiplier = 2\n    result_sum = 0\n    output_list = []\n    for e in range_num:\n        loop_value = len(range_num) // loop_multiplier\n        for i in range(loop_value):\n            if result_sum == target_sum:\n                break\n            result_sum += e\n            output_list.append(result_sum)\n        if loop_value == result_sum // e:\n            break\n        loop_multiplier += 1\n    return output_list", "input": "range_num = range(2, 21), target_sum = 320", "output": "[2, 4, 6, 8, 10, 12, 14, 16, 18]", "imports": [], "original_snippet": "def f(range_num: range, target_sum: int):\n    loop_multiplier = 2\n    result_sum = 0\n    output_list = []\n    for e in range_num:\n        loop_value = len(range_num) // loop_multiplier\n        for i in range(loop_value):\n            if result_sum == target_sum:\n                break\n            result_sum += e\n            output_list.append(result_sum)\n        if loop_value == result_sum // e:\n            break\n        loop_multiplier += 1\n    return output_list", "composite_functions": []}
{"snippet": "from itertools import permutations\ndef f(output):\n    given_output = tuple(output)\n    starting = 120\n    while starting > 0:\n        combinations = permutations([1, 2, 3, 4, 5])\n        for combination in combinations:\n            if tuple(combination) == given_output:\n                return (starting, combination)\n            starting -= 1", "input": "(5, 4, 3, 2, 1)", "output": "(1, (5, 4, 3, 2, 1))", "imports": ["from itertools import permutations"], "original_snippet": "from itertools import permutations\ndef f(output):\n    given_output = tuple(output)\n    starting = 120\n    while starting > 0:\n        combinations = permutations([1, 2, 3, 4, 5])\n        for combination in combinations:\n            if tuple(combination) == given_output:\n                return (starting, combination)\n            starting -= 1", "composite_functions": []}
{"snippet": "def f(data: list):\n    unique_numbers = {}\n    for number in data:\n        if number in unique_numbers:\n            del unique_numbers[number]\n        else:\n            unique_numbers[number] = True\n    return unique_numbers.keys()", "input": "[23, 42, 32, 23, 4, 32, 23, 4, 5, 5, 4]", "output": "dict_keys([42, 23, 4])", "imports": [], "original_snippet": "def f(data: list):\n    unique_numbers = {}\n    for number in data:\n        if number in unique_numbers:\n            del unique_numbers[number]\n        else:\n            unique_numbers[number] = True\n    return unique_numbers.keys()", "composite_functions": []}
{"snippet": "def f(numbers: list):\n    if len(numbers) < 25:\n        return 'Error: Input list length is shorter than required'\n    half_way_point = len(numbers) // 2\n    first_half = numbers[:half_way_point]\n    second_half = numbers[half_way_point:]\n    reversed_list = second_half + first_half\n    return reversed_list", "input": "list(range(30))", "output": "[15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]", "imports": [], "original_snippet": "def f(numbers: list):\n    if len(numbers) < 25:\n        return 'Error: Input list length is shorter than required'\n    half_way_point = len(numbers) // 2\n    first_half = numbers[:half_way_point]\n    second_half = numbers[half_way_point:]\n    reversed_list = second_half + first_half\n    return reversed_list", "composite_functions": []}
{"snippet": "def f(tuple_list: tuple) -> list:\n    merged_list = []\n    for lst in list(tuple_list):\n        merged_list.extend(lst)\n    sorted_list = sorted(merged_list)\n    reversed_list = sorted_list[::-1]\n    return reversed_list", "input": "[[1, 2, 3], [4, 5, 6]]", "output": "[6, 5, 4, 3, 2, 1]", "imports": [], "original_snippet": "def f(tuple_list: tuple) -> list:\n    merged_list = []\n    for lst in list(tuple_list):\n        merged_list.extend(lst)\n    sorted_list = sorted(merged_list)\n    reversed_list = sorted_list[::-1]\n    return reversed_list", "composite_functions": []}
{"snippet": "def f(sequence: list) -> int:\n    transformed_elements = {}\n    sum_of_items = 0\n    for i in sequence:\n        digits = [int(d) for d in str(i)]\n        product = 1\n        for digit in digits:\n            product *= digit\n        transformed_elements[i] = product\n        sum_of_items += product\n    return sum_of_items", "input": "[25, 34, 123]", "output": "28", "imports": [], "original_snippet": "def f(sequence: list) -> int:\n    transformed_elements = {}\n    sum_of_items = 0\n    for i in sequence:\n        digits = [int(d) for d in str(i)]\n        product = 1\n        for digit in digits:\n            product *= digit\n        transformed_elements[i] = product\n        sum_of_items += product\n    return sum_of_items", "composite_functions": []}
{"snippet": "def f(string: str) -> dict:\n    counter = {str(digit): 0 for digit in range(10)}\n    for char in string:\n        counter[char] = counter.get(char, 0) + 1\n    return counter", "input": "'abcd1234abcd5678'", "output": "{'0': 0, '1': 1, '2': 1, '3': 1, '4': 1, '5': 1, '6': 1, '7': 1, '8': 1, '9': 0, 'a': 2, 'b': 2, 'c': 2, 'd': 2}", "imports": [], "original_snippet": "def f(string: str) -> dict:\n    counter = {str(digit): 0 for digit in range(10)}\n    for char in string:\n        counter[char] = counter.get(char, 0) + 1\n    return counter", "composite_functions": []}
{"snippet": "def f(elements):\n    return_elements = {}\n    unique_elements = set()\n    for innerlist in elements:\n        for element in innerlist:\n            if element in unique_elements:\n                return_elements.update({element: return_elements.get(element, 0) + 1})\n            else:\n                unique_elements.add(element)\n    return_list = []\n    for (key, value) in sorted(return_elements.items()):\n        return_list.append([key, value])\n    return return_list", "input": "[[1, 2, 2, 3, 4, 4, 5, 1, 2], [2, 3, 4, 3, 3, 1, 5, 6, 7]]", "output": "[[1, 2], [2, 3], [3, 3], [4, 2], [5, 1]]", "imports": [], "original_snippet": "def f(elements):\n    return_elements = {}\n    unique_elements = set()\n    for innerlist in elements:\n        for element in innerlist:\n            if element in unique_elements:\n                return_elements.update({element: return_elements.get(element, 0) + 1})\n            else:\n                unique_elements.add(element)\n    return_list = []\n    for (key, value) in sorted(return_elements.items()):\n        return_list.append([key, value])\n    return return_list", "composite_functions": []}
{"snippet": "def f(params):\n    matcher = {'nested_key': {'nested_sub_key': {'attr_1': 'feature_1', 'attr_2': 'feature_1', 'attr_1b': 'feature_2', 'attr_1c': 'feature_3'}}}\n    for (key, value) in matcher.items():\n        if all((key in params for key in value)):\n            return True\n    return False", "input": "{\n    \"location\": \"Melbourne\",\n    \"material\": \"sandal\",\n    \"season\": \"summer\",\n    \"feature_1\": \"resin\",\n    \"feature_2\": \"stone-encrusted\",\n    \"feature_3\": \"pattern on feet\"\n}", "output": "False", "imports": [], "original_snippet": "def f(params):\n    matcher = {'nested_key': {'nested_sub_key': {'attr_1': 'feature_1', 'attr_2': 'feature_1', 'attr_1b': 'feature_2', 'attr_1c': 'feature_3'}}}\n    for (key, value) in matcher.items():\n        if all((key in params for key in value)):\n            return True\n    return False", "composite_functions": []}
{"snippet": "def f(image_path: str, label: str):\n    from typing import List\n    import cv2\n    import numpy as np\n    img = cv2.imread(image_path)\n    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)\n    lower_range = np.array([0, 50, 50])\n    upper_range = np.array([10, 255, 255])\n    mask = cv2.inRange(hsv, lower_range, upper_range)\n    result = cv2.bitwise_not(cv2.bitwise_and(img, img, mask=mask))\n    text = ''\n    if label == 'sky':\n        text = 'Sky' in text\n    elif label == 'tree':\n        text = 'Tree' in text\n    elif label == 'ground':\n        text = 'Ground' in text\n    elif label == 'sun':\n        text = 'Sun' in text\n    elif label == 'skyline':\n        text = 'Skyline' in text\n    return text", "input": "\"sun.png\", \"sun\"", "output": "", "imports": ["from typing import List", "import cv2", "import numpy as np"], "original_snippet": "def f(image_path: str, label: str):\n    from typing import List\n    import cv2\n    import numpy as np\n    img = cv2.imread(image_path)\n    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)\n    lower_range = np.array([0, 50, 50])\n    upper_range = np.array([10, 255, 255])\n    mask = cv2.inRange(hsv, lower_range, upper_range)\n    result = cv2.bitwise_not(cv2.bitwise_and(img, img, mask=mask))\n    text = ''\n    if label == 'sky':\n        text = 'Sky' in text\n    elif label == 'tree':\n        text = 'Tree' in text\n    elif label == 'ground':\n        text = 'Ground' in text\n    elif label == 'sun':\n        text = 'Sun' in text\n    elif label == 'skyline':\n        text = 'Skyline' in text\n    return text", "composite_functions": []}
{"snippet": "def f(word: str):\n    output_list = []\n    word_length = len(word)\n    for i in range(word_length):\n        if i % 5 == 0:\n            output_list.append(word[i])\n    return ''.join(output_list)", "input": "'hello world'", "output": "'h d'", "imports": [], "original_snippet": "def f(word: str):\n    output_list = []\n    word_length = len(word)\n    for i in range(word_length):\n        if i % 5 == 0:\n            output_list.append(word[i])\n    return ''.join(output_list)", "composite_functions": []}
{"snippet": "def f(input_array):\n    result_array = []\n    for start in range(len(input_array) - 4):\n        rolling_window = input_array[start:start + 5]\n        average_value = sum(rolling_window) / 5\n        if average_value.is_integer():\n            result_array.append(int(average_value))\n    return result_array", "input": "[10, 11, 13, 14, 15, 18, 20, 21, 22, 24]", "output": "[16, 21]", "imports": [], "original_snippet": "def f(input_array):\n    result_array = []\n    for start in range(len(input_array) - 4):\n        rolling_window = input_array[start:start + 5]\n        average_value = sum(rolling_window) / 5\n        if average_value.is_integer():\n            result_array.append(int(average_value))\n    return result_array", "composite_functions": []}
{"snippet": "def f(colors: str, start_color: str) -> str:\n    color_map = {}\n    color_list = colors.split()\n    for color in color_list:\n        if color.endswith('e'):\n            color_map[color[0:len(color) - 1]] = True\n        else:\n            color_map[color[0:len(color) - 1]] = False\n    for i in range(len(color_list)):\n        if color_map[color_list[i][0:len(color_list[i]) - 1]] is False:\n            return color_list[i + 1]\n    return color_list[0]", "input": "'text texte measures te wears  blue bluee  te vetooses text'.lower(), 'text'", "output": "'texte'", "imports": [], "original_snippet": "def f(colors: str, start_color: str) -> str:\n    color_map = {}\n    color_list = colors.split()\n    for color in color_list:\n        if color.endswith('e'):\n            color_map[color[0:len(color) - 1]] = True\n        else:\n            color_map[color[0:len(color) - 1]] = False\n    for i in range(len(color_list)):\n        if color_map[color_list[i][0:len(color_list[i]) - 1]] is False:\n            return color_list[i + 1]\n    return color_list[0]", "composite_functions": []}
{"snippet": "def f(strings: list[str], pattern: str) -> dict:\n    paths = {}\n    for (index, string) in enumerate(strings):\n        path_list = string.split('/')\n        for i in range(len(path_list)):\n            path = '/'.join(path_list[:i + 1])\n            if path in paths:\n                paths[path].append(index)\n            else:\n                paths[path] = [index]\n    final_dict = {(path, pattern.replace('*', path)): tuple(sorted(indices)) for (path, indices) in paths.items()}\n    return final_dict", "input": "['/usr/local', '/usr/local/bin', '/usr', '/usr/bin'], '*'", "output": "{('', ''): (0, 1, 2, 3), ('/usr', '/usr'): (0, 1, 2, 3), ('/usr/local', '/usr/local'): (0, 1), ('/usr/local/bin', '/usr/local/bin'): (1,), ('/usr/bin', '/usr/bin'): (3,)}", "imports": [], "original_snippet": "def f(strings: list[str], pattern: str) -> dict:\n    paths = {}\n    for (index, string) in enumerate(strings):\n        path_list = string.split('/')\n        for i in range(len(path_list)):\n            path = '/'.join(path_list[:i + 1])\n            if path in paths:\n                paths[path].append(index)\n            else:\n                paths[path] = [index]\n    final_dict = {(path, pattern.replace('*', path)): tuple(sorted(indices)) for (path, indices) in paths.items()}\n    return final_dict", "composite_functions": []}
{"snippet": "def f(a, b):\n    moves = 0\n    target = max(a, b)\n    current = a\n    while current < target:\n        if a > b:\n            moves += a // b\n            current += a // b * b\n        else:\n            moves += b // a\n            current += b // a * a\n    return moves", "input": "40, 60", "output": "1", "imports": [], "original_snippet": "def f(a, b):\n    moves = 0\n    target = max(a, b)\n    current = a\n    while current < target:\n        if a > b:\n            moves += a // b\n            current += a // b * b\n        else:\n            moves += b // a\n            current += b // a * a\n    return moves", "composite_functions": []}
{"snippet": "def f(times: list):\n    dict1 = {'12 or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'], '1 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '2 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '3 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '4 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '5 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '6 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '7 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '8 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '9 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '10 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '11 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '12 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], '1 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '2 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '3 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '4 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '5 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '6 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '7 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '8 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '9 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '10 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '11 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']}\n    converted_times = []\n    for time in times:\n        time_minutes = time.split(':')[1]\n        time_hours = time.split(':')[0]\n        if int(time_minutes) > 45:\n            time_hours = str(int(time_hours) + 1)\n            converted_times.append(time_hours + ' hours')\n        else:\n            converted_times.append(time_hours + ' hours')\n    simplified_times = []\n    for time in converted_times:\n        range_time = time.split(' ')[0]\n        if range_time in dict1.keys():\n            for key in dict1:\n                simplified_times.append(key)\n    return simplified_times", "input": "['01:45', '08:30', '05:15']", "output": "[]", "imports": [], "original_snippet": "def f(times: list):\n    dict1 = {'12 or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'], '1 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '2 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '3 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '4 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '5 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '6 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '7 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '8 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '9 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '10 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '11 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '12 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'], '1 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '2 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '3 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '4 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '5 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '6 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '7 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '8 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '9 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '10 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], '11 PM or less': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']}\n    converted_times = []\n    for time in times:\n        time_minutes = time.split(':')[1]\n        time_hours = time.split(':')[0]\n        if int(time_minutes) > 45:\n            time_hours = str(int(time_hours) + 1)\n            converted_times.append(time_hours + ' hours')\n        else:\n            converted_times.append(time_hours + ' hours')\n    simplified_times = []\n    for time in converted_times:\n        range_time = time.split(' ')[0]\n        if range_time in dict1.keys():\n            for key in dict1:\n                simplified_times.append(key)\n    return simplified_times", "composite_functions": []}
{"snippet": "def f(b: list):\n    total_sum = sum(b)\n    if total_sum % 2 == 0:\n        return b + b[::-1]\n    else:\n        for i in range(len(b)):\n            if isinstance(i, int) and i % 2 != 0:\n                b.append(b[i])\n            else:\n                continue\n        return b", "input": "[0,1,0,1,1,1,1,1,0,1]", "output": "[0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1]", "imports": [], "original_snippet": "def f(b: list):\n    total_sum = sum(b)\n    if total_sum % 2 == 0:\n        return b + b[::-1]\n    else:\n        for i in range(len(b)):\n            if isinstance(i, int) and i % 2 != 0:\n                b.append(b[i])\n            else:\n                continue\n        return b", "composite_functions": []}
{"snippet": "def f(input_string: str) -> str:\n    characters = list(input_string.lower())\n    mapping = {chr(i): i for i in range(65, 91)}\n    transformed_values = [x ** x % 97 ^ 23 for x in mapping.values()]\n    transformed_characters = [chr(num + 65) for num in transformed_values]\n    encrypted_characters = []\n    for (i, char) in enumerate(characters):\n        if i % 3 == 0:\n            encrypted_characters.append(transformed_characters[0])\n        elif i % 3 == 1:\n            encrypted_characters.append(transformed_characters[1])\n        else:\n            encrypted_characters.append(char)\n    return ''.join(encrypted_characters)", "input": "'ABC123'", "output": "'|wc|w3'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    characters = list(input_string.lower())\n    mapping = {chr(i): i for i in range(65, 91)}\n    transformed_values = [x ** x % 97 ^ 23 for x in mapping.values()]\n    transformed_characters = [chr(num + 65) for num in transformed_values]\n    encrypted_characters = []\n    for (i, char) in enumerate(characters):\n        if i % 3 == 0:\n            encrypted_characters.append(transformed_characters[0])\n        elif i % 3 == 1:\n            encrypted_characters.append(transformed_characters[1])\n        else:\n            encrypted_characters.append(char)\n    return ''.join(encrypted_characters)", "composite_functions": []}
{"snippet": "def f(name: str) -> str:\n    black = '\\x1b[0;30m'\n    red = '\\x1b[0;31m'\n    green = '\\x1b[0;32m'\n    yellow = '\\x1b[0;33m'\n    blue = '\\x1b[0;34m'\n    magenta = '\\x1b[0;35m'\n    cyan = '\\x1b[0;36m'\n    white = '\\x1b[0;37m'\n    bold = '\\x1b[1m'\n    reset = '\\x1b[0m'\n    modified_name = []\n    name_list = list(name)\n    for (i, char) in enumerate(name_list):\n        if i % 2 == 0:\n            modified_name.append(black + bold + char)\n        else:\n            modified_name.append(blue + bold + char)\n    return ''.join(modified_name)", "input": "'Goku'", "output": "'\\x1b[0;30m\\x1b[1mG\\x1b[0;34m\\x1b[1mo\\x1b[0;30m\\x1b[1mk\\x1b[0;34m\\x1b[1mu'", "imports": [], "original_snippet": "def f(name: str) -> str:\n    black = '\\x1b[0;30m'\n    red = '\\x1b[0;31m'\n    green = '\\x1b[0;32m'\n    yellow = '\\x1b[0;33m'\n    blue = '\\x1b[0;34m'\n    magenta = '\\x1b[0;35m'\n    cyan = '\\x1b[0;36m'\n    white = '\\x1b[0;37m'\n    bold = '\\x1b[1m'\n    reset = '\\x1b[0m'\n    modified_name = []\n    name_list = list(name)\n    for (i, char) in enumerate(name_list):\n        if i % 2 == 0:\n            modified_name.append(black + bold + char)\n        else:\n            modified_name.append(blue + bold + char)\n    return ''.join(modified_name)", "composite_functions": []}
{"snippet": "def f(start_x: int, end_x: int, move_length: int):\n    moves = 0\n    current_x = start_x\n    while abs(current_x - end_x) > move_length:\n        moves += abs(current_x - end_x) // move_length\n        current_x = current_x + (1 if current_x < end_x else -1) * move_length\n    moves += abs(current_x - end_x)\n    return moves", "input": "1, 20, 12", "output": "8", "imports": [], "original_snippet": "def f(start_x: int, end_x: int, move_length: int):\n    moves = 0\n    current_x = start_x\n    while abs(current_x - end_x) > move_length:\n        moves += abs(current_x - end_x) // move_length\n        current_x = current_x + (1 if current_x < end_x else -1) * move_length\n    moves += abs(current_x - end_x)\n    return moves", "composite_functions": []}
{"snippet": "def f(lst: list) -> int:\n    max_sum = 0\n    for element in lst:\n        current_sum = 0\n        for digit in str(element):\n            current_sum += int(digit)\n        if current_sum % 2 == 1 and element > max_sum:\n            max_sum = element\n    return max_sum", "input": "[345, 871, 949, 123, 456,789,210,567,890]", "output": "890", "imports": [], "original_snippet": "def f(lst: list) -> int:\n    max_sum = 0\n    for element in lst:\n        current_sum = 0\n        for digit in str(element):\n            current_sum += int(digit)\n        if current_sum % 2 == 1 and element > max_sum:\n            max_sum = element\n    return max_sum", "composite_functions": []}
{"snippet": "def f(age_limit: int, country: str):\n    if country == 'USA':\n        age_limit = 18\n    elif country == 'Canada':\n        age_limit = 21\n    ages = range(age_limit + 1)\n    newborns = range(1)\n    juvenile_proportion = len([age for age in ages if age < age_limit]) / age_limit\n    adult_proportion = 1 - juvenile_proportion\n    juvenile_players = juvenile_proportion * len(ages) * len(newborns)\n    newborn_players = adult_proportion * len(ages) * len(newborns)\n    return (int(juvenile_players), int(newborn_players))", "input": "18, \"USA\"", "output": "(19, 0)", "imports": [], "original_snippet": "def f(age_limit: int, country: str):\n    if country == 'USA':\n        age_limit = 18\n    elif country == 'Canada':\n        age_limit = 21\n    ages = range(age_limit + 1)\n    newborns = range(1)\n    juvenile_proportion = len([age for age in ages if age < age_limit]) / age_limit\n    adult_proportion = 1 - juvenile_proportion\n    juvenile_players = juvenile_proportion * len(ages) * len(newborns)\n    newborn_players = adult_proportion * len(ages) * len(newborns)\n    return (int(juvenile_players), int(newborn_players))", "composite_functions": []}
{"snippet": "def f(stack: list):\n    stack = stack.copy()\n    unique_elements = []\n    max_elem = [0]\n    unique_values = set()\n    while stack:\n        c = stack.pop()\n        unique_values.add(c)\n        if max_elem[0] < c:\n            max_elem[0] = c\n        if unique_values.issubset(unique_elements):\n            return unique_elements.count(max_elem[0]) + 1\n        unique_elements.append(c)\n    else:\n        return len(unique_elements)", "input": "[3, 5, 4, 8, 8, 8]", "output": "2", "imports": [], "original_snippet": "def f(stack: list):\n    stack = stack.copy()\n    unique_elements = []\n    max_elem = [0]\n    unique_values = set()\n    while stack:\n        c = stack.pop()\n        unique_values.add(c)\n        if max_elem[0] < c:\n            max_elem[0] = c\n        if unique_values.issubset(unique_elements):\n            return unique_elements.count(max_elem[0]) + 1\n        unique_elements.append(c)\n    else:\n        return len(unique_elements)", "composite_functions": []}
{"snippet": "def f(x: list[int]) -> int:\n    return sum((n for n in x if n > 0 and n % 2 == 0))", "input": "[x for x in range(2, 20) if x % 2 == 0]", "output": "90", "imports": [], "original_snippet": "def f(x: list[int]) -> int:\n    return sum((n for n in x if n > 0 and n % 2 == 0))", "composite_functions": []}
{"snippet": "def f(maze: list[list[int]]) -> list[list[int]]:\n    max_row = len(maze)\n    max_col = len(maze[0])\n    can_reach = [[False] * max_col for _ in range(max_row)]\n    can_reach[0][0] = True\n    q = [(0, 0)]\n    while q:\n        (r, c) = q.pop(0)\n        if r == max_row - 1 and c == max_col - 1:\n            break\n        for (dr, dc) in [(-1, 0), (1, 0), (0, -1), (0, 1)]:\n            (new_r, new_c) = (r + dr, c + dc)\n            if 0 <= new_r < max_row and 0 <= new_c < max_col and (maze[new_r][new_c] != 1) and (not can_reach[new_r][new_c]):\n                can_reach[new_r][new_c] = True\n                q.append((new_r, new_c))\n    return can_reach[max_row - 1][max_col - 1]", "input": "[[0, 1, 0],\n [0, 0, 0],\n [1, 0, 0]]", "output": "True", "imports": [], "original_snippet": "def f(maze: list[list[int]]) -> list[list[int]]:\n    max_row = len(maze)\n    max_col = len(maze[0])\n    can_reach = [[False] * max_col for _ in range(max_row)]\n    can_reach[0][0] = True\n    q = [(0, 0)]\n    while q:\n        (r, c) = q.pop(0)\n        if r == max_row - 1 and c == max_col - 1:\n            break\n        for (dr, dc) in [(-1, 0), (1, 0), (0, -1), (0, 1)]:\n            (new_r, new_c) = (r + dr, c + dc)\n            if 0 <= new_r < max_row and 0 <= new_c < max_col and (maze[new_r][new_c] != 1) and (not can_reach[new_r][new_c]):\n                can_reach[new_r][new_c] = True\n                q.append((new_r, new_c))\n    return can_reach[max_row - 1][max_col - 1]", "composite_functions": []}
{"snippet": "from typing import List\ndef f(lst: List[int]) -> int:\n    even_nums = [num for num in lst if num % 2 == 0]\n    sorted_nums = sorted(even_nums, reverse=True)\n    length = len(sorted_nums)\n    if length % 2 == 0:\n        median = (sorted_nums[length // 2] + sorted_nums[length // 2 - 1]) / 2\n    else:\n        median = sorted_nums[length // 2]\n    return int(median)", "input": "[1, 8, 3, 5, 9, 2, 7, 4, 6]", "output": "5", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(lst: List[int]) -> int:\n    even_nums = [num for num in lst if num % 2 == 0]\n    sorted_nums = sorted(even_nums, reverse=True)\n    length = len(sorted_nums)\n    if length % 2 == 0:\n        median = (sorted_nums[length // 2] + sorted_nums[length // 2 - 1]) / 2\n    else:\n        median = sorted_nums[length // 2]\n    return int(median)", "composite_functions": []}
{"snippet": "def f(input_list: list):\n    dict_list = [{input_list[i]: i + input_list[i]} for i in range(len(input_list))]\n    return dict_list", "input": "[5, 3, 1, 2, 8, 7, 6, 4]", "output": "[{5: 5}, {3: 4}, {1: 3}, {2: 5}, {8: 12}, {7: 12}, {6: 12}, {4: 11}]", "imports": [], "original_snippet": "def f(input_list: list):\n    dict_list = [{input_list[i]: i + input_list[i]} for i in range(len(input_list))]\n    return dict_list", "composite_functions": []}
{"snippet": "import heapq\ndef f(graph: list[list[int]]) -> list[int]:\n    distances = [float('inf')] * len(graph)\n    distances[0] = 0\n    heap = [(0, 0)]\n    while heap:\n        (current_distance, current_node) = heapq.heappop(heap)\n        if current_distance > distances[current_node]:\n            continue\n        for (neighbour, weight) in enumerate(graph[current_node]):\n            distance = current_distance + weight\n            if distance < distances[neighbour]:\n                distances[neighbour] = distance\n                heapq.heappush(heap, (distance, neighbour))\n    return distances", "input": "[[0, 3, 1, 8, 10, 11],\n [3, 0, 2, 5, 7, 4],\n [1, 2, 0, 9, 6, 3],\n [8, 5, 9, 0, 3, 2],\n [10, 7, 6, 3, 0, 5],\n [11, 4, 3, 2, 5, 0]]", "output": "[0, 3, 1, 6, 7, 4]", "imports": ["import heapq"], "original_snippet": "import heapq\ndef f(graph: list[list[int]]) -> list[int]:\n    distances = [float('inf')] * len(graph)\n    distances[0] = 0\n    heap = [(0, 0)]\n    while heap:\n        (current_distance, current_node) = heapq.heappop(heap)\n        if current_distance > distances[current_node]:\n            continue\n        for (neighbour, weight) in enumerate(graph[current_node]):\n            distance = current_distance + weight\n            if distance < distances[neighbour]:\n                distances[neighbour] = distance\n                heapq.heappush(heap, (distance, neighbour))\n    return distances", "composite_functions": []}
{"snippet": "def f(input_string: str) -> str:\n    result = ''\n    uppercase = True\n    for char in input_string:\n        if char.isupper():\n            result += char.lower()\n            print('UPPER')\n        elif char.islower():\n            result += char.upper()\n            print('LOWER')\n        elif char.isdigit():\n            result += char\n            print('NUMBER')\n        else:\n            result += char\n            print('OTHER')\n        uppercase = not uppercase\n    return result", "input": "'WeLl, Yous3'", "output": "'wElL, yOUS3'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    result = ''\n    uppercase = True\n    for char in input_string:\n        if char.isupper():\n            result += char.lower()\n            print('UPPER')\n        elif char.islower():\n            result += char.upper()\n            print('LOWER')\n        elif char.isdigit():\n            result += char\n            print('NUMBER')\n        else:\n            result += char\n            print('OTHER')\n        uppercase = not uppercase\n    return result", "composite_functions": []}
{"snippet": "def f(dictionary: dict, num: int) -> tuple:\n    sorted_keys = sorted([key for (key, value) in dictionary.items() if value > num], key=lambda key: dictionary[key], reverse=True)\n    return tuple(sorted_keys)", "input": "{'apple': 10, 'banana': 5, 'orange': 15, 'watermelon': 18, 'kiwi': 7}, 9", "output": "('watermelon', 'orange', 'apple')", "imports": [], "original_snippet": "def f(dictionary: dict, num: int) -> tuple:\n    sorted_keys = sorted([key for (key, value) in dictionary.items() if value > num], key=lambda key: dictionary[key], reverse=True)\n    return tuple(sorted_keys)", "composite_functions": []}
{"snippet": "from typing import Union\ndef f(partial_sequence: str) -> Union[str, None]:\n    if len(partial_sequence) <= 3:\n        return None\n    best_match = None\n    max_length = len(partial_sequence)\n    for word in COMMON_WORDS:\n        if word.startswith(partial_sequence):\n            if len(word) > max_length:\n                max_length = len(word)\n                best_match = word\n    return best_match if best_match else None", "input": "\"app\"", "output": "None", "imports": ["from typing import Union"], "original_snippet": "from typing import Union\ndef f(partial_sequence: str) -> Union[str, None]:\n    if len(partial_sequence) <= 3:\n        return None\n    best_match = None\n    max_length = len(partial_sequence)\n    for word in COMMON_WORDS:\n        if word.startswith(partial_sequence):\n            if len(word) > max_length:\n                max_length = len(word)\n                best_match = word\n    return best_match if best_match else None", "composite_functions": []}
{"snippet": "def f(file_list: list):\n    import os\n    results = []\n    for file_name in file_list:\n        file_exists = os.path.isfile(file_name)\n        if file_exists:\n            file_length = os.path.getsize(file_name)\n            results.append((file_name, file_length))\n        else:\n            results.append((file_name, ''))\n    return results", "input": "['test1.txt', 'test2.txt', 'nonexistent.txt']", "output": "[('test1.txt', ''), ('test2.txt', ''), ('nonexistent.txt', '')]", "imports": ["import os"], "original_snippet": "def f(file_list: list):\n    import os\n    results = []\n    for file_name in file_list:\n        file_exists = os.path.isfile(file_name)\n        if file_exists:\n            file_length = os.path.getsize(file_name)\n            results.append((file_name, file_length))\n        else:\n            results.append((file_name, ''))\n    return results", "composite_functions": []}
{"snippet": "def f(encoded_list):\n    result = []\n    for item1 in encoded_list:\n        for item2 in encoded_list:\n            if item1 != item2:\n                if item1 % item2 ** 2 == 1:\n                    result.append((item1, item2))\n    return result", "input": "[3, 11, 17, 17, 33]", "output": "[]", "imports": [], "original_snippet": "def f(encoded_list):\n    result = []\n    for item1 in encoded_list:\n        for item2 in encoded_list:\n            if item1 != item2:\n                if item1 % item2 ** 2 == 1:\n                    result.append((item1, item2))\n    return result", "composite_functions": []}
{"snippet": "def f(text: str):\n    reversed_text = text[::-1]\n    mutated_text = ''.join([char if i % 3 != 0 else chr(ord(char) + (2 * i + 1)) for (i, char) in enumerate(reversed_text)])\n    transformed_text = ''.join([char * (i + 1) if i % 3 == 0 else char for (i, char) in enumerate(mutated_text)])\n    return transformed_text", "input": "'TestString'", "output": "'hniyyyytS\\x81\\x81\\x81\\x81\\x81\\x81\\x81segggggggggg'", "imports": [], "original_snippet": "def f(text: str):\n    reversed_text = text[::-1]\n    mutated_text = ''.join([char if i % 3 != 0 else chr(ord(char) + (2 * i + 1)) for (i, char) in enumerate(reversed_text)])\n    transformed_text = ''.join([char * (i + 1) if i % 3 == 0 else char for (i, char) in enumerate(mutated_text)])\n    return transformed_text", "composite_functions": []}
{"snippet": "def f(input_list):\n    output_list = []\n    for i in range(len(input_list)):\n        output_list.append(input_list[i] - (i + 1))\n    return output_list", "input": "[1, 2, 3]", "output": "[0, 0, 0]", "imports": [], "original_snippet": "def f(input_list):\n    output_list = []\n    for i in range(len(input_list)):\n        output_list.append(input_list[i] - (i + 1))\n    return output_list", "composite_functions": []}
{"snippet": "def f(activations_and_readings: list):\n    (activations, readings) = activations_and_readings\n    min_val = min(readings)\n    peak_acv = min(activations)\n    for (acv, reading) in zip(activations, readings):\n        adjusted_acv = acv - min_val\n        if adjusted_acv > peak_acv:\n            peak_acv = adjusted_acv\n    return peak_acv - min_val", "input": "[[3, 5, 2, 4, 7], [1, 2, 3, 4, 5]]", "output": "5", "imports": [], "original_snippet": "def f(activations_and_readings: list):\n    (activations, readings) = activations_and_readings\n    min_val = min(readings)\n    peak_acv = min(activations)\n    for (acv, reading) in zip(activations, readings):\n        adjusted_acv = acv - min_val\n        if adjusted_acv > peak_acv:\n            peak_acv = adjusted_acv\n    return peak_acv - min_val", "composite_functions": []}
{"snippet": "def f(a: list):\n    max_sum = 0\n    current_sum = 0\n    for num in a:\n        current_sum += num\n        if current_sum < 0:\n            current_sum = 0\n        elif current_sum > max_sum:\n            max_sum = current_sum\n    return max_sum", "input": "[-2, 1, -3, 4, -1, 2, 1, -5, 4]", "output": "6", "imports": [], "original_snippet": "def f(a: list):\n    max_sum = 0\n    current_sum = 0\n    for num in a:\n        current_sum += num\n        if current_sum < 0:\n            current_sum = 0\n        elif current_sum > max_sum:\n            max_sum = current_sum\n    return max_sum", "composite_functions": []}
{"snippet": "def f(lst: list):\n    if not lst:\n        return []\n    sorted_and_indices = sorted(((x, i) for (i, x) in enumerate(lst)))\n    result = [sorted_and_indices[i][1] for i in range(len(sorted_and_indices))]\n    return result", "input": "[3, 2, 1, 5, 4]", "output": "[2, 1, 0, 4, 3]", "imports": [], "original_snippet": "def f(lst: list):\n    if not lst:\n        return []\n    sorted_and_indices = sorted(((x, i) for (i, x) in enumerate(lst)))\n    result = [sorted_and_indices[i][1] for i in range(len(sorted_and_indices))]\n    return result", "composite_functions": []}
{"snippet": "def f(list1: list, list2: list):\n    return [[a * b for a in list1] for b in list2]", "input": "[1, 2, 3], [4, 5]", "output": "[[4, 8, 12], [5, 10, 15]]", "imports": [], "original_snippet": "def f(list1: list, list2: list):\n    return [[a * b for a in list1] for b in list2]", "composite_functions": []}
{"snippet": "def f(numbers):\n    data = numbers[:]\n    n = len(data)\n    for i in range(n):\n        min_index = i\n        tmp_value = data[min_index]\n        for j in range(i + 1, n):\n            if tmp_value > data[j]:\n                tmp_value = data[j]\n                min_index = j\n        (data[i], data[min_index]) = (data[min_index], tmp_value)\n    return data", "input": "[5, 3, 1, 4, 2]", "output": "[1, 1, 1, 2, 2]", "imports": [], "original_snippet": "def f(numbers):\n    data = numbers[:]\n    n = len(data)\n    for i in range(n):\n        min_index = i\n        tmp_value = data[min_index]\n        for j in range(i + 1, n):\n            if tmp_value > data[j]:\n                tmp_value = data[j]\n                min_index = j\n        (data[i], data[min_index]) = (data[min_index], tmp_value)\n    return data", "composite_functions": []}
{"snippet": "def f(array):\n    state = {}\n    ctr = 0\n    for (current, previous) in zip(array[1:], array):\n        if current >= previous:\n            ctr += 1\n        else:\n            ctr = 0\n        if 'best' not in state:\n            state['best'] = ctr\n        else:\n            state['best'] = max(state['best'], ctr)\n    return state['best']", "input": "[[9, 3, 6], [10, 5, 7, 8, 2], [0]]", "output": "1", "imports": [], "original_snippet": "def f(array):\n    state = {}\n    ctr = 0\n    for (current, previous) in zip(array[1:], array):\n        if current >= previous:\n            ctr += 1\n        else:\n            ctr = 0\n        if 'best' not in state:\n            state['best'] = ctr\n        else:\n            state['best'] = max(state['best'], ctr)\n    return state['best']", "composite_functions": []}
{"snippet": "def f(original: str) -> str:\n    reversed_and_mapped = ''\n    for c in original:\n        reversed_and_mapped += str(ord(c)) + c[::-1]\n    return reversed_and_mapped[::-1]", "input": "\"HelloWorld\"", "output": "'d001l801r411o111W78o111l801l801e101H27'", "imports": [], "original_snippet": "def f(original: str) -> str:\n    reversed_and_mapped = ''\n    for c in original:\n        reversed_and_mapped += str(ord(c)) + c[::-1]\n    return reversed_and_mapped[::-1]", "composite_functions": []}
{"snippet": "def f(n: int):\n    target = 0\n    for n in (1, n):\n        target = target and target >= n - target and n / 2 and 0\n    return target", "input": "100", "output": "0", "imports": [], "original_snippet": "def f(n: int):\n    target = 0\n    for n in (1, n):\n        target = target and target >= n - target and n / 2 and 0\n    return target", "composite_functions": []}
{"snippet": "def f(sample: str, first_index: int, last_index: int, reverse: bool):\n    length = len(sample)\n    try:\n        if first_index < 0 or last_index > length:\n            return 'Indexes out of range'\n        elif not reverse:\n            return sample[:first_index] + sample[last_index::-1] + sample[last_index + 1:]\n        else:\n            return 'Current option does not reverse the string'\n    except IndexError as e:\n        return e", "input": "\"Hello, World!\", 2, 7, False", "output": "'HeW ,olleHorld!'", "imports": [], "original_snippet": "def f(sample: str, first_index: int, last_index: int, reverse: bool):\n    length = len(sample)\n    try:\n        if first_index < 0 or last_index > length:\n            return 'Indexes out of range'\n        elif not reverse:\n            return sample[:first_index] + sample[last_index::-1] + sample[last_index + 1:]\n        else:\n            return 'Current option does not reverse the string'\n    except IndexError as e:\n        return e", "composite_functions": []}
{"snippet": "def f(points):\n    num_points = len(points)\n    total_distance = 0\n    count = 0\n    for i in range(num_points - 1):\n        for j in range(i + 1, num_points):\n            distance = ((points[i][0] - points[j][0]) ** 2 + (points[i][1] - points[j][1]) ** 2) ** 0.5\n            total_distance += distance\n            count += 1\n    average_distance = total_distance / count\n    return average_distance", "input": "[(0, 0), (3, 4), (6, 8)]", "output": "6.666666666666667", "imports": [], "original_snippet": "def f(points):\n    num_points = len(points)\n    total_distance = 0\n    count = 0\n    for i in range(num_points - 1):\n        for j in range(i + 1, num_points):\n            distance = ((points[i][0] - points[j][0]) ** 2 + (points[i][1] - points[j][1]) ** 2) ** 0.5\n            total_distance += distance\n            count += 1\n    average_distance = total_distance / count\n    return average_distance", "composite_functions": []}
{"snippet": "def f(num: int) -> int:\n    if num < 1:\n        return 0\n    smallest_power_of_2 = 1\n    while smallest_power_of_2 << 1 <= num:\n        smallest_power_of_2 <<= 1\n    return num - smallest_power_of_2", "input": "42", "output": "10", "imports": [], "original_snippet": "def f(num: int) -> int:\n    if num < 1:\n        return 0\n    smallest_power_of_2 = 1\n    while smallest_power_of_2 << 1 <= num:\n        smallest_power_of_2 <<= 1\n    return num - smallest_power_of_2", "composite_functions": []}
{"snippet": "def f(sequence: list, amount: int, value: int):\n    result = 0\n    for (i, num) in enumerate(sequence):\n        result += num * i\n    if result > value:\n        return True\n    return False", "input": "[1, 2, 3, 4, 5], 2, 5", "output": "True", "imports": [], "original_snippet": "def f(sequence: list, amount: int, value: int):\n    result = 0\n    for (i, num) in enumerate(sequence):\n        result += num * i\n    if result > value:\n        return True\n    return False", "composite_functions": []}
{"snippet": "def f(numbers):\n    total_sum = 0\n    current_index = 0\n    for number in numbers:\n        total_sum += current_index / len(numbers) + 1\n        current_index += 1\n    return total_sum", "input": "[5, 10, 20, 30, 40, 50]", "output": "8.5", "imports": [], "original_snippet": "def f(numbers):\n    total_sum = 0\n    current_index = 0\n    for number in numbers:\n        total_sum += current_index / len(numbers) + 1\n        current_index += 1\n    return total_sum", "composite_functions": []}
{"snippet": "def f(lst: list, different_value: float=3.14159) -> str:\n    num_dict = {num: lst.count(num) for num in lst}\n    num_dict = {num: different_value ** abs(count) for (num, count) in num_dict.items()}\n    result = []\n    for (num, division) in num_dict.items():\n        if division == 1:\n            result.append(str(num))\n        else:\n            result.append(f'{num}:{division}')\n    return ', '.join(result)", "input": "[1, 1, 1, 2, 2, 4, 4, 4, 4]", "output": "'1:31.006198110721677, 2:9.869587728099999, 4:97.40876192266211'", "imports": [], "original_snippet": "def f(lst: list, different_value: float=3.14159) -> str:\n    num_dict = {num: lst.count(num) for num in lst}\n    num_dict = {num: different_value ** abs(count) for (num, count) in num_dict.items()}\n    result = []\n    for (num, division) in num_dict.items():\n        if division == 1:\n            result.append(str(num))\n        else:\n            result.append(f'{num}:{division}')\n    return ', '.join(result)", "composite_functions": []}
{"snippet": "import base64\nimport zlib\ndef f(input_string: str, shift: int) -> str:\n    processed_string = ''\n    for char in input_string:\n        if char.isalpha():\n            shift_amount = (ord(char.lower()) - ord('a') + shift) % 26\n            processed_string += chr(ord('a') + shift_amount)\n        elif char.isnumeric():\n            shift_amount = (int(char) + shift) % 10\n            processed_string += str(shift_amount)\n        else:\n            processed_string += char\n    compressed_data = zlib.compress(processed_string.encode('utf-8'))\n    encrypted_data = base64.b64encode(compressed_data)\n    result = encrypted_data.decode('utf-8')\n    return result", "input": "'HelloWorld123', 3", "output": "'eJzLzsjPL6oqKs1PNzE1AwAmKAT6'", "imports": ["import base64", "import zlib"], "original_snippet": "import base64\nimport zlib\ndef f(input_string: str, shift: int) -> str:\n    processed_string = ''\n    for char in input_string:\n        if char.isalpha():\n            shift_amount = (ord(char.lower()) - ord('a') + shift) % 26\n            processed_string += chr(ord('a') + shift_amount)\n        elif char.isnumeric():\n            shift_amount = (int(char) + shift) % 10\n            processed_string += str(shift_amount)\n        else:\n            processed_string += char\n    compressed_data = zlib.compress(processed_string.encode('utf-8'))\n    encrypted_data = base64.b64encode(compressed_data)\n    result = encrypted_data.decode('utf-8')\n    return result", "composite_functions": []}
{"snippet": "def f(numbers: list[int]):\n    abs_sum = 0\n    for number in numbers:\n        abs_sum += abs(number)\n    result = abs_sum / len(numbers)\n    return result", "input": "[1, -2, 3, -4, 5, -6]", "output": "3.5", "imports": [], "original_snippet": "def f(numbers: list[int]):\n    abs_sum = 0\n    for number in numbers:\n        abs_sum += abs(number)\n    result = abs_sum / len(numbers)\n    return result", "composite_functions": []}
{"snippet": "def f(path: str):\n    levels = 0\n    words = 0\n    total_chars = 0\n    current_level = 0\n    current_word = ''\n    current_char = ''\n    for char in path:\n        if char == '(':\n            current_level += 1\n        elif char == ')':\n            if current_level > 0 and current_word != '' and (current_char != ''):\n                total_chars += len(current_char) * current_word\n                words += current_word\n            current_level -= 1\n        elif char == ' ':\n            if current_level != 0:\n                current_char += char\n    if current_level != 0:\n        print('Invalid path format.')\n        return (0, 0, 0)\n    print('Cuncuting off %d levels with %d words which make up %d total chars', levels, words, total_chars)\n    return (levels, words, total_chars)", "input": "\"( ( ( one two ) three ) four five ) six \"", "output": "(0, 0, 0)", "imports": [], "original_snippet": "def f(path: str):\n    levels = 0\n    words = 0\n    total_chars = 0\n    current_level = 0\n    current_word = ''\n    current_char = ''\n    for char in path:\n        if char == '(':\n            current_level += 1\n        elif char == ')':\n            if current_level > 0 and current_word != '' and (current_char != ''):\n                total_chars += len(current_char) * current_word\n                words += current_word\n            current_level -= 1\n        elif char == ' ':\n            if current_level != 0:\n                current_char += char\n    if current_level != 0:\n        print('Invalid path format.')\n        return (0, 0, 0)\n    print('Cuncuting off %d levels with %d words which make up %d total chars', levels, words, total_chars)\n    return (levels, words, total_chars)", "composite_functions": []}
{"snippet": "from collections import Counter\ndef f(lst: list) -> list:\n    result = []\n    for i in range(len(lst)):\n        item_counter = Counter(lst[i])\n        common_items = {k: v for (k, v) in item_counter.items() if item_counter[k] > 1}\n        uncommon_items = {k: v for (k, v) in item_counter.items() if item_counter[k] < 2}\n        if uncommon_items:\n            result.append(uncommon_items)\n            break\n    return result", "input": "[[1, 2, 3, 1, 3], [2, 1, 3, 2, 3], [3, 2, 1, 3, 2], [4, 4, 4, 4, 4], [1, 2, 3, 2, 3]]", "output": "[{2: 1}]", "imports": ["from collections import Counter"], "original_snippet": "from collections import Counter\ndef f(lst: list) -> list:\n    result = []\n    for i in range(len(lst)):\n        item_counter = Counter(lst[i])\n        common_items = {k: v for (k, v) in item_counter.items() if item_counter[k] > 1}\n        uncommon_items = {k: v for (k, v) in item_counter.items() if item_counter[k] < 2}\n        if uncommon_items:\n            result.append(uncommon_items)\n            break\n    return result", "composite_functions": []}
{"snippet": "def f(n):\n    if type(n) != int:\n        return None\n    if n > 30:\n        return [i for i in range(n, 1, -1)]\n    elif n < 0:\n        return [n ** j % n for j in range(1, 10)]\n    else:\n        return sum((i for i in range(1, n + 1)), 2)", "input": "4", "output": "12", "imports": [], "original_snippet": "def f(n):\n    if type(n) != int:\n        return None\n    if n > 30:\n        return [i for i in range(n, 1, -1)]\n    elif n < 0:\n        return [n ** j % n for j in range(1, 10)]\n    else:\n        return sum((i for i in range(1, n + 1)), 2)", "composite_functions": []}
{"snippet": "def f(arr):\n    unique_dicts = {k: v for d in arr for (k, v) in d.items()}\n    sorted_nums = sorted(unique_dicts.items(), key=lambda x: x[1], reverse=True)\n    odd_pairs = [num for num in sorted_nums if sorted_nums.count(num) % 2 == 1]\n    result = [num[1] for num in odd_pairs]\n    return result", "input": "[{2: [7, 7]}, {6: [7, 7]}, {9: [7, 7]}, {9: [7, 7]}, {3: [7, 7]}, {8: [7, 7]}, {8: [7, 7]}, {6: [7, 7]}, {3: [7, 7]}, {6: [7, 7]}]", "output": "[[7, 7], [7, 7], [7, 7], [7, 7], [7, 7]]", "imports": [], "original_snippet": "def f(arr):\n    unique_dicts = {k: v for d in arr for (k, v) in d.items()}\n    sorted_nums = sorted(unique_dicts.items(), key=lambda x: x[1], reverse=True)\n    odd_pairs = [num for num in sorted_nums if sorted_nums.count(num) % 2 == 1]\n    result = [num[1] for num in odd_pairs]\n    return result", "composite_functions": []}
{"snippet": "def f(input_string: str) -> int:\n    ascii_sum = 0\n    for char in input_string:\n        ascii_sum += ord(char)\n    return ascii_sum + len(input_string)", "input": "'ABCDEFGHIJKLMNOPQRSTUVWXYZ'", "output": "2041", "imports": [], "original_snippet": "def f(input_string: str) -> int:\n    ascii_sum = 0\n    for char in input_string:\n        ascii_sum += ord(char)\n    return ascii_sum + len(input_string)", "composite_functions": []}
{"snippet": "def f(name: str, info: dict):\n    age = info['age']\n    desired_age = 20\n    age_gap = desired_age - age\n    return age_gap", "input": "'John', {'age': 20}", "output": "0", "imports": [], "original_snippet": "def f(name: str, info: dict):\n    age = info['age']\n    desired_age = 20\n    age_gap = desired_age - age\n    return age_gap", "composite_functions": []}
{"snippet": "def f(dicts: list[dict]) -> list[tuple]:\n    result = []\n    for d in dicts:\n        mentioned = d.get('mentioned', 0)\n        words = d.get('words', 0)\n        negation_proximities = d.get('negation_proximities', 0)\n        cooccurrences = d.get('cooccurrences', 0)\n        mentions = d.get('mentions', 0)\n        linked = d.get('linked', 0)\n        mfs_key_freq = d.get('mfs_key_freq', 0)\n        result.append({'element_name': d.get('element_name'), 'max_value': max(mentioned, words, negation_proximities, cooccurrences, mentions, linked, mfs_key_freq)})\n    result.sort(key=lambda x: x['max_value'], reverse=True)\n    return result", "input": "[{'element_name': 'mentioned', 'mentioned': 10, 'words': 5, 'negation_proximities': 15, 'cooccurrences': 20, 'mentions': 8, 'linked': 2, 'mfs_key_freq': 7},\n {'element_name': 'words', 'mentioned': 12, 'words': 25, 'negation_proximities': 6, 'cooccurrences': 8, 'mentions': 4, 'linked': 11, 'mfs_key_freq': 20},\n {'element_name': 'negation_proximities', 'mentioned': 1, 'words': 7, 'negation_proximities': 30, 'cooccurrences': 5, 'mentions': 3, 'linked': 9, 'mfs_key_freq': 6},\n {'element_name': 'cooccurrences', 'mentioned': 16, 'words': 13, 'negation_proximities': 9, 'cooccurrences': 15, 'mentions': 18, 'linked': 12, 'mfs_key_freq': 4},\n {'element_name': 'mentions', 'mentioned': 14, 'words': 19, 'negation_proximities': 1, 'cooccurrences': 16, 'mentions': 20, 'linked': 3, 'mfs_key_freq': 17}]", "output": "[{'element_name': 'negation_proximities', 'max_value': 30}, {'element_name': 'words', 'max_value': 25}, {'element_name': 'mentioned', 'max_value': 20}, {'element_name': 'mentions', 'max_value': 20}, {'element_name': 'cooccurrences', 'max_value': 18}]", "imports": [], "original_snippet": "def f(dicts: list[dict]) -> list[tuple]:\n    result = []\n    for d in dicts:\n        mentioned = d.get('mentioned', 0)\n        words = d.get('words', 0)\n        negation_proximities = d.get('negation_proximities', 0)\n        cooccurrences = d.get('cooccurrences', 0)\n        mentions = d.get('mentions', 0)\n        linked = d.get('linked', 0)\n        mfs_key_freq = d.get('mfs_key_freq', 0)\n        result.append({'element_name': d.get('element_name'), 'max_value': max(mentioned, words, negation_proximities, cooccurrences, mentions, linked, mfs_key_freq)})\n    result.sort(key=lambda x: x['max_value'], reverse=True)\n    return result", "composite_functions": []}
{"snippet": "def f(strings: list[str], threshold: int) -> int:\n    vowel_count = 0\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    for string in strings:\n        num_vowels = sum((1 for char in string if char in vowels))\n        if len(string) > threshold:\n            vowel_count += num_vowels\n    return vowel_count", "input": "['apple', 'banana', 'orange', 'grape'], 2", "output": "10", "imports": [], "original_snippet": "def f(strings: list[str], threshold: int) -> int:\n    vowel_count = 0\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    for string in strings:\n        num_vowels = sum((1 for char in string if char in vowels))\n        if len(string) > threshold:\n            vowel_count += num_vowels\n    return vowel_count", "composite_functions": []}
{"snippet": "def f(s: str) -> str:\n    pairs = []\n    pairs.append((s[0], s[1]))\n    idx = 2\n    while idx < len(s):\n        if idx + 1 < len(s):\n            num = idx\n            num_max = idx + 3\n            while num < num_max:\n                if num >= len(s):\n                    break\n                str1 = s[num:idx]\n                str2 = s[idx:]\n                pairs.append((str1, str2))\n                num += 2\n                idx += 2\n        else:\n            break\n        idx += 2\n    return pairs", "input": "'HelloWorld'", "output": "[('H', 'e'), ('', 'lloWorld'), ('', 'oWorld'), ('', 'ld')]", "imports": [], "original_snippet": "def f(s: str) -> str:\n    pairs = []\n    pairs.append((s[0], s[1]))\n    idx = 2\n    while idx < len(s):\n        if idx + 1 < len(s):\n            num = idx\n            num_max = idx + 3\n            while num < num_max:\n                if num >= len(s):\n                    break\n                str1 = s[num:idx]\n                str2 = s[idx:]\n                pairs.append((str1, str2))\n                num += 2\n                idx += 2\n        else:\n            break\n        idx += 2\n    return pairs", "composite_functions": []}
{"snippet": "def f(input_string: str) -> str:\n    input_list = input_string.split()\n    freq_dict = {word: input_list.count(word) for word in input_list}\n    max_freq = max(freq_dict, key=freq_dict.get)\n    cleaned_list = [word for word in input_list if freq_dict[word] == 1]\n    cleaned_list.sort()\n    if len(cleaned_list) > 0:\n        return 'one'\n    else:\n        return 'none'", "input": "\"I am an Iq test subject\"", "output": "'one'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    input_list = input_string.split()\n    freq_dict = {word: input_list.count(word) for word in input_list}\n    max_freq = max(freq_dict, key=freq_dict.get)\n    cleaned_list = [word for word in input_list if freq_dict[word] == 1]\n    cleaned_list.sort()\n    if len(cleaned_list) > 0:\n        return 'one'\n    else:\n        return 'none'", "composite_functions": []}
{"snippet": "def f(string):\n    if string in ['Hello Universe!']:\n        leng = 20\n        return f'{string}, Goodbye Universe!' + ' ' * leng\n    else:\n        return string", "input": "'Hello Universe!'", "output": "'Hello Universe!, Goodbye Universe!                    '", "imports": [], "original_snippet": "def f(string):\n    if string in ['Hello Universe!']:\n        leng = 20\n        return f'{string}, Goodbye Universe!' + ' ' * leng\n    else:\n        return string", "composite_functions": []}
{"snippet": "def f(start: int, end: int, n: int):\n    happy_days = 0\n    prev_day = start\n    for day in range(start + 1, end):\n        if day > prev_day and (day - start) % n == 0:\n            happy_days += 1\n            prev_day = day\n    return happy_days", "input": "10, 30, 5", "output": "3", "imports": [], "original_snippet": "def f(start: int, end: int, n: int):\n    happy_days = 0\n    prev_day = start\n    for day in range(start + 1, end):\n        if day > prev_day and (day - start) % n == 0:\n            happy_days += 1\n            prev_day = day\n    return happy_days", "composite_functions": []}
{"snippet": "def f(encoded_str: str):\n    char_to_decoded_map = {'A': lambda pos: str(pos % 10), 'B': lambda pos: str(pos % 5) if pos % 2 == 0 else 'X', 'C': lambda pos: chr((pos + 2) % 26 + ord('A'))}\n    result = []\n    pos = 0\n    for char in encoded_str:\n        if char.isupper() and char in char_to_decoded_map:\n            result.append(char_to_decoded_map[char](pos))\n        elif char.islower() and pos % 2 == 1:\n            result.append(char.upper())\n        elif char.isdigit():\n            result.append(str(int(char) + pos % 10))\n        pos += 1\n    return ''.join(result)", "input": "'AB4Cd6BC12313'", "output": "'0X6F111J911325'", "imports": [], "original_snippet": "def f(encoded_str: str):\n    char_to_decoded_map = {'A': lambda pos: str(pos % 10), 'B': lambda pos: str(pos % 5) if pos % 2 == 0 else 'X', 'C': lambda pos: chr((pos + 2) % 26 + ord('A'))}\n    result = []\n    pos = 0\n    for char in encoded_str:\n        if char.isupper() and char in char_to_decoded_map:\n            result.append(char_to_decoded_map[char](pos))\n        elif char.islower() and pos % 2 == 1:\n            result.append(char.upper())\n        elif char.isdigit():\n            result.append(str(int(char) + pos % 10))\n        pos += 1\n    return ''.join(result)", "composite_functions": []}
{"snippet": "def f(x):\n    if x < 1:\n        return []\n    prime_list = [2]\n    for n in range(3, x + 1, 2):\n        is_prime = all((n % prime != 0 for prime in prime_list))\n        if is_prime:\n            prime_list.append(n)\n    return prime_list", "input": "97", "output": "[2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97]", "imports": [], "original_snippet": "def f(x):\n    if x < 1:\n        return []\n    prime_list = [2]\n    for n in range(3, x + 1, 2):\n        is_prime = all((n % prime != 0 for prime in prime_list))\n        if is_prime:\n            prime_list.append(n)\n    return prime_list", "composite_functions": []}
{"snippet": "from typing import List\ndef f(n: int, m: int, sub_tri: List[List[int]]):\n    result = 0\n    for i in range(n):\n        for j in range(m):\n            result += sub_tri[i][j] ** 2\n    return result", "input": "1, 2, [[3, 4, 5], [6, 7, 8]]", "output": "25", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(n: int, m: int, sub_tri: List[List[int]]):\n    result = 0\n    for i in range(n):\n        for j in range(m):\n            result += sub_tri[i][j] ** 2\n    return result", "composite_functions": []}
{"snippet": "def f(input_list: list) -> dict:\n    largest = max(input_list)\n    primes_with_value = {}\n    precondition = [9997, 10007, 10009]\n    for i in precondition:\n        if largest % i == 0:\n            primes_with_value[i] = i ** (largest // i)\n            break\n    return primes_with_value", "input": "[5623, 9999, 9999, 10007, 10007, 5723, 5723, 123456, 123456, 123456, 123456]", "output": "{}", "imports": [], "original_snippet": "def f(input_list: list) -> dict:\n    largest = max(input_list)\n    primes_with_value = {}\n    precondition = [9997, 10007, 10009]\n    for i in precondition:\n        if largest % i == 0:\n            primes_with_value[i] = i ** (largest // i)\n            break\n    return primes_with_value", "composite_functions": []}
{"snippet": "def f(nested_list: list) -> int:\n    product = 1\n    for inner_list in nested_list:\n        if len(inner_list) >= 2:\n            product *= inner_list[0] * inner_list[1]\n    return product", "input": "[[1, 3], [4, 2], [5, 6]]", "output": "720", "imports": [], "original_snippet": "def f(nested_list: list) -> int:\n    product = 1\n    for inner_list in nested_list:\n        if len(inner_list) >= 2:\n            product *= inner_list[0] * inner_list[1]\n    return product", "composite_functions": []}
{"snippet": "def f(pairs: list[tuple[str]]) -> dict[str, int]:\n    result_dict = {}\n    for (str, count) in pairs:\n        if str not in result_dict:\n            result_dict[str] = int(count)\n    return result_dict", "input": "[('a', '2'), ('b', '3'), ('a', '1')]", "output": "{'a': 2, 'b': 3}", "imports": [], "original_snippet": "def f(pairs: list[tuple[str]]) -> dict[str, int]:\n    result_dict = {}\n    for (str, count) in pairs:\n        if str not in result_dict:\n            result_dict[str] = int(count)\n    return result_dict", "composite_functions": []}
{"snippet": "def f(tuples_list: list[tuple[str, int]]) -> str:\n    sorted_list = sorted(tuples_list, key=lambda x: (-x[1], x[0]))\n    formatted_str = ''\n    for tuple_item in sorted_list:\n        formatted_str += f'{tuple_item[0]} {str(tuple_item[1])}\\n'\n    return formatted_str", "input": "[(\"apple\", -3), (\"orange\", -2), (\"banana\", -1), (\"grape\", 1), (\"fig\", 1), (\"kiwi\", 2), (\"melon\", 2)]", "output": "'kiwi 2\\nmelon 2\\nfig 1\\ngrape 1\\nbanana -1\\norange -2\\napple -3\\n'", "imports": [], "original_snippet": "def f(tuples_list: list[tuple[str, int]]) -> str:\n    sorted_list = sorted(tuples_list, key=lambda x: (-x[1], x[0]))\n    formatted_str = ''\n    for tuple_item in sorted_list:\n        formatted_str += f'{tuple_item[0]} {str(tuple_item[1])}\\n'\n    return formatted_str", "composite_functions": []}
{"snippet": "def f(sum_of_numbers):\n    input_list = []\n    for i in range(1, sum_of_numbers):\n        if sum(input_list) + i == sum_of_numbers:\n            input_list.append(i)\n            break\n        elif sum(input_list) + i < sum_of_numbers:\n            input_list.append(i)\n    if sum(input_list) < sum_of_numbers:\n        last_item = sum_of_numbers - sum(input_list)\n        input_list.append(last_item)\n    return input_list", "input": "15", "output": "[1, 2, 3, 4, 5]", "imports": [], "original_snippet": "def f(sum_of_numbers):\n    input_list = []\n    for i in range(1, sum_of_numbers):\n        if sum(input_list) + i == sum_of_numbers:\n            input_list.append(i)\n            break\n        elif sum(input_list) + i < sum_of_numbers:\n            input_list.append(i)\n    if sum(input_list) < sum_of_numbers:\n        last_item = sum_of_numbers - sum(input_list)\n        input_list.append(last_item)\n    return input_list", "composite_functions": []}
{"snippet": "def f(lst: list) -> int:\n    set_lst = set(lst)\n    lst = sorted(set_lst)\n    for i in range(1, len(lst) + 2):\n        if i not in lst:\n            return i", "input": "[1, 3, -2, 9, -5, 6, 0, -1]", "output": "2", "imports": [], "original_snippet": "def f(lst: list) -> int:\n    set_lst = set(lst)\n    lst = sorted(set_lst)\n    for i in range(1, len(lst) + 2):\n        if i not in lst:\n            return i", "composite_functions": []}
{"snippet": "from string import ascii_lowercase\ndef f(s: str):\n    result = ''\n    cipher_map = dict(zip(ascii_lowercase, reversed(ascii_lowercase)))\n    for char in s.lower():\n        if char.isalpha():\n            result += cipher_map[char]\n        else:\n            result += char\n    return result", "input": "\"Incredible String @123!\"", "output": "'rmxivwryov hgirmt @123!'", "imports": ["from string import ascii_lowercase"], "original_snippet": "from string import ascii_lowercase\ndef f(s: str):\n    result = ''\n    cipher_map = dict(zip(ascii_lowercase, reversed(ascii_lowercase)))\n    for char in s.lower():\n        if char.isalpha():\n            result += cipher_map[char]\n        else:\n            result += char\n    return result", "composite_functions": []}
{"snippet": "def f(num: int, divisors: list=None):\n    if divisors is None:\n        divisors = [1]\n    if num == 0:\n        return -1\n    else:\n        divisor = divisors[-1]\n        if divisor == num:\n            return int(num > 1)\n        else:\n            while True:\n                if divisor > num / divisor:\n                    break\n                if num % divisor == 0:\n                    num_divisor = num // divisor\n                    divisors.append(num_divisor)\n                    result = 1 + f(num_divisor, divisors)\n                    divisors.pop()\n                    return result\n                divisor += 1\n    return 0", "input": "7", "output": "2", "imports": [], "original_snippet": "def f(num: int, divisors: list=None):\n    if divisors is None:\n        divisors = [1]\n    if num == 0:\n        return -1\n    else:\n        divisor = divisors[-1]\n        if divisor == num:\n            return int(num > 1)\n        else:\n            while True:\n                if divisor > num / divisor:\n                    break\n                if num % divisor == 0:\n                    num_divisor = num // divisor\n                    divisors.append(num_divisor)\n                    result = 1 + f(num_divisor, divisors)\n                    divisors.pop()\n                    return result\n                divisor += 1\n    return 0", "composite_functions": []}
{"snippet": "def f(numbers: list) -> list:\n    sum_of_numbers = sum(numbers)\n    multiplication = 1\n    factorial_numbers = []\n    for num in numbers:\n        if num % 2 == 0:\n            multiplication *= num\n        else:\n            odd_factorial = 1\n            for factor in range(1, num + 1):\n                odd_factorial *= factor\n            factorial_numbers.append(num * odd_factorial)\n    combined_factor = 1\n    for num in factorial_numbers:\n        combined_factor *= num\n    response_sum = combined_factor\n    transformed_list = []\n    for (index, num) in enumerate(numbers):\n        diff = num - index\n        if diff % response_sum == 0:\n            transformed_list.append(diff // response_sum)\n    return transformed_list", "input": "[5, 3, 3, 2, 2]", "output": "[]", "imports": [], "original_snippet": "def f(numbers: list) -> list:\n    sum_of_numbers = sum(numbers)\n    multiplication = 1\n    factorial_numbers = []\n    for num in numbers:\n        if num % 2 == 0:\n            multiplication *= num\n        else:\n            odd_factorial = 1\n            for factor in range(1, num + 1):\n                odd_factorial *= factor\n            factorial_numbers.append(num * odd_factorial)\n    combined_factor = 1\n    for num in factorial_numbers:\n        combined_factor *= num\n    response_sum = combined_factor\n    transformed_list = []\n    for (index, num) in enumerate(numbers):\n        diff = num - index\n        if diff % response_sum == 0:\n            transformed_list.append(diff // response_sum)\n    return transformed_list", "composite_functions": []}
{"snippet": "def f(n: int, k: int) -> int:\n    num = 1\n    while True:\n        if num > 1:\n            if k == (num + 1) // 2:\n                return k\n            if not num % 2:\n                return k + 1\n        num += 1", "input": "10, 5", "output": "6", "imports": [], "original_snippet": "def f(n: int, k: int) -> int:\n    num = 1\n    while True:\n        if num > 1:\n            if k == (num + 1) // 2:\n                return k\n            if not num % 2:\n                return k + 1\n        num += 1", "composite_functions": []}
{"snippet": "def f(numbers: list, threshold: int, string: str):\n    even_nums = [num for num in numbers if num > threshold and num % 2 == 0]\n    sum_evens = sum(even_nums)\n    result = str(sum_evens) + string\n    return result", "input": "[7, 15, 22, 28, 9, 16, 24], 20, 'challenge'", "output": "'74challenge'", "imports": [], "original_snippet": "def f(numbers: list, threshold: int, string: str):\n    even_nums = [num for num in numbers if num > threshold and num % 2 == 0]\n    sum_evens = sum(even_nums)\n    result = str(sum_evens) + string\n    return result", "composite_functions": []}
{"snippet": "def f(sentence: str) -> str:\n    char_map = {}\n    for char in sentence:\n        char_map[char] = char_map.get(char, 0) + 1\n    max_freq = -1\n    most_common_char = ''\n    for (char, freq) in char_map.items():\n        if freq > max_freq:\n            max_freq = freq\n            most_common_char = char\n    return most_common_char", "input": "\"The quick brown fox jumps over the lazy dog\"", "output": "' '", "imports": [], "original_snippet": "def f(sentence: str) -> str:\n    char_map = {}\n    for char in sentence:\n        char_map[char] = char_map.get(char, 0) + 1\n    max_freq = -1\n    most_common_char = ''\n    for (char, freq) in char_map.items():\n        if freq > max_freq:\n            max_freq = freq\n            most_common_char = char\n    return most_common_char", "composite_functions": []}
{"snippet": "def f(regions: list[tuple[int, int]]) -> bool:\n    tops = []\n    rights = []\n    for (x, y) in regions:\n        if y not in tops:\n            tops.append(y)\n        if x not in rights:\n            rights.append(x)\n    if tops[-1] == len(regions) - 1 or len(regions) == 1:\n        return True\n    if rights[-1] == len(regions) - 1:\n        return False\n    else:\n        return True", "input": "[(4, 3), (4, 2), (3, 1), (2, 2), (1, 1)]", "output": "True", "imports": [], "original_snippet": "def f(regions: list[tuple[int, int]]) -> bool:\n    tops = []\n    rights = []\n    for (x, y) in regions:\n        if y not in tops:\n            tops.append(y)\n        if x not in rights:\n            rights.append(x)\n    if tops[-1] == len(regions) - 1 or len(regions) == 1:\n        return True\n    if rights[-1] == len(regions) - 1:\n        return False\n    else:\n        return True", "composite_functions": []}
{"snippet": "import collections\ndef f(array: list) -> list:\n    str_buff = lambda str_seq: collections.deque(''.join(sorted(str_seq, reverse=True)))\n    count_sort = lambda s_arr, char_idx: sorted(s_arr, key=lambda word: word[char_idx])\n    return str_buff(array)", "input": "['potato', 'radish', 'carrot', 'onion', 'beetroot']", "output": "deque(['r', 'a', 'd', 'i', 's', 'h', 'p', 'o', 't', 'a', 't', 'o', 'o', 'n', 'i', 'o', 'n', 'c', 'a', 'r', 'r', 'o', 't', 'b', 'e', 'e', 't', 'r', 'o', 'o', 't'])", "imports": ["import collections"], "original_snippet": "import collections\ndef f(array: list) -> list:\n    str_buff = lambda str_seq: collections.deque(''.join(sorted(str_seq, reverse=True)))\n    count_sort = lambda s_arr, char_idx: sorted(s_arr, key=lambda word: word[char_idx])\n    return str_buff(array)", "composite_functions": []}
{"snippet": "def f(input_list: list) -> list:\n    for i in range(0, len(input_list) - 1, 2):\n        input_list[i] -= input_list[i + 1]\n    for i in range(1, len(input_list) - 1, 2):\n        input_list[i] += input_list[i + 1]\n    return input_list", "input": "[1, 10, 2, 3, 4, 5, 6, 7]", "output": "[-9, 9, -1, 2, -1, 4, -1, 7]", "imports": [], "original_snippet": "def f(input_list: list) -> list:\n    for i in range(0, len(input_list) - 1, 2):\n        input_list[i] -= input_list[i + 1]\n    for i in range(1, len(input_list) - 1, 2):\n        input_list[i] += input_list[i + 1]\n    return input_list", "composite_functions": []}
{"snippet": "def f(sequence: list[int], k: int) -> list[int]:\n    sequence.sort()\n    inclusive_sequence = [x for pair in zip(sequence, sequence[1:] + [0]) for x in pair]\n    exclusive_sequence = [x for pair in zip(sequence, sequence[2:] + [0, 0]) for x in pair]\n    diff_sequence = [in_seq - exc_seq for (in_seq, exc_seq) in zip(inclusive_sequence, exclusive_sequence)]\n    delta = 0\n    delta_slots = {i: 0 for i in inclusive_sequence}\n    for exc in exclusive_sequence:\n        delta += 1\n        delta_slots[exc] += delta\n    return [diff_sequence[-1] - delta_slots[num] for num in inclusive_sequence]", "input": "[23, 45, 36, 77, 54], 2", "output": "[-1, -3, -3, -7, -7, -11, -11, -15, -15, -18]", "imports": [], "original_snippet": "def f(sequence: list[int], k: int) -> list[int]:\n    sequence.sort()\n    inclusive_sequence = [x for pair in zip(sequence, sequence[1:] + [0]) for x in pair]\n    exclusive_sequence = [x for pair in zip(sequence, sequence[2:] + [0, 0]) for x in pair]\n    diff_sequence = [in_seq - exc_seq for (in_seq, exc_seq) in zip(inclusive_sequence, exclusive_sequence)]\n    delta = 0\n    delta_slots = {i: 0 for i in inclusive_sequence}\n    for exc in exclusive_sequence:\n        delta += 1\n        delta_slots[exc] += delta\n    return [diff_sequence[-1] - delta_slots[num] for num in inclusive_sequence]", "composite_functions": []}
{"snippet": "def f(word: str) -> int:\n    points_dictionary = {'a': 10, 'b': 7, 'c': 6, 'd': 5, 'e': 4, 'f': 3, 'g': 2, 'h': 5, 'i': 9, 'j': 7, 'k': 6, 'l': 5, 'm': 4, 'n': 7, 'o': 5, 'p': 3, 'q': 4, 'r': 4, 's': 6, 't': 3, 'u': 7, 'v': 6, 'w': 4, 'x': 3, 'y': 5, 'z': 3}\n    if isinstance(word, str) and len(word) > 0:\n        return points_dictionary[word[0].lower()]", "input": "'horse'", "output": "5", "imports": [], "original_snippet": "def f(word: str) -> int:\n    points_dictionary = {'a': 10, 'b': 7, 'c': 6, 'd': 5, 'e': 4, 'f': 3, 'g': 2, 'h': 5, 'i': 9, 'j': 7, 'k': 6, 'l': 5, 'm': 4, 'n': 7, 'o': 5, 'p': 3, 'q': 4, 'r': 4, 's': 6, 't': 3, 'u': 7, 'v': 6, 'w': 4, 'x': 3, 'y': 5, 'z': 3}\n    if isinstance(word, str) and len(word) > 0:\n        return points_dictionary[word[0].lower()]", "composite_functions": []}
{"snippet": "def f(start: int, stop: int) -> str:\n    result = 0\n    for i in range(start, stop + 1):\n        if i % 2 == 0:\n            result += i\n    return str(result)", "input": "7, 21", "output": "'98'", "imports": [], "original_snippet": "def f(start: int, stop: int) -> str:\n    result = 0\n    for i in range(start, stop + 1):\n        if i % 2 == 0:\n            result += i\n    return str(result)", "composite_functions": []}
{"snippet": "def f(input_string: str):\n    vowels = {'a': '1', 'e': '2', 'i': '3', 'o': '4', 'u': '5', 'A': '1', 'E': '2', 'I': '3', 'O': '4', 'U': '5'}\n    transformed = ''.join([vowels.get(char, chr((ord(char) - 32 + 26) % 26 + ord('a'))) for char in input_string[::-1]])\n    return transformed", "input": "'Hello, World!'", "output": "'bqye4dam4yy2o'", "imports": [], "original_snippet": "def f(input_string: str):\n    vowels = {'a': '1', 'e': '2', 'i': '3', 'o': '4', 'u': '5', 'A': '1', 'E': '2', 'I': '3', 'O': '4', 'U': '5'}\n    transformed = ''.join([vowels.get(char, chr((ord(char) - 32 + 26) % 26 + ord('a'))) for char in input_string[::-1]])\n    return transformed", "composite_functions": []}
{"snippet": "def f(data):\n    total_amount = 0\n    total_people = 0\n    for (wasted_food, customers) in data:\n        total_amount += wasted_food\n        total_people += customers\n    try:\n        average_waste = total_amount / total_people\n    except ZeroDivisionError:\n        average_waste = 0\n    return (total_amount, total_people, average_waste)", "input": "[(10, 20), (5, 30), (15, 40)]", "output": "(30, 90, 0.3333333333333333)", "imports": [], "original_snippet": "def f(data):\n    total_amount = 0\n    total_people = 0\n    for (wasted_food, customers) in data:\n        total_amount += wasted_food\n        total_people += customers\n    try:\n        average_waste = total_amount / total_people\n    except ZeroDivisionError:\n        average_waste = 0\n    return (total_amount, total_people, average_waste)", "composite_functions": []}
{"snippet": "def f(bucket):\n    numbers = sorted(bucket)\n    ranks = [0] * len(numbers)\n    run_sum = 0\n    for i in range(len(numbers) - 2):\n        run_sum += numbers[i]\n        ranks[i] = run_sum\n    run_sum += numbers[-2]\n    ranks[-2] = run_sum + 1000\n    ranks[-1] = run_sum\n    return ranks[len(ranks) // 2]", "input": "[3, 12, 88, 4, 25, 76, 54, 75, 19, 68, 97, 78]", "output": "185", "imports": [], "original_snippet": "def f(bucket):\n    numbers = sorted(bucket)\n    ranks = [0] * len(numbers)\n    run_sum = 0\n    for i in range(len(numbers) - 2):\n        run_sum += numbers[i]\n        ranks[i] = run_sum\n    run_sum += numbers[-2]\n    ranks[-2] = run_sum + 1000\n    ranks[-1] = run_sum\n    return ranks[len(ranks) // 2]", "composite_functions": []}
{"snippet": "from typing import List\nfrom collections import deque\ndef f(graph: List[List[int]]) -> List[int]:\n    in_degree = {node: 0 for node in range(len(graph))}\n    for (node, neighbors) in enumerate(graph):\n        for neighbor in neighbors:\n            in_degree[neighbor] += 1\n    queue = deque([node for (node, degree) in in_degree.items() if degree == 0])\n    topological_order = []\n    while queue:\n        node = queue.popleft()\n        topological_order.append(node)\n        for neighbor in graph[node]:\n            in_degree[neighbor] -= 1\n            if in_degree[neighbor] == 0:\n                queue.append(neighbor)\n    if len(topological_order) == len(graph):\n        return topological_order\n    else:\n        return 'There is no topological ordering for this graph, as it contains cycles.'", "input": "[[1, 2], [3], [], [0, 2], []]", "output": "'There is no topological ordering for this graph, as it contains cycles.'", "imports": ["from typing import List", "from collections import deque"], "original_snippet": "from typing import List\nfrom collections import deque\ndef f(graph: List[List[int]]) -> List[int]:\n    in_degree = {node: 0 for node in range(len(graph))}\n    for (node, neighbors) in enumerate(graph):\n        for neighbor in neighbors:\n            in_degree[neighbor] += 1\n    queue = deque([node for (node, degree) in in_degree.items() if degree == 0])\n    topological_order = []\n    while queue:\n        node = queue.popleft()\n        topological_order.append(node)\n        for neighbor in graph[node]:\n            in_degree[neighbor] -= 1\n            if in_degree[neighbor] == 0:\n                queue.append(neighbor)\n    if len(topological_order) == len(graph):\n        return topological_order\n    else:\n        return 'There is no topological ordering for this graph, as it contains cycles.'", "composite_functions": []}
{"snippet": "from collections import deque\ndef f(original: str, rotated: str) -> bool:\n    if len(original) != len(rotated):\n        return False\n    joined = original * 2\n    found = deque()\n    for s in joined:\n        if s == rotated[found[-1]] if found else ' ':\n            found.append(len(found))\n        else:\n            found.clear()\n            continue\n        if len(found) == len(original):\n            return True\n    return False", "input": "'waterbottle', 'erbottlewat'", "output": "True", "imports": ["from collections import deque"], "original_snippet": "from collections import deque\ndef f(original: str, rotated: str) -> bool:\n    if len(original) != len(rotated):\n        return False\n    joined = original * 2\n    found = deque()\n    for s in joined:\n        if s == rotated[found[-1]] if found else ' ':\n            found.append(len(found))\n        else:\n            found.clear()\n            continue\n        if len(found) == len(original):\n            return True\n    return False", "composite_functions": []}
{"snippet": "import re\ndef f(text: str) -> int:\n    words = re.findall('\\\\b\\\\w+\\\\b', text)\n    unique_words = set(words)\n    filtered_words = [word for word in unique_words if not any((char.isdigit() for char in word))]\n    return sum((len(word) for word in filtered_words))", "input": "\"Hello world 123\"", "output": "10", "imports": ["import re"], "original_snippet": "import re\ndef f(text: str) -> int:\n    words = re.findall('\\\\b\\\\w+\\\\b', text)\n    unique_words = set(words)\n    filtered_words = [word for word in unique_words if not any((char.isdigit() for char in word))]\n    return sum((len(word) for word in filtered_words))", "composite_functions": []}
{"snippet": "def f(array: list[int], target: int):\n    counts = []\n    for i in range(len(array)):\n        for j in range(len(array)):\n            if i != j and array[i] + array[j] == target:\n                counts.append((array[i], array[j]))\n    return len(counts)", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 11", "output": "10", "imports": [], "original_snippet": "def f(array: list[int], target: int):\n    counts = []\n    for i in range(len(array)):\n        for j in range(len(array)):\n            if i != j and array[i] + array[j] == target:\n                counts.append((array[i], array[j]))\n    return len(counts)", "composite_functions": []}
{"snippet": "import numpy as np\ndef f(arr: list[list[int]]) -> list[int]:\n    arr_np = np.array(arr)\n    return [np.sum(arr_np[i, :]) for i in range(arr_np.shape[0])]", "input": "[[1, 2, 3], [4, 5, 6], [7, 8, 9]]", "output": "[6, 15, 24]", "imports": ["import numpy as np"], "original_snippet": "import numpy as np\ndef f(arr: list[list[int]]) -> list[int]:\n    arr_np = np.array(arr)\n    return [np.sum(arr_np[i, :]) for i in range(arr_np.shape[0])]", "composite_functions": []}
{"snippet": "def f(lst):\n    for num in lst:\n        output = []\n        if num % 2 != 0 and num != 0:\n            new_num = num * 3 + 1\n            output.append(new_num)\n        else:\n            output.append(num)\n        length = len(output)\n        if length % 2 == 0:\n            output.remove(output[0])\n        return output", "input": "[1, 2, 3, 4, 5]", "output": "[4]", "imports": [], "original_snippet": "def f(lst):\n    for num in lst:\n        output = []\n        if num % 2 != 0 and num != 0:\n            new_num = num * 3 + 1\n            output.append(new_num)\n        else:\n            output.append(num)\n        length = len(output)\n        if length % 2 == 0:\n            output.remove(output[0])\n        return output", "composite_functions": []}
{"snippet": "def f(input_string: str, nums: list) -> list:\n    longest_substring = ''\n    current_substring = ''\n    for char in input_string:\n        if char not in current_substring:\n            current_substring += char\n        else:\n            if len(current_substring) > len(longest_substring):\n                longest_substring = current_substring\n            current_substring = char\n    if len(current_substring) > len(longest_substring):\n        longest_substring = current_substring\n    change_flag = True\n    start_index = 0\n    while change_flag:\n        change_flag = False\n        for i in range(len(nums) - 1):\n            if nums[i + 1] - nums[i] in range(ord(longest_substring[0]) - 82, ord(longest_substring[0]) - 78 + 1):\n                start_index = i + 1\n                change_flag = True\n                break\n    return nums[start_index - 1:] + nums[:start_index - 1]", "input": "\"pwwkew\", [1, 2, 3, 4, 5, 6, 7, 8, 9]", "output": "[9, 1, 2, 3, 4, 5, 6, 7, 8]", "imports": [], "original_snippet": "def f(input_string: str, nums: list) -> list:\n    longest_substring = ''\n    current_substring = ''\n    for char in input_string:\n        if char not in current_substring:\n            current_substring += char\n        else:\n            if len(current_substring) > len(longest_substring):\n                longest_substring = current_substring\n            current_substring = char\n    if len(current_substring) > len(longest_substring):\n        longest_substring = current_substring\n    change_flag = True\n    start_index = 0\n    while change_flag:\n        change_flag = False\n        for i in range(len(nums) - 1):\n            if nums[i + 1] - nums[i] in range(ord(longest_substring[0]) - 82, ord(longest_substring[0]) - 78 + 1):\n                start_index = i + 1\n                change_flag = True\n                break\n    return nums[start_index - 1:] + nums[:start_index - 1]", "composite_functions": []}
{"snippet": "def f(sequence: list, index: int):\n    skip_count = index % len(sequence)\n    return sequence[(skip_count + index) % len(sequence)]", "input": "[1, 2, 13, 24, 15], 4", "output": "24", "imports": [], "original_snippet": "def f(sequence: list, index: int):\n    skip_count = index % len(sequence)\n    return sequence[(skip_count + index) % len(sequence)]", "composite_functions": []}
{"snippet": "def f(nums: list, target: int) -> list:\n    (left, right) = (0, len(nums) - 1)\n    result = []\n    while left <= right:\n        mid = (left + right) // 2\n        if nums[mid] <= target:\n            result.append(nums[mid])\n            left = mid + 1\n        else:\n            right = mid - 1\n    if not result:\n        result.append(nums[-1])\n    return result[-3:] if len(result) > 3 else result", "input": "[-4, -3, -2, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 6", "output": "[3, 5, 6]", "imports": [], "original_snippet": "def f(nums: list, target: int) -> list:\n    (left, right) = (0, len(nums) - 1)\n    result = []\n    while left <= right:\n        mid = (left + right) // 2\n        if nums[mid] <= target:\n            result.append(nums[mid])\n            left = mid + 1\n        else:\n            right = mid - 1\n    if not result:\n        result.append(nums[-1])\n    return result[-3:] if len(result) > 3 else result", "composite_functions": []}
{"snippet": "def f(income: list[int], deductions: list[int], tax_brackets: list[tuple[int, float]], tax_rate: float) -> float:\n    total_taxable_income = sum(income) - sum(deductions)\n    tax = 0\n    for (limit, bracket) in tax_brackets:\n        if total_taxable_income > limit:\n            tax += (total_taxable_income - limit) * bracket\n            total_taxable_income = limit\n        else:\n            tax += total_taxable_income * bracket\n    total_tax = tax * tax_rate\n    return total_tax", "input": "income = [30000, 50000, 70000], deductions = [7000], tax_brackets = [(10000, 0.1), (40000, 0.15)], tax_rate = 0.2", "output": "2960.0", "imports": [], "original_snippet": "def f(income: list[int], deductions: list[int], tax_brackets: list[tuple[int, float]], tax_rate: float) -> float:\n    total_taxable_income = sum(income) - sum(deductions)\n    tax = 0\n    for (limit, bracket) in tax_brackets:\n        if total_taxable_income > limit:\n            tax += (total_taxable_income - limit) * bracket\n            total_taxable_income = limit\n        else:\n            tax += total_taxable_income * bracket\n    total_tax = tax * tax_rate\n    return total_tax", "composite_functions": []}
{"snippet": "def f(numbers: list):\n    import math\n    if len(numbers) != 2:\n        raise ValueError('Input list must contain exactly two integers.')\n    (a, b) = numbers\n    if not isinstance(a, int) or not isinstance(b, int):\n        raise ValueError('Input numbers must be integers.')\n    if a <= 0 or b <= 0:\n        raise ValueError('Input numbers must be positive.')\n    result = math.sqrt(a) - math.sqrt(b)\n    return result", "input": "[1234567890, *********]", "output": "3709.4502335127545", "imports": ["import math"], "original_snippet": "def f(numbers: list):\n    import math\n    if len(numbers) != 2:\n        raise ValueError('Input list must contain exactly two integers.')\n    (a, b) = numbers\n    if not isinstance(a, int) or not isinstance(b, int):\n        raise ValueError('Input numbers must be integers.')\n    if a <= 0 or b <= 0:\n        raise ValueError('Input numbers must be positive.')\n    result = math.sqrt(a) - math.sqrt(b)\n    return result", "composite_functions": []}
{"snippet": "def f(list1, list2):\n    len1 = len(list1)\n    len2 = len(list2)\n    n = min(len1, len2)\n    sum_list1 = 0\n    sum_list2 = 0\n    for i in range(n):\n        if list1[i] % 3 == 0:\n            sum_list1 += list1[i]\n        if list2[i] % 5 == 0:\n            sum_list2 += list2[i]\n    return sum_list1 + sum_list2", "input": "[3, 6, 9], [5, 10, 15]", "output": "48", "imports": [], "original_snippet": "def f(list1, list2):\n    len1 = len(list1)\n    len2 = len(list2)\n    n = min(len1, len2)\n    sum_list1 = 0\n    sum_list2 = 0\n    for i in range(n):\n        if list1[i] % 3 == 0:\n            sum_list1 += list1[i]\n        if list2[i] % 5 == 0:\n            sum_list2 += list2[i]\n    return sum_list1 + sum_list2", "composite_functions": []}
{"snippet": "def f(target: int, array: list[int]) -> list[int]:\n    subsets = [[] for _ in range(target + 1)]\n    for i in range(1, target + 1):\n        for j in range(len(array)):\n            if i + array[j] <= target:\n                subsets[i] = subsets[i].copy() if i - array[j] < 0 else subsets[i - array[j]][:]\n                subsets[i].append(array[j])\n    return subsets[target]", "input": "10, [3, 1, 4, 2]", "output": "[]", "imports": [], "original_snippet": "def f(target: int, array: list[int]) -> list[int]:\n    subsets = [[] for _ in range(target + 1)]\n    for i in range(1, target + 1):\n        for j in range(len(array)):\n            if i + array[j] <= target:\n                subsets[i] = subsets[i].copy() if i - array[j] < 0 else subsets[i - array[j]][:]\n                subsets[i].append(array[j])\n    return subsets[target]", "composite_functions": []}
{"snippet": "def f(lst: list[tuple[int]]):\n    sorted_lst = sorted(lst, key=lambda x: x[0] % x[1])\n    result = []\n    for (a, b) in sorted_lst:\n        result.append(b - a % b)\n    return result", "input": "[(7, 5), (3, 6), (8, 4), (9, 4), (1, 6)]", "output": "[4, 3, 5, 3, 3]", "imports": [], "original_snippet": "def f(lst: list[tuple[int]]):\n    sorted_lst = sorted(lst, key=lambda x: x[0] % x[1])\n    result = []\n    for (a, b) in sorted_lst:\n        result.append(b - a % b)\n    return result", "composite_functions": []}
{"snippet": "def f(num_sections: int, num_symbols: int, section_fraction: float, scaler: int) -> float:\n    sections = num_sections // 2 / section_fraction\n    symbols = num_symbols * scaler\n    return sections * symbols ** section_fraction", "input": "2, 3, 0.5, 2", "output": "4.898979485566356", "imports": [], "original_snippet": "def f(num_sections: int, num_symbols: int, section_fraction: float, scaler: int) -> float:\n    sections = num_sections // 2 / section_fraction\n    symbols = num_symbols * scaler\n    return sections * symbols ** section_fraction", "composite_functions": []}
{"snippet": "def f(l: list):\n    sorted_l = sorted(l)\n    total_sum = sum(sorted_l)\n    power_sum = sum((num ** 2 for num in sorted_l))\n    formula_result = (2 * total_sum + 3) ** 0.5 + 0.001\n    return (formula_result, power_sum)", "input": "[1, 2, 3, 4, 5, 6]", "output": "(6.70920393249937, 91)", "imports": [], "original_snippet": "def f(l: list):\n    sorted_l = sorted(l)\n    total_sum = sum(sorted_l)\n    power_sum = sum((num ** 2 for num in sorted_l))\n    formula_result = (2 * total_sum + 3) ** 0.5 + 0.001\n    return (formula_result, power_sum)", "composite_functions": []}
{"snippet": "def f(data: list, target_sum: int) -> list:\n    if not data or len(data) == 1:\n        return []\n    data.sort()\n    left = 0\n    right = len(data) - 1\n    found_pairs = []\n    while left < right:\n        current_sum = data[left] + data[right]\n        if current_sum == target_sum:\n            found_pairs.append((data[left], data[right]))\n            left += 1\n            right -= 1\n        elif current_sum < target_sum:\n            left += 1\n        else:\n            right -= 1\n    return found_pairs", "input": "[1, 2, 3, 4, 5], 6", "output": "[(1, 5), (2, 4)]", "imports": [], "original_snippet": "def f(data: list, target_sum: int) -> list:\n    if not data or len(data) == 1:\n        return []\n    data.sort()\n    left = 0\n    right = len(data) - 1\n    found_pairs = []\n    while left < right:\n        current_sum = data[left] + data[right]\n        if current_sum == target_sum:\n            found_pairs.append((data[left], data[right]))\n            left += 1\n            right -= 1\n        elif current_sum < target_sum:\n            left += 1\n        else:\n            right -= 1\n    return found_pairs", "composite_functions": []}
{"snippet": "def f(name: str, info: dict):\n    words = name.split()\n    lengths = [len(word) for word in words]\n    median_length = sum(lengths) / len(lengths)\n    info['median_length'] = median_length\n    return info", "input": "'John Doe', {'greeting': 'Hello'}", "output": "{'greeting': 'Hello', 'median_length': 3.5}", "imports": [], "original_snippet": "def f(name: str, info: dict):\n    words = name.split()\n    lengths = [len(word) for word in words]\n    median_length = sum(lengths) / len(lengths)\n    info['median_length'] = median_length\n    return info", "composite_functions": []}
{"snippet": "def f(limit: int, first_index: int, fibonacci_list: list) -> list[int]:\n    fibonacci_list[first_index - 1] = 1\n    fibonacci_list[first_index] = 1\n    for i in range(first_index + 1, limit):\n        fibonacci_list[i] = fibonacci_list[i - 1] + fibonacci_list[i - 2]\n    return fibonacci_list[first_index - 1:]", "input": "4, 50, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]", "output": "[1, 1, 0, 0, 0, 0, 0, 0, 0]", "imports": [], "original_snippet": "def f(limit: int, first_index: int, fibonacci_list: list) -> list[int]:\n    fibonacci_list[first_index - 1] = 1\n    fibonacci_list[first_index] = 1\n    for i in range(first_index + 1, limit):\n        fibonacci_list[i] = fibonacci_list[i - 1] + fibonacci_list[i - 2]\n    return fibonacci_list[first_index - 1:]", "composite_functions": []}
{"snippet": "def f(s: str) -> list:\n    lengths = []\n    word = ''\n    for c in s:\n        if c in 'aeiouAEIOU':\n            word += c\n        else:\n            if word:\n                lengths.append(len(word))\n            word = ''\n    if word:\n        lengths.append(len(word))\n    return lengths", "input": "\"Hello World!\"", "output": "[1, 1, 1]", "imports": [], "original_snippet": "def f(s: str) -> list:\n    lengths = []\n    word = ''\n    for c in s:\n        if c in 'aeiouAEIOU':\n            word += c\n        else:\n            if word:\n                lengths.append(len(word))\n            word = ''\n    if word:\n        lengths.append(len(word))\n    return lengths", "composite_functions": []}
{"snippet": "def f(values: list[int]) -> int:\n    results = [a * b ** 2 for a in values for b in values if a != b]\n    return sum((1 for result in results if result > 0))", "input": "[1, 5, 9, -3]", "output": "9", "imports": [], "original_snippet": "def f(values: list[int]) -> int:\n    results = [a * b ** 2 for a in values for b in values if a != b]\n    return sum((1 for result in results if result > 0))", "composite_functions": []}
{"snippet": "def f(intervals):\n    if len(intervals) == 0:\n        return {}\n    intervals.sort()\n    dp = [1] * len(intervals)\n    for j in range(1, len(intervals)):\n        for i in range(j):\n            if intervals[i][1] <= intervals[j][0]:\n                dp[j] = max(dp[j], dp[i] + 1)\n                break\n    maximum = max(dp)\n    max_intervals = []\n    prev_end = None\n    for i in range(len(intervals) - 1, -1, -1):\n        if dp[i] == maximum and (prev_end is None or intervals[i][0] > prev_end):\n            max_intervals.append(intervals[i])\n            prev_end = intervals[i][1]\n            maximum -= 1\n    return (maximum, max_intervals)", "input": "[(1, 3), (2, 6), (3, 5), (1, 6)]", "output": "(1, [(3, 5)])", "imports": [], "original_snippet": "def f(intervals):\n    if len(intervals) == 0:\n        return {}\n    intervals.sort()\n    dp = [1] * len(intervals)\n    for j in range(1, len(intervals)):\n        for i in range(j):\n            if intervals[i][1] <= intervals[j][0]:\n                dp[j] = max(dp[j], dp[i] + 1)\n                break\n    maximum = max(dp)\n    max_intervals = []\n    prev_end = None\n    for i in range(len(intervals) - 1, -1, -1):\n        if dp[i] == maximum and (prev_end is None or intervals[i][0] > prev_end):\n            max_intervals.append(intervals[i])\n            prev_end = intervals[i][1]\n            maximum -= 1\n    return (maximum, max_intervals)", "composite_functions": []}
{"snippet": "def f(input_list: list[dict]):\n    output_list = []\n    for dic in input_list:\n        merged_dict = {}\n        for key in dic:\n            if isinstance(dic[key], list):\n                for item in dic[key]:\n                    if item not in merged_dict.keys():\n                        merged_dict[item] = [key]\n                    else:\n                        merged_dict[item].append(key)\n        output_list.append(merged_dict)\n    return output_list", "input": "[{'a': ['b', 'c', 'd'], 'e': ['f']}, {'g': ['h', 'i']}, {'a': ['b', 'c'], 'e': ['d']}]", "output": "[{'b': ['a'], 'c': ['a'], 'd': ['a'], 'f': ['e']}, {'h': ['g'], 'i': ['g']}, {'b': ['a'], 'c': ['a'], 'd': ['e']}]", "imports": [], "original_snippet": "def f(input_list: list[dict]):\n    output_list = []\n    for dic in input_list:\n        merged_dict = {}\n        for key in dic:\n            if isinstance(dic[key], list):\n                for item in dic[key]:\n                    if item not in merged_dict.keys():\n                        merged_dict[item] = [key]\n                    else:\n                        merged_dict[item].append(key)\n        output_list.append(merged_dict)\n    return output_list", "composite_functions": []}
{"snippet": "def f(input_str: str):\n    sequence_list = ['lower', 'lower', 'upper', 'lower', 'upper', 'upper', 'lower', 'lower', 'upper', 'lower']\n    text_file = open('transformed_output.txt', 'w')\n    for char in input_str:\n        if char.isalpha():\n            if sequence_list[len(sequence_list) - 1] == 'lower':\n                text_file.write(char.lower())\n            elif sequence_list[len(sequence_list) - 1] == 'upper':\n                text_file.write(char.upper())\n            else:\n                text_file.write(char)\n        sequence_list.append(sequence_list.pop(0))\n    return 'Written'", "input": "'HelloWorld'", "output": "'Written'", "imports": [], "original_snippet": "def f(input_str: str):\n    sequence_list = ['lower', 'lower', 'upper', 'lower', 'upper', 'upper', 'lower', 'lower', 'upper', 'lower']\n    text_file = open('transformed_output.txt', 'w')\n    for char in input_str:\n        if char.isalpha():\n            if sequence_list[len(sequence_list) - 1] == 'lower':\n                text_file.write(char.lower())\n            elif sequence_list[len(sequence_list) - 1] == 'upper':\n                text_file.write(char.upper())\n            else:\n                text_file.write(char)\n        sequence_list.append(sequence_list.pop(0))\n    return 'Written'", "composite_functions": []}
{"snippet": "def f(num, lst):\n    for i in range(len(lst)):\n        lst[i] *= num\n        for k in range(len(lst)):\n            lst[i] = k * lst[i]\n    res = 0\n    for num in lst:\n        res += num\n    return res", "input": "4, [1, 2, 3]", "output": "0", "imports": [], "original_snippet": "def f(num, lst):\n    for i in range(len(lst)):\n        lst[i] *= num\n        for k in range(len(lst)):\n            lst[i] = k * lst[i]\n    res = 0\n    for num in lst:\n        res += num\n    return res", "composite_functions": []}
{"snippet": "def f(s: str) -> bool:\n    from collections import Counter\n    char_counts = Counter(s)\n    total_count = sum(char_counts.values())\n    if 'a' not in char_counts and 'b' not in char_counts:\n        return True\n    a_count = char_counts['a'] if 'a' in char_counts else 0\n    b_count = char_counts['b'] if 'b' in char_counts else 0\n    if a_count > 0 and b_count > 0 and (a_count == 2 * b_count):\n        return True\n    elif a_count > 0 and b_count == 0 and (total_count % 2 == 0) and (a_count // 2 == 0):\n        return True\n    return False", "input": "\"abbaa\"", "output": "False", "imports": ["from collections import Counter"], "original_snippet": "def f(s: str) -> bool:\n    from collections import Counter\n    char_counts = Counter(s)\n    total_count = sum(char_counts.values())\n    if 'a' not in char_counts and 'b' not in char_counts:\n        return True\n    a_count = char_counts['a'] if 'a' in char_counts else 0\n    b_count = char_counts['b'] if 'b' in char_counts else 0\n    if a_count > 0 and b_count > 0 and (a_count == 2 * b_count):\n        return True\n    elif a_count > 0 and b_count == 0 and (total_count % 2 == 0) and (a_count // 2 == 0):\n        return True\n    return False", "composite_functions": []}
{"snippet": "def f(input_list):\n    result = 0\n    stack = []\n    for num in input_list:\n        if num % 2 == 0:\n            result += num / 2\n        else:\n            result += num * 2\n        if len(stack) < 3:\n            stack.append(result)\n        elif result > stack[2]:\n            stack.pop(0)\n            stack.append(result)\n    return sum(stack)", "input": "[2, 5, 8, 10]", "output": "46.0", "imports": [], "original_snippet": "def f(input_list):\n    result = 0\n    stack = []\n    for num in input_list:\n        if num % 2 == 0:\n            result += num / 2\n        else:\n            result += num * 2\n        if len(stack) < 3:\n            stack.append(result)\n        elif result > stack[2]:\n            stack.pop(0)\n            stack.append(result)\n    return sum(stack)", "composite_functions": []}
{"snippet": "def f(n1: int, n2: int, lst: list) -> int:\n    for (i, val) in enumerate(lst):\n        if val % n1 != 0:\n            lst[i] = 0\n            continue\n        if val % n2 == 0:\n            lst[i] /= n2\n    return sum(lst)", "input": "10, 4, [45, 20, 31, 15, 12, 28, 26, 56]", "output": "5.0", "imports": [], "original_snippet": "def f(n1: int, n2: int, lst: list) -> int:\n    for (i, val) in enumerate(lst):\n        if val % n1 != 0:\n            lst[i] = 0\n            continue\n        if val % n2 == 0:\n            lst[i] /= n2\n    return sum(lst)", "composite_functions": []}
{"snippet": "def f(connections: list[tuple], start: int) -> list:\n    pass", "input": "[(1, 2), (2, 3), (3, 4), (2, 5), (5, 6), (6, 4), (3, 7), (7, 1)], 1", "output": "None", "imports": [], "original_snippet": "def f(connections: list[tuple], start: int) -> list:\n    pass", "composite_functions": []}
{"snippet": "import math\ndef f(t1: tuple[int, int], t2: tuple[int, int]):\n    min_sum = min(t1[0], t2[0]) + min(t1[1], t2[1])\n    x = math.factorial(min_sum)\n    return x", "input": "(3, 5), (2, 4)", "output": "720", "imports": ["import math"], "original_snippet": "import math\ndef f(t1: tuple[int, int], t2: tuple[int, int]):\n    min_sum = min(t1[0], t2[0]) + min(t1[1], t2[1])\n    x = math.factorial(min_sum)\n    return x", "composite_functions": []}
{"snippet": "def f(dict_list: list[dict[str, int]]) -> int:\n    result = 0\n    for elem in dict_list:\n        for (key, value) in elem.items():\n            if value >= 0 and value % 2 == 0:\n                next_even = 0\n                while next_even % 2 == 0:\n                    next_even += 1\n                result += value ** next_even\n    return result", "input": "[{'A': 2}, {'B': 4}, {'C': -3, 'E': 6}, {'D': 20}]", "output": "32", "imports": [], "original_snippet": "def f(dict_list: list[dict[str, int]]) -> int:\n    result = 0\n    for elem in dict_list:\n        for (key, value) in elem.items():\n            if value >= 0 and value % 2 == 0:\n                next_even = 0\n                while next_even % 2 == 0:\n                    next_even += 1\n                result += value ** next_even\n    return result", "composite_functions": []}
{"snippet": "def f(strings: list) -> tuple:\n    index_nums = []\n    for i in range(len(strings)):\n        new_num = sum((ord(char) for char in strings[i]))\n        index_nums.append((new_num, len(strings) - i))\n    index_nums = sorted(index_nums, key=lambda x: (x[0], x[1]))\n    (max_num, max_index) = index_nums[-1]\n    (min_num, min_index) = index_nums[0]\n    index_nums[max_index] = (min_num, max_num)\n    return (index_nums[max_index][-1], min_num)", "input": "['a', 'b', 'ccc', 'ddd', 'ee']", "output": "(300, 97)", "imports": [], "original_snippet": "def f(strings: list) -> tuple:\n    index_nums = []\n    for i in range(len(strings)):\n        new_num = sum((ord(char) for char in strings[i]))\n        index_nums.append((new_num, len(strings) - i))\n    index_nums = sorted(index_nums, key=lambda x: (x[0], x[1]))\n    (max_num, max_index) = index_nums[-1]\n    (min_num, min_index) = index_nums[0]\n    index_nums[max_index] = (min_num, max_num)\n    return (index_nums[max_index][-1], min_num)", "composite_functions": []}
{"snippet": "def f(n: int) -> int:\n    if n <= 0:\n        raise ValueError('n must be positive')\n    fibs = [1, 2]\n    for i in range(2, n):\n        fibs.append(fibs[-1] + fibs[-2])\n    evens = [x for x in fibs if x % 2 == 0]\n    even_sum = sum(evens)\n    factors = []\n    i = even_sum\n    while i > 1:\n        is_prime = True\n        for j in range(2, i):\n            if i % j == 0:\n                is_prime = False\n                break\n        if is_prime:\n            factors.append(i)\n        i -= 1\n    largest_prime_factor = max(factors)\n    return largest_prime_factor", "input": "10", "output": "43", "imports": [], "original_snippet": "def f(n: int) -> int:\n    if n <= 0:\n        raise ValueError('n must be positive')\n    fibs = [1, 2]\n    for i in range(2, n):\n        fibs.append(fibs[-1] + fibs[-2])\n    evens = [x for x in fibs if x % 2 == 0]\n    even_sum = sum(evens)\n    factors = []\n    i = even_sum\n    while i > 1:\n        is_prime = True\n        for j in range(2, i):\n            if i % j == 0:\n                is_prime = False\n                break\n        if is_prime:\n            factors.append(i)\n        i -= 1\n    largest_prime_factor = max(factors)\n    return largest_prime_factor", "composite_functions": []}
{"snippet": "def f(numbers):\n    seen = set()\n    unique = []\n    shift = 0\n    for num in numbers:\n        if num not in seen:\n            unique.append(num + shift)\n            seen.add(num)\n        else:\n            shift += 1\n    return (tuple(unique), shift)", "input": "(12, 12, 12, 1, 1, 2)", "output": "((12, 3, 5), 3)", "imports": [], "original_snippet": "def f(numbers):\n    seen = set()\n    unique = []\n    shift = 0\n    for num in numbers:\n        if num not in seen:\n            unique.append(num + shift)\n            seen.add(num)\n        else:\n            shift += 1\n    return (tuple(unique), shift)", "composite_functions": []}
{"snippet": "def f(intervals: list) -> int:\n    max_count = 0\n    for i in intervals:\n        if i > max_count:\n            max_count = i\n            return max_count", "input": "[30, 70, 20, 10]", "output": "30", "imports": [], "original_snippet": "def f(intervals: list) -> int:\n    max_count = 0\n    for i in intervals:\n        if i > max_count:\n            max_count = i\n            return max_count", "composite_functions": []}
{"snippet": "def f(lst, begin, end):\n    count = 0\n    for date in lst:\n        (year, _, _) = date.split('-')\n        if begin <= int(year) <= end:\n            count += 1\n    return count", "input": "['2020-08-15', '2021-07-29', '2023-12-01'], 2020, 2022", "output": "2", "imports": [], "original_snippet": "def f(lst, begin, end):\n    count = 0\n    for date in lst:\n        (year, _, _) = date.split('-')\n        if begin <= int(year) <= end:\n            count += 1\n    return count", "composite_functions": []}
{"snippet": "def f(words: list[str], target: int):\n    scores = []\n    for word in words:\n        score = 0\n        for char in word:\n            if char == 'a':\n                score += 2\n            elif char == 'e':\n                score += 3\n            elif char == 'i':\n                score += 5\n            elif char == 'o':\n                score += 7\n            elif char == 'u':\n                score += 11\n            else:\n                score -= 1\n        scores.append((score, word))\n    scores.sort(reverse=True)\n    filtered_scores = [(s, w) for (s, w) in scores if s == target]\n    if filtered_scores:\n        return filtered_scores[0][1]\n    else:\n        return words[-1]", "input": "['apple, banana, cherry, date, elderberry'], 10", "output": "'apple, banana, cherry, date, elderberry'", "imports": [], "original_snippet": "def f(words: list[str], target: int):\n    scores = []\n    for word in words:\n        score = 0\n        for char in word:\n            if char == 'a':\n                score += 2\n            elif char == 'e':\n                score += 3\n            elif char == 'i':\n                score += 5\n            elif char == 'o':\n                score += 7\n            elif char == 'u':\n                score += 11\n            else:\n                score -= 1\n        scores.append((score, word))\n    scores.sort(reverse=True)\n    filtered_scores = [(s, w) for (s, w) in scores if s == target]\n    if filtered_scores:\n        return filtered_scores[0][1]\n    else:\n        return words[-1]", "composite_functions": []}
