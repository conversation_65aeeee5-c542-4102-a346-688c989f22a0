#!/usr/bin/env python3
"""
Minimal installation test for Absolute Zero Reasoner.

This script verifies that all core dependencies are properly installed
and can be imported without errors.
"""

import sys
import os
from typing import List, <PERSON><PERSON>

def test_import(module_name: str, description: str) -> Tuple[bool, str]:
    """Test if a module can be imported successfully."""
    try:
        __import__(module_name)
        return True, f"✓ {description}"
    except ImportError as e:
        return False, f"✗ {description}: {str(e)}"
    except Exception as e:
        return False, f"✗ {description}: Unexpected error - {str(e)}"

def run_core_tests() -> List[Tuple[bool, str]]:
    """Run tests for core dependencies."""
    tests = [
        ("torch", "PyTorch"),
        ("transformers", "Hugging Face Transformers"),
        ("vllm", "vLLM"),
        ("ray", "Ray"),
        ("absolute_zero_reasoner", "Absolute Zero Reasoner"),
        ("flash_attn", "Flash Attention"),
        ("verl", "veRL Framework"),
    ]

    results = []
    for module, desc in tests:
        results.append(test_import(module, desc))

    return results

def test_cuda() -> <PERSON><PERSON>[bool, str]:
    """Test CUDA availability."""
    try:
        import torch
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            return True, f"✓ CUDA available with {device_count} device(s) - {device_name}"
        else:
            return False, "✗ CUDA not available (CPU-only mode)"
    except Exception as e:
        return False, f"✗ CUDA test failed: {str(e)}"

def test_python_version() -> Tuple[bool, str]:
    """Test Python version compatibility."""
    version = sys.version_info
    if version.major == 3 and version.minor >= 10:
        return True, f"✓ Python {version.major}.{version.minor}.{version.micro}"
    else:
        return False, f"✗ Python {version.major}.{version.minor}.{version.micro} (requires Python 3.10+)"

def test_optional_dependencies() -> List[Tuple[bool, str]]:
    """Test optional dependencies."""
    tests = [
        ("wandb", "Weights & Biases"),
        ("hydra", "Hydra Configuration"),
        ("omegaconf", "OmegaConf"),
        ("datasets", "Hugging Face Datasets"),
        ("pandas", "Pandas"),
        ("numpy", "NumPy"),
    ]

    results = []
    for module, desc in tests:
        results.append(test_import(module, desc))

    return results

def test_environment_variables() -> List[Tuple[bool, str]]:
    """Test important environment variables."""
    results = []

    # Check CUDA_VISIBLE_DEVICES if CUDA is available
    try:
        import torch
        if torch.cuda.is_available():
            cuda_devices = os.environ.get('CUDA_VISIBLE_DEVICES', 'not set')
            results.append((True, f"✓ CUDA_VISIBLE_DEVICES: {cuda_devices}"))
    except:
        pass

    # Check Python path
    python_path = os.environ.get('PYTHONPATH', 'not set')
    results.append((True, f"✓ PYTHONPATH: {python_path}"))

    return results

def main():
    """Main test execution."""
    print("Absolute Zero Reasoner - Installation Verification")
    print("=" * 60)

    # Test Python version
    print("\nTesting Python version:")
    python_result = test_python_version()
    print(f"  {python_result[1]}")

    # Test core dependencies
    print("\nTesting core dependencies:")
    core_results = run_core_tests()
    for success, message in core_results:
        print(f"  {message}")

    # Test CUDA
    print("\nTesting CUDA:")
    cuda_result = test_cuda()
    print(f"  {cuda_result[1]}")

    # Test optional dependencies
    print("\nTesting optional dependencies:")
    optional_results = test_optional_dependencies()
    for success, message in optional_results:
        print(f"  {message}")

    # Test environment
    print("\nEnvironment variables:")
    env_results = test_environment_variables()
    for success, message in env_results:
        print(f"  {message}")

    # Calculate results
    critical_results = [python_result] + core_results  # Only core deps are critical

    passed = sum(1 for success, _ in critical_results if success)
    total_critical = len(critical_results)

    optional_passed = sum(1 for success, _ in optional_results if success)
    total_optional = len(optional_results)

    print(f"\n" + "=" * 60)
    print(f"Results Summary:")
    print(f"  Critical dependencies: {passed}/{total_critical} passed")
    print(f"  Optional dependencies: {optional_passed}/{total_optional} passed")
    print(f"  CUDA support: {'Available' if cuda_result[0] else 'Not available'}")

    if passed == total_critical:
        print("\n✓ All critical tests passed! Installation is ready for Absolute Zero.")
        if not cuda_result[0]:
            print("⚠️  Note: CUDA not available - training will be slower on CPU")
        sys.exit(0)
    else:
        print(f"\n✗ {total_critical - passed} critical tests failed. Please fix installation issues.")
        print("\nTroubleshooting:")
        print("  1. Ensure you're using Python 3.10+")
        print("  2. Install missing dependencies: pip install -r requirements.txt")
        print("  3. For CUDA issues, check your NVIDIA driver and CUDA installation")
        print("  4. For veRL issues, ensure it's properly installed from the verl directory")
        sys.exit(1)

if __name__ == "__main__":
    main()