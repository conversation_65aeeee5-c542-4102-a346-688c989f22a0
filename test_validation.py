#!/usr/bin/env python3
"""
Test script for input validation system.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'absolute_zero_reasoner'))

from absolute_zero_reasoner.validation import (
    TypeRule, RangeRule, RegexRule, LengthRule, ChoiceRule, PathRule, SecurityRule,
    Validator, ConfigValidator, create_training_config_validator,
    create_code_execution_validator, sanitize_string, validate_and_sanitize_config
)
from absolute_zero_reasoner.exceptions import ValidationError, SecurityError

def test_validation_rules():
    """Test individual validation rules."""
    print("Testing Validation Rules")
    print("=" * 40)

    # Test TypeRule
    print("\n1. Testing TypeRule:")
    type_rule = TypeRule(int)
    print(f"   int(42): {type_rule.validate(42)}")  # Should be True
    print(f"   str('42'): {type_rule.validate('42')}")  # Should be False

    # Test RangeRule
    print("\n2. Testing RangeRule:")
    range_rule = RangeRule(0, 100)
    print(f"   50: {range_rule.validate(50)}")  # Should be True
    print(f"   150: {range_rule.validate(150)}")  # Should be False
    print(f"   -10: {range_rule.validate(-10)}")  # Should be False

    # Test RegexRule
    print("\n3. Testing RegexRule:")
    email_rule = RegexRule(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    print(f"   '<EMAIL>': {email_rule.validate('<EMAIL>')}")  # Should be True
    print(f"   'invalid-email': {email_rule.validate('invalid-email')}")  # Should be False

    # Test LengthRule
    print("\n4. Testing LengthRule:")
    length_rule = LengthRule(3, 10)
    print(f"   'hello': {length_rule.validate('hello')}")  # Should be True
    print(f"   'hi': {length_rule.validate('hi')}")  # Should be False
    print(f"   'very long string': {length_rule.validate('very long string')}")  # Should be False

    # Test ChoiceRule
    print("\n5. Testing ChoiceRule:")
    choice_rule = ChoiceRule(['red', 'green', 'blue'])
    print(f"   'red': {choice_rule.validate('red')}")  # Should be True
    print(f"   'yellow': {choice_rule.validate('yellow')}")  # Should be False

    # Test SecurityRule
    print("\n6. Testing SecurityRule:")
    security_rule = SecurityRule()
    print(f"   'print(hello)': {security_rule.validate('print(hello)')}")  # Should be True
    print(f"   'exec(malicious_code)': {security_rule.validate('exec(malicious_code)')}")  # Should be False
    print(f"   '__import__(os)': {security_rule.validate('__import__(os)')}")  # Should be False

def test_validator():
    """Test the Validator class."""
    print("\n\nTesting Validator Class")
    print("=" * 40)

    # Create a validator for user age
    age_validator = Validator([
        TypeRule(int),
        RangeRule(0, 150)
    ])

    print("\n1. Testing valid age:")
    try:
        age_validator.validate(25, "age")
        print("   Age 25: Valid")
    except ValidationError as e:
        print(f"   Age 25: Invalid - {e}")

    print("\n2. Testing invalid age (negative):")
    try:
        age_validator.validate(-5, "age")
        print("   Age -5: Valid")
    except ValidationError as e:
        print(f"   Age -5: Invalid - {e}")

    print("\n3. Testing invalid age (wrong type):")
    try:
        age_validator.validate("25", "age")
        print("   Age '25': Valid")
    except ValidationError as e:
        print(f"   Age '25': Invalid - {e}")

def test_config_validator():
    """Test configuration validation."""
    print("\n\nTesting Configuration Validation")
    print("=" * 40)

    # Create training config validator
    config_validator = create_training_config_validator()

    # Test valid configuration
    print("\n1. Testing valid configuration:")
    valid_config = {
        "learning_rate": 0.001,
        "batch_size": 32,
        "epochs": 100,
        "model_path": "/path/to/model",
        "optimizer": "adam"
    }

    try:
        config_validator.validate_config(valid_config)
        print("   Valid config: Passed validation")
    except ValidationError as e:
        print(f"   Valid config: Failed - {e}")

    # Test invalid configuration (missing required field)
    print("\n2. Testing invalid configuration (missing field):")
    invalid_config = {
        "learning_rate": 0.001,
        "batch_size": 32,
        # Missing epochs and model_path
    }

    try:
        config_validator.validate_config(invalid_config)
        print("   Invalid config: Passed validation")
    except ValidationError as e:
        print(f"   Invalid config: Failed - {e}")

    # Test invalid configuration (bad values)
    print("\n3. Testing invalid configuration (bad values):")
    bad_config = {
        "learning_rate": -0.001,  # Negative learning rate
        "batch_size": 0,  # Zero batch size
        "epochs": 100,
        "model_path": "/path/to/model",
        "optimizer": "invalid_optimizer"  # Invalid optimizer
    }

    try:
        config_validator.validate_config(bad_config)
        print("   Bad config: Passed validation")
    except ValidationError as e:
        print(f"   Bad config: Failed - {e}")

def test_security_validation():
    """Test security validation."""
    print("\n\nTesting Security Validation")
    print("=" * 40)

    code_validator = create_code_execution_validator()

    # Test safe code
    print("\n1. Testing safe code:")
    safe_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
    """

    try:
        code_validator.validate(safe_code, "code")
        print("   Safe code: Passed validation")
    except (ValidationError, SecurityError) as e:
        print(f"   Safe code: Failed - {e}")

    # Test dangerous code
    print("\n2. Testing dangerous code:")
    dangerous_code = """
import os
os.system('rm -rf /')
    """

    try:
        code_validator.validate(dangerous_code, "code")
        print("   Dangerous code: Passed validation")
    except (ValidationError, SecurityError) as e:
        print(f"   Dangerous code: Failed - {e}")

    # Test code with exec
    print("\n3. Testing code with exec:")
    exec_code = "exec('print(hello)')"

    try:
        code_validator.validate(exec_code, "code")
        print("   Exec code: Passed validation")
    except (ValidationError, SecurityError) as e:
        print(f"   Exec code: Failed - {e}")

def test_sanitization():
    """Test string sanitization."""
    print("\n\nTesting String Sanitization")
    print("=" * 40)

    # Test normal string
    print("\n1. Testing normal string:")
    normal_str = "Hello, World!"
    sanitized = sanitize_string(normal_str)
    print(f"   Original: '{normal_str}'")
    print(f"   Sanitized: '{sanitized}'")

    # Test string with dangerous content
    print("\n2. Testing dangerous string:")
    dangerous_str = "<script>alert('xss')</script>Hello"
    sanitized = sanitize_string(dangerous_str)
    print(f"   Original: '{dangerous_str}'")
    print(f"   Sanitized: '{sanitized}'")

    # Test very long string
    print("\n3. Testing long string:")
    long_str = "A" * 2000
    sanitized = sanitize_string(long_str, max_length=100)
    print(f"   Original length: {len(long_str)}")
    print(f"   Sanitized length: {len(sanitized)}")

def main():
    """Main test execution."""
    print("Absolute Zero - Input Validation Tests")
    print("=" * 50)

    try:
        test_validation_rules()
        test_validator()
        test_config_validator()
        test_security_validation()
        test_sanitization()

        print("\n" + "=" * 50)
        print("All validation tests completed!")

    except Exception as e:
        print(f"Test execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()