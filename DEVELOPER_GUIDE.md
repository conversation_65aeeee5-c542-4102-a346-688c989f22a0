# Developer Guide: Absolute Zero Reasoner

## 1. Environment Setup

- **Python Version:** The project requires Python 3.10. Using Python 3.11+ may cause incompatibilities with GPU libraries (e.g., flash-attn).
- **CUDA:** CUDA 12.4.1 is required for GPU acceleration. Ensure your drivers and toolkit match this version.
- **Dependencies:**
    - Use the provided `requirements.txt` for core dependencies. Avoid local file path dependencies.
    - For development/test tools, use `requirements-dev.txt` (if present).
    - If installing `flash-attn` fails, see the README for build instructions.

**Example setup:**
```bash
conda create -n azr python=3.10
conda activate azr
conda install nvidia/label/cuda-12.4.1::cuda-toolkit
pip install -r requirements.txt
```

## 2. Installation Verification

After installing dependencies, run the minimal installation test:
```bash
python test_installation_minimal.py
```
This checks Python, PyTorch (with CUDA), transformers, vllm, flash-attn, verl, and absolute_zero_reasoner. If any import fails, resolve the error before proceeding.

## 3. Troubleshooting

- **flash-attn installation fails:**
    - Ensure your Python and CUDA versions match project requirements.
    - See the README for custom build instructions.
- **vllm version warnings:**
    - Ensure vllm is installed from PyPI. Some warnings are non-fatal but check for runtime issues.
- **CUDA not available:**
    - Check your CUDA toolkit and driver installation. Use `torch.cuda.is_available()` to verify.
- **Python version mismatch:**
    - Use Python 3.10 for best compatibility. Some dependencies may not work with newer versions.

## 4. Development Workflow

- **Code Style:**
    - Follow PEP8. Use type hints and docstrings for all public functions and classes.
    - Use yapf or black for formatting (see `.style.yapf` if present).
- **Logging:**
    - Use Python's `logging` module instead of print for all code except notebooks or scripts.
- **Testing:**
    - Place tests in the `tests/` directory. Use pytest or unittest.
    - Run all tests before submitting code or PRs.
- **Version Control:**
    - Do not commit `.venv/`, `__pycache__/`, data, or model artifacts. See `.gitignore` for details.
- **Contributing:**
    - Document new modules and functions. Update the README and this guide as needed.

## 5. Continuous Integration (CI)

- Set up GitHub Actions or another CI system to run tests on PRs and pushes.
- Ensure CI uses Python 3.10 and installs CUDA dependencies if possible.

## 6. Minimal Working Example

After setup, verify your environment with:
```bash
python test_installation_minimal.py
```
If all checks pass, you are ready for development or research!

---
For further questions, see the README or contact the maintainers.
