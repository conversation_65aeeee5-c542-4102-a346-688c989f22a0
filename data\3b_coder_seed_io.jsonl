{"snippet": "def f(a):\n    return a", "input": "\"Hello world\"", "output": "\"Hello world\"", "imports": [], "original_snippet": "def f(a):\n    return a", "composite_functions": []}
{"snippet": "def f(number):\n    a = number % 10\n    b = (number % 100 - a) / 10\n    c = (number % 1000 - 10 * b - a) / 100\n    d = (number % 10000 - 100 * b - 10 * a - c) / 1000\n    e = (number % 100000 - 1000 * b - 100 * a - 10 * c - d) / 10000", "input": "4321", "output": "None", "imports": [], "original_snippet": "def f(number):\n    a = number % 10\n    b = (number % 100 - a) / 10\n    c = (number % 1000 - 10 * b - a) / 100\n    d = (number % 10000 - 100 * b - 10 * a - c) / 1000\n    e = (number % 100000 - 1000 * b - 100 * a - 10 * c - d) / 10000", "composite_functions": [], "_input_type": "int", "_output_type": "NoneType"}
{"snippet": "def f(text: str):\n    binary_encoding = bin(int.from_bytes(text.encode(), 'big'))\n    return binary_encoding", "input": "\"Python\"", "output": "'0b10100000111100101110100011010000110111101101110'", "imports": [], "original_snippet": "def f(text: str):\n    binary_encoding = bin(int.from_bytes(text.encode(), 'big'))\n    return binary_encoding", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from collections import defaultdict\ndef f(input_dict: dict):\n    squared_dict = {k: v ** 2 for (k, v) in input_dict.items()}\n    return squared_dict", "input": "{'a': 2, 'b': 3}", "output": "{'a': 4, 'b': 9}", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(input_dict: dict):\n    squared_dict = {k: v ** 2 for (k, v) in input_dict.items()}\n    return squared_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "import re\ndef f(input_string: str):\n    if len(input_string) == 10:\n        input_string = re.sub('ee', 'eeeee', input_string)\n    return input_string", "input": "'Hello world'", "output": "'Hello world'", "imports": ["import re"], "original_snippet": "import re\ndef f(input_string: str):\n    if len(input_string) == 10:\n        input_string = re.sub('ee', 'eeeee', input_string)\n    return input_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(d: dict, s: str):\n    if 'key' in d:\n        del d['key']\n        d['value'] = s[::-1]\n    elif 'value' not in d:\n        d['key'] = len(s)\n        d['value'] = s\n    return d", "input": "{'value': 'abc'}, 'def'", "output": "{'value': 'abc'}", "imports": [], "original_snippet": "def f(d: dict, s: str):\n    if 'key' in d:\n        del d['key']\n        d['value'] = s[::-1]\n    elif 'value' not in d:\n        d['key'] = len(s)\n        d['value'] = s\n    return d", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(a):\n    n = 101779791\n    return (a ^ a >> 13) * n % 2 ** 17", "input": "4096123512413759491", "output": "109630", "imports": [], "original_snippet": "def f(a):\n    n = 101779791\n    return (a ^ a >> 13) * n % 2 ** 17", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(input_string: str) -> str:\n    sep = ''\n    for _ in reversed(list(input_string)):\n        sep += _\n    return sep", "input": "\"Hello World\"", "output": "'dlroW olleH'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    sep = ''\n    for _ in reversed(list(input_string)):\n        sep += _\n    return sep", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(item):\n    return item", "input": "'Hello world'", "output": "'Hello world'", "imports": [], "original_snippet": "def f(item):\n    return item", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from collections import defaultdict\ndef f(a: dict):\n    reversed_dict = defaultdict(list)\n    for (key, value) in a.items():\n        reversed_dict[value].append(key)\n    reversed_dict = {key: reversed_dict[key] for key in reversed_dict.keys()}\n    return reversed_dict", "input": "{1: 'one', 2: 'two', 3: 'three', 4: 'four', 5: 'five'}", "output": "{'one': [1], 'two': [2], 'three': [3], 'four': [4], 'five': [5]}", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(a: dict):\n    reversed_dict = defaultdict(list)\n    for (key, value) in a.items():\n        reversed_dict[value].append(key)\n    reversed_dict = {key: reversed_dict[key] for key in reversed_dict.keys()}\n    return reversed_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "from typing import Dict, List\ndef f(input_dict: Dict[int, str]) -> Dict[str, List[int]]:\n    temp_dict = {}\n    for (key, value) in input_dict.items():\n        if value in temp_dict:\n            temp_dict[value].append(key)\n        else:\n            temp_dict[value] = [key]\n    rev_dict = {value: keys for (value, keys) in temp_dict.items()}\n    return rev_dict", "input": "{1: 'one', 2: 'one', 3: 'three'}", "output": "{'one': [1, 2], 'three': [3]}", "imports": ["from typing import Dict, List"], "original_snippet": "from typing import Dict, List\ndef f(input_dict: Dict[int, str]) -> Dict[str, List[int]]:\n    temp_dict = {}\n    for (key, value) in input_dict.items():\n        if value in temp_dict:\n            temp_dict[value].append(key)\n        else:\n            temp_dict[value] = [key]\n    rev_dict = {value: keys for (value, keys) in temp_dict.items()}\n    return rev_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(input_string: str, custom_dict: dict={'eeddddd': 'eeeeee'}):\n    for (key, value) in custom_dict.items():\n        if key in input_string:\n            input_string = input_string.replace(key, value)\n    return input_string", "input": "'Hello world'", "output": "'Hello world'", "imports": [], "original_snippet": "def f(input_string: str, custom_dict: dict={'eeddddd': 'eeeeee'}):\n    for (key, value) in custom_dict.items():\n        if key in input_string:\n            input_string = input_string.replace(key, value)\n    return input_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import math\ndef f(numbers):\n    sums = []\n    averages = []\n    standard_deviations = []\n    for number in numbers:\n        if number not in sums:\n            sums.append(number)\n            averages.append(sum(numbers) / len(numbers))\n            standard_deviations.append(math.sqrt(sum(((number - a) ** 2 for a in averages)) / float(len(numbers))))\n    return (sum(sums), sum(averages), sum(standard_deviations) / 2)", "input": "[1, 1, 2, 3, 4, 5]", "output": "(15, 13.333333333333332, 2.2598552666371594)", "imports": ["import math"], "original_snippet": "import math\ndef f(numbers):\n    sums = []\n    averages = []\n    standard_deviations = []\n    for number in numbers:\n        if number not in sums:\n            sums.append(number)\n            averages.append(sum(numbers) / len(numbers))\n            standard_deviations.append(math.sqrt(sum(((number - a) ** 2 for a in averages)) / float(len(numbers))))\n    return (sum(sums), sum(averages), sum(standard_deviations) / 2)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "from collections import defaultdict\ndef f(item):\n    if not hasattr(f, 'dict'):\n        f.dict = defaultdict(int)\n    if not hasattr(f, 'sum'):\n        f.sum = 0\n    if not hasattr(f, 'result'):\n        f.result = []\n    f.sum += item\n    f.dict[f.sum] += 1\n    if len(f.result) >= item:\n        if item in f.result:\n            return 'Already seen'\n        elif f.result[-1] < item:\n            return 'Not defined yet'\n        else:\n            f.result = reversed(f.result)\n            return\n    else:\n        return None", "input": "5 + 6 + 3 + 2", "output": "None", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(item):\n    if not hasattr(f, 'dict'):\n        f.dict = defaultdict(int)\n    if not hasattr(f, 'sum'):\n        f.sum = 0\n    if not hasattr(f, 'result'):\n        f.result = []\n    f.sum += item\n    f.dict[f.sum] += 1\n    if len(f.result) >= item:\n        if item in f.result:\n            return 'Already seen'\n        elif f.result[-1] < item:\n            return 'Not defined yet'\n        else:\n            f.result = reversed(f.result)\n            return\n    else:\n        return None", "composite_functions": [], "_input_type": "str", "_output_type": "NoneType"}
{"snippet": "def f(numbers):\n    numbers.reverse()\n    numbers = [x * x for x in numbers]\n    numbers.reverse()\n    numbers = [x + 1 for x in numbers]\n    from math import factorial\n    numbers = [factorial(x) for x in numbers]\n    from functools import reduce\n    product = reduce(lambda x, y: x * y, numbers)\n    return product", "input": "numbers = [2, 3, 4, 5]", "output": "62464291863199167050758240415917277184000000000000", "imports": ["from math import factorial", "from functools import reduce"], "original_snippet": "def f(numbers):\n    numbers.reverse()\n    numbers = [x * x for x in numbers]\n    numbers.reverse()\n    numbers = [x + 1 for x in numbers]\n    from math import factorial\n    numbers = [factorial(x) for x in numbers]\n    from functools import reduce\n    product = reduce(lambda x, y: x * y, numbers)\n    return product", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(a_dict: dict):\n    reversed_dict = {}\n    for (key, value) in a_dict.items():\n        if value in reversed_dict:\n            if key not in reversed_dict[value]:\n                reversed_dict[value].append(key)\n        else:\n            reversed_dict[value] = [key]\n    return reversed_dict", "input": "{'a': 2, 'b': 3}", "output": "{2: ['a'], 3: ['b']}", "imports": [], "original_snippet": "def f(a_dict: dict):\n    reversed_dict = {}\n    for (key, value) in a_dict.items():\n        if value in reversed_dict:\n            if key not in reversed_dict[value]:\n                reversed_dict[value].append(key)\n        else:\n            reversed_dict[value] = [key]\n    return reversed_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(text: str, mapping: dict={'a': 128, 'e': 230, 'i': 345, 'o': 456, 'u': 567}):\n    if not hasattr(f, 'reverse_mapping'):\n        f.reverse_mapping = {value: key for (key, value) in mapping.items()}\n    if not hasattr(f, 'length'):\n        f.length = len(text)\n        if text.islower():\n            capitalization = [c for c in text]\n            for i in range(len(text)):\n                if i + 1 <= f.length:\n                    if i + 1 not in mapping:\n                        capitalization[i] = text[i] if text[i] >= 'A' else text[i].upper()\n                        if capitalization[i] in mapping:\n                            alpha_i = mapping[capitalization[i]]\n                            alpha_i_capitalized = f.reverse_mapping[alpha_i]\n                            new_char = alpha_i_capitalized\n                            if new_char >= 'A' and (capitalization[i] < 'a' or capitalization[i].isupper()):\n                                new_char = new_char.upper()\n                            if i != 0 and (new_char < 'a' or new_char.islower()):\n                                new_char = new_char.lower()\n                            if i <= f.length and (text[i] <= 'Z' and (not text[i].islower())):\n                                text = text[:i] + new_char + text[i:]\n                            elif i <= f.length and (text[i] <= 'z' and (not text[i].isupper())):\n                                text = text[:i] + new_char + text[i:]\n                else:\n                    break\n            else:\n                return 'Encountered non-mapping or invalid values'\n        elif f.length != len(text):\n            return \"Total text length isn't equal to initial one\"\n    return text", "input": "'hello world'", "output": "", "imports": [], "original_snippet": "def f(text: str, mapping: dict={'a': 128, 'e': 230, 'i': 345, 'o': 456, 'u': 567}):\n    if not hasattr(f, 'reverse_mapping'):\n        f.reverse_mapping = {value: key for (key, value) in mapping.items()}\n    if not hasattr(f, 'length'):\n        f.length = len(text)\n        if text.islower():\n            capitalization = [c for c in text]\n            for i in range(len(text)):\n                if i + 1 <= f.length:\n                    if i + 1 not in mapping:\n                        capitalization[i] = text[i] if text[i] >= 'A' else text[i].upper()\n                        if capitalization[i] in mapping:\n                            alpha_i = mapping[capitalization[i]]\n                            alpha_i_capitalized = f.reverse_mapping[alpha_i]\n                            new_char = alpha_i_capitalized\n                            if new_char >= 'A' and (capitalization[i] < 'a' or capitalization[i].isupper()):\n                                new_char = new_char.upper()\n                            if i != 0 and (new_char < 'a' or new_char.islower()):\n                                new_char = new_char.lower()\n                            if i <= f.length and (text[i] <= 'Z' and (not text[i].islower())):\n                                text = text[:i] + new_char + text[i:]\n                            elif i <= f.length and (text[i] <= 'z' and (not text[i].isupper())):\n                                text = text[:i] + new_char + text[i:]\n                else:\n                    break\n            else:\n                return 'Encountered non-mapping or invalid values'\n        elif f.length != len(text):\n            return \"Total text length isn't equal to initial one\"\n    return text", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(s: str):\n    rev_s = s[::-1]\n    result = s[0] + s[1] + rev_s[-3] + rev_s[-2] + rev_s[-1]\n    return result", "input": "\"squirre1\"", "output": "'squqs'", "imports": [], "original_snippet": "def f(s: str):\n    rev_s = s[::-1]\n    result = s[0] + s[1] + rev_s[-3] + rev_s[-2] + rev_s[-1]\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_str: str):\n    str_list = list(input_str)\n    reversed_str = ''.join(str_list[::-1])\n    sum_str = sum((ord(char) for char in reversed_str))\n    return sum_str", "input": "'Hello world'", "output": "1084", "imports": [], "original_snippet": "def f(input_str: str):\n    str_list = list(input_str)\n    reversed_str = ''.join(str_list[::-1])\n    sum_str = sum((ord(char) for char in reversed_str))\n    return sum_str", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "from collections import deque\ndef f(sorted_array: list):\n    stack = deque(sorted_array)\n    current_sum = sum(stack)\n    current_product = 1\n    for idx in range(len(stack)):\n        stack[idx] = stack[idx] ** 2\n        stack[idx] = stack[idx] % current_sum\n        current_product *= stack[idx]\n    return current_product", "input": "[1, 2, 3, 4]", "output": "216", "imports": ["from collections import deque"], "original_snippet": "from collections import deque\ndef f(sorted_array: list):\n    stack = deque(sorted_array)\n    current_sum = sum(stack)\n    current_product = 1\n    for idx in range(len(stack)):\n        stack[idx] = stack[idx] ** 2\n        stack[idx] = stack[idx] % current_sum\n        current_product *= stack[idx]\n    return current_product", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_dict: dict):\n    if input_dict['b']:\n        input_dict['a'] = 'value1' + input_dict['a']\n        input_dict['c'] = 20\n        input_dict = f(input_dict)\n    else:\n        input_dict['b'] = 1\n    return input_dict", "input": "{'a': 'value0', 'b': 0}", "output": "{'a': 'value0', 'b': 1}", "imports": [], "original_snippet": "def f(input_dict: dict):\n    if input_dict['b']:\n        input_dict['a'] = 'value1' + input_dict['a']\n        input_dict['c'] = 20\n        input_dict = f(input_dict)\n    else:\n        input_dict['b'] = 1\n    return input_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "import functools\ndef f(input_tuple: tuple) -> tuple:\n    (name, info) = input_tuple\n    info = info.upper()\n    reversed_info = ''.join(reversed(info.split()))\n    return (name, reversed_info)", "input": "('Prof. Gordon', 'I WILL GET MY RING BACK!')", "output": "('Prof. Gordon', 'BACK!RINGMYGETWILLI')", "imports": ["import functools"], "original_snippet": "import functools\ndef f(input_tuple: tuple) -> tuple:\n    (name, info) = input_tuple\n    info = info.upper()\n    reversed_info = ''.join(reversed(info.split()))\n    return (name, reversed_info)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(numbers):\n    return sorted(numbers)", "input": "[5, 6, 3, 2]", "output": "[2, 3, 5, 6]", "imports": [], "original_snippet": "def f(numbers):\n    return sorted(numbers)", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import List, Dict\ndef f(strings: List[str]) -> Dict[int, List[str]]:\n    length_dict = {}\n    for string in strings:\n        temp_length = len(string)\n        if temp_length in length_dict:\n            length_dict[temp_length].append(string)\n        else:\n            length_dict[temp_length] = [string]\n    return length_dict", "input": "['one', 'two', 'three', 'four']", "output": "{3: ['one', 'two'], 5: ['three'], 4: ['four']}", "imports": ["from typing import List, Dict"], "original_snippet": "from typing import List, Dict\ndef f(strings: List[str]) -> Dict[int, List[str]]:\n    length_dict = {}\n    for string in strings:\n        temp_length = len(string)\n        if temp_length in length_dict:\n            length_dict[temp_length].append(string)\n        else:\n            length_dict[temp_length] = [string]\n    return length_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(number):\n    a = number % 10\n    b = (number % 100 - a) / 10\n    c = (number % 1000 - 10 * b - a) / 100\n    d = (number % 10000 - 100 * b - 10 * a - c) / 1000\n    e = (number % 100000 - 1000 * b - 100 * a - 10 * c - d) / 10000\n    result = a * 1000 + b * 100 + c * 10 + d * 1 + e\n    return result", "input": "number = 123456", "output": "6544.6733108", "imports": [], "original_snippet": "def f(number):\n    a = number % 10\n    b = (number % 100 - a) / 10\n    c = (number % 1000 - 10 * b - a) / 100\n    d = (number % 10000 - 100 * b - 10 * a - c) / 1000\n    e = (number % 100000 - 1000 * b - 100 * a - 10 * c - d) / 10000\n    result = a * 1000 + b * 100 + c * 10 + d * 1 + e\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "float"}
{"snippet": "from collections import namedtuple\ndef f(input_list):\n    if not input_list:\n        return 'The input list is empty'\n    s = sum(input_list)\n    if s == 1:\n        return 'The list contains a single item'\n    elif s - 1 / 2 <= 1:\n        return 'The list contains almost a single item'\n    elif s % 2 == 0:\n        return 'The list contains an even number of items'\n    elif s % 3 == 0:\n        return 'The list contains an integer multiple of 3 items'\n    elif s % 3 != 0:\n        return 'The list contains an odd number of items'\n    elif 'key' in set(input_list):\n        return 'The list contains at least one predefined key'\n    elif s >= 2 and s <= 6:\n        return 'The list contains a small number of items'\n    elif s > 6:\n        return 'The list contains a large number of items'\n    elif 'key' not in set(input_list) and s != 0:\n        return 'The list contains at least one item'\n    else:\n        return 'The list contains no items'", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "'The list contains an odd number of items'", "imports": ["from collections import namedtuple"], "original_snippet": "from collections import namedtuple\ndef f(input_list):\n    if not input_list:\n        return 'The input list is empty'\n    s = sum(input_list)\n    if s == 1:\n        return 'The list contains a single item'\n    elif s - 1 / 2 <= 1:\n        return 'The list contains almost a single item'\n    elif s % 2 == 0:\n        return 'The list contains an even number of items'\n    elif s % 3 == 0:\n        return 'The list contains an integer multiple of 3 items'\n    elif s % 3 != 0:\n        return 'The list contains an odd number of items'\n    elif 'key' in set(input_list):\n        return 'The list contains at least one predefined key'\n    elif s >= 2 and s <= 6:\n        return 'The list contains a small number of items'\n    elif s > 6:\n        return 'The list contains a large number of items'\n    elif 'key' not in set(input_list) and s != 0:\n        return 'The list contains at least one item'\n    else:\n        return 'The list contains no items'", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "from typing import Tuple, Dict\ndef f(person_info: Tuple[str, Dict]) -> Tuple[str, str]:\n    (name, info) = person_info\n    reversed_keys = (key[::-1] for key in info.keys())\n    uppercased_keys = (key.upper() for key in reversed_keys)\n    final_name = name\n    final_keys = ' | '.join(uppercased_keys)\n    return (final_name, final_keys)", "input": "('Prof. Gordon', {'age': 20, 'city': 'New York'})", "output": "('Prof. Gordon', 'EGA | YTIC')", "imports": ["from typing import Tuple, Dict"], "original_snippet": "from typing import Tuple, Dict\ndef f(person_info: Tuple[str, Dict]) -> Tuple[str, str]:\n    (name, info) = person_info\n    reversed_keys = (key[::-1] for key in info.keys())\n    uppercased_keys = (key.upper() for key in reversed_keys)\n    final_name = name\n    final_keys = ' | '.join(uppercased_keys)\n    return (final_name, final_keys)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "from collections import namedtuple\ndef f(input_list):\n    if not input_list:\n        return 'The input list is empty'\n    s = sum(input_list)\n    if s == 1:\n        return 'The list contains a single item'\n    elif s - 1 / 2 <= 1:\n        return 'The list contains almost a single item'\n    elif s % 2 == 0:\n        return 'The list contains an even number of items'\n    elif s % 3 == 0:\n        return 'The list contains an integer multiple of 3 items'\n    elif s % 3 != 0:\n        return 'The list contains an odd number of items'\n    elif 'key' in set(input_list):\n        return 'The list contains at least one predefined key'\n    elif abs(s - 1) <= 2:\n        return 'The list contains a small number of items'\n    elif s >= 2 and s <= 6:\n        return 'The list contains a small number of items'\n    elif s > 6:\n        return 'The list contains a large number of items'\n    elif 'key' not in set(input_list) and s != 0:\n        return 'The list contains at least one item'\n    else:\n        return 'The list contains no items'", "input": "[2, 4, 6, 8]", "output": "'The list contains an even number of items'", "imports": ["from collections import namedtuple"], "original_snippet": "from collections import namedtuple\ndef f(input_list):\n    if not input_list:\n        return 'The input list is empty'\n    s = sum(input_list)\n    if s == 1:\n        return 'The list contains a single item'\n    elif s - 1 / 2 <= 1:\n        return 'The list contains almost a single item'\n    elif s % 2 == 0:\n        return 'The list contains an even number of items'\n    elif s % 3 == 0:\n        return 'The list contains an integer multiple of 3 items'\n    elif s % 3 != 0:\n        return 'The list contains an odd number of items'\n    elif 'key' in set(input_list):\n        return 'The list contains at least one predefined key'\n    elif abs(s - 1) <= 2:\n        return 'The list contains a small number of items'\n    elif s >= 2 and s <= 6:\n        return 'The list contains a small number of items'\n    elif s > 6:\n        return 'The list contains a large number of items'\n    elif 'key' not in set(input_list) and s != 0:\n        return 'The list contains at least one item'\n    else:\n        return 'The list contains no items'", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(num1: int, num2: int) -> int:\n    total = num1 + num2\n    return total", "input": "5, 7", "output": "12", "imports": [], "original_snippet": "def f(num1: int, num2: int) -> int:\n    total = num1 + num2\n    return total", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(x):\n    if x == 0:\n        return []\n    else:\n        result = f(x // 2)\n        result.append(x % 2)\n        return result", "input": "5", "output": "[1, 0, 1]", "imports": [], "original_snippet": "def f(x):\n    if x == 0:\n        return []\n    else:\n        result = f(x // 2)\n        result.append(x % 2)\n        return result", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(the_string: str):\n    output = ''\n    for i in range(len(the_string) - 1, -1, -1):\n        output += chr(ord(the_string[i]) + i)\n    return output", "input": "\"deAR\"", "output": "'UCfd'", "imports": [], "original_snippet": "def f(the_string: str):\n    output = ''\n    for i in range(len(the_string) - 1, -1, -1):\n        output += chr(ord(the_string[i]) + i)\n    return output", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from typing import Dict, List\ndef f(input_dict: Dict[str, int]) -> int:\n    temp_dict = {}\n    for (key, value) in input_dict.items():\n        if value in temp_dict:\n            temp_dict[value] += 1\n        else:\n            temp_dict[value] = 1\n    sorted_count_dict = {k: v for (k, v) in sorted(temp_dict.items(), key=lambda item: item[1], reverse=True)}\n    result = None\n    for (key, value) in sorted_count_dict.items():\n        if result is None:\n            result = key * value\n        else:\n            result += key * value\n    return result", "input": "{'cat': 1, 'dog': 2, 'bird': 3, 'fish': 5, 'penguin': 1}", "output": "12", "imports": ["from typing import Dict, List"], "original_snippet": "from typing import Dict, List\ndef f(input_dict: Dict[str, int]) -> int:\n    temp_dict = {}\n    for (key, value) in input_dict.items():\n        if value in temp_dict:\n            temp_dict[value] += 1\n        else:\n            temp_dict[value] = 1\n    sorted_count_dict = {k: v for (k, v) in sorted(temp_dict.items(), key=lambda item: item[1], reverse=True)}\n    result = None\n    for (key, value) in sorted_count_dict.items():\n        if result is None:\n            result = key * value\n        else:\n            result += key * value\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(text: str) -> str:\n    reversed_text = text[::-1]\n    reversed_and_or_not = [(reversed_text, text) if text == 'not' else (reversed_text, 'not')]\n    extracted_chars = [char + 'a' for char in reversed(reversed_and_or_not[0][1])]\n    concatenated_and_reversed = ''.join(extracted_chars) + reversed_text[::-1]\n    character_indexes = [(char, index) for (index, char) in enumerate(reversed_text)]\n    sorted_indexes = sorted(character_indexes, key=lambda x: x[1])\n    concatenated_sorted = ''.join((char for (char, _) in sorted_indexes)) + reversed_text[::-1]\n    final_text = concatenated_and_reversed + concatenated_sorted[::-1]\n    return final_text[::-1]", "input": "'secret'", "output": "'tercessecrettercesanaoat'", "imports": [], "original_snippet": "def f(text: str) -> str:\n    reversed_text = text[::-1]\n    reversed_and_or_not = [(reversed_text, text) if text == 'not' else (reversed_text, 'not')]\n    extracted_chars = [char + 'a' for char in reversed(reversed_and_or_not[0][1])]\n    concatenated_and_reversed = ''.join(extracted_chars) + reversed_text[::-1]\n    character_indexes = [(char, index) for (index, char) in enumerate(reversed_text)]\n    sorted_indexes = sorted(character_indexes, key=lambda x: x[1])\n    concatenated_sorted = ''.join((char for (char, _) in sorted_indexes)) + reversed_text[::-1]\n    final_text = concatenated_and_reversed + concatenated_sorted[::-1]\n    return final_text[::-1]", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from collections import defaultdict\ndef f(n: int) -> dict:\n    result = defaultdict(lambda : [0, 0])\n    for year in range(1851, n + 1):\n        if year % 4 == 0 and year % 100 != 0 or year % 400 == 0:\n            result['leap_years'][0] += 1\n        decade = year % 100 // 10\n        if decade in [0, 2, 19, 21]:\n            place = 'lisbon'\n        else:\n            place = 'london'\n        result[place][1] += 1\n    return dict(result)", "input": "n = 2001", "output": "{'london': [0, 129], 'leap_years': [37, 0], 'lisbon': [0, 22]}", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(n: int) -> dict:\n    result = defaultdict(lambda : [0, 0])\n    for year in range(1851, n + 1):\n        if year % 4 == 0 and year % 100 != 0 or year % 400 == 0:\n            result['leap_years'][0] += 1\n        decade = year % 100 // 10\n        if decade in [0, 2, 19, 21]:\n            place = 'lisbon'\n        else:\n            place = 'london'\n        result[place][1] += 1\n    return dict(result)", "composite_functions": [], "_input_type": "str", "_output_type": "dict"}
{"snippet": "def f(input_string: str) -> list:\n    try:\n        return [int(num) for num in input_string.split(',')]\n    except TypeError:\n        raise TypeError('Input must be a string of comma-separated integers.')", "input": "\"1, 2, 3, 4, 5\"", "output": "[1, 2, 3, 4, 5]", "imports": [], "original_snippet": "def f(input_string: str) -> list:\n    try:\n        return [int(num) for num in input_string.split(',')]\n    except TypeError:\n        raise TypeError('Input must be a string of comma-separated integers.')", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "from typing import List, Dict\ndef f(input_string: str, n: int) -> List[str]:\n    res = []\n    for i in range(0, len(input_string), n):\n        sub_str = input_string[i:i + n]\n        temp = ord(sub_str[-1])\n        count = len([s for s in sub_str if ord(s) == temp])\n        res.append(count)\n    return res", "input": "\"abcabcabc\", 3", "output": "[1, 1, 1]", "imports": ["from typing import List, Dict"], "original_snippet": "from typing import List, Dict\ndef f(input_string: str, n: int) -> List[str]:\n    res = []\n    for i in range(0, len(input_string), n):\n        sub_str = input_string[i:i + n]\n        temp = ord(sub_str[-1])\n        count = len([s for s in sub_str if ord(s) == temp])\n        res.append(count)\n    return res", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "from collections import deque\ndef f(nums: list):\n    stack = deque([num ** 2 for num in nums if num % 2 == 0])\n    result = 1\n    for num in stack:\n        result *= num\n    stack = deque([num ** 2 for num in nums if num % 3 == 0])\n    result *= sum(stack)\n    return result", "input": "[4, 6, 8, 9, 12]", "output": "1385496576", "imports": ["from collections import deque"], "original_snippet": "from collections import deque\ndef f(nums: list):\n    stack = deque([num ** 2 for num in nums if num % 2 == 0])\n    result = 1\n    for num in stack:\n        result *= num\n    stack = deque([num ** 2 for num in nums if num % 3 == 0])\n    result *= sum(stack)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(integers: List[int]) -> float:\n    if integers:\n        sum_ints = sum(integers)\n        average = sum_ints / len(integers)\n        return average\n    else:\n        return 0.0", "input": "None", "output": "0.0", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(integers: List[int]) -> float:\n    if integers:\n        sum_ints = sum(integers)\n        average = sum_ints / len(integers)\n        return average\n    else:\n        return 0.0", "composite_functions": [], "_input_type": "NoneType", "_output_type": "float"}
{"snippet": "def f(input_string: str) -> str:\n    alphabets = {chr(i + 97): chr((i + 87) % 26 + 97) for i in range(52)}\n    translated_string = input_string.translate(str.maketrans(alphabets))\n    return translated_string", "input": "\"aaaaaaaaaaaaAAAaaaAAAaaa\"", "output": "'jjjjjjjjjjjjAAAjjjAAAjjj'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    alphabets = {chr(i + 97): chr((i + 87) % 26 + 97) for i in range(52)}\n    translated_string = input_string.translate(str.maketrans(alphabets))\n    return translated_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(numbers):\n    formatted_numbers = []\n    for (i, num) in enumerate(numbers):\n        if num % 2 == 0:\n            str_num = str(num)\n            rev_num = ''.join((str(digit) for digit in reversed(str_num)))\n            formatted_numbers.append(f'{rev_num}')\n        else:\n            formatted_numbers.append('!@#$%')\n    return ' '.join(formatted_numbers)", "input": "[1, 2, 3, 4, 5]", "output": "'!@#$% 2 !@#$% 4 !@#$%'", "imports": [], "original_snippet": "def f(numbers):\n    formatted_numbers = []\n    for (i, num) in enumerate(numbers):\n        if num % 2 == 0:\n            str_num = str(num)\n            rev_num = ''.join((str(digit) for digit in reversed(str_num)))\n            formatted_numbers.append(f'{rev_num}')\n        else:\n            formatted_numbers.append('!@#$%')\n    return ' '.join(formatted_numbers)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "from collections import defaultdict, deque\ndef f(n: int, x: int) -> bool:\n    result = False\n    while x != 0:\n        if x == 1:\n            result = True\n            break\n        if x % 3 == 0 and x % 4 == 0:\n            result = f(n, x // 12)\n            if result == True:\n                break\n        x -= 1\n    return result", "input": "10, 23", "output": "True", "imports": ["from collections import defaultdict, deque"], "original_snippet": "from collections import defaultdict, deque\ndef f(n: int, x: int) -> bool:\n    result = False\n    while x != 0:\n        if x == 1:\n            result = True\n            break\n        if x % 3 == 0 and x % 4 == 0:\n            result = f(n, x // 12)\n            if result == True:\n                break\n        x -= 1\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "bool"}
{"snippet": "from math import factorial\nfrom functools import reduce\ndef f(numbers):\n    numbers.reverse()\n    numbers = [x * x for x in numbers]\n    numbers.reverse()\n    numbers = [x + 1 for x in numbers]\n    numbers = [factorial(x) for x in numbers]\n    product = reduce(lambda x, y: x * y, numbers)\n    return product", "input": "numbers = [2, 3, 4, 5]", "output": "62464291863199167050758240415917277184000000000000", "imports": ["from math import factorial", "from functools import reduce"], "original_snippet": "from math import factorial\nfrom functools import reduce\ndef f(numbers):\n    numbers.reverse()\n    numbers = [x * x for x in numbers]\n    numbers.reverse()\n    numbers = [x + 1 for x in numbers]\n    numbers = [factorial(x) for x in numbers]\n    product = reduce(lambda x, y: x * y, numbers)\n    return product", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(qubits: list):\n    graph = {i: [] for i in range(8)}\n    visited = [False] * 8\n    for i in range(8):\n        for j in range(i + 1, 8):\n            if qubits[i] == qubits[j] or abs(qubits[i] - qubits[j]) == abs(i - j):\n                graph[i].append(j)\n                graph[j].append(i)\n    bfs_order = []\n    queue = [[0]]\n    while queue:\n        state = queue.pop(0)\n        current_qubit = state[-1]\n        if not visited[current_qubit]:\n            visited[current_qubit] = True\n            next_order = list(state)\n            next_order.append(current_qubit)\n            bfs_order.append(next_order)\n            for neighbor in graph[current_qubit]:\n                queue.append(next_order + [neighbor])\n    return bfs_order", "input": "[0, 2, 4, 6, 1, 3, 5, 7]", "output": "[[0, 0], [0, 0, 7, 7]]", "imports": [], "original_snippet": "def f(qubits: list):\n    graph = {i: [] for i in range(8)}\n    visited = [False] * 8\n    for i in range(8):\n        for j in range(i + 1, 8):\n            if qubits[i] == qubits[j] or abs(qubits[i] - qubits[j]) == abs(i - j):\n                graph[i].append(j)\n                graph[j].append(i)\n    bfs_order = []\n    queue = [[0]]\n    while queue:\n        state = queue.pop(0)\n        current_qubit = state[-1]\n        if not visited[current_qubit]:\n            visited[current_qubit] = True\n            next_order = list(state)\n            next_order.append(current_qubit)\n            bfs_order.append(next_order)\n            for neighbor in graph[current_qubit]:\n                queue.append(next_order + [neighbor])\n    return bfs_order", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(list_of_strings):\n    unique_elements = list(dict.fromkeys(list_of_strings))\n    sum_integers = sum(list(map(int, unique_elements)))\n    reference_value = 5\n    return sum_integers == reference_value", "input": "['1', '2', '3', '4', '1', '2', '5', '6', '7']", "output": "False", "imports": [], "original_snippet": "def f(list_of_strings):\n    unique_elements = list(dict.fromkeys(list_of_strings))\n    sum_integers = sum(list(map(int, unique_elements)))\n    reference_value = 5\n    return sum_integers == reference_value", "composite_functions": [], "_input_type": "list", "_output_type": "bool"}
{"snippet": "def f(s: str) -> str:\n    len_s = len(s)\n    first_char = s[0]\n    last_char = s[len_s - 1]\n    replaced_last_char = first_char + last_char * (len_s - 2)\n    return replaced_last_char", "input": "'John'", "output": "'Jnn'", "imports": [], "original_snippet": "def f(s: str) -> str:\n    len_s = len(s)\n    first_char = s[0]\n    last_char = s[len_s - 1]\n    replaced_last_char = first_char + last_char * (len_s - 2)\n    return replaced_last_char", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import math\ndef f(binary_string: str) -> str:\n    reversed_binary_str = binary_string[::-1]\n    swapped_string = ''.join(reversed(reversed_binary_str))\n    multiplied_num = math.factorial(int(swapped_string))\n    result = math.log2(multiplied_num)\n    return str(result).count('1')", "input": "'011'", "output": "0", "imports": ["import math"], "original_snippet": "import math\ndef f(binary_string: str) -> str:\n    reversed_binary_str = binary_string[::-1]\n    swapped_string = ''.join(reversed(reversed_binary_str))\n    multiplied_num = math.factorial(int(swapped_string))\n    result = math.log2(multiplied_num)\n    return str(result).count('1')", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "from collections import defaultdict, deque\ndef f(nums: list):\n    if not nums:\n        return {}\n    unique_indices = deque()\n    unique_counts = defaultdict(int)\n    unique_counts[0] = 1\n    for (i, num) in enumerate(nums):\n        if i not in unique_indices and num in nums[:i]:\n            continue\n        unique_indices.append(i)\n        unique_counts[num] += 1\n    return unique_counts", "input": "[1, 2, 2, 3, 3, 4]", "output": "defaultdict(<class 'int'>, {0: 1, 1: 1, 2: 1, 3: 1, 4: 1})", "imports": ["from collections import defaultdict, deque"], "original_snippet": "from collections import defaultdict, deque\ndef f(nums: list):\n    if not nums:\n        return {}\n    unique_indices = deque()\n    unique_counts = defaultdict(int)\n    unique_counts[0] = 1\n    for (i, num) in enumerate(nums):\n        if i not in unique_indices and num in nums[:i]:\n            continue\n        unique_indices.append(i)\n        unique_counts[num] += 1\n    return unique_counts", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(input_sequence: list):\n    n = len(input_sequence)\n    result = []\n    for (idx, val) in enumerate(input_sequence):\n        result.extend([idx] * val)\n    return result", "input": "[2, 3, 1, 4, 5]", "output": "[0, 0, 1, 1, 1, 2, 3, 3, 3, 3, 4, 4, 4, 4, 4]", "imports": [], "original_snippet": "def f(input_sequence: list):\n    n = len(input_sequence)\n    result = []\n    for (idx, val) in enumerate(input_sequence):\n        result.extend([idx] * val)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import List\ndef f(numbers: List[int]) -> List[int]:\n    transformed_numbers = [number * 2 if number % 2 == 0 else number + 3 for number in numbers]\n    return transformed_numbers", "input": "[1, 2, 3, 4, 5]", "output": "[4, 4, 6, 8, 8]", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(numbers: List[int]) -> List[int]:\n    transformed_numbers = [number * 2 if number % 2 == 0 else number + 3 for number in numbers]\n    return transformed_numbers", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import Dict, List, Tuple\ndef f(s: str) -> Tuple[Dict[int, List[int]], Dict[int, str]]:\n    temp_dict = dict()\n    indices_dict = {}\n    for i in range(len(s)):\n        if s[i] in temp_dict:\n            temp_dict[s[i]].append(i)\n        else:\n            temp_dict[s[i]] = [i]\n    for (key, value) in temp_dict.items():\n        indices_dict[len(key)] = value\n    return (indices_dict, temp_dict)", "input": "'special'", "output": "({1: [6]}, {'s': [0], 'p': [1], 'e': [2], 'c': [3], 'i': [4], 'a': [5], 'l': [6]})", "imports": ["from typing import Dict, List, Tuple"], "original_snippet": "from typing import Dict, List, Tuple\ndef f(s: str) -> Tuple[Dict[int, List[int]], Dict[int, str]]:\n    temp_dict = dict()\n    indices_dict = {}\n    for i in range(len(s)):\n        if s[i] in temp_dict:\n            temp_dict[s[i]].append(i)\n        else:\n            temp_dict[s[i]] = [i]\n    for (key, value) in temp_dict.items():\n        indices_dict[len(key)] = value\n    return (indices_dict, temp_dict)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(numbers):\n    sum_products = 0\n    for i in range(len(numbers) - 1):\n        sum_products += numbers[i] * numbers[i + 1]\n    return sum_products", "input": "[1, 2, 3, 4, 5, 6]", "output": "70", "imports": [], "original_snippet": "def f(numbers):\n    sum_products = 0\n    for i in range(len(numbers) - 1):\n        sum_products += numbers[i] * numbers[i + 1]\n    return sum_products", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_string):\n    substring = input_string[1:]\n    first_letter = input_string[0]\n    substring = substring + first_letter + 'ay'\n    return substring", "input": "'may'", "output": "'aymay'", "imports": [], "original_snippet": "def f(input_string):\n    substring = input_string[1:]\n    first_letter = input_string[0]\n    substring = substring + first_letter + 'ay'\n    return substring", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(nums_dict):\n    summ = sum(sorted(nums_dict.values()))\n    return summ", "input": "{'a': 12, 'b': 34, 'c': 23}", "output": "69", "imports": [], "original_snippet": "def f(nums_dict):\n    summ = sum(sorted(nums_dict.values()))\n    return summ", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(x, y):\n    sum_of_products = 0\n    sum_of_x = 0\n    sum_of_y = 0\n    result_x = 0\n    result_y = 0\n    for i in range(x):\n        sum_of_products += i * i\n        sum_of_x += i\n    for j in range(y):\n        sum_of_y += j\n    result_x = sum_of_x * 2 ** sum_of_products\n    result_y = sum_of_y / sum_of_products\n    return (result_x, result_y)", "input": "15, 2", "output": "(36866753742293587951479891607978046294899918513465271511152653362425646405425002204949980700884357973081273354270500590926118995557644033909665976986610904413409431268771242097679166129161796645590964277283057823687511370402262895614767337122630585746390241448987424443739181295977285570327563309786217840640, 0.0009852216748768472)", "imports": [], "original_snippet": "def f(x, y):\n    sum_of_products = 0\n    sum_of_x = 0\n    sum_of_y = 0\n    result_x = 0\n    result_y = 0\n    for i in range(x):\n        sum_of_products += i * i\n        sum_of_x += i\n    for j in range(y):\n        sum_of_y += j\n    result_x = sum_of_x * 2 ** sum_of_products\n    result_y = sum_of_y / sum_of_products\n    return (result_x, result_y)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(word: str) -> int:\n    vowel_stack = str.casefold('aeiou')\n    shared_vowels = [v for v in word if v in vowel_stack]\n    return len(shared_vowels)", "input": "\"Hello\"", "output": "2", "imports": [], "original_snippet": "def f(word: str) -> int:\n    vowel_stack = str.casefold('aeiou')\n    shared_vowels = [v for v in word if v in vowel_stack]\n    return len(shared_vowels)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_string: str) -> str:\n    output1 = list(input_string)\n    output1.reverse()\n    output2 = output1[1:]\n    output3 = output1[0:]\n    output3 = ''.join(output3) + ''.join(output2) + output1[0]\n    return output3", "input": "'happy'", "output": "'yppahppahy'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    output1 = list(input_string)\n    output1.reverse()\n    output2 = output1[1:]\n    output3 = output1[0:]\n    output3 = ''.join(output3) + ''.join(output2) + output1[0]\n    return output3", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(numbers):\n    if not numbers:\n        return 0\n    max_diff = abs(numbers[0] - numbers[1])\n    for i in range(1, len(numbers) - 1):\n        diff = abs(numbers[i] - numbers[i + 1])\n        if diff > max_diff:\n            max_diff = diff\n    return max_diff", "input": "[5, 3, 1, 6, 8, 9]", "output": "5", "imports": [], "original_snippet": "def f(numbers):\n    if not numbers:\n        return 0\n    max_diff = abs(numbers[0] - numbers[1])\n    for i in range(1, len(numbers) - 1):\n        diff = abs(numbers[i] - numbers[i + 1])\n        if diff > max_diff:\n            max_diff = diff\n    return max_diff", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(nums: list):\n    even_sum = 0\n    odd_count = 0\n    if len(nums) == 0:\n        return 'The list is empty'\n    if nums[0] % 2 == 0:\n        even_sum += nums[0]\n    if nums[0] % 2 != 0:\n        odd_count += 1\n    for i in nums[1:]:\n        if i % 2 == 0:\n            even_sum += i\n        else:\n            odd_count += 1\n    return f'The sum of even numbers is {even_sum} and the number of odd numbers is {odd_count}'", "input": "[2, 3, 4, 4, 4, 5]", "output": "'The sum of even numbers is 14 and the number of odd numbers is 2'", "imports": [], "original_snippet": "def f(nums: list):\n    even_sum = 0\n    odd_count = 0\n    if len(nums) == 0:\n        return 'The list is empty'\n    if nums[0] % 2 == 0:\n        even_sum += nums[0]\n    if nums[0] % 2 != 0:\n        odd_count += 1\n    for i in nums[1:]:\n        if i % 2 == 0:\n            even_sum += i\n        else:\n            odd_count += 1\n    return f'The sum of even numbers is {even_sum} and the number of odd numbers is {odd_count}'", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(word, ap='ap'):\n    if not word[0] in word[1:]:\n        last_char = word[-1]\n        first_char = word[0]\n        comb = last_char + first_char + ap\n        return comb\n    else:\n        return None", "input": "'censor'", "output": "'rcap'", "imports": [], "original_snippet": "def f(word, ap='ap'):\n    if not word[0] in word[1:]:\n        last_char = word[-1]\n        first_char = word[0]\n        comb = last_char + first_char + ap\n        return comb\n    else:\n        return None", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_str: str) -> int:\n    sum_str = 0\n    for char in input_str:\n        sum_str += ord(char)\n    return sum_str", "input": "'Hello'", "output": "500", "imports": [], "original_snippet": "def f(input_str: str) -> int:\n    sum_str = 0\n    for char in input_str:\n        sum_str += ord(char)\n    return sum_str", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_list):\n    if not input_list:\n        return 'The input list is empty'\n    s = sum(input_list)\n    if s == 1:\n        return 'The list contains a single item'\n    elif s - 1 / 2 <= 1:\n        return 'The list contains almost a single item'\n    elif s % 2 == 0:\n        return 'The list contains an even number of items'\n    elif s % 3 == 0:\n        return 'The list contains an integer multiple of 3 items'\n    elif s % 3 != 0:\n        return 'The list contains an odd number of items'\n    elif 'key' in set(input_list):\n        return 'The list contains at least one predefined key'\n    elif abs(s - 1) <= 2:\n        return 'The list contains a small number of items'\n    elif s >= 2 and s <= 6:\n        return 'The list contains a small number of items'\n    elif s > 6:\n        return 'The list contains a large number of items'\n    elif 'key' not in set(input_list) and s != 0:\n        return 'The list contains at least one item'\n    else:\n        return 'The list contains no items'", "input": "[2, 4, 6, 8]", "output": "'The list contains an even number of items'", "imports": [], "original_snippet": "def f(input_list):\n    if not input_list:\n        return 'The input list is empty'\n    s = sum(input_list)\n    if s == 1:\n        return 'The list contains a single item'\n    elif s - 1 / 2 <= 1:\n        return 'The list contains almost a single item'\n    elif s % 2 == 0:\n        return 'The list contains an even number of items'\n    elif s % 3 == 0:\n        return 'The list contains an integer multiple of 3 items'\n    elif s % 3 != 0:\n        return 'The list contains an odd number of items'\n    elif 'key' in set(input_list):\n        return 'The list contains at least one predefined key'\n    elif abs(s - 1) <= 2:\n        return 'The list contains a small number of items'\n    elif s >= 2 and s <= 6:\n        return 'The list contains a small number of items'\n    elif s > 6:\n        return 'The list contains a large number of items'\n    elif 'key' not in set(input_list) and s != 0:\n        return 'The list contains at least one item'\n    else:\n        return 'The list contains no items'", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "import math\ndef f(num: int) -> int:\n    prime_numbers = [True] * (num + 1)\n    p = 2\n    while p * p <= num:\n        if prime_numbers[p] == True:\n            for i in range(p * p, num + 1, p):\n                prime_numbers[i] = False\n        p += 1\n    is_prime = list(filter(lambda x: x, prime_numbers[2:num + 1]))\n    return is_prime", "input": "20", "output": "[True, True, True, True, True, True, True, True]", "imports": ["import math"], "original_snippet": "import math\ndef f(num: int) -> int:\n    prime_numbers = [True] * (num + 1)\n    p = 2\n    while p * p <= num:\n        if prime_numbers[p] == True:\n            for i in range(p * p, num + 1, p):\n                prime_numbers[i] = False\n        p += 1\n    is_prime = list(filter(lambda x: x, prime_numbers[2:num + 1]))\n    return is_prime", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(numbers):\n    max_val = float('-inf')\n    min_val = float('inf')\n    result = 0\n    for num in numbers:\n        max_val = max(max_val, num)\n        min_val = min(min_val, num)\n        result |= num\n    return max(max_val, min_val) + (max_val | min_val)", "input": "numbers = [1, 2, 3, 4, 5, 6]", "output": "13", "imports": [], "original_snippet": "def f(numbers):\n    max_val = float('-inf')\n    min_val = float('inf')\n    result = 0\n    for num in numbers:\n        max_val = max(max_val, num)\n        min_val = min(min_val, num)\n        result |= num\n    return max(max_val, min_val) + (max_val | min_val)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_string: str, special_dict: dict={'aab': 'zzz', 'cd': 'vvv'}):\n    count = 0\n    for (key, value) in special_dict.items():\n        if key in input_string:\n            count += 1\n            input_string = input_string.replace(key, value)\n    num_vowels = sum((1 for char in input_string if char.lower() in {'a', 'e', 'i', 'o', 'u'}))\n    return num_vowels * 2 + len(input_string)", "input": "'Hello world!'", "output": "18", "imports": [], "original_snippet": "def f(input_string: str, special_dict: dict={'aab': 'zzz', 'cd': 'vvv'}):\n    count = 0\n    for (key, value) in special_dict.items():\n        if key in input_string:\n            count += 1\n            input_string = input_string.replace(key, value)\n    num_vowels = sum((1 for char in input_string if char.lower() in {'a', 'e', 'i', 'o', 'u'}))\n    return num_vowels * 2 + len(input_string)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(numbers):\n    transformed_numbers = []\n    for x in sorted(numbers, reverse=True):\n        transformed_numbers.append(x + (x // 2 if x % 2 != 0 else x))\n    return sum(transformed_numbers)", "input": "[1, 2, 3, 4, 5]", "output": "24", "imports": [], "original_snippet": "def f(numbers):\n    transformed_numbers = []\n    for x in sorted(numbers, reverse=True):\n        transformed_numbers.append(x + (x // 2 if x % 2 != 0 else x))\n    return sum(transformed_numbers)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(custom_data: list, input_list: list):\n    stack = []\n    for (i, elem) in enumerate(input_list):\n        for j in range(i, -1, -1):\n            if custom_data[i] and stack == []:\n                break\n            if not custom_data[j] and stack == []:\n                break\n            if custom_data[j]:\n                stack.append((j, elem))\n    return [i for (i, _) in stack]", "input": "custom_data = [False, False, False, True, False, True, False],\ninput_list = [1, 2, 3, 4, 5, 6, 7]", "output": "[]", "imports": [], "original_snippet": "def f(custom_data: list, input_list: list):\n    stack = []\n    for (i, elem) in enumerate(input_list):\n        for j in range(i, -1, -1):\n            if custom_data[i] and stack == []:\n                break\n            if not custom_data[j] and stack == []:\n                break\n            if custom_data[j]:\n                stack.append((j, elem))\n    return [i for (i, _) in stack]", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(s: str, c: str):\n    reversed_s = s[::-1]\n    indices = [i for (i, char) in enumerate(reversed_s) if char == c]\n    return indices", "input": "'programming', 'm'", "output": "[3, 4]", "imports": [], "original_snippet": "def f(s: str, c: str):\n    reversed_s = s[::-1]\n    indices = [i for (i, char) in enumerate(reversed_s) if char == c]\n    return indices", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(alist):\n    total_sum = 0\n    for n in alist:\n        total_sum += n\n    total_sum *= len(alist)\n    reversed_list = alist[::-1]\n    total_sum += sum(reversed_list)\n    return total_sum", "input": "[1, 2, 3, 4, 5]", "output": "90", "imports": [], "original_snippet": "def f(alist):\n    total_sum = 0\n    for n in alist:\n        total_sum += n\n    total_sum *= len(alist)\n    reversed_list = alist[::-1]\n    total_sum += sum(reversed_list)\n    return total_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from collections import deque\ndef f(input_tuple: tuple) -> tuple:\n    solution = []\n    d = deque(input_tuple)\n    n = len(d) - 1\n    while n > 0:\n        solution.append(d[n])\n        solution.append(d[0])\n        d.rotate(-1)\n        n -= 1\n    if n == 0:\n        solution.append(d[0])\n    return tuple(solution)", "input": "(1, 2, 3, 4, 5, 6)", "output": "(6, 1, 6, 2, 6, 3, 6, 4, 6, 5, 6)", "imports": ["from collections import deque"], "original_snippet": "from collections import deque\ndef f(input_tuple: tuple) -> tuple:\n    solution = []\n    d = deque(input_tuple)\n    n = len(d) - 1\n    while n > 0:\n        solution.append(d[n])\n        solution.append(d[0])\n        d.rotate(-1)\n        n -= 1\n    if n == 0:\n        solution.append(d[0])\n    return tuple(solution)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(numbers):\n    result = []\n    for num in numbers:\n        if num % 2 == 0:\n            result.append(num * 2)\n        else:\n            result.append(num + 3)\n    return result", "input": "numbers = [2, 3, 4, 5]", "output": "[4, 6, 8, 8]", "imports": [], "original_snippet": "def f(numbers):\n    result = []\n    for num in numbers:\n        if num % 2 == 0:\n            result.append(num * 2)\n        else:\n            result.append(num + 3)\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "import collections\ndef f(info_dict: dict) -> str:\n    reversed_info = collections.defaultdict(lambda : collections.defaultdict(str))\n    for (key, value) in info_dict.items():\n        if 'name' in key.lower() and len(key) == 4:\n            reversed_info['name'][len(key) - 1] = value\n    reversed_info_str = str()\n    for value in reversed_info.values():\n        for key in value:\n            reversed_info_str += value[key] + key\n        reversed_info_str += ' '\n    return reversed_info_str.strip()", "input": "info_dict = {'Mr Anonymous': 'John', 'Mrs Anonymous': 'Alice', 'Mr John Doe': 'Jane'}", "output": "''", "imports": ["import collections"], "original_snippet": "import collections\ndef f(info_dict: dict) -> str:\n    reversed_info = collections.defaultdict(lambda : collections.defaultdict(str))\n    for (key, value) in info_dict.items():\n        if 'name' in key.lower() and len(key) == 4:\n            reversed_info['name'][len(key) - 1] = value\n    reversed_info_str = str()\n    for value in reversed_info.values():\n        for key in value:\n            reversed_info_str += value[key] + key\n        reversed_info_str += ' '\n    return reversed_info_str.strip()", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_dict: dict):\n    squared_dict = {k: v ** 2 for (k, v) in input_dict.items()}\n    return squared_dict", "input": "{1: 4, 2: 9, 3: 16}", "output": "{1: 16, 2: 81, 3: 256}", "imports": [], "original_snippet": "def f(input_dict: dict):\n    squared_dict = {k: v ** 2 for (k, v) in input_dict.items()}\n    return squared_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(n: int) -> int:\n    return n * (n + 1) // 2", "input": "10", "output": "55", "imports": [], "original_snippet": "def f(n: int) -> int:\n    return n * (n + 1) // 2", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "from collections import defaultdict\ndef f(s: str) -> str:\n    result = [s[0]]\n    count = 1\n    for c in s[1:]:\n        if c == result[-1][0]:\n            count += 1\n        else:\n            result.append(str(count) + result[-1][0])\n            result[-1] = c\n            count = 1\n    result.append(str(count) + result[-1][0])\n    return ''.join(result)", "input": "'aaaaab'", "output": "'ab1b'", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(s: str) -> str:\n    result = [s[0]]\n    count = 1\n    for c in s[1:]:\n        if c == result[-1][0]:\n            count += 1\n        else:\n            result.append(str(count) + result[-1][0])\n            result[-1] = c\n            count = 1\n    result.append(str(count) + result[-1][0])\n    return ''.join(result)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import functools\ndef f(input_list: list) -> int:\n    sum_of_numbers = functools.reduce(lambda x, y: x + y, input_list)\n    return sum_of_numbers % 10", "input": "[1, 2, 3, 4, 5]", "output": "5", "imports": ["import functools"], "original_snippet": "import functools\ndef f(input_list: list) -> int:\n    sum_of_numbers = functools.reduce(lambda x, y: x + y, input_list)\n    return sum_of_numbers % 10", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(the_string: str):\n    original_string = the_string\n    start = 0\n    end = len(the_string) - 1\n    while start < end:\n        if the_string[start] != the_string[end]:\n            return False\n        start += 1\n        end -= 1\n    return True", "input": "'racecar'", "output": "True", "imports": [], "original_snippet": "def f(the_string: str):\n    original_string = the_string\n    start = 0\n    end = len(the_string) - 1\n    while start < end:\n        if the_string[start] != the_string[end]:\n            return False\n        start += 1\n        end -= 1\n    return True", "composite_functions": [], "_input_type": "str", "_output_type": "bool"}
{"snippet": "def f(string_input):\n    digits = [int(c) for c in string_input if c.isdigit()]\n    if not digits:\n        return 'No digits found'\n    digit_rank = {str(d): i for (i, d) in enumerate(digits)}\n    if not len(digit_rank):\n        return 'No digits found'\n    index_digit = sorted(digit_rank.keys(), reverse=True)[:6]\n    return [str(d) + ' appears at index ' + str(digit_rank.get(d)) for d in index_digit]", "input": "string_input = 'The 1st digit is 2, the 2nd is 5, the 3rd is 9, the 4th is 1, the 5th is 0 and the 6th is 2.'", "output": "['9 appears at index 5', '6 appears at index 10', '5 appears at index 8', '4 appears at index 6', '3 appears at index 4', '2 appears at index 11']", "imports": [], "original_snippet": "def f(string_input):\n    digits = [int(c) for c in string_input if c.isdigit()]\n    if not digits:\n        return 'No digits found'\n    digit_rank = {str(d): i for (i, d) in enumerate(digits)}\n    if not len(digit_rank):\n        return 'No digits found'\n    index_digit = sorted(digit_rank.keys(), reverse=True)[:6]\n    return [str(d) + ' appears at index ' + str(digit_rank.get(d)) for d in index_digit]", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(dna_read: str, dna_ref: str) -> int:\n    transform = dna_read.replace('T', '')\n    count_diff = sum((transform.count(base) for base in 'AT')) - sum((dna_ref.count(base) for base in 'AT'))\n    return count_diff", "input": "'datadog', 'having'", "output": "0", "imports": [], "original_snippet": "def f(dna_read: str, dna_ref: str) -> int:\n    transform = dna_read.replace('T', '')\n    count_diff = sum((transform.count(base) for base in 'AT')) - sum((dna_ref.count(base) for base in 'AT'))\n    return count_diff", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(nums):\n    filtered_nums = []\n    for n in nums:\n        n_str = str(n)\n        if '1' in n_str or '7' in n_str:\n            filtered_nums.append(n)\n    return filtered_nums", "input": "[50, 82, 199, 141, 91, 37, 44]", "output": "[199, 141, 91, 37]", "imports": [], "original_snippet": "def f(nums):\n    filtered_nums = []\n    for n in nums:\n        n_str = str(n)\n        if '1' in n_str or '7' in n_str:\n            filtered_nums.append(n)\n    return filtered_nums", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_string: str, rotations=0) -> str:\n    rotation_count = rotations\n    while '0123456789' in input_string:\n        first_digit = input_string.find('0123456789')\n        if first_digit >= 0 and first_digit + 3 < len(input_string):\n            input_string = input_string[:first_digit + 1] + input_string[first_digit + 1:20] + input_string[:first_digit + 1]\n        rotation_count += 1\n    return input_string", "input": "f('The 1st digit is 2, the 2nd is 5, the 3rd is 9, the 4th is 1, the 5th is 0 and the 6th is 2.')", "output": "'The 1st digit is 2, the 2nd is 5, the 3rd is 9, the 4th is 1, the 5th is 0 and the 6th is 2.'", "imports": [], "original_snippet": "def f(input_string: str, rotations=0) -> str:\n    rotation_count = rotations\n    while '0123456789' in input_string:\n        first_digit = input_string.find('0123456789')\n        if first_digit >= 0 and first_digit + 3 < len(input_string):\n            input_string = input_string[:first_digit + 1] + input_string[first_digit + 1:20] + input_string[:first_digit + 1]\n        rotation_count += 1\n    return input_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from typing import List\ndef f(data: List[int]) -> List[int]:\n    if data == []:\n        return []\n    max_sublist = []\n    for i in range(len(data) - 1):\n        if len(data[i:i + 2]) == 2 and data[i:i + 2] == [1, data[i + 1]]:\n            max_sublist = data[i + 1:]\n            break\n    return max_sublist", "input": "[1, 2, 0, 1, 0, 1, 1, 1, 1, 1, 1]", "output": "[2, 0, 1, 0, 1, 1, 1, 1, 1, 1]", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(data: List[int]) -> List[int]:\n    if data == []:\n        return []\n    max_sublist = []\n    for i in range(len(data) - 1):\n        if len(data[i:i + 2]) == 2 and data[i:i + 2] == [1, data[i + 1]]:\n            max_sublist = data[i + 1:]\n            break\n    return max_sublist", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list: list) -> int:\n    result = 0\n    for index in range(0, len(input_list) - 1):\n        result += input_list[index] + input_list[index + 1]\n    return result", "input": "[1, 2, 3, 4, 5]", "output": "24", "imports": [], "original_snippet": "def f(input_list: list) -> int:\n    result = 0\n    for index in range(0, len(input_list) - 1):\n        result += input_list[index] + input_list[index + 1]\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(string_input):\n    alphabets = {'a': 'b', 'b': 'c', 'c': 'd', 'd': 'e', 'e': 'f', 'f': 'g', 'g': 'h', 'h': 'i', 'i': 'j', 'j': 'k', 'k': 'l', 'l': 'm', 'm': 'n', 'n': 'o', 'o': 'p', 'p': 'q', 'q': 'r', 'r': 's', 's': 't', 't': 'u', 'u': 'v', 'v': 'w', 'w': 'x', 'x': 'y', 'y': 'z', 'z': 'a'}\n    translated_string = ''\n    for c in string_input:\n        if c in alphabets:\n            translated_string += alphabets[c]\n        else:\n            translated_string += c\n    return translated_string", "input": "string_input = 'Hello, World!'", "output": "'Hfmmp, Wpsme!'", "imports": [], "original_snippet": "def f(string_input):\n    alphabets = {'a': 'b', 'b': 'c', 'c': 'd', 'd': 'e', 'e': 'f', 'f': 'g', 'g': 'h', 'h': 'i', 'i': 'j', 'j': 'k', 'k': 'l', 'l': 'm', 'm': 'n', 'n': 'o', 'o': 'p', 'p': 'q', 'q': 'r', 'r': 's', 's': 't', 't': 'u', 'u': 'v', 'v': 'w', 'w': 'x', 'x': 'y', 'y': 'z', 'z': 'a'}\n    translated_string = ''\n    for c in string_input:\n        if c in alphabets:\n            translated_string += alphabets[c]\n        else:\n            translated_string += c\n    return translated_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(name: str, info: dict):\n    name_tuple = tuple(name)\n    info_str = ','.join(info.keys()) + ' = ' + ', '.join([f'{key}: {info[key]}' for key in info.keys()])\n    foo = 'bar'\n    bar = 'foo'\n    baz = 'baz'\n    bslash_foo = '\\\\' + foo\n    usual_foo = '' + foo\n    usual_baz = '' + baz\n    foo_bars = usual_foo + ', ' + bslash_foo\n    bslash_foo_separators = usual_foo.split(', ')\n    num1 = 111222333\n    num2 = 222333111\n    transformed_num = (num1 // 100 + num2 // 100 - (num1 % 100 // 10 + num2 % 100 // 10)) % 10\n    return f'{info_str} // {transformed_num}'", "input": "'John', {'age': 20, 'city': 'New York'}", "output": "'age,city = age: 20, city: New York // 0'", "imports": [], "original_snippet": "def f(name: str, info: dict):\n    name_tuple = tuple(name)\n    info_str = ','.join(info.keys()) + ' = ' + ', '.join([f'{key}: {info[key]}' for key in info.keys()])\n    foo = 'bar'\n    bar = 'foo'\n    baz = 'baz'\n    bslash_foo = '\\\\' + foo\n    usual_foo = '' + foo\n    usual_baz = '' + baz\n    foo_bars = usual_foo + ', ' + bslash_foo\n    bslash_foo_separators = usual_foo.split(', ')\n    num1 = 111222333\n    num2 = 222333111\n    transformed_num = (num1 // 100 + num2 // 100 - (num1 % 100 // 10 + num2 % 100 // 10)) % 10\n    return f'{info_str} // {transformed_num}'", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(string: str) -> str:\n    return string[::-1]", "input": "'Hello world'", "output": "'dlrow olleH'", "imports": [], "original_snippet": "def f(string: str) -> str:\n    return string[::-1]", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import numpy as np\ndef f(s: str) -> list:\n    temp_list = [ch for ch in reversed(s) if ch == '0']\n    return ['2' if ch != '0' else 'a' for ch in temp_list]", "input": "'0230456'", "output": "['a', 'a']", "imports": ["import numpy as np"], "original_snippet": "import numpy as np\ndef f(s: str) -> list:\n    temp_list = [ch for ch in reversed(s) if ch == '0']\n    return ['2' if ch != '0' else 'a' for ch in temp_list]", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(binary_string: str) -> int:\n    decimal = 0\n    for digit in binary_string:\n        value = int(digit) * 2 ** (len(binary_string) - 1) if digit == '1' else 0\n        decimal += value\n        binary_string = binary_string[:-1]\n    return decimal", "input": "binary_string = '1101'", "output": "13", "imports": [], "original_snippet": "def f(binary_string: str) -> int:\n    decimal = 0\n    for digit in binary_string:\n        value = int(digit) * 2 ** (len(binary_string) - 1) if digit == '1' else 0\n        decimal += value\n        binary_string = binary_string[:-1]\n    return decimal", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(n: int):\n    (a, b) = (0, 1)\n    for _ in range(n):\n        (a, b) = (b, a + b)\n    return a", "input": "5", "output": "5", "imports": [], "original_snippet": "def f(n: int):\n    (a, b) = (0, 1)\n    for _ in range(n):\n        (a, b) = (b, a + b)\n    return a", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "import math\ndef f(number: int) -> int:\n    digit_list = [str(digit) for digit in list(str(number))]\n    digit_list.reverse()\n    result = sum([math.pow(int(digit), 3) for digit in digit_list])\n    return result", "input": "423", "output": "99.0", "imports": ["import math"], "original_snippet": "import math\ndef f(number: int) -> int:\n    digit_list = [str(digit) for digit in list(str(number))]\n    digit_list.reverse()\n    result = sum([math.pow(int(digit), 3) for digit in digit_list])\n    return result", "composite_functions": [], "_input_type": "int", "_output_type": "float"}
{"snippet": "import math\ndef f(nums: list) -> int:\n    if not nums:\n        return 0\n    unique_indices = []\n    unique_counts = {}\n    for (i, num) in enumerate(nums):\n        num_idx = i\n        if i not in unique_indices and num in nums[:i]:\n            continue\n        unique_indices.append(i)\n        num_idx = i if num not in unique_counts else unique_counts[num]\n        unique_counts[num] = num_idx\n        unique_counts[num_idx] = num_idx + 1\n    return max(unique_counts.values())", "input": "[1, 2, 1, 2, 3, 3, 4]", "output": "6", "imports": ["import math"], "original_snippet": "import math\ndef f(nums: list) -> int:\n    if not nums:\n        return 0\n    unique_indices = []\n    unique_counts = {}\n    for (i, num) in enumerate(nums):\n        num_idx = i\n        if i not in unique_indices and num in nums[:i]:\n            continue\n        unique_indices.append(i)\n        num_idx = i if num not in unique_counts else unique_counts[num]\n        unique_counts[num] = num_idx\n        unique_counts[num_idx] = num_idx + 1\n    return max(unique_counts.values())", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from collections import deque\ndef f(nums: list):\n    swaps = 0\n    for i in range(len(nums)):\n        for j in range(len(nums) - 1):\n            if nums[j] > nums[j + 1]:\n                (nums[j], nums[j + 1]) = (nums[j + 1], nums[j])\n                swaps += 1\n    return swaps", "input": "[3, 2, 1]", "output": "3", "imports": ["from collections import deque"], "original_snippet": "from collections import deque\ndef f(nums: list):\n    swaps = 0\n    for i in range(len(nums)):\n        for j in range(len(nums) - 1):\n            if nums[j] > nums[j + 1]:\n                (nums[j], nums[j + 1]) = (nums[j + 1], nums[j])\n                swaps += 1\n    return swaps", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(name, info):\n    digits = str(info).replace(',', '').split('.')\n    reverse_digits = digits[::-1]\n    reverse_string = ''.join(reverse_digits)\n    return reverse_string + name", "input": "'5.1, 2, 3', 'hello_world'", "output": "'hello_world5.1, 2, 3'", "imports": [], "original_snippet": "def f(name, info):\n    digits = str(info).replace(',', '').split('.')\n    reverse_digits = digits[::-1]\n    reverse_string = ''.join(reverse_digits)\n    return reverse_string + name", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "from collections import defaultdict\ndef f(layers: list[dict[str, int]]) -> dict[str, int]:\n    local_counts = defaultdict(int)\n    for layer in layers:\n        for (key, value) in layer.items():\n            local_counts[key] += value\n    final_dict = {}\n    for (key, value) in local_counts.items():\n        all_keys = [k for l in layers for (k, v) in l.items() if k == key]\n        if len(all_keys) > 1:\n            final_dict[key] = value\n    return final_dict", "input": "[{'a': 1, 'b': 2}, {'b': 1, 'c': 1}, {'a': 2, 'b': 1}]", "output": "{'a': 3, 'b': 4}", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(layers: list[dict[str, int]]) -> dict[str, int]:\n    local_counts = defaultdict(int)\n    for layer in layers:\n        for (key, value) in layer.items():\n            local_counts[key] += value\n    final_dict = {}\n    for (key, value) in local_counts.items():\n        all_keys = [k for l in layers for (k, v) in l.items() if k == key]\n        if len(all_keys) > 1:\n            final_dict[key] = value\n    return final_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(input_string):\n    output = ''\n    for i in range(len(input_string)):\n        output += input_string[-(i + 1)]\n    return output", "input": "'World'", "output": "'dlroW'", "imports": [], "original_snippet": "def f(input_string):\n    output = ''\n    for i in range(len(input_string)):\n        output += input_string[-(i + 1)]\n    return output", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(val):\n    val = int(val)\n    binary_number = bin(val)\n    new_val = bin(val | 1 << 31)\n    return new_val[2:]", "input": "0", "output": "'10000000000000000000000000000000'", "imports": [], "original_snippet": "def f(val):\n    val = int(val)\n    binary_number = bin(val)\n    new_val = bin(val | 1 << 31)\n    return new_val[2:]", "composite_functions": [], "_input_type": "int", "_output_type": "str"}
{"snippet": "def f(custom_data: list, proposed_list: list):\n    new_list = []\n    for element in proposed_list:\n        stack = []\n        for item in custom_data:\n            if isinstance(item, dict):\n                values = item.values()\n                if all((value > element for value in values)):\n                    stack.append((custom_data.index(item), element))\n            elif item > element:\n                stack.append((custom_data.index(item), element))\n        new_list.append(stack[-1][0] if stack else None)\n    return [item if item is not None else False for item in new_list]", "input": "custom_data = [1, 2, 3, 4, 5, 6, 7],\nproposed_list = [9, 8, 7, 6, 5, 4, 3]", "output": "[False, False, False, 6, 6, 6, 6]", "imports": [], "original_snippet": "def f(custom_data: list, proposed_list: list):\n    new_list = []\n    for element in proposed_list:\n        stack = []\n        for item in custom_data:\n            if isinstance(item, dict):\n                values = item.values()\n                if all((value > element for value in values)):\n                    stack.append((custom_data.index(item), element))\n            elif item > element:\n                stack.append((custom_data.index(item), element))\n        new_list.append(stack[-1][0] if stack else None)\n    return [item if item is not None else False for item in new_list]", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "import math\ndef f(input_list: list):\n    input_list.sort()\n    even_list = [input_list[i] for i in range(len(input_list)) if i % 2 == 0]\n    new_list = [even_list[0], even_list[len(even_list) - 1]]\n    for i in range(1, len(even_list)):\n        avg = (even_list[i] + even_list[i - 1]) / 2\n        new_list.append(avg)\n        input_list.pop(i)\n    while len(input_list) > 1:\n        avg = sum(input_list) / len(input_list)\n        new_list.append(avg)\n        input_list = [input_list[0]]\n    new_list.sort(reverse=True)\n    product = math.prod(new_list)\n    return product", "input": "[3, 5, 7, 11, 17]", "output": "27540.0", "imports": ["import math"], "original_snippet": "import math\ndef f(input_list: list):\n    input_list.sort()\n    even_list = [input_list[i] for i in range(len(input_list)) if i % 2 == 0]\n    new_list = [even_list[0], even_list[len(even_list) - 1]]\n    for i in range(1, len(even_list)):\n        avg = (even_list[i] + even_list[i - 1]) / 2\n        new_list.append(avg)\n        input_list.pop(i)\n    while len(input_list) > 1:\n        avg = sum(input_list) / len(input_list)\n        new_list.append(avg)\n        input_list = [input_list[0]]\n    new_list.sort(reverse=True)\n    product = math.prod(new_list)\n    return product", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(output_list: list) -> int:\n    if not hasattr(f, 'length'):\n        f.length = len(output_list)\n    total_sum = 0\n    for i in range(f.length):\n        total_sum += output_list[i]\n    return total_sum", "input": "[1, 8, 3, 4, 6]", "output": "22", "imports": [], "original_snippet": "def f(output_list: list) -> int:\n    if not hasattr(f, 'length'):\n        f.length = len(output_list)\n    total_sum = 0\n    for i in range(f.length):\n        total_sum += output_list[i]\n    return total_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List, Dict, Iterator\ndef f(data: List[Dict[str, Dict[str, int]]]) -> Iterator[int]:\n    for row in data:\n        local_counts = {}\n        for (key, val) in row.items():\n            if len(val) == 3 and 'name' in val and (val['name'] == 'Deep-Thinking'):\n                yield val['value']\n        for (key, val) in row.items():\n            if 'role' in val and val['role'] == 'Solver':\n                all_keys = [k for r in data for (k, v) in r.items() if k == key]\n                if len(all_keys) > 1:\n                    names = [name for r in data for (k, name) in r.items() if k == key]\n                    yield names[1]", "input": "data = [{'a': {'name': 'Dog', 'role': 'Listener'}, 'counts': {'name': 'Deep-Thinking', 'value': 2}}, \n        {'b': {'name': 'Cat', 'role': 'Solver'}, 'counts': {'name': 'Deep-Thinking', 'value': 3}}, \n        {'c': {'name': 'Bird', 'role': 'Listener'}, 'counts': {'name': 'Deeper-Then-Deep-Thinking', 'value': 4}}]", "output": "", "imports": ["from typing import List, Dict, Iterator"], "original_snippet": "from typing import List, Dict, Iterator\ndef f(data: List[Dict[str, Dict[str, int]]]) -> Iterator[int]:\n    for row in data:\n        local_counts = {}\n        for (key, val) in row.items():\n            if len(val) == 3 and 'name' in val and (val['name'] == 'Deep-Thinking'):\n                yield val['value']\n        for (key, val) in row.items():\n            if 'role' in val and val['role'] == 'Solver':\n                all_keys = [k for r in data for (k, v) in r.items() if k == key]\n                if len(all_keys) > 1:\n                    names = [name for r in data for (k, name) in r.items() if k == key]\n                    yield names[1]", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(x):\n    result = ''\n    for i in range(len(x)):\n        palindrome = x[i] + result + x[i][::-1]\n        result = palindrome\n    return result", "input": "['abc', 'def', 'ghi']", "output": "'ghidefabccbafedihg'", "imports": [], "original_snippet": "def f(x):\n    result = ''\n    for i in range(len(x)):\n        palindrome = x[i] + result + x[i][::-1]\n        result = palindrome\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(input_string):\n    if len(input_string) == 0:\n        return ''\n    a_to_f = ['a', 'b', 'c', 'd', 'e', 'f']\n    transformed_characters = [''] * len(input_string)\n    for (i, curr_char) in enumerate(input_string):\n        if i % 3 == 0:\n            transformed_characters[i] = 'a'\n        elif i % 3 == 1:\n            transformed_characters[i] = 'b'\n        elif i % 3 == 2:\n            transformed_characters[i] = 'c'\n    return ''.join(transformed_characters) + input_string[::-1]", "input": "'abcabcdefgabcdefg'", "output": "'abcabcabcabcabcabgfedcbagfedcbacba'", "imports": [], "original_snippet": "def f(input_string):\n    if len(input_string) == 0:\n        return ''\n    a_to_f = ['a', 'b', 'c', 'd', 'e', 'f']\n    transformed_characters = [''] * len(input_string)\n    for (i, curr_char) in enumerate(input_string):\n        if i % 3 == 0:\n            transformed_characters[i] = 'a'\n        elif i % 3 == 1:\n            transformed_characters[i] = 'b'\n        elif i % 3 == 2:\n            transformed_characters[i] = 'c'\n    return ''.join(transformed_characters) + input_string[::-1]", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(n: int) -> int:\n    if n == 1:\n        return 1\n    elif n == 4:\n        return 3\n    elif n == 7:\n        return 6\n    elif n == 10:\n        return 7\n    elif n == 13:\n        return 10", "input": "12", "output": "None", "imports": [], "original_snippet": "def f(n: int) -> int:\n    if n == 1:\n        return 1\n    elif n == 4:\n        return 3\n    elif n == 7:\n        return 6\n    elif n == 10:\n        return 7\n    elif n == 13:\n        return 10", "composite_functions": [], "_input_type": "int", "_output_type": "NoneType"}
{"snippet": "def f(mystring: str) -> int:\n    finallist = []\n    stringtemp = str(mystring)\n    stringlist = stringtemp.split()\n    for word in stringlist:\n        if word.isdigit():\n            if ''.join(word) in ['12', '31', '365']:\n                finallist += [abs(int(word))]\n            else:\n                finallist += [int(word)]\n        else:\n            finallist += ['abc']\n    return len(finallist)", "input": "\"Jan 1 2022\"", "output": "3", "imports": [], "original_snippet": "def f(mystring: str) -> int:\n    finallist = []\n    stringtemp = str(mystring)\n    stringlist = stringtemp.split()\n    for word in stringlist:\n        if word.isdigit():\n            if ''.join(word) in ['12', '31', '365']:\n                finallist += [abs(int(word))]\n            else:\n                finallist += [int(word)]\n        else:\n            finallist += ['abc']\n    return len(finallist)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(m: str) -> str:\n    middle_char = m[len(m) // 2]\n    middle_index = len(m) // 2\n    tuple_m_minus_theres = tuple((ord(char) for char in m))\n    message_minus_theres_converted = chr(tuple_m_minus_theres[middle_index]) + chr(middle_index)\n    final_composition_mess_x = middle_char + message_minus_theres_converted\n    final_string_x = final_composition_mess_x[::-1] + 'x'\n    return final_string_x", "input": "'Hello world'", "output": "'\\x05  x'", "imports": [], "original_snippet": "def f(m: str) -> str:\n    middle_char = m[len(m) // 2]\n    middle_index = len(m) // 2\n    tuple_m_minus_theres = tuple((ord(char) for char in m))\n    message_minus_theres_converted = chr(tuple_m_minus_theres[middle_index]) + chr(middle_index)\n    final_composition_mess_x = middle_char + message_minus_theres_converted\n    final_string_x = final_composition_mess_x[::-1] + 'x'\n    return final_string_x", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(nums: list, target: int) -> list:\n    dp = [[False for _ in range(target + 1)] for _ in range(len(nums) + 1)]\n    for i in range(len(nums) + 1):\n        dp[i][0] = True\n    for i in range(1, len(nums) + 1):\n        for j in range(1, target + 1):\n            if nums[i - 1] > j:\n                dp[i][j] = dp[i - 1][j]\n            else:\n                dp[i][j] = dp[i - 1][j] or dp[i - 1][j - nums[i - 1]]\n    result = []\n    (i, j) = (len(nums), target)\n    while i > 0 and j > 0:\n        if dp[i][j] == dp[i - 1][j]:\n            i -= 1\n        else:\n            result.append(nums[i - 1])\n            j -= nums[i - 1]\n            i -= 1\n    result.reverse()\n    return result", "input": "[4, 11, 6, 3, 5, 8], 20", "output": "[11, 6, 3]", "imports": [], "original_snippet": "def f(nums: list, target: int) -> list:\n    dp = [[False for _ in range(target + 1)] for _ in range(len(nums) + 1)]\n    for i in range(len(nums) + 1):\n        dp[i][0] = True\n    for i in range(1, len(nums) + 1):\n        for j in range(1, target + 1):\n            if nums[i - 1] > j:\n                dp[i][j] = dp[i - 1][j]\n            else:\n                dp[i][j] = dp[i - 1][j] or dp[i - 1][j - nums[i - 1]]\n    result = []\n    (i, j) = (len(nums), target)\n    while i > 0 and j > 0:\n        if dp[i][j] == dp[i - 1][j]:\n            i -= 1\n        else:\n            result.append(nums[i - 1])\n            j -= nums[i - 1]\n            i -= 1\n    result.reverse()\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(matrix):\n    visited = [[False] * len(matrix[0]) for _ in range(len(matrix))]\n    start_row = None\n    start_col = None\n    end_row = None\n    end_col = None\n    for i in range(len(matrix)):\n        for j in range(len(matrix[0])):\n            if matrix[i][j] == 'X':\n                start_row = i\n                start_col = j\n                break\n        if start_row is not None:\n            break\n    for i in range(len(matrix) - 1, -1, -1):\n        for j in range(len(matrix[0]) - 1, -1, -1):\n            if matrix[i][j] == 'X':\n                end_row = i\n                end_col = j\n                break\n        if end_row is not None:\n            break\n    total_area = len(matrix) * len(matrix[0])\n    area = 0\n    (dy, dx) = ([1, -1, 0, 0], [0, 0, 1, -1])\n    visited[start_row][start_col] = True\n    stack = [(start_row, start_col)]\n    while stack:\n        (y, x) = stack.pop()\n        area += 1\n        for (dy_, dx_) in zip(dy, dx):\n            (ny, nx) = (y + dy_, x + dx_)\n            if 0 <= ny < len(matrix) and 0 <= nx < len(matrix[0]) and (matrix[ny][nx] == 'X') and (not visited[ny][nx]):\n                stack.append((ny, nx))\n                visited[ny][nx] = True\n    return total_area - area", "input": "[\n    ['X', 'X', 'X', 'X'],\n    ['X', 'X', 'X', 'X'],\n    ['X', 'X', 'X', 'X'],\n    ['X', 'X', 'X', 'X']\n]", "output": "0", "imports": [], "original_snippet": "def f(matrix):\n    visited = [[False] * len(matrix[0]) for _ in range(len(matrix))]\n    start_row = None\n    start_col = None\n    end_row = None\n    end_col = None\n    for i in range(len(matrix)):\n        for j in range(len(matrix[0])):\n            if matrix[i][j] == 'X':\n                start_row = i\n                start_col = j\n                break\n        if start_row is not None:\n            break\n    for i in range(len(matrix) - 1, -1, -1):\n        for j in range(len(matrix[0]) - 1, -1, -1):\n            if matrix[i][j] == 'X':\n                end_row = i\n                end_col = j\n                break\n        if end_row is not None:\n            break\n    total_area = len(matrix) * len(matrix[0])\n    area = 0\n    (dy, dx) = ([1, -1, 0, 0], [0, 0, 1, -1])\n    visited[start_row][start_col] = True\n    stack = [(start_row, start_col)]\n    while stack:\n        (y, x) = stack.pop()\n        area += 1\n        for (dy_, dx_) in zip(dy, dx):\n            (ny, nx) = (y + dy_, x + dx_)\n            if 0 <= ny < len(matrix) and 0 <= nx < len(matrix[0]) and (matrix[ny][nx] == 'X') and (not visited[ny][nx]):\n                stack.append((ny, nx))\n                visited[ny][nx] = True\n    return total_area - area", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(strings):\n    strings.reverse()\n    ascii_values = [ord(c) for s in strings for c in s]\n    products = {}\n    for (i, value) in enumerate(ascii_values):\n        if value in products:\n            products[value] *= i + 1\n        else:\n            products[value] = i + 1\n    return dict(sorted(products.items(), key=lambda item: item[0]))", "input": "['Code', 'Snippets', 'Have', '5', 'Input', 'Points', '109630']", "output": "{48: 12, 49: 1, 51: 5, 53: 18, 54: 4, 57: 3, 67: 31, 72: 19, 73: 13, 80: 7, 83: 23, 97: 20, 100: 33, 101: 20944, 105: 225, 110: 3360, 111: 256, 112: 10530, 115: 360, 116: 5423, 117: 16, 118: 21}", "imports": [], "original_snippet": "def f(strings):\n    strings.reverse()\n    ascii_values = [ord(c) for s in strings for c in s]\n    products = {}\n    for (i, value) in enumerate(ascii_values):\n        if value in products:\n            products[value] *= i + 1\n        else:\n            products[value] = i + 1\n    return dict(sorted(products.items(), key=lambda item: item[0]))", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "import timeit\ndef f(s: str):\n    word = 'pith'\n    for start in range(len(word)):\n        for end in range(len(word)):\n            sub = word[start:end + 1]\n            if sub == sub[::-1] and s.count(sub) < 1:\n                return 'does not end with dummy pith'\n    return 'not at pith'", "input": "'addhamama'", "output": "'does not end with dummy pith'", "imports": ["import timeit"], "original_snippet": "import timeit\ndef f(s: str):\n    word = 'pith'\n    for start in range(len(word)):\n        for end in range(len(word)):\n            sub = word[start:end + 1]\n            if sub == sub[::-1] and s.count(sub) < 1:\n                return 'does not end with dummy pith'\n    return 'not at pith'", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(n: int) -> int:\n    result = 0\n    for i in range(1, n + 1):\n        result += i * i * (i - 1) % 256\n    return result ** 3", "input": "10", "output": "609800192", "imports": [], "original_snippet": "def f(n: int) -> int:\n    result = 0\n    for i in range(1, n + 1):\n        result += i * i * (i - 1) % 256\n    return result ** 3", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "import string\ndef f(words: list) -> dict:\n    frequency = {}\n    for word in words:\n        word = word.strip(string.punctuation)\n        word = word.lower()\n        if word not in frequency:\n            frequency[word] = 1\n        else:\n            frequency[word] += 1\n    return frequency", "input": "['Hello', 'world!', 'Hello,', 'world!', 'Hi', 'world!']", "output": "{'hello': 2, 'world': 3, 'hi': 1}", "imports": ["import string"], "original_snippet": "import string\ndef f(words: list) -> dict:\n    frequency = {}\n    for word in words:\n        word = word.strip(string.punctuation)\n        word = word.lower()\n        if word not in frequency:\n            frequency[word] = 1\n        else:\n            frequency[word] += 1\n    return frequency", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(s):\n    temp_string = []\n    for (i, ch) in enumerate(s):\n        if ch.isdigit():\n            if len(temp_string) > 0 and int(temp_string[-1]) % 2:\n                temp_string.append(ch)\n    return temp_string", "input": "'sg112g4f'", "output": "[]", "imports": [], "original_snippet": "def f(s):\n    temp_string = []\n    for (i, ch) in enumerate(s):\n        if ch.isdigit():\n            if len(temp_string) > 0 and int(temp_string[-1]) % 2:\n                temp_string.append(ch)\n    return temp_string", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(input_string: str) -> str:\n    result = ''\n    odd = True\n    for char in reversed(input_string):\n        if odd:\n            result += char\n        else:\n            result = char + result\n        odd = not odd\n    return result", "input": "input_string = \"GWD\"", "output": "'WDG'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    result = ''\n    odd = True\n    for char in reversed(input_string):\n        if odd:\n            result += char\n        else:\n            result = char + result\n        odd = not odd\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(numbers):\n    sum_of_squares = 0\n    for num in numbers:\n        sum_of_squares += num ** 2\n    return sum_of_squares", "input": "[1, 2, 3, 4, 5]", "output": "55", "imports": [], "original_snippet": "def f(numbers):\n    sum_of_squares = 0\n    for num in numbers:\n        sum_of_squares += num ** 2\n    return sum_of_squares", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers):\n    if not numbers:\n        return {}\n    sorted_numbers = sorted(numbers)\n    unique_sorted_numbers = {}\n    for i in range(len(sorted_numbers)):\n        if sorted_numbers[i] not in unique_sorted_numbers:\n            unique_sorted_numbers[sorted_numbers[i]] = []\n        unique_sorted_numbers[sorted_numbers[i]].append(i)\n    return unique_sorted_numbers", "input": "numbers = [5, 6, 3, 2, 1, 1, 4, 5]", "output": "{1: [0, 1], 2: [2], 3: [3], 4: [4], 5: [5, 6], 6: [7]}", "imports": [], "original_snippet": "def f(numbers):\n    if not numbers:\n        return {}\n    sorted_numbers = sorted(numbers)\n    unique_sorted_numbers = {}\n    for i in range(len(sorted_numbers)):\n        if sorted_numbers[i] not in unique_sorted_numbers:\n            unique_sorted_numbers[sorted_numbers[i]] = []\n        unique_sorted_numbers[sorted_numbers[i]].append(i)\n    return unique_sorted_numbers", "composite_functions": [], "_input_type": "str", "_output_type": "dict"}
{"snippet": "def f(list_of_strings):\n    unique_elements = list(dict.fromkeys(list_of_strings))\n    sub_lists = []\n    for i in range(len(unique_elements)):\n        for j in range(i + 1, len(unique_elements)):\n            sub_lists.append(unique_elements[i:j])\n    for sub_list in sub_lists:\n        sum_sub_list = sum(list(map(int, sub_list)))\n        if sum_sub_list == 5:\n            sub_list_str = ''.join((str(i) for i in sub_list))\n            return sub_list_str\n    return None", "input": "['1', '2', '3', '4', '1', '2', '5', '6', '7']", "output": "'23'", "imports": [], "original_snippet": "def f(list_of_strings):\n    unique_elements = list(dict.fromkeys(list_of_strings))\n    sub_lists = []\n    for i in range(len(unique_elements)):\n        for j in range(i + 1, len(unique_elements)):\n            sub_lists.append(unique_elements[i:j])\n    for sub_list in sub_lists:\n        sum_sub_list = sum(list(map(int, sub_list)))\n        if sum_sub_list == 5:\n            sub_list_str = ''.join((str(i) for i in sub_list))\n            return sub_list_str\n    return None", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(input_str: str, s: str) -> dict:\n    if len(input_str) != len(s):\n        return 'The strings have different lengths'\n    output_dict = {}\n    for i in range(len(input_str)):\n        if input_str[i] == s[i]:\n            output_dict[input_str[i]] = i\n    return output_dict", "input": "\"abcde\", \"edcba\"", "output": "{'c': 2}", "imports": [], "original_snippet": "def f(input_str: str, s: str) -> dict:\n    if len(input_str) != len(s):\n        return 'The strings have different lengths'\n    output_dict = {}\n    for i in range(len(input_str)):\n        if input_str[i] == s[i]:\n            output_dict[input_str[i]] = i\n    return output_dict", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "import sympy\ndef f(s: str) -> int:\n    substr_count = 0\n    for i in range(len(s) + 1):\n        for j in range(i):\n            sub_str = s[j:i]\n            digit_sum = sum((int(ch) for ch in sub_str if ch.isdigit()))\n            if sympy.isprime(digit_sum):\n                substr_count += 1\n    return substr_count", "input": "\"abcabcabc\"", "output": "0", "imports": ["import sympy"], "original_snippet": "import sympy\ndef f(s: str) -> int:\n    substr_count = 0\n    for i in range(len(s) + 1):\n        for j in range(i):\n            sub_str = s[j:i]\n            digit_sum = sum((int(ch) for ch in sub_str if ch.isdigit()))\n            if sympy.isprime(digit_sum):\n                substr_count += 1\n    return substr_count", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_string: str) -> str:\n    transformed_string = []\n    shift_count = 0\n    for (i, char) in enumerate(input_string):\n        shifted_char = chr(ord(char) + i % len(input_string))\n        transformed_string.append(shifted_char)\n    return ''.join(transformed_string)", "input": "'hello world'", "output": "'hfnos%}vzun'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    transformed_string = []\n    shift_count = 0\n    for (i, char) in enumerate(input_string):\n        shifted_char = chr(ord(char) + i % len(input_string))\n        transformed_string.append(shifted_char)\n    return ''.join(transformed_string)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(output_string: str) -> str:\n    alpha = 'abcdefghijklmnopqrstuvwxyz'\n    index = 125\n    result = ''\n    temp_alpha = ''\n    for char in output_string:\n        if char.isalpha():\n            temp_alpha += char\n        else:\n            result += (alpha[index - 1] if index - 1 >= 0 else alpha[25]) * len(temp_alpha)\n            temp_alpha = alpha[index - 1] if index - 1 >= 0 else alpha[25]\n            index += 1\n    return result", "input": "'abcdefghijklmnopqrstuvwxyz'", "output": "''", "imports": [], "original_snippet": "def f(output_string: str) -> str:\n    alpha = 'abcdefghijklmnopqrstuvwxyz'\n    index = 125\n    result = ''\n    temp_alpha = ''\n    for char in output_string:\n        if char.isalpha():\n            temp_alpha += char\n        else:\n            result += (alpha[index - 1] if index - 1 >= 0 else alpha[25]) * len(temp_alpha)\n            temp_alpha = alpha[index - 1] if index - 1 >= 0 else alpha[25]\n            index += 1\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(word):\n    word_length = len(word)\n    first_char = word[0]\n    last_char = word[-1]\n    if word_length == 1 or word_length == 3:\n        return word\n    first_char_count = f'Jen{last_char}'\n    if word_length % 3 == 2 and word[-3] in 'aeiou':\n        return first_char + first_char_count + word[1] + 'aph'\n    if word_length % 3 == 2 and word[-3] not in 'aeiou':\n        return first_char + first_char_count + 'vnz'\n    if word_length % 3 == 1 and word[-2] == word[-3]:\n        return word + 'oph'\n    if word_length % 3 == 1 and word[-2] not in 'aeiou':\n        return word + 'oph'\n    if word_length % 3 == 0:\n        return 'not possible'", "input": "'Jens'", "output": "'Jensoph'", "imports": [], "original_snippet": "def f(word):\n    word_length = len(word)\n    first_char = word[0]\n    last_char = word[-1]\n    if word_length == 1 or word_length == 3:\n        return word\n    first_char_count = f'Jen{last_char}'\n    if word_length % 3 == 2 and word[-3] in 'aeiou':\n        return first_char + first_char_count + word[1] + 'aph'\n    if word_length % 3 == 2 and word[-3] not in 'aeiou':\n        return first_char + first_char_count + 'vnz'\n    if word_length % 3 == 1 and word[-2] == word[-3]:\n        return word + 'oph'\n    if word_length % 3 == 1 and word[-2] not in 'aeiou':\n        return word + 'oph'\n    if word_length % 3 == 0:\n        return 'not possible'", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(lst):\n    even_list = [lst[i] for i in range(len(lst)) if i % 2 == 0 and lst[i] % 2 == 0]\n    new_list = []\n    for i in range(len(even_list)):\n        if i % 2 == 0:\n            new_list.append(even_list[i])\n        else:\n            new_list.append(even_list[i] * even_list[i - 1])\n    result = 1\n    for elem in new_list:\n        result *= elem\n    return result", "input": "[3, 5, 7, 11, 17]", "output": "1", "imports": [], "original_snippet": "def f(lst):\n    even_list = [lst[i] for i in range(len(lst)) if i % 2 == 0 and lst[i] % 2 == 0]\n    new_list = []\n    for i in range(len(even_list)):\n        if i % 2 == 0:\n            new_list.append(even_list[i])\n        else:\n            new_list.append(even_list[i] * even_list[i - 1])\n    result = 1\n    for elem in new_list:\n        result *= elem\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from collections import deque\ndef f(number_string: str) -> str:\n    bin_str = bin(int(number_string, 2))[2:]\n    parent_number_str = str(int(bin_str, 2) * 2 ** len(bin_str))\n    parent_reverse_str = parent_number_str[::-1]\n    child_reverse_str = parent_reverse_str[len(parent_reverse_str) // 2:][::-1]\n    result = parent_reverse_str[:len(parent_reverse_str) // 2].ljust(len(parent_reverse_str), '0') + '1' + child_reverse_str\n    return result", "input": "'110'", "output": "'8014'", "imports": ["from collections import deque"], "original_snippet": "from collections import deque\ndef f(number_string: str) -> str:\n    bin_str = bin(int(number_string, 2))[2:]\n    parent_number_str = str(int(bin_str, 2) * 2 ** len(bin_str))\n    parent_reverse_str = parent_number_str[::-1]\n    child_reverse_str = parent_reverse_str[len(parent_reverse_str) // 2:][::-1]\n    result = parent_reverse_str[:len(parent_reverse_str) // 2].ljust(len(parent_reverse_str), '0') + '1' + child_reverse_str\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(n, fib_dict={}):\n    if n in fib_dict:\n        return fib_dict[n]\n    if n == 0:\n        fib_dict[0] = 0\n    elif n == 1:\n        fib_dict[1] = 1\n    else:\n        fib_dict[n] = f(n - 1, fib_dict) + f(n - 2, fib_dict)\n    return fib_dict[n]", "input": "f(5)", "output": "5", "imports": [], "original_snippet": "def f(n, fib_dict={}):\n    if n in fib_dict:\n        return fib_dict[n]\n    if n == 0:\n        fib_dict[0] = 0\n    elif n == 1:\n        fib_dict[1] = 1\n    else:\n        fib_dict[n] = f(n - 1, fib_dict) + f(n - 2, fib_dict)\n    return fib_dict[n]", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(n: int):\n    return f'sum({n - 2 * n // 2} + {n - n // 4})'", "input": "10", "output": "'sum(0 + 8)'", "imports": [], "original_snippet": "def f(n: int):\n    return f'sum({n - 2 * n // 2} + {n - n // 4})'", "composite_functions": [], "_input_type": "int", "_output_type": "str"}
{"snippet": "def f(numbers: list) -> int:\n    sum_products = 0\n    length = len(numbers)\n    for i in range(1, length - 1):\n        sum_products += numbers[i] * numbers[i + 1]\n    return sum_products", "input": "[1, 2, 3, 4, 5, 6]", "output": "68", "imports": [], "original_snippet": "def f(numbers: list) -> int:\n    sum_products = 0\n    length = len(numbers)\n    for i in range(1, length - 1):\n        sum_products += numbers[i] * numbers[i + 1]\n    return sum_products", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(nums):\n    if not nums:\n        return 0\n    nums.sort()\n    total = nums[0]\n    prev = nums[0]\n    for i in range(1, len(nums)):\n        if nums[i] != prev:\n            total += nums[i]\n            prev = nums[i]\n    return total", "input": "[1, 2, 3, 1, 4, 5, 4, 6]", "output": "21", "imports": [], "original_snippet": "def f(nums):\n    if not nums:\n        return 0\n    nums.sort()\n    total = nums[0]\n    prev = nums[0]\n    for i in range(1, len(nums)):\n        if nums[i] != prev:\n            total += nums[i]\n            prev = nums[i]\n    return total", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(array: list):\n    max_current = array[0]\n    max_global = array[0]\n    for i in range(1, len(array)):\n        max_current = max(array[i], max_current + array[i])\n        if max_current > max_global:\n            max_global = max_current\n    return max_global", "input": "[-2, 1, -3, 4, -1, 2, 1, -5, 4]", "output": "6", "imports": [], "original_snippet": "def f(array: list):\n    max_current = array[0]\n    max_global = array[0]\n    for i in range(1, len(array)):\n        max_current = max(array[i], max_current + array[i])\n        if max_current > max_global:\n            max_global = max_current\n    return max_global", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(number_list: list):\n    return sum(number_list[::-1])", "input": "[2, 5, 8, 10]", "output": "25", "imports": [], "original_snippet": "def f(number_list: list):\n    return sum(number_list[::-1])", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(person: dict, items: list):\n    items_set = {item for item in items}\n    items_set = {items_set: person.items() for items_set in items_set}\n    return items_set", "input": "{\n    \"name\": \"John Doe\",\n    \"age\": 30,\n    \"city\": \"New York\"\n},\n[\"items1\", \"items2\", \"items3\"]", "output": "{'items1': dict_items([('name', 'John Doe'), ('age', 30), ('city', 'New York')]), 'items3': dict_items([('name', 'John Doe'), ('age', 30), ('city', 'New York')]), 'items2': dict_items([('name', 'John Doe'), ('age', 30), ('city', 'New York')])}", "imports": [], "original_snippet": "def f(person: dict, items: list):\n    items_set = {item for item in items}\n    items_set = {items_set: person.items() for items_set in items_set}\n    return items_set", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(the_array: list):\n    first = the_array[0]\n    last = the_array[-1]\n    middle_elements = the_array[1:-1]\n    if len(middle_elements) % 2 == 0:\n        approx = middle_elements[len(middle_elements) // 2 - 1]\n    else:\n        approx = (middle_elements[len(middle_elements) // 2] + middle_elements[len(middle_elements) // 2 + 1]) / 2\n    product = first * last * approx\n    return product", "input": "[3, 5, 7, 11, 17]", "output": "459.0", "imports": [], "original_snippet": "def f(the_array: list):\n    first = the_array[0]\n    last = the_array[-1]\n    middle_elements = the_array[1:-1]\n    if len(middle_elements) % 2 == 0:\n        approx = middle_elements[len(middle_elements) // 2 - 1]\n    else:\n        approx = (middle_elements[len(middle_elements) // 2] + middle_elements[len(middle_elements) // 2 + 1]) / 2\n    product = first * last * approx\n    return product", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(items_dict: dict, string_to_check: str, head_dict: dict=None) -> dict:\n    if not head_dict:\n        head_dict = {string_to_check: 0}\n    for (key, value) in items_dict.items():\n        if key not in head_dict:\n            head_dict[key] = 0\n        if key == string_to_check:\n            head_dict[key] = 1\n    return head_dict", "input": "{'first': 'first', 'second': 'second', 'third': 'third', 'fourth': 'fourth', 'fifth': 'fourth', 'sixth': 'fourth'},\n'remove_this'", "output": "{'remove_this': 0, 'first': 0, 'second': 0, 'third': 0, 'fourth': 0, 'fifth': 0, 'sixth': 0}", "imports": [], "original_snippet": "def f(items_dict: dict, string_to_check: str, head_dict: dict=None) -> dict:\n    if not head_dict:\n        head_dict = {string_to_check: 0}\n    for (key, value) in items_dict.items():\n        if key not in head_dict:\n            head_dict[key] = 0\n        if key == string_to_check:\n            head_dict[key] = 1\n    return head_dict", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(datalist: list):\n    output = []\n    for (i, item) in enumerate(datalist):\n        if i % 2 == 0:\n            sublist = datalist[:i + 1]\n            sublist.reverse()\n            output.extend(sublist)\n        else:\n            output.append(item)\n        datalist = output\n    return output", "input": "[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]", "output": "[0, 1, 1, 0, 3, 3, 0, 1, 1, 0, 5, 0, 3, 3, 0, 1, 1, 0, 7, 1, 1, 0, 3, 3, 0, 1, 1, 0, 9]", "imports": [], "original_snippet": "def f(datalist: list):\n    output = []\n    for (i, item) in enumerate(datalist):\n        if i % 2 == 0:\n            sublist = datalist[:i + 1]\n            sublist.reverse()\n            output.extend(sublist)\n        else:\n            output.append(item)\n        datalist = output\n    return output", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_string: str):\n    my_string = ''\n    for (i, elem) in enumerate(input_string):\n        if i < 2:\n            my_string += elem\n        else:\n            my_string += elem + 'e'\n    my_list = [i for i in my_string]\n    return my_list", "input": "'Hello world'", "output": "['H', 'e', 'l', 'e', 'l', 'e', 'o', 'e', ' ', 'e', 'w', 'e', 'o', 'e', 'r', 'e', 'l', 'e', 'd', 'e']", "imports": [], "original_snippet": "def f(input_string: str):\n    my_string = ''\n    for (i, elem) in enumerate(input_string):\n        if i < 2:\n            my_string += elem\n        else:\n            my_string += elem + 'e'\n    my_list = [i for i in my_string]\n    return my_list", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "from typing import Dict\ndef f(name: str, info: Dict[str, int]):\n    if len(name) != len(info):\n        return 'The strings have different lengths'\n    result = {}\n    for i in range(len(name)):\n        if name[i] == info.get(name[i], None):\n            result[name[i]] = i\n    return result", "input": "'abC', {'a': 1, 'b': 2, 'C': 3}", "output": "{}", "imports": ["from typing import Dict"], "original_snippet": "from typing import Dict\ndef f(name: str, info: Dict[str, int]):\n    if len(name) != len(info):\n        return 'The strings have different lengths'\n    result = {}\n    for i in range(len(name)):\n        if name[i] == info.get(name[i], None):\n            result[name[i]] = i\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(numbers):\n    if len(numbers) == 0:\n        return 0\n    if len(numbers) == 1:\n        return numbers[0]\n    max_sum = -float('inf')\n    max_length = len(numbers)\n    for end in range(3, max_length):\n        current_sum = numbers[0]\n        for i in range(end):\n            current_sum += numbers[i]\n            max_sum = max(max_sum, current_sum)\n            if end - i > 3:\n                current_sum -= numbers[i - (end - i - 1)]\n    return max_sum", "input": "[1, 2, 3, 4, 5]", "output": "8", "imports": [], "original_snippet": "def f(numbers):\n    if len(numbers) == 0:\n        return 0\n    if len(numbers) == 1:\n        return numbers[0]\n    max_sum = -float('inf')\n    max_length = len(numbers)\n    for end in range(3, max_length):\n        current_sum = numbers[0]\n        for i in range(end):\n            current_sum += numbers[i]\n            max_sum = max(max_sum, current_sum)\n            if end - i > 3:\n                current_sum -= numbers[i - (end - i - 1)]\n    return max_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers):\n    transformed_numbers = []\n    for x in sorted(numbers, reverse=True):\n        transformed_numbers.append(x + (x // 2 if x % 2 != 0 else x))\n    return sum(transformed_numbers)", "input": "[98, 54, 2, 4, 11]", "output": "332", "imports": [], "original_snippet": "def f(numbers):\n    transformed_numbers = []\n    for x in sorted(numbers, reverse=True):\n        transformed_numbers.append(x + (x // 2 if x % 2 != 0 else x))\n    return sum(transformed_numbers)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers):\n    current_sum = 0\n    current_product = 1\n    for num in numbers:\n        num_squared = num ** 2\n        current_sum += num_squared\n        current_product *= num_squared\n        current_product %= current_sum\n    return current_product", "input": "[1, 2, 3, 4]", "output": "0", "imports": [], "original_snippet": "def f(numbers):\n    current_sum = 0\n    current_product = 1\n    for num in numbers:\n        num_squared = num ** 2\n        current_sum += num_squared\n        current_product *= num_squared\n        current_product %= current_sum\n    return current_product", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(s, k):\n    if len(s) < k:\n        return s\n    custom_string = ''\n    while len(custom_string) < k:\n        custom_string += s[len(s) - k] + s[len(s) - (k - 1):]\n        s = s[:-1]\n    return custom_string", "input": "'hello world', 4", "output": "'orld'", "imports": [], "original_snippet": "def f(s, k):\n    if len(s) < k:\n        return s\n    custom_string = ''\n    while len(custom_string) < k:\n        custom_string += s[len(s) - k] + s[len(s) - (k - 1):]\n        s = s[:-1]\n    return custom_string", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "import string\ndef f(text):\n    output = ''\n    for char in text:\n        if char.lower() in string.ascii_lowercase:\n            output += char.upper()\n        elif char in string.whitespace:\n            output += char\n        else:\n            output += char.lower()\n    half_len = len(text) // 2\n    trip_chars = set('trip')\n    for (i, char) in enumerate(text):\n        if half_len <= i <= len(text) - half_len and char.lower() not in trip_chars:\n            output += char\n    word_list = output.split()\n    i = 0\n    for word in word_list:\n        if i % 2 == 0:\n            output = output.replace(word, word.capitalize())\n            i += 1\n        else:\n            i += 1\n    return output[::-1]", "input": "'sunny day'", "output": "' yYAD ynnuS'", "imports": ["import string"], "original_snippet": "import string\ndef f(text):\n    output = ''\n    for char in text:\n        if char.lower() in string.ascii_lowercase:\n            output += char.upper()\n        elif char in string.whitespace:\n            output += char\n        else:\n            output += char.lower()\n    half_len = len(text) // 2\n    trip_chars = set('trip')\n    for (i, char) in enumerate(text):\n        if half_len <= i <= len(text) - half_len and char.lower() not in trip_chars:\n            output += char\n    word_list = output.split()\n    i = 0\n    for word in word_list:\n        if i % 2 == 0:\n            output = output.replace(word, word.capitalize())\n            i += 1\n        else:\n            i += 1\n    return output[::-1]", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from collections import defaultdict\ndef f(nums):\n    if not nums:\n        return 0\n    nums.sort()\n    total = nums[0]\n    prev = nums[0]\n    answers_dict = defaultdict(list)\n    for i in range(1, len(nums)):\n        if abs(nums[i] - prev) == 2 and (not prev == nums[i]):\n            answers_dict[nums[i]].append(prev)\n        prev = nums[i]\n    for num in sorted(answers_dict.keys(), reverse=True):\n        answer_options = [answer for answer in answers_dict[num]]\n        if len(answer_options) >= 1:\n            return num + sum(answer_options)\n    return 0", "input": "[2, 5, 7, 9, 11, 13, 17, 19, 23, 29]", "output": "36", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(nums):\n    if not nums:\n        return 0\n    nums.sort()\n    total = nums[0]\n    prev = nums[0]\n    answers_dict = defaultdict(list)\n    for i in range(1, len(nums)):\n        if abs(nums[i] - prev) == 2 and (not prev == nums[i]):\n            answers_dict[nums[i]].append(prev)\n        prev = nums[i]\n    for num in sorted(answers_dict.keys(), reverse=True):\n        answer_options = [answer for answer in answers_dict[num]]\n        if len(answer_options) >= 1:\n            return num + sum(answer_options)\n    return 0", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(custom_arr):\n    result_arr = []\n    (i, n) = (0, len(custom_arr))\n    while i < n:\n        if i + 1 < n and custom_arr[i] + 1 == custom_arr[i + 1]:\n            i += 1\n            continue\n        result_arr.append(custom_arr[i])\n        result_arr.append(custom_arr[i] + 1)\n        i += 1\n    return result_arr", "input": "[1, 3, 8, 10, 12]", "output": "[1, 2, 3, 4, 8, 9, 10, 11, 12, 13]", "imports": [], "original_snippet": "def f(custom_arr):\n    result_arr = []\n    (i, n) = (0, len(custom_arr))\n    while i < n:\n        if i + 1 < n and custom_arr[i] + 1 == custom_arr[i + 1]:\n            i += 1\n            continue\n        result_arr.append(custom_arr[i])\n        result_arr.append(custom_arr[i] + 1)\n        i += 1\n    return result_arr", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from collections import Counter\ndef f(strings: list):\n    if not strings:\n        return {}\n    combined_chars = ''.join(strings)\n    freq_dict = Counter(combined_chars)\n    return sorted(freq_dict.items(), key=lambda pair: (-pair[1], pair[0]))", "input": "['Hello world.', 'Sample text!']", "output": "[('l', 4), ('e', 3), (' ', 2), ('o', 2), ('t', 2), ('!', 1), ('.', 1), ('H', 1), ('S', 1), ('a', 1), ('d', 1), ('m', 1), ('p', 1), ('r', 1), ('w', 1), ('x', 1)]", "imports": ["from collections import Counter"], "original_snippet": "from collections import Counter\ndef f(strings: list):\n    if not strings:\n        return {}\n    combined_chars = ''.join(strings)\n    freq_dict = Counter(combined_chars)\n    return sorted(freq_dict.items(), key=lambda pair: (-pair[1], pair[0]))", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list: list) -> list:\n    transformed_list = []\n    for number in input_list:\n        if number % 2 == 0 and number % 3 == 0:\n            transformed_list.append(number // 3)\n        elif number % 2 == 0:\n            transformed_list.append(number ** 2)\n        elif number % 3 == 0:\n            transformed_list.append(number - 1)\n        else:\n            transformed_list.append(number + 1)\n    return transformed_list", "input": "[1, 2, 3, 4, 5, 6, 8, 9, 12, 15]", "output": "[2, 4, 2, 16, 6, 2, 64, 8, 4, 14]", "imports": [], "original_snippet": "def f(input_list: list) -> list:\n    transformed_list = []\n    for number in input_list:\n        if number % 2 == 0 and number % 3 == 0:\n            transformed_list.append(number // 3)\n        elif number % 2 == 0:\n            transformed_list.append(number ** 2)\n        elif number % 3 == 0:\n            transformed_list.append(number - 1)\n        else:\n            transformed_list.append(number + 1)\n    return transformed_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(a_list: list):\n    sorted_list = sorted(a_list, reverse=True)\n    fibonacci_list = []\n    sum_list = []\n    for (i, val) in enumerate(sorted_list):\n        fibonacci_list.append(val * pow(1.618033988749895, i) - val)\n        sum_list.append(sum(fibonacci_list))\n    return sum_list[-1]", "input": "[5, 3, 7, 2, 1]", "output": "20.270509831248425", "imports": [], "original_snippet": "def f(a_list: list):\n    sorted_list = sorted(a_list, reverse=True)\n    fibonacci_list = []\n    sum_list = []\n    for (i, val) in enumerate(sorted_list):\n        fibonacci_list.append(val * pow(1.618033988749895, i) - val)\n        sum_list.append(sum(fibonacci_list))\n    return sum_list[-1]", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(n: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    frequency = {}\n    for char in n:\n        if char.lower() in alphabet:\n            frequency[char.lower()] = frequency.get(char.lower(), 0) + 1\n    return [(key, value) for (key, value) in sorted(frequency.items())]", "input": "'Example'", "output": "[('a', 1), ('e', 2), ('l', 1), ('m', 1), ('p', 1), ('x', 1)]", "imports": [], "original_snippet": "def f(n: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    frequency = {}\n    for char in n:\n        if char.lower() in alphabet:\n            frequency[char.lower()] = frequency.get(char.lower(), 0) + 1\n    return [(key, value) for (key, value) in sorted(frequency.items())]", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(words_list) -> str:\n    if len(words_list) == 1:\n        result = words_list[0]\n    elif words_list[0][0] == 'z':\n        words_list.sort(reverse=True)\n        result = f(words_list[1:])\n    else:\n        words_list.sort(key=lambda x: x[0])\n        result = words_list[0]\n    return result", "input": "['abc', 'smol', 'fs', 'milk', 'world', 'we', 'hello', 'moon', 'twigs', 'kitty', 'desk', 'robot', 'ktg', 'gun',]", "output": "'abc'", "imports": [], "original_snippet": "def f(words_list) -> str:\n    if len(words_list) == 1:\n        result = words_list[0]\n    elif words_list[0][0] == 'z':\n        words_list.sort(reverse=True)\n        result = f(words_list[1:])\n    else:\n        words_list.sort(key=lambda x: x[0])\n        result = words_list[0]\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "from collections.abc import Sequence\ndef f(nums: Sequence[int]) -> int:\n    n = len(nums)\n    if n == 1:\n        return nums[0]\n    bestSum = [nums[0]] + [0] * (n - 1)\n    for i in range(n - 1):\n        currMax = nums[i]\n        for j in range(i + 1, n):\n            currMax = max(currMax + nums[j], bestSum[j])\n        bestSum[i] = currMax\n    return max(bestSum)", "input": "[2, 3, 7, 9, 5]", "output": "26", "imports": ["from collections.abc import Sequence"], "original_snippet": "from collections.abc import Sequence\ndef f(nums: Sequence[int]) -> int:\n    n = len(nums)\n    if n == 1:\n        return nums[0]\n    bestSum = [nums[0]] + [0] * (n - 1)\n    for i in range(n - 1):\n        currMax = nums[i]\n        for j in range(i + 1, n):\n            currMax = max(currMax + nums[j], bestSum[j])\n        bestSum[i] = currMax\n    return max(bestSum)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(entry: str) -> str:\n    letters = {}\n    for i in range(len(entry)):\n        if entry[i] in letters:\n            letters[entry[i]] += 1\n        else:\n            letters[entry[i]] = 0\n    max_occurrences = max(letters.values())\n    count = 0\n    result = ''\n    for (key, value) in letters.items():\n        if value == max_occurrences:\n            result += key\n            count += 1\n        elif value > max_occurrences:\n            result += entry[i]\n    if count == 1:\n        result = result[0]\n    else:\n        result = result[0]\n    return result", "input": "'aaabbbccc'", "output": "'a'", "imports": [], "original_snippet": "def f(entry: str) -> str:\n    letters = {}\n    for i in range(len(entry)):\n        if entry[i] in letters:\n            letters[entry[i]] += 1\n        else:\n            letters[entry[i]] = 0\n    max_occurrences = max(letters.values())\n    count = 0\n    result = ''\n    for (key, value) in letters.items():\n        if value == max_occurrences:\n            result += key\n            count += 1\n        elif value > max_occurrences:\n            result += entry[i]\n    if count == 1:\n        result = result[0]\n    else:\n        result = result[0]\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(string_with_positive_integers: str) -> int:\n    total_sum = 0\n    numbers = [int(char) for char in string_with_positive_integers]\n    total_sum = sum(numbers)\n    return total_sum", "input": "'1234'", "output": "10", "imports": [], "original_snippet": "def f(string_with_positive_integers: str) -> int:\n    total_sum = 0\n    numbers = [int(char) for char in string_with_positive_integers]\n    total_sum = sum(numbers)\n    return total_sum", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(nums):\n    if not nums:\n        return 0\n    nums.sort()\n    max_diff = 0\n    for i in range(1, len(nums)):\n        diff = abs(nums[i] - nums[i - 1])\n        if diff > max_diff:\n            max_diff = diff\n    return max_diff", "input": "[5, 3, 1, 6, 8, 9]", "output": "2", "imports": [], "original_snippet": "def f(nums):\n    if not nums:\n        return 0\n    nums.sort()\n    max_diff = 0\n    for i in range(1, len(nums)):\n        diff = abs(nums[i] - nums[i - 1])\n        if diff > max_diff:\n            max_diff = diff\n    return max_diff", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_list):\n    result = 0\n    for (i, s) in enumerate(input_list):\n        if len(s) % 2 == 0:\n            for j in range(len(s) // 2):\n                result += ord(s[j]) - ord(s[-1 - j])\n        else:\n            result += ord(s[int(len(s) / 2)]) - ord(s[int(len(s) / 2) - 1])\n    return result", "input": "['a', 'b', 'c', 'd', 'e']", "output": "0", "imports": [], "original_snippet": "def f(input_list):\n    result = 0\n    for (i, s) in enumerate(input_list):\n        if len(s) % 2 == 0:\n            for j in range(len(s) // 2):\n                result += ord(s[j]) - ord(s[-1 - j])\n        else:\n            result += ord(s[int(len(s) / 2)]) - ord(s[int(len(s) / 2) - 1])\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_string: str):\n    vowels = 'aeiouAEIOU'\n    count = sum((char in vowels for char in input_string))\n    return count * 2 + len(input_string)", "input": "'eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee'", "output": "99", "imports": [], "original_snippet": "def f(input_string: str):\n    vowels = 'aeiouAEIOU'\n    count = sum((char in vowels for char in input_string))\n    return count * 2 + len(input_string)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(dna, ref_strand):\n    (a, t) = ('A', 'T')\n    (c, g) = ('C', 'G')\n    transorn = {a: t, t: a, c: g, g: c}\n    transformed_strand = dna.replace('A', '')\n    transformed_strand = transformed_strand.replace('T', '')\n    transformed_strand = transformed_strand.replace('G', '')\n    transformed_strand = transformed_strand.replace('C', '')\n    print('AAAAA' * 100000)\n    print(dna)\n    print(transformed_strand)", "input": "'AAAAA', 'CGCGG'", "output": "None", "imports": [], "original_snippet": "def f(dna, ref_strand):\n    (a, t) = ('A', 'T')\n    (c, g) = ('C', 'G')\n    transorn = {a: t, t: a, c: g, g: c}\n    transformed_strand = dna.replace('A', '')\n    transformed_strand = transformed_strand.replace('T', '')\n    transformed_strand = transformed_strand.replace('G', '')\n    transformed_strand = transformed_strand.replace('C', '')\n    print('AAAAA' * 100000)\n    print(dna)\n    print(transformed_strand)", "composite_functions": [], "_input_type": "tuple", "_output_type": "NoneType"}
{"snippet": "def f(some_input: str) -> str:\n    new_string = ''\n    shift_sum = sum((ord(char) for char in some_input))\n    shifted_chars = ''\n    for (i, char) in enumerate(some_input):\n        shifted_char = chr(ord(char) + shift_sum)\n        shifted_chars += shifted_char + str(i)\n    new_string = str(shifted_chars)\n    return new_string", "input": "'Hello, World!'", "output": "'\u04b10\u04ce1\u04d52\u04d53\u04d84\u04955\u04896\u04c07\u04d88\u04db9\u04d510\u04cd11\u048a12'", "imports": [], "original_snippet": "def f(some_input: str) -> str:\n    new_string = ''\n    shift_sum = sum((ord(char) for char in some_input))\n    shifted_chars = ''\n    for (i, char) in enumerate(some_input):\n        shifted_char = chr(ord(char) + shift_sum)\n        shifted_chars += shifted_char + str(i)\n    new_string = str(shifted_chars)\n    return new_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from typing import Dict, List\ndef f(count_dict: Dict[str, int]) -> List[str]:\n    result = []\n    for (key, value) in count_dict.items():\n        while value > 3:\n            result.append(key)\n            value -= 3\n        if value > 0:\n            result.append(key * (value + 3))\n    return result", "input": "{'Cat': 3, 'Dog': 4, 'Bird': 2, 'Fish': 1}", "output": "['CatCatCatCatCatCat', 'Dog', 'DogDogDogDog', 'BirdBirdBirdBirdBird', 'FishFishFishFish']", "imports": ["from typing import Dict, List"], "original_snippet": "from typing import Dict, List\ndef f(count_dict: Dict[str, int]) -> List[str]:\n    result = []\n    for (key, value) in count_dict.items():\n        while value > 3:\n            result.append(key)\n            value -= 3\n        if value > 0:\n            result.append(key * (value + 3))\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "list"}
{"snippet": "def f(n):\n    total = 0\n    lst = [i for i in range(1, n + 1)]\n    for x in lst:\n        total += x\n    return total", "input": "10", "output": "55", "imports": [], "original_snippet": "def f(n):\n    total = 0\n    lst = [i for i in range(1, n + 1)]\n    for x in lst:\n        total += x\n    return total", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(input_string: str):\n    if not input_string:\n        return ''\n    stateDict = {'state': 'start', 'rightendIndex': 0}\n    result_index = dict()\n    for (index, char) in enumerate(input_string):\n        if stateDict['state'] == 'start':\n            if char.islower():\n                stateDict['state'] = 'alphabets'\n                result_index[index] = 0\n            else:\n                stateDict['state'] = 'numbers'\n                result_index[index] = 1\n        elif stateDict['state'] == 'alphabets':\n            if char.isnumeric():\n                stateDict['state'] = 'reject'\n            else:\n                stateDict['state'] = ''\n                result_index[index] = 0\n        elif stateDict['state'] == 'numbers':\n            if char.islower():\n                stateDict['state'] = 'reject'\n            else:\n                stateDict['state'] = ''\n                result_index[index] = 1\n    newCharList = list()\n    for (key, value) in result_index.items():\n        if value == 0:\n            newCharList.append(input_string[key])\n        else:\n            newCharList.append(input_string[key + 1])\n    newString = ''.join(newCharList)\n    return newString[::-1]", "input": "\"S0p5GEt\"", "output": "'p0'", "imports": [], "original_snippet": "def f(input_string: str):\n    if not input_string:\n        return ''\n    stateDict = {'state': 'start', 'rightendIndex': 0}\n    result_index = dict()\n    for (index, char) in enumerate(input_string):\n        if stateDict['state'] == 'start':\n            if char.islower():\n                stateDict['state'] = 'alphabets'\n                result_index[index] = 0\n            else:\n                stateDict['state'] = 'numbers'\n                result_index[index] = 1\n        elif stateDict['state'] == 'alphabets':\n            if char.isnumeric():\n                stateDict['state'] = 'reject'\n            else:\n                stateDict['state'] = ''\n                result_index[index] = 0\n        elif stateDict['state'] == 'numbers':\n            if char.islower():\n                stateDict['state'] = 'reject'\n            else:\n                stateDict['state'] = ''\n                result_index[index] = 1\n    newCharList = list()\n    for (key, value) in result_index.items():\n        if value == 0:\n            newCharList.append(input_string[key])\n        else:\n            newCharList.append(input_string[key + 1])\n    newString = ''.join(newCharList)\n    return newString[::-1]", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(d, s):\n    if not s:\n        return d\n    unique_s = dict.fromkeys(s)\n    if 'key' in d:\n        del d['key']\n        d['value'] = s[::-1]\n        return unique_s\n    elif 'value' in d:\n        rev_s = list(reversed(s))\n        indices = [i for (i, char) in enumerate(d['value']) if char == rev_s[0]]\n        d['value'] = ''.join(d['value']) + ''.join(d['value'][indices[0]:]) + ''.join(d['value'])[:indices[0]]\n        return unique_s\n    d['key'] = len(s)\n    d['value'] = s\n    return unique_s", "input": "{'key': 1, 'value': 'aaa'}, 'test'", "output": "{'t': None, 'e': None, 's': None}", "imports": [], "original_snippet": "def f(d, s):\n    if not s:\n        return d\n    unique_s = dict.fromkeys(s)\n    if 'key' in d:\n        del d['key']\n        d['value'] = s[::-1]\n        return unique_s\n    elif 'value' in d:\n        rev_s = list(reversed(s))\n        indices = [i for (i, char) in enumerate(d['value']) if char == rev_s[0]]\n        d['value'] = ''.join(d['value']) + ''.join(d['value'][indices[0]:]) + ''.join(d['value'])[:indices[0]]\n        return unique_s\n    d['key'] = len(s)\n    d['value'] = s\n    return unique_s", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(n: int, m: int) -> dict:\n    result = {}\n    for i in range(1, n + 1):\n        number = i\n        digit_sum = 0\n        while number > 0:\n            digit = number % 10\n            digit_sum += digit ** m\n            number //= 10\n        result[i] = digit_sum\n    return result", "input": "n = 10, m = 2", "output": "{1: 1, 2: 4, 3: 9, 4: 16, 5: 25, 6: 36, 7: 49, 8: 64, 9: 81, 10: 1}", "imports": [], "original_snippet": "def f(n: int, m: int) -> dict:\n    result = {}\n    for i in range(1, n + 1):\n        number = i\n        digit_sum = 0\n        while number > 0:\n            digit = number % 10\n            digit_sum += digit ** m\n            number //= 10\n        result[i] = digit_sum\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "dict"}
{"snippet": "def f(strings: list) -> dict:\n    items = ''.join(strings)\n    items = set(items)\n    freq_dict = {i: 0 for i in items}\n    for i in items:\n        freq_dict[i] += strings.count(i)\n    sorted_freq_dict = sorted(freq_dict.items(), key=lambda x: (-x[1], x[0]))\n    return dict(sorted_freq_dict)", "input": "['Hello world.', 'Sample text!']", "output": "{' ': 0, '!': 0, '.': 0, 'H': 0, 'S': 0, 'a': 0, 'd': 0, 'e': 0, 'l': 0, 'm': 0, 'o': 0, 'p': 0, 'r': 0, 't': 0, 'w': 0, 'x': 0}", "imports": [], "original_snippet": "def f(strings: list) -> dict:\n    items = ''.join(strings)\n    items = set(items)\n    freq_dict = {i: 0 for i in items}\n    for i in items:\n        freq_dict[i] += strings.count(i)\n    sorted_freq_dict = sorted(freq_dict.items(), key=lambda x: (-x[1], x[0]))\n    return dict(sorted_freq_dict)", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(arr: list) -> list:\n    n = len(arr)\n    fib_sequence = [0, 1]\n    for i in range(2, n):\n        if i % 2 == 0:\n            fib_sequence.append(arr[i] * fib_sequence[i - 1])\n        else:\n            fib_sequence.append(arr[i] + fib_sequence[i - 1])\n    return fib_sequence", "input": "arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "[0, 1, 3, 7, 35, 41, 287, 295, 2655, 2665]", "imports": [], "original_snippet": "def f(arr: list) -> list:\n    n = len(arr)\n    fib_sequence = [0, 1]\n    for i in range(2, n):\n        if i % 2 == 0:\n            fib_sequence.append(arr[i] * fib_sequence[i - 1])\n        else:\n            fib_sequence.append(arr[i] + fib_sequence[i - 1])\n    return fib_sequence", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(nums, iterations):\n    result = [0] * len(nums)\n    for i in range(iterations):\n        new_list = []\n        for (idx, val) in enumerate(nums):\n            result[idx] = val + 1 if idx == val % (i + 1) else 0\n            new_list.append(result[idx])\n        nums = new_list\n    return result", "input": "[2, 3, 1, 4, 5], 2", "output": "[0, 0, 0, 0, 0]", "imports": [], "original_snippet": "def f(nums, iterations):\n    result = [0] * len(nums)\n    for i in range(iterations):\n        new_list = []\n        for (idx, val) in enumerate(nums):\n            result[idx] = val + 1 if idx == val % (i + 1) else 0\n            new_list.append(result[idx])\n        nums = new_list\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(lst):\n    length_of_lst = len(lst)\n    if length_of_lst % 2 == 0:\n        half_lst_before_adding_last_two_items = lst[:length_of_lst // 2 - 1]\n        last_two_items = lst[length_of_lst - 2:length_of_lst]\n        times_elements_are_multiplied = lst[length_of_lst - 2] * lst[length_of_lst - 3:length_of_lst - 1]\n        total_elements_are_added = half_lst_before_adding_last_two_items + times_elements_are_multiplied + last_two_items\n        return total_elements_are_added\n    else:\n        half_lst_before_adding_last_two_items = lst[:length_of_lst // 2]\n        last_two_items = lst[length_of_lst - 2:length_of_lst]\n        times_elements_are_multiplied = lst[length_of_lst - 2] * lst[length_of_lst - 3:length_of_lst]\n        total_elements_are_added = half_lst_before_adding_last_two_items + times_elements_are_multiplied + last_two_items\n        return total_elements_are_added", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "[1, 2, 3, 4, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 8, 9, 9, 10]", "imports": [], "original_snippet": "def f(lst):\n    length_of_lst = len(lst)\n    if length_of_lst % 2 == 0:\n        half_lst_before_adding_last_two_items = lst[:length_of_lst // 2 - 1]\n        last_two_items = lst[length_of_lst - 2:length_of_lst]\n        times_elements_are_multiplied = lst[length_of_lst - 2] * lst[length_of_lst - 3:length_of_lst - 1]\n        total_elements_are_added = half_lst_before_adding_last_two_items + times_elements_are_multiplied + last_two_items\n        return total_elements_are_added\n    else:\n        half_lst_before_adding_last_two_items = lst[:length_of_lst // 2]\n        last_two_items = lst[length_of_lst - 2:length_of_lst]\n        times_elements_are_multiplied = lst[length_of_lst - 2] * lst[length_of_lst - 3:length_of_lst]\n        total_elements_are_added = half_lst_before_adding_last_two_items + times_elements_are_multiplied + last_two_items\n        return total_elements_are_added", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list: list) -> int:\n    max_diff = 0\n    for i in range(len(input_list) - 1):\n        for j in range(i + 1, len(input_list)):\n            diff = abs(input_list[i] - input_list[j])\n            if diff > max_diff:\n                max_diff = diff\n    return max_diff", "input": "[1, 5, 3, 1, 13, 8, 15]", "output": "14", "imports": [], "original_snippet": "def f(input_list: list) -> int:\n    max_diff = 0\n    for i in range(len(input_list) - 1):\n        for j in range(i + 1, len(input_list)):\n            diff = abs(input_list[i] - input_list[j])\n            if diff > max_diff:\n                max_diff = diff\n    return max_diff", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "import math\ndef f(numbers):\n    from functools import reduce\n    product = reduce(lambda x, y: x * math.factorial(y), numbers)\n    return product", "input": "[2, 3, 4]", "output": "288", "imports": ["import math", "from functools import reduce"], "original_snippet": "import math\ndef f(numbers):\n    from functools import reduce\n    product = reduce(lambda x, y: x * math.factorial(y), numbers)\n    return product", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(numbers: List[float]) -> List[float]:\n    result = []\n    for number in numbers:\n        result.append(number ** 2 - number)\n    return result", "input": "[1.0, 2.0, 3.0, 4.0]", "output": "[0.0, 2.0, 6.0, 12.0]", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(numbers: List[float]) -> List[float]:\n    result = []\n    for number in numbers:\n        result.append(number ** 2 - number)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import Dict\ndef f(numbers: list) -> Dict[int, int]:\n    frequency_dict = {}\n    for num in numbers:\n        if num in frequency_dict:\n            frequency_dict[num] += 1\n        else:\n            frequency_dict[num] = 1\n    return frequency_dict", "input": "[1, 2, 3, 3, 2, 4, 3, 4, 4, 4]", "output": "{1: 1, 2: 2, 3: 3, 4: 4}", "imports": ["from typing import Dict"], "original_snippet": "from typing import Dict\ndef f(numbers: list) -> Dict[int, int]:\n    frequency_dict = {}\n    for num in numbers:\n        if num in frequency_dict:\n            frequency_dict[num] += 1\n        else:\n            frequency_dict[num] = 1\n    return frequency_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(numbers: list) -> int:\n    sieve = [True] * (max(numbers) + 1)\n    for i in range(2, int(len(sieve) ** 0.5) + 1):\n        if not sieve[i]:\n            continue\n        for j in range(i * i, len(sieve), i):\n            sieve[j] = False\n    primes = [i for i in range(2, len(sieve)) if sieve[i]]\n    prime_count = 0\n    for number in numbers:\n        if number in primes:\n            prime_count += 1\n    return prime_count", "input": "numbers = [11, 2, 12, 13, 14, 15, 17, 18, 19, 20]", "output": "5", "imports": [], "original_snippet": "def f(numbers: list) -> int:\n    sieve = [True] * (max(numbers) + 1)\n    for i in range(2, int(len(sieve) ** 0.5) + 1):\n        if not sieve[i]:\n            continue\n        for j in range(i * i, len(sieve), i):\n            sieve[j] = False\n    primes = [i for i in range(2, len(sieve)) if sieve[i]]\n    prime_count = 0\n    for number in numbers:\n        if number in primes:\n            prime_count += 1\n    return prime_count", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(s: str) -> str:\n    return ''.join(sorted(s))", "input": "\"Hello!!! World!!\"", "output": "' !!!!!HWdellloor'", "imports": [], "original_snippet": "def f(s: str) -> str:\n    return ''.join(sorted(s))", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_string: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    reposition_scheme = {'a': 'a', 'e': 'e', 'i': 'i', 'o': 'o', 'u': 'u', 'A': 'A', 'E': 'E', 'I': 'I', 'O': 'O', 'U': 'U'}\n    vowels = []\n    new_string = ''\n    for c in input_string:\n        if c.isalpha():\n            char_class = 'vowel' if c in reposition_scheme else 'consonant'\n            if char_class == 'vowel':\n                vowels.append(c)\n                new_string += reposition_scheme[c]\n            else:\n                new_string += c\n        else:\n            new_string += c\n    for (i, c) in enumerate(vowels):\n        new_string = new_string[:i] + c + new_string[i + 1:]\n    return new_string", "input": "'Hello, World!'", "output": "'eoolo, World!'", "imports": [], "original_snippet": "def f(input_string: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    reposition_scheme = {'a': 'a', 'e': 'e', 'i': 'i', 'o': 'o', 'u': 'u', 'A': 'A', 'E': 'E', 'I': 'I', 'O': 'O', 'U': 'U'}\n    vowels = []\n    new_string = ''\n    for c in input_string:\n        if c.isalpha():\n            char_class = 'vowel' if c in reposition_scheme else 'consonant'\n            if char_class == 'vowel':\n                vowels.append(c)\n                new_string += reposition_scheme[c]\n            else:\n                new_string += c\n        else:\n            new_string += c\n    for (i, c) in enumerate(vowels):\n        new_string = new_string[:i] + c + new_string[i + 1:]\n    return new_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(numbers: list) -> int:\n    product = 1\n    for number in numbers:\n        if number % 2 == 0:\n            product *= number\n    return product", "input": "[1, 2, 3, 4, 5]", "output": "8", "imports": [], "original_snippet": "def f(numbers: list) -> int:\n    product = 1\n    for number in numbers:\n        if number % 2 == 0:\n            product *= number\n    return product", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List, Dict\ndef f(s: str) -> int:\n    new_string = ''\n    for (i, ch) in enumerate(s):\n        new_string += s[:i]\n        if s[i] == 'a':\n            return i\n    return -1", "input": "'abcba'", "output": "0", "imports": ["from typing import List, Dict"], "original_snippet": "from typing import List, Dict\ndef f(s: str) -> int:\n    new_string = ''\n    for (i, ch) in enumerate(s):\n        new_string += s[:i]\n        if s[i] == 'a':\n            return i\n    return -1", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(lst):\n    for i in range(len(lst)):\n        if lst[i] < 1 or lst[i] >= len(lst) or lst[lst[i] - 1] != 0:\n            return i + 1", "input": "[1, 2, 3]", "output": "1", "imports": [], "original_snippet": "def f(lst):\n    for i in range(len(lst)):\n        if lst[i] < 1 or lst[i] >= len(lst) or lst[lst[i] - 1] != 0:\n            return i + 1", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_string: str, rotations=0) -> str:\n    rotated_string = input_string\n    rotation_count = 0\n    while True:\n        first_digit = rotated_string.find('0123456789')\n        if first_digit >= 0 and first_digit + 3 < len(rotated_string):\n            rotated_string = rotated_string[:first_digit + 1] + rotated_string[first_digit + 1:20] + rotated_string[:first_digit + 1]\n            rotation_count += 1\n        else:\n            break\n        if not rotated_string:\n            break\n    return f'The input string has been rotated {rotation_count} times.'", "input": "'123456789', 0", "output": "'The input string has been rotated 0 times.'", "imports": [], "original_snippet": "def f(input_string: str, rotations=0) -> str:\n    rotated_string = input_string\n    rotation_count = 0\n    while True:\n        first_digit = rotated_string.find('0123456789')\n        if first_digit >= 0 and first_digit + 3 < len(rotated_string):\n            rotated_string = rotated_string[:first_digit + 1] + rotated_string[first_digit + 1:20] + rotated_string[:first_digit + 1]\n            rotation_count += 1\n        else:\n            break\n        if not rotated_string:\n            break\n    return f'The input string has been rotated {rotation_count} times.'", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(nums: list):\n    if not nums:\n        return 0\n    nums.sort()\n    diff_max = float('inf')\n    diff_min = 0\n    for i in range(1, len(nums)):\n        diff = abs(nums[i] - nums[i - 1])\n        if diff > diff_max:\n            diff_max = diff\n        elif diff_min == 0 or diff < diff_min:\n            diff_min = diff\n    return f'The highest difference between two consecutive numbers in the list is {diff_max} and the lowest is {diff_min}.'", "input": "[10, 8, 12, 5, 15, 20, 1, 7]", "output": "'The highest difference between two consecutive numbers in the list is inf and the lowest is 1.'", "imports": [], "original_snippet": "def f(nums: list):\n    if not nums:\n        return 0\n    nums.sort()\n    diff_max = float('inf')\n    diff_min = 0\n    for i in range(1, len(nums)):\n        diff = abs(nums[i] - nums[i - 1])\n        if diff > diff_max:\n            diff_max = diff\n        elif diff_min == 0 or diff < diff_min:\n            diff_min = diff\n    return f'The highest difference between two consecutive numbers in the list is {diff_max} and the lowest is {diff_min}.'", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(numbers):\n    pairs = set()\n    pairs_count = {}\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            if numbers[i] + numbers[j] not in pairs:\n                pairs.add(numbers[i] + numbers[j])\n                if numbers[i] + numbers[j] in pairs_count:\n                    pairs_count[numbers[i] + numbers[j]] += 1\n                else:\n                    pairs_count[numbers[i] + numbers[j]] = 1\n    output = []\n    for (pair, count) in pairs_count.items():\n        if count > 0:\n            output.append(f'{pair} - {count}')\n    return output", "input": "numbers = [3, 5, 2, 7, 6, 1]", "output": "['8 - 1', '5 - 1', '10 - 1', '9 - 1', '4 - 1', '7 - 1', '12 - 1', '11 - 1', '6 - 1', '3 - 1', '13 - 1']", "imports": [], "original_snippet": "def f(numbers):\n    pairs = set()\n    pairs_count = {}\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            if numbers[i] + numbers[j] not in pairs:\n                pairs.add(numbers[i] + numbers[j])\n                if numbers[i] + numbers[j] in pairs_count:\n                    pairs_count[numbers[i] + numbers[j]] += 1\n                else:\n                    pairs_count[numbers[i] + numbers[j]] = 1\n    output = []\n    for (pair, count) in pairs_count.items():\n        if count > 0:\n            output.append(f'{pair} - {count}')\n    return output", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(numbers):\n    if not numbers or len(numbers) < 3:\n        raise ValueError('Input list must contain at least three unique positive integers')\n    largest = numbers[0]\n    second_largest = float('-inf')\n    for num in numbers:\n        if num > largest:\n            (second_largest, largest) = (largest, num)\n        elif second_largest < num < largest:\n            second_largest = num\n    if second_largest == float('-inf'):\n        raise ValueError('Input list must contain at least two unique positive integers to form a pair')\n    return second_largest", "input": "[100, 200, 150, 300, 50, 350]", "output": "300", "imports": [], "original_snippet": "def f(numbers):\n    if not numbers or len(numbers) < 3:\n        raise ValueError('Input list must contain at least three unique positive integers')\n    largest = numbers[0]\n    second_largest = float('-inf')\n    for num in numbers:\n        if num > largest:\n            (second_largest, largest) = (largest, num)\n        elif second_largest < num < largest:\n            second_largest = num\n    if second_largest == float('-inf'):\n        raise ValueError('Input list must contain at least two unique positive integers to form a pair')\n    return second_largest", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(data_list: list[int]):\n    result = []\n    for (index, item) in enumerate(data_list):\n        if item % 2 == 0:\n            result.append(2 * item)\n        else:\n            power_of_two = 1\n            while power_of_two <= abs(item):\n                if item >= 0 and power_of_two <= 1:\n                    break\n                elif item < 0 and power_of_two >= -1:\n                    break\n                power_of_two *= 2\n            result.append(-1 ** (item // abs(item)) * abs(item) ** (power_of_two // 2))\n    return result[::-1]", "input": "[0, 1, 2, 3, 4, 5]", "output": "[-1, 8, -1, 4, -1, 0]", "imports": [], "original_snippet": "def f(data_list: list[int]):\n    result = []\n    for (index, item) in enumerate(data_list):\n        if item % 2 == 0:\n            result.append(2 * item)\n        else:\n            power_of_two = 1\n            while power_of_two <= abs(item):\n                if item >= 0 and power_of_two <= 1:\n                    break\n                elif item < 0 and power_of_two >= -1:\n                    break\n                power_of_two *= 2\n            result.append(-1 ** (item // abs(item)) * abs(item) ** (power_of_two // 2))\n    return result[::-1]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(n: int) -> list:\n    stack = [0]\n    for i in range(1, n + 1):\n        if i % 2 == 0:\n            stack.append(stack.pop())\n            stack.append(i)\n        else:\n            stack.append(stack.pop())\n    return stack", "input": "10", "output": "[0, 2, 4, 6, 8, 10]", "imports": [], "original_snippet": "def f(n: int) -> list:\n    stack = [0]\n    for i in range(1, n + 1):\n        if i % 2 == 0:\n            stack.append(stack.pop())\n            stack.append(i)\n        else:\n            stack.append(stack.pop())\n    return stack", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(input_string: str):\n    if not input_string:\n        return ''\n    stateDict = {'state': 'start', 'rightendIndex': 0}\n    result_index = dict()\n    for (index, char) in enumerate(input_string):\n        if stateDict['state'] == 'start':\n            if char.islower():\n                stateDict['state'] = 'alphabets'\n                result_index[index] = 0\n            elif char.isupper():\n                stateDict['state'] = 'numbers'\n                result_index[index] = 1\n            elif char.isdigit():\n                stateDict['state'] = 'numbers'\n                result_index[index] = 2\n        elif stateDict['state'] == 'alphabets':\n            stateDict['rightendIndex'] = index\n            if char.islower():\n                stateDict['state'] = 'start'\n            else:\n                stateDict['state'] = 'reject'\n        elif stateDict['state'] == 'numbers':\n            stateDict['rightendIndex'] = index\n            if char.islower():\n                stateDict['state'] = 'reject'\n            else:\n                stateDict['state'] = 'end'\n    state_reverse_characters = []\n    for i in range(stateDict['rightendIndex'] + 1):\n        char = input_string[i]\n        if stateDict['state'] == 'start':\n            state_reverse_characters.append(char)\n        elif stateDict['state'] == 'alphabets':\n            state_reverse_characters.insert(0, char)\n        elif stateDict['state'] == 'numbers':\n            state_reverse_characters.insert(0, char)\n    result = ''\n    for i in range(stateDict['rightendIndex'] + 1):\n        char = input_string[i]\n        if stateDict['state'] == 'start':\n            result += char\n        elif stateDict['state'] == 'alphabets':\n            result += char\n        elif stateDict['state'] == 'numbers':\n            result += char\n    for char in state_reverse_characters:\n        result += char\n    return result[::-1]", "input": "\"753md5\"", "output": "''", "imports": [], "original_snippet": "def f(input_string: str):\n    if not input_string:\n        return ''\n    stateDict = {'state': 'start', 'rightendIndex': 0}\n    result_index = dict()\n    for (index, char) in enumerate(input_string):\n        if stateDict['state'] == 'start':\n            if char.islower():\n                stateDict['state'] = 'alphabets'\n                result_index[index] = 0\n            elif char.isupper():\n                stateDict['state'] = 'numbers'\n                result_index[index] = 1\n            elif char.isdigit():\n                stateDict['state'] = 'numbers'\n                result_index[index] = 2\n        elif stateDict['state'] == 'alphabets':\n            stateDict['rightendIndex'] = index\n            if char.islower():\n                stateDict['state'] = 'start'\n            else:\n                stateDict['state'] = 'reject'\n        elif stateDict['state'] == 'numbers':\n            stateDict['rightendIndex'] = index\n            if char.islower():\n                stateDict['state'] = 'reject'\n            else:\n                stateDict['state'] = 'end'\n    state_reverse_characters = []\n    for i in range(stateDict['rightendIndex'] + 1):\n        char = input_string[i]\n        if stateDict['state'] == 'start':\n            state_reverse_characters.append(char)\n        elif stateDict['state'] == 'alphabets':\n            state_reverse_characters.insert(0, char)\n        elif stateDict['state'] == 'numbers':\n            state_reverse_characters.insert(0, char)\n    result = ''\n    for i in range(stateDict['rightendIndex'] + 1):\n        char = input_string[i]\n        if stateDict['state'] == 'start':\n            result += char\n        elif stateDict['state'] == 'alphabets':\n            result += char\n        elif stateDict['state'] == 'numbers':\n            result += char\n    for char in state_reverse_characters:\n        result += char\n    return result[::-1]", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from typing import List\ndef f(nums: List[int]) -> bool:\n    frequency_dict = {}\n    for num in nums:\n        if num in frequency_dict:\n            return False\n        frequency_dict[num] = 1\n    return True", "input": "[1, 2, 3, 4, 5]", "output": "True", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(nums: List[int]) -> bool:\n    frequency_dict = {}\n    for num in nums:\n        if num in frequency_dict:\n            return False\n        frequency_dict[num] = 1\n    return True", "composite_functions": [], "_input_type": "list", "_output_type": "bool"}
{"snippet": "def f(str: str) -> str:\n    result = ''\n    length = len(str)\n    for i in range(length):\n        if str[i] == 'a':\n            result += str[length - 1 - i]\n        elif str[i] == '$':\n            result += str[:i]\n        elif str[i] == '&':\n            result += chr(ord(str[i]) * ord(str[length - 1 - i]))\n        elif str[i] == '#':\n            result += chr(ord(str[i]) + ord(str[length - 1 - i]))\n        elif str[i] == 'c':\n            result += str[length - 1 - i] * length\n        elif str[i] == '*':\n            result += str[:i] * length\n        elif str[i] == '#':\n            result += str[length - 1 - i] - ord(str[i])\n        elif str[i] == '%' or str[i] == '/':\n            result = str[:i] + str[length - 1 - i] + str[i + 1:length]\n        elif str[i] == '^':\n            result += str + str\n        elif str[i] == 'z':\n            result = str[:i] + str[length - 1 - i] * length + str[i + 1:length]\n    return result[::-1]", "input": "'xyz#$%%*^&z'", "output": "'xxxxxxxxxxx&^*%%$#zyx'", "imports": [], "original_snippet": "def f(str: str) -> str:\n    result = ''\n    length = len(str)\n    for i in range(length):\n        if str[i] == 'a':\n            result += str[length - 1 - i]\n        elif str[i] == '$':\n            result += str[:i]\n        elif str[i] == '&':\n            result += chr(ord(str[i]) * ord(str[length - 1 - i]))\n        elif str[i] == '#':\n            result += chr(ord(str[i]) + ord(str[length - 1 - i]))\n        elif str[i] == 'c':\n            result += str[length - 1 - i] * length\n        elif str[i] == '*':\n            result += str[:i] * length\n        elif str[i] == '#':\n            result += str[length - 1 - i] - ord(str[i])\n        elif str[i] == '%' or str[i] == '/':\n            result = str[:i] + str[length - 1 - i] + str[i + 1:length]\n        elif str[i] == '^':\n            result += str + str\n        elif str[i] == 'z':\n            result = str[:i] + str[length - 1 - i] * length + str[i + 1:length]\n    return result[::-1]", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(l: list):\n    total_sum = 0\n    for n in range(len(l) - 1, 0, -1):\n        total_sum += l[n]\n    total_sum *= len(l) - n\n    return total_sum", "input": "[1, 2, 3, 7, 5, 6, 4, 2]", "output": "203", "imports": [], "original_snippet": "def f(l: list):\n    total_sum = 0\n    for n in range(len(l) - 1, 0, -1):\n        total_sum += l[n]\n    total_sum *= len(l) - n\n    return total_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(lst: list) -> list:\n    result = []\n    for i in range(len(lst) - 1):\n        to_add = lst[i] * (i + 1) + lst[i + 1]\n        result.append(to_add)\n    return result", "input": "[1, 2, 3]", "output": "[3, 7]", "imports": [], "original_snippet": "def f(lst: list) -> list:\n    result = []\n    for i in range(len(lst) - 1):\n        to_add = lst[i] * (i + 1) + lst[i + 1]\n        result.append(to_add)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(numbers):\n    total_squares = 0\n    for num in numbers:\n        square = num ** 2\n        total_squares += square\n    return total_squares", "input": "[1, 2, 3, 4]", "output": "30", "imports": [], "original_snippet": "def f(numbers):\n    total_squares = 0\n    for num in numbers:\n        square = num ** 2\n        total_squares += square\n    return total_squares", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(custom_data: list, input_list: list):\n    binary_slice_mapping = {0: '001', 1: '010', 2: '011', 3: '100', 4: '101', 5: '110', 6: '111'}\n    result = []\n    for i in range(len(input_list)):\n        stack = []\n        for j in range(i, -1, -1):\n            if custom_data[j] and stack == []:\n                break\n            if not custom_data[j] and stack == []:\n                break\n            if custom_data[j]:\n                lookup = binary_slice_mapping[input_list[i] & (1 << j) - 1]\n                stack.append(lookup)\n        if stack:\n            result.append(''.join(stack))\n    return result", "input": "[False, False, False, True, False, True, False],\nrange(6),", "output": "[]", "imports": [], "original_snippet": "def f(custom_data: list, input_list: list):\n    binary_slice_mapping = {0: '001', 1: '010', 2: '011', 3: '100', 4: '101', 5: '110', 6: '111'}\n    result = []\n    for i in range(len(input_list)):\n        stack = []\n        for j in range(i, -1, -1):\n            if custom_data[j] and stack == []:\n                break\n            if not custom_data[j] and stack == []:\n                break\n            if custom_data[j]:\n                lookup = binary_slice_mapping[input_list[i] & (1 << j) - 1]\n                stack.append(lookup)\n        if stack:\n            result.append(''.join(stack))\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(input_string: str) -> list:\n    input_length = len(input_string)\n    substrings = set()\n    for i in range(input_length):\n        for j in range(i + 1, input_length + 1):\n            substring = input_string[i:j]\n            if len(substring) >= 2 and substring not in substrings:\n                substrings.add(substring)\n                yield (substring, i)", "input": "'stand-up-circuit-net'", "output": "", "imports": [], "original_snippet": "def f(input_string: str) -> list:\n    input_length = len(input_string)\n    substrings = set()\n    for i in range(input_length):\n        for j in range(i + 1, input_length + 1):\n            substring = input_string[i:j]\n            if len(substring) >= 2 and substring not in substrings:\n                substrings.add(substring)\n                yield (substring, i)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(matrix: list) -> int:\n    result = 0\n    for row in matrix:\n        for col in range(len(row)):\n            if row[col] != col and any((row[i] != i for i in range(len(row)))):\n                result += 1\n    return result", "input": "[[1, 2, 3, 4, 5], [6, 7, 8, 9, 10], [11, 12, 13, 14, 15], [16, 17, 18, 19, 20], [21, 22, 23, 24, 25]]", "output": "25", "imports": [], "original_snippet": "def f(matrix: list) -> int:\n    result = 0\n    for row in matrix:\n        for col in range(len(row)):\n            if row[col] != col and any((row[i] != i for i in range(len(row)))):\n                result += 1\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import Dict, List\ndef f(input_dict: Dict[str, int]) -> Dict[str, int]:\n    temp_dict = {}\n    for (key, value) in input_dict.items():\n        if value in temp_dict:\n            temp_dict[value] += 1\n        else:\n            temp_dict[value] = 1\n    sorted_count_dict = {k: v for (k, v) in sorted(temp_dict.items(), key=lambda item: item[1], reverse=True)}\n    result = {}\n    for (key, value) in sorted_count_dict.items():\n        if result is None:\n            result = {value: key}\n        else:\n            result[key] = value\n    return result", "input": "{1: 4, 4: 9, 1: 16}", "output": "{16: 1, 9: 1}", "imports": ["from typing import Dict, List"], "original_snippet": "from typing import Dict, List\ndef f(input_dict: Dict[str, int]) -> Dict[str, int]:\n    temp_dict = {}\n    for (key, value) in input_dict.items():\n        if value in temp_dict:\n            temp_dict[value] += 1\n        else:\n            temp_dict[value] = 1\n    sorted_count_dict = {k: v for (k, v) in sorted(temp_dict.items(), key=lambda item: item[1], reverse=True)}\n    result = {}\n    for (key, value) in sorted_count_dict.items():\n        if result is None:\n            result = {value: key}\n        else:\n            result[key] = value\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "from typing import Dict\ndef f(hashtable: Dict[int, int]) -> int:\n    product = 1\n    sorted_hashtable = sorted(list(hashtable.items()), key=lambda item: item[1], reverse=True)\n    for (key, val) in sorted_hashtable:\n        if product == 1:\n            product = val * val\n        else:\n            product *= key * val\n    return product", "input": "{1: 3, 2: 5, 3: 7, 4: 2, 5: 1}", "output": "58800", "imports": ["from typing import Dict"], "original_snippet": "from typing import Dict\ndef f(hashtable: Dict[int, int]) -> int:\n    product = 1\n    sorted_hashtable = sorted(list(hashtable.items()), key=lambda item: item[1], reverse=True)\n    for (key, val) in sorted_hashtable:\n        if product == 1:\n            product = val * val\n        else:\n            product *= key * val\n    return product", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(decoded_vote_results: list) -> list:\n    num_votes = 0\n    result = []\n    for i in range(len(decoded_vote_results)):\n        if i % 5 == 0:\n            num_votes ^= decoded_vote_results[i] << (i + 1) % 5\n        else:\n            num_votes += decoded_vote_results[i]\n        if i % 5 == 4:\n            result.append(num_votes)\n            num_votes = 0\n    return result", "input": "[1, 0, 1, 2, 0, 1, 1, 2, 1, 0, 2, 1, 1, 0, 1, 1, 0, 1, 1, 0, 2, 3]", "output": "[5, 6, 7, 4]", "imports": [], "original_snippet": "def f(decoded_vote_results: list) -> list:\n    num_votes = 0\n    result = []\n    for i in range(len(decoded_vote_results)):\n        if i % 5 == 0:\n            num_votes ^= decoded_vote_results[i] << (i + 1) % 5\n        else:\n            num_votes += decoded_vote_results[i]\n        if i % 5 == 4:\n            result.append(num_votes)\n            num_votes = 0\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "import collections\ndef f(sequence: tuple) -> dict:\n    count_dict = collections.Counter(sequence)\n    return count_dict", "input": "('a', 'b', 'c', 'a', 'b', 'b', 'c')", "output": "Counter({'b': 3, 'a': 2, 'c': 2})", "imports": ["import collections"], "original_snippet": "import collections\ndef f(sequence: tuple) -> dict:\n    count_dict = collections.Counter(sequence)\n    return count_dict", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(input_list):\n    filtered_list = []\n    for num in input_list:\n        num_str = str(num)\n        if '1' in num_str or '7' in num_str:\n            filtered_list.append(num)\n    return filtered_list", "input": "[50, 82, 199, 141, 91, 37, 44]", "output": "[199, 141, 91, 37]", "imports": [], "original_snippet": "def f(input_list):\n    filtered_list = []\n    for num in input_list:\n        num_str = str(num)\n        if '1' in num_str or '7' in num_str:\n            filtered_list.append(num)\n    return filtered_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(num):\n    nums = [1, 5, 3, 1, 13, 8, 15]\n    return abs(nums[0] - num) <= 2", "input": "4.6733108", "output": "False", "imports": [], "original_snippet": "def f(num):\n    nums = [1, 5, 3, 1, 13, 8, 15]\n    return abs(nums[0] - num) <= 2", "composite_functions": [], "_input_type": "float", "_output_type": "bool"}
{"snippet": "import os\nimport sys\ndef f(input_list: list) -> list:\n    output_list = []\n    for (i, value) in enumerate(input_list):\n        if i % 2 == 0:\n            output_list.append(value)\n        elif i % 2 == 1:\n            output_list.append(-value)\n    return output_list", "input": "[1, 2, 3, 4, 5, 6]", "output": "[1, -2, 3, -4, 5, -6]", "imports": ["import os", "import sys"], "original_snippet": "import os\nimport sys\ndef f(input_list: list) -> list:\n    output_list = []\n    for (i, value) in enumerate(input_list):\n        if i % 2 == 0:\n            output_list.append(value)\n        elif i % 2 == 1:\n            output_list.append(-value)\n    return output_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(x):\n    return x[::-1]", "input": "'abcd'", "output": "'dcba'", "imports": [], "original_snippet": "def f(x):\n    return x[::-1]", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import re\ndef f(l):\n    l = [str(x) for x in l]\n    transformed_string = ''\n    for (i, num) in enumerate(l):\n        if i % 2 == 0:\n            transformed_string += num * 2\n        else:\n            transformed_string += num\n    transformed_list = re.findall('\\\\d', transformed_string)\n    transformed_list = [int(x) for x in transformed_list]\n    return sum(transformed_list)", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "71", "imports": ["import re"], "original_snippet": "import re\ndef f(l):\n    l = [str(x) for x in l]\n    transformed_string = ''\n    for (i, num) in enumerate(l):\n        if i % 2 == 0:\n            transformed_string += num * 2\n        else:\n            transformed_string += num\n    transformed_list = re.findall('\\\\d', transformed_string)\n    transformed_list = [int(x) for x in transformed_list]\n    return sum(transformed_list)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers):\n    result = 0\n    for num in numbers:\n        result += num\n    return result", "input": "numbers = [1, 2, 3, 4, 5]", "output": "15", "imports": [], "original_snippet": "def f(numbers):\n    result = 0\n    for num in numbers:\n        result += num\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "from collections import defaultdict\ndef f(numbers: list) -> list:\n    if not hasattr(f, 'temp'):\n        f.temp = []\n    if not hasattr(f, 'frequency'):\n        f.frequency = 0\n    if not hasattr(f, 'dict'):\n        f.dict = defaultdict(int)\n    result = []\n    for num in numbers:\n        if num in f.temp:\n            result.append(num)\n            f.frequency += 1\n        elif f.frequency >= 2:\n            result.append(num)\n            f.frequency -= 1\n        else:\n            f.temp.append(num)\n            f.dict[num] += 1\n            f.frequency += 1\n    if result:\n        return result\n    else:\n        return None", "input": "[1, 2, 2, 3, 3, 3, 4, 4, 4, 4, 5]", "output": "", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(numbers: list) -> list:\n    if not hasattr(f, 'temp'):\n        f.temp = []\n    if not hasattr(f, 'frequency'):\n        f.frequency = 0\n    if not hasattr(f, 'dict'):\n        f.dict = defaultdict(int)\n    result = []\n    for num in numbers:\n        if num in f.temp:\n            result.append(num)\n            f.frequency += 1\n        elif f.frequency >= 2:\n            result.append(num)\n            f.frequency -= 1\n        else:\n            f.temp.append(num)\n            f.dict[num] += 1\n            f.frequency += 1\n    if result:\n        return result\n    else:\n        return None", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "from typing import List\ndef f(numbers: List[int]) -> tuple:\n    n = len(numbers)\n    half = n // 2\n    first_half_sum = sum(numbers[:half])\n    last_half_product = 1\n    for num in numbers[half:]:\n        last_half_product *= num\n    return (first_half_sum, last_half_product)", "input": "numbers = [2, 5, 8, 10]", "output": "(7, 80)", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(numbers: List[int]) -> tuple:\n    n = len(numbers)\n    half = n // 2\n    first_half_sum = sum(numbers[:half])\n    last_half_product = 1\n    for num in numbers[half:]:\n        last_half_product *= num\n    return (first_half_sum, last_half_product)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(d: dict, c: str) -> str:\n    if c not in d:\n        d['condition'] = c\n    return d['condition']", "input": "{'condition': 'other'}, 'start'", "output": "'start'", "imports": [], "original_snippet": "def f(d: dict, c: str) -> str:\n    if c not in d:\n        d['condition'] = c\n    return d['condition']", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "from typing import List, Dict\ndef f(numbers: List[int]) -> int:\n    sum_of_squares = 0\n    for num in numbers:\n        sum_of_squares += num ** 2\n    return sum_of_squares", "input": "[1, 2, 3, 4, 5]", "output": "55", "imports": ["from typing import List, Dict"], "original_snippet": "from typing import List, Dict\ndef f(numbers: List[int]) -> int:\n    sum_of_squares = 0\n    for num in numbers:\n        sum_of_squares += num ** 2\n    return sum_of_squares", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(text):\n    reversed_text = text[::-1]\n    new_text = ''\n    for i in range(len(reversed_text)):\n        new_text += chr(ord(reversed_text[i]) - i)\n    return (reversed_text, new_text)", "input": "\"Python\"", "output": "('nohtyP', 'nnfquK')", "imports": [], "original_snippet": "def f(text):\n    reversed_text = text[::-1]\n    new_text = ''\n    for i in range(len(reversed_text)):\n        new_text += chr(ord(reversed_text[i]) - i)\n    return (reversed_text, new_text)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(numbers):\n    if not numbers:\n        return 0\n    average = sum(numbers) / len(numbers)\n    return round(average, 2)", "input": "[98.5, 95.7, 96.3, 97.4, 96.9, 96.5, 95.0, 94.3, 94.5, 94.8, 93.7, 94.1, 95.4]", "output": "95.62", "imports": [], "original_snippet": "def f(numbers):\n    if not numbers:\n        return 0\n    average = sum(numbers) / len(numbers)\n    return round(average, 2)", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(numbers: list) -> list:\n    result = []\n    for (index, num) in enumerate(numbers):\n        if index % 2 == 0:\n            sum_previous = sum(numbers[:index])\n            result.append(sum_previous)\n        else:\n            result.append(num)\n    for i in range(len(result)):\n        if i % 2 == 0:\n            result[i] = str(result[i])\n        elif isinstance(result[i], str):\n            result[i] = int(result[i])\n    if not result:\n        result = [0]\n    return result", "input": "[1, 2, 3, 4, 5]", "output": "['0', 2, '3', 4, '10']", "imports": [], "original_snippet": "def f(numbers: list) -> list:\n    result = []\n    for (index, num) in enumerate(numbers):\n        if index % 2 == 0:\n            sum_previous = sum(numbers[:index])\n            result.append(sum_previous)\n        else:\n            result.append(num)\n    for i in range(len(result)):\n        if i % 2 == 0:\n            result[i] = str(result[i])\n        elif isinstance(result[i], str):\n            result[i] = int(result[i])\n    if not result:\n        result = [0]\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(nums: list) -> str:\n    if not nums:\n        return 'Empty list provided'\n    unique_indices = set()\n    unique_nums = {}\n    unique_entries = []\n    for (i, num) in enumerate(nums):\n        if i not in unique_indices and num not in unique_nums:\n            unique_indices.add(i)\n            unique_nums[num] = i\n            unique_entries.append(f'{num}: {i}')\n    sorted_entry = sorted(unique_entries, key=lambda x: int(x.split(': ')[0]), reverse=True)\n    return ', '.join([entry for pair in sorted_entry for entry in [pair + ' appears at index ' + pair.split(': ')[1]] if pair.split(': ')[0].isdigit()])", "input": "[1, 2, 1, 2, 3, 3, 4]", "output": "'4: 6 appears at index 6, 3: 4 appears at index 4, 2: 1 appears at index 1, 1: 0 appears at index 0'", "imports": [], "original_snippet": "def f(nums: list) -> str:\n    if not nums:\n        return 'Empty list provided'\n    unique_indices = set()\n    unique_nums = {}\n    unique_entries = []\n    for (i, num) in enumerate(nums):\n        if i not in unique_indices and num not in unique_nums:\n            unique_indices.add(i)\n            unique_nums[num] = i\n            unique_entries.append(f'{num}: {i}')\n    sorted_entry = sorted(unique_entries, key=lambda x: int(x.split(': ')[0]), reverse=True)\n    return ', '.join([entry for pair in sorted_entry for entry in [pair + ' appears at index ' + pair.split(': ')[1]] if pair.split(': ')[0].isdigit()])", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(numbers):\n    total = 0\n    for num in numbers:\n        if num > 0:\n            product = num * abs(num)\n            total += product\n    return total", "input": "[1, -2, 3, 4, -5]", "output": "26", "imports": [], "original_snippet": "def f(numbers):\n    total = 0\n    for num in numbers:\n        if num > 0:\n            product = num * abs(num)\n            total += product\n    return total", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_str):\n    input_str_list = list(input_str)\n    uppercase_count = sum((1 if char.isupper() else 0 for char in input_str))\n    for _ in range(uppercase_count):\n        if input_str[-1].isupper():\n            first_char = input_str_list.pop()\n            input_str_list[0] = first_char\n    reversed_str = ''.join(reversed(input_str_list))\n    initial_uppercase_count = sum((1 if char.isupper() else 0 for char in input_str))\n    return (reversed_str, initial_uppercase_count)", "input": "'egGEFDH'", "output": "('gG', 5)", "imports": [], "original_snippet": "def f(input_str):\n    input_str_list = list(input_str)\n    uppercase_count = sum((1 if char.isupper() else 0 for char in input_str))\n    for _ in range(uppercase_count):\n        if input_str[-1].isupper():\n            first_char = input_str_list.pop()\n            input_str_list[0] = first_char\n    reversed_str = ''.join(reversed(input_str_list))\n    initial_uppercase_count = sum((1 if char.isupper() else 0 for char in input_str))\n    return (reversed_str, initial_uppercase_count)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(lst):\n    lst = [abs(x) for x in reversed(lst)]\n    lst = [sum((abs(x) for x in lst)) ** 2 + x ** 3 for x in lst]\n    return [sum((abs(x) for x in lst))] + [sum((x ** 3 * abs(x) - x ** 2 for x in lst))]", "input": "[1, 2, -3, -4]", "output": "[500, 1223583864]", "imports": [], "original_snippet": "def f(lst):\n    lst = [abs(x) for x in reversed(lst)]\n    lst = [sum((abs(x) for x in lst)) ** 2 + x ** 3 for x in lst]\n    return [sum((abs(x) for x in lst))] + [sum((x ** 3 * abs(x) - x ** 2 for x in lst))]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(numbers):\n    if not numbers:\n        return 0\n    if len(numbers) == 1:\n        return numbers[0]\n    min_time = float('inf')\n    for i in range(len(numbers)):\n        current_num = numbers[i]\n        remaining_nums = numbers[:i] + numbers[i + 1:]\n        total_time = current_num + f(remaining_nums)\n        if total_time < min_time:\n            min_time = total_time\n    return min_time", "input": "numbers = [3, 5, 2, 10, 15]", "output": "35", "imports": [], "original_snippet": "def f(numbers):\n    if not numbers:\n        return 0\n    if len(numbers) == 1:\n        return numbers[0]\n    min_time = float('inf')\n    for i in range(len(numbers)):\n        current_num = numbers[i]\n        remaining_nums = numbers[:i] + numbers[i + 1:]\n        total_time = current_num + f(remaining_nums)\n        if total_time < min_time:\n            min_time = total_time\n    return min_time", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_list):\n    return [elem ** 2 for elem in input_list]", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "[1, 4, 9, 16, 25, 36, 49, 64, 81, 100]", "imports": [], "original_snippet": "def f(input_list):\n    return [elem ** 2 for elem in input_list]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "import string\ndef f(text: str, mapping: dict={'a': 128, 'e': 230, 'i': 345, 'o': 456, 'u': 567}):\n    if not hasattr(f, 'original_text'):\n        f.original_text = text\n    if not hasattr(f, 'mapping_dict'):\n        f.mapping_dict = mapping\n    if len(f.mapping_dict) == 0:\n        return text\n    if not hasattr(f, 'modified_text'):\n        f.modified_text = text\n    state = 'check'\n    for char in f.modified_text:\n        if state == 'check' and char.lower() in f.mapping_dict:\n            state = 'replace'\n            f.original_text = text\n        elif state == 'replace' and f.original_text.count(char) == 1:\n            state = 'replace_letter'\n            f.mapping_dict = {}\n        elif state == 'replace_letter' and char.lower() in string.ascii_lowercase:\n            state = 'check'\n            f.mapping_dict = {}\n        elif state == 'check' and char.isalpha():\n            if state == 'check':\n                state = 'increase'\n                f.mapping_dict = {}\n        elif state == 'increase' and char.isdigit():\n            state = 'output'\n            f.modified_text = f.modified_text.split(char)\n            f.original_text = f.original_text.replace(char, '')\n            f.modified_text = f.modified_text[0]\n        elif state == 'output':\n            state = 'break'\n    results = []\n    for (key, value) in f.mapping_dict.items():\n        results.append((key, ''.join([string.ascii_uppercase[ord(char) - ord('a')].upper() if ord(char) in string.ascii_lowercase and chr((ord(char) - ord('a')) * value + ord('A')) else chr((ord(char) - ord('a')) * value + ord('A')) for char in key]), ''.join([string.ascii_lowercase[ord(char) - ord('a')] if ord(char) in string.ascii_lowercase and chr((ord(char) - ord('a')) * value + ord('a')) else chr((ord(char) - ord('a')) * value + ord('a')) for char in key])))\n    if not results:\n        return text\n    else:\n        return {'original_string': f.original_text, 'mapping': f.mapping_dict, 'modified_string': f.modified_text, 'results': results}", "input": "'Python\u00e4chsnowlok'", "output": "'Python\u00e4chsnowlok'", "imports": ["import string"], "original_snippet": "import string\ndef f(text: str, mapping: dict={'a': 128, 'e': 230, 'i': 345, 'o': 456, 'u': 567}):\n    if not hasattr(f, 'original_text'):\n        f.original_text = text\n    if not hasattr(f, 'mapping_dict'):\n        f.mapping_dict = mapping\n    if len(f.mapping_dict) == 0:\n        return text\n    if not hasattr(f, 'modified_text'):\n        f.modified_text = text\n    state = 'check'\n    for char in f.modified_text:\n        if state == 'check' and char.lower() in f.mapping_dict:\n            state = 'replace'\n            f.original_text = text\n        elif state == 'replace' and f.original_text.count(char) == 1:\n            state = 'replace_letter'\n            f.mapping_dict = {}\n        elif state == 'replace_letter' and char.lower() in string.ascii_lowercase:\n            state = 'check'\n            f.mapping_dict = {}\n        elif state == 'check' and char.isalpha():\n            if state == 'check':\n                state = 'increase'\n                f.mapping_dict = {}\n        elif state == 'increase' and char.isdigit():\n            state = 'output'\n            f.modified_text = f.modified_text.split(char)\n            f.original_text = f.original_text.replace(char, '')\n            f.modified_text = f.modified_text[0]\n        elif state == 'output':\n            state = 'break'\n    results = []\n    for (key, value) in f.mapping_dict.items():\n        results.append((key, ''.join([string.ascii_uppercase[ord(char) - ord('a')].upper() if ord(char) in string.ascii_lowercase and chr((ord(char) - ord('a')) * value + ord('A')) else chr((ord(char) - ord('a')) * value + ord('A')) for char in key]), ''.join([string.ascii_lowercase[ord(char) - ord('a')] if ord(char) in string.ascii_lowercase and chr((ord(char) - ord('a')) * value + ord('a')) else chr((ord(char) - ord('a')) * value + ord('a')) for char in key])))\n    if not results:\n        return text\n    else:\n        return {'original_string': f.original_text, 'mapping': f.mapping_dict, 'modified_string': f.modified_text, 'results': results}", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(binary_string: str) -> str:\n    binary_int = int(binary_string, 2)\n    reversed_binary_int = binary_int ^ (1 << 8) - 1\n    reversed_binary_string = bin(reversed_binary_int)[2:].zfill(len(binary_string))\n    return reversed_binary_string", "input": "'10101010'", "output": "'01010101'", "imports": [], "original_snippet": "def f(binary_string: str) -> str:\n    binary_int = int(binary_string, 2)\n    reversed_binary_int = binary_int ^ (1 << 8) - 1\n    reversed_binary_string = bin(reversed_binary_int)[2:].zfill(len(binary_string))\n    return reversed_binary_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import collections\nclass String:\n    pass\ndef f(sequence: tuple):\n    count_dict = collections.Counter({name[::-1]: 0 for name in sequence})\n    return count_dict", "input": "('a', 'b', 'c', 'a', 'b', 'b', 'c')", "output": "Counter({'a': 0, 'b': 0, 'c': 0})", "imports": ["import collections"], "original_snippet": "import collections\nclass String:\n    pass\ndef f(sequence: tuple):\n    count_dict = collections.Counter({name[::-1]: 0 for name in sequence})\n    return count_dict", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "from typing import List, Union, Dict\ndef f(the_list: List[Union[int, str]], info: Dict[str, int]) -> int:\n    total_sum = 0\n    for (n, item) in enumerate(the_list):\n        if isinstance(item, int):\n            if n % 2 == 0:\n                total_sum += item\n            else:\n                total_sum -= item\n        elif isinstance(item, str):\n            if len(item) in info.values():\n                total_sum += len(item)\n            else:\n                total_sum -= ord(item[0])\n    return total_sum", "input": "[10, 'apple', 15, 'banana', 20, 'cherry'], {'apple': 2, 'banana': 3, 'cherry': 4}", "output": "-249", "imports": ["from typing import List, Union, Dict"], "original_snippet": "from typing import List, Union, Dict\ndef f(the_list: List[Union[int, str]], info: Dict[str, int]) -> int:\n    total_sum = 0\n    for (n, item) in enumerate(the_list):\n        if isinstance(item, int):\n            if n % 2 == 0:\n                total_sum += item\n            else:\n                total_sum -= item\n        elif isinstance(item, str):\n            if len(item) in info.values():\n                total_sum += len(item)\n            else:\n                total_sum -= ord(item[0])\n    return total_sum", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(numbers: list) -> int:\n    dp = [[0 for _ in range(len(numbers) + 1)] for _ in range(len(numbers) + 1)]\n    max_so_far = -float('inf')\n    for i in range(len(numbers) + 1):\n        dp[i][1] = numbers[i - 1]\n        max_so_far = max(max_so_far, numbers[i - 1])\n    for i in range(2, len(numbers) + 1):\n        for j in range(2, i + 1):\n            dp[i][j] = max(dp[i - 2][j - 2] + numbers[i - 1] * (i - 1), dp[i - 1][j - 1] + j * numbers[i - 1])\n            max_so_far = max(max_so_far, dp[i][j])\n    return max_so_far", "input": "[-1, 3, -4, 5, -3, -4, 6]", "output": "42", "imports": [], "original_snippet": "def f(numbers: list) -> int:\n    dp = [[0 for _ in range(len(numbers) + 1)] for _ in range(len(numbers) + 1)]\n    max_so_far = -float('inf')\n    for i in range(len(numbers) + 1):\n        dp[i][1] = numbers[i - 1]\n        max_so_far = max(max_so_far, numbers[i - 1])\n    for i in range(2, len(numbers) + 1):\n        for j in range(2, i + 1):\n            dp[i][j] = max(dp[i - 2][j - 2] + numbers[i - 1] * (i - 1), dp[i - 1][j - 1] + j * numbers[i - 1])\n            max_so_far = max(max_so_far, dp[i][j])\n    return max_so_far", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(company_name: str, years: list[int]) -> int:\n    stock_prices = {2015: {'GOOG': [100, 105, 110, 115, 120], 'MSFT': [80, 85, 90, 95, 100]}, 2016: {'GOOG': [120, 125, 130, 135, 140], 'MSFT': [90, 95, 100, 105, 110]}, 2017: {'GOOG': [140, 145, 150, 155, 160], 'MSFT': [110, 115, 120, 125, 130]}, 2018: {'GOOG': [160, 165, 170, 175, 180], 'MSFT': [130, 135, 140, 145, 150]}, 2019: {'GOOG': [180, 185, 190, 195, 200], 'MSFT': [150, 155, 160, 165, 170]}}\n    company_price = stock_prices[years[-1]][company_name]\n    return sum(company_price)", "input": "'GOOG', [2015, 2017]", "output": "750", "imports": [], "original_snippet": "def f(company_name: str, years: list[int]) -> int:\n    stock_prices = {2015: {'GOOG': [100, 105, 110, 115, 120], 'MSFT': [80, 85, 90, 95, 100]}, 2016: {'GOOG': [120, 125, 130, 135, 140], 'MSFT': [90, 95, 100, 105, 110]}, 2017: {'GOOG': [140, 145, 150, 155, 160], 'MSFT': [110, 115, 120, 125, 130]}, 2018: {'GOOG': [160, 165, 170, 175, 180], 'MSFT': [130, 135, 140, 145, 150]}, 2019: {'GOOG': [180, 185, 190, 195, 200], 'MSFT': [150, 155, 160, 165, 170]}}\n    company_price = stock_prices[years[-1]][company_name]\n    return sum(company_price)", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(n: int):\n    queue = [n]\n    while True:\n        last_val = queue[-1]\n        current_step = abs(last_val - queue[0])\n        yield abs(current_step)\n        queue.pop(0)\n        if current_step == 1:\n            queue.append(last_val)\n        else:\n            while True:\n                if queue[0] + current_step == last_val:\n                    queue[0] += current_step\n                    break\n                elif queue[0] - current_step == last_val:\n                    queue[0] -= current_step\n                    break\n                else:\n                    queue.append(queue[0])\n                    break", "input": "10", "output": "", "imports": [], "original_snippet": "def f(n: int):\n    queue = [n]\n    while True:\n        last_val = queue[-1]\n        current_step = abs(last_val - queue[0])\n        yield abs(current_step)\n        queue.pop(0)\n        if current_step == 1:\n            queue.append(last_val)\n        else:\n            while True:\n                if queue[0] + current_step == last_val:\n                    queue[0] += current_step\n                    break\n                elif queue[0] - current_step == last_val:\n                    queue[0] -= current_step\n                    break\n                else:\n                    queue.append(queue[0])\n                    break", "composite_functions": [], "_input_type": "int", "_output_type": "str"}
{"snippet": "def f(output_value: str) -> str:\n    input_string = ''\n    for (i, char) in reversed(list(enumerate(output_value))):\n        if char == '1':\n            input_string += '0'\n        else:\n            input_string += '1'\n    return reversed(input_string)", "input": "'01001'", "output": "", "imports": [], "original_snippet": "def f(output_value: str) -> str:\n    input_string = ''\n    for (i, char) in reversed(list(enumerate(output_value))):\n        if char == '1':\n            input_string += '0'\n        else:\n            input_string += '1'\n    return reversed(input_string)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(output):\n    pass", "input": "[(3, 'root'), (4, 'left'), (5, 'right')]", "output": "None", "imports": [], "original_snippet": "def f(output):\n    pass", "composite_functions": [], "_input_type": "list", "_output_type": "NoneType"}
{"snippet": "import math\ndef f(nums: list[int]) -> int:\n    max_first = max(nums)\n    second_max = max([x for x in nums if x < max_first])\n    return 500 * int(second_max / 500)", "input": "[100, 200, 300, 400, 500]", "output": "0", "imports": ["import math"], "original_snippet": "import math\ndef f(nums: list[int]) -> int:\n    max_first = max(nums)\n    second_max = max([x for x in nums if x < max_first])\n    return 500 * int(second_max / 500)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers: list) -> int:\n    total_sum = 0\n    for num in numbers:\n        total_sum += num\n    return 32 - total_sum", "input": "[1, 2, 3]", "output": "26", "imports": [], "original_snippet": "def f(numbers: list) -> int:\n    total_sum = 0\n    for num in numbers:\n        total_sum += num\n    return 32 - total_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import Dict\ndef f(person_info: Dict[str, int]) -> Dict[str, str]:\n    (name, info) = person_info\n    reversed_keys = {key[::-1].upper(): value for (key, value) in info.items()}\n    swapped_dict = {}\n    for (key, value) in reversed_keys.items():\n        if key in name and value is not None:\n            swapped_dict[key[0]] = value\n    return swapped_dict", "input": "('Prof. Gordon', {'age': 20, 'city': 'New York', 'hobby': 1})", "output": "{}", "imports": ["from typing import Dict"], "original_snippet": "from typing import Dict\ndef f(person_info: Dict[str, int]) -> Dict[str, str]:\n    (name, info) = person_info\n    reversed_keys = {key[::-1].upper(): value for (key, value) in info.items()}\n    swapped_dict = {}\n    for (key, value) in reversed_keys.items():\n        if key in name and value is not None:\n            swapped_dict[key[0]] = value\n    return swapped_dict", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(input_word: str) -> str:\n    translation_table = str.maketrans({chr(i): chr(i + 1) for i in range(ord('a'), ord('z') + 1)})\n    return input_word.translate(translation_table)", "input": "'apple'", "output": "'bqqmf'", "imports": [], "original_snippet": "def f(input_word: str) -> str:\n    translation_table = str.maketrans({chr(i): chr(i + 1) for i in range(ord('a'), ord('z') + 1)})\n    return input_word.translate(translation_table)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(lst):\n    new_list = []\n    for x in lst:\n        if x % 2 == 0:\n            new_list.append('Even')\n        else:\n            new_list.append('Odd')\n    product = 1\n    for elem in new_list:\n        if elem == 'Even':\n            product *= 2\n        else:\n            product *= x\n    return [str(x) for x in range(1, product + 1)]", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35'...', '3199983', '3199984', '3199985', '3199986', '3199987', '3199988', '3199989', '3199990', '3199991', '3199992', '3199993', '3199994', '3199995', '3199996', '3199997', '3199998', '3199999', '3200000']", "imports": [], "original_snippet": "def f(lst):\n    new_list = []\n    for x in lst:\n        if x % 2 == 0:\n            new_list.append('Even')\n        else:\n            new_list.append('Odd')\n    product = 1\n    for elem in new_list:\n        if elem == 'Even':\n            product *= 2\n        else:\n            product *= x\n    return [str(x) for x in range(1, product + 1)]", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(numbers):\n    even_sum = 0", "input": "f([2, 4, 6, 8, 10])", "output": "None", "imports": [], "original_snippet": "def f(numbers):\n    even_sum = 0", "composite_functions": [], "_input_type": "str", "_output_type": "NoneType"}
{"snippet": "def f(nums):\n    if not nums:\n        return 0\n    nums.sort()\n    max_diff = 0\n    for i in range(1, len(nums)):\n        diff = abs(nums[i] - nums[i - 1])\n        if diff > max_diff:\n            max_diff = diff\n    return max_diff", "input": "[1, 3, 2, 7, 5, 6]", "output": "2", "imports": [], "original_snippet": "def f(nums):\n    if not nums:\n        return 0\n    nums.sort()\n    max_diff = 0\n    for i in range(1, len(nums)):\n        diff = abs(nums[i] - nums[i - 1])\n        if diff > max_diff:\n            max_diff = diff\n    return max_diff", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List, Dict\ndef f(lst: List[List[int]]) -> Dict[int, int]:\n    sum_dict = {}\n    for (i, sublist) in enumerate(lst):\n        sum_dict[i] = sum(sublist)\n    return sum_dict", "input": "[[1, 2, 3], [4, 5, 6]]", "output": "{0: 6, 1: 15}", "imports": ["from typing import List, Dict"], "original_snippet": "from typing import List, Dict\ndef f(lst: List[List[int]]) -> Dict[int, int]:\n    sum_dict = {}\n    for (i, sublist) in enumerate(lst):\n        sum_dict[i] = sum(sublist)\n    return sum_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "import math\ndef f(depth):\n    radius = depth\n    area = math.pi * radius ** 2\n    circunference = 2 * math.pi * radius\n    cubed_value = math.pow(area, 3)\n    cube_root = math.pow(area, 0.5)\n    quorients = [circunference / cubed_value, math.log(circunference + 1, math.e), area / cube_root]\n    result = sum((1 / q for q in quorients)) / (len(quorients) - 1)\n    return result", "input": "4", "output": "2526.8424747235717", "imports": ["import math"], "original_snippet": "import math\ndef f(depth):\n    radius = depth\n    area = math.pi * radius ** 2\n    circunference = 2 * math.pi * radius\n    cubed_value = math.pow(area, 3)\n    cube_root = math.pow(area, 0.5)\n    quorients = [circunference / cubed_value, math.log(circunference + 1, math.e), area / cube_root]\n    result = sum((1 / q for q in quorients)) / (len(quorients) - 1)\n    return result", "composite_functions": [], "_input_type": "int", "_output_type": "float"}
{"snippet": "def f(input_string: str) -> list[str]:\n    reverse_string = []\n    for char in input_string[::-1]:\n        reverse_string.append(char)\n    return reverse_string", "input": "'Hello!!! World!!'", "output": "['!', '!', 'd', 'l', 'r', 'o', 'W', ' ', '!', '!', '!', 'o', 'l', 'l', 'e', 'H']", "imports": [], "original_snippet": "def f(input_string: str) -> list[str]:\n    reverse_string = []\n    for char in input_string[::-1]:\n        reverse_string.append(char)\n    return reverse_string", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "import collections\ndef f(names: list, ingredients: list) -> list:\n    ingredient_histogram = collections.Counter(ingredients)\n    name_pairs = list(zip(names, ingredients))\n    valid_pairs = []\n    for (name, ingredient) in name_pairs:\n        ingredient_name = ingredient if isinstance(ingredient, str) else str(ingredient)\n        if ingredient_histogram[ingredient_name] > 0 and ingredient_name in name:\n            valid_pairs.append((name, ingredient))\n            ingredient_histogram[ingredient_name] -= 1\n    return valid_pairs", "input": "names = ['John, Mary, Peter', 'Alice, Bob, Charles'],\ningredients = ['egg, sugar, flour', 'milk, butter, sugar', 'sugar, eggs, milk']", "output": "[]", "imports": ["import collections"], "original_snippet": "import collections\ndef f(names: list, ingredients: list) -> list:\n    ingredient_histogram = collections.Counter(ingredients)\n    name_pairs = list(zip(names, ingredients))\n    valid_pairs = []\n    for (name, ingredient) in name_pairs:\n        ingredient_name = ingredient if isinstance(ingredient, str) else str(ingredient)\n        if ingredient_histogram[ingredient_name] > 0 and ingredient_name in name:\n            valid_pairs.append((name, ingredient))\n            ingredient_histogram[ingredient_name] -= 1\n    return valid_pairs", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "from typing import Tuple\ndef f(numbers: list[int]) -> str:\n    max_first = max(numbers)\n    second_max = max([x for x in numbers if x < max_first])\n    even_numbers = [x for x in numbers if x % 2 == 0]\n    even_count = len(even_numbers)\n    return ' '.join((str(x) for x in [max_first, second_max, even_count]))", "input": "[1, 2, 3, 4, 5]", "output": "'5 4 2'", "imports": ["from typing import Tuple"], "original_snippet": "from typing import Tuple\ndef f(numbers: list[int]) -> str:\n    max_first = max(numbers)\n    second_max = max([x for x in numbers if x < max_first])\n    even_numbers = [x for x in numbers if x % 2 == 0]\n    even_count = len(even_numbers)\n    return ' '.join((str(x) for x in [max_first, second_max, even_count]))", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(numbers: list) -> list:\n    result = []\n    for num in numbers:\n        abs_num = abs(num)\n        if abs_num % 3 == 0:\n            reversed_word = ''.join((str(abs_num) for _ in range(abs_num)))\n            result.append(reversed_word[::-1])\n    return result", "input": "[-2, 0, 3, 4, -7, -8, -4]", "output": "['', '333']", "imports": [], "original_snippet": "def f(numbers: list) -> list:\n    result = []\n    for num in numbers:\n        abs_num = abs(num)\n        if abs_num % 3 == 0:\n            reversed_word = ''.join((str(abs_num) for _ in range(abs_num)))\n            result.append(reversed_word[::-1])\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(value: int):\n    index = 0\n    while value % 10 == value // 10:\n        value //= 10\n        index += 1\n    if len(str(value)) < 6:\n        raise ValueError\n    else:\n        return f'{value}, index: {index}'", "input": "19292229", "output": "'19292229, index: 0'", "imports": [], "original_snippet": "def f(value: int):\n    index = 0\n    while value % 10 == value // 10:\n        value //= 10\n        index += 1\n    if len(str(value)) < 6:\n        raise ValueError\n    else:\n        return f'{value}, index: {index}'", "composite_functions": [], "_input_type": "int", "_output_type": "str"}
{"snippet": "def f(input_string: str) -> str:\n    unique_characters = set()\n    max_character = ''\n    max_length = 0\n    for i in range(len(input_string)):\n        current_character = input_string[i]\n        character_count = 1\n        j = i + 1\n        while j < len(input_string) and input_string[j] == current_character:\n            character_count += 1\n            j += 1\n        if character_count > max_length:\n            max_length = character_count\n            max_character = current_character\n        elif character_count == max_length:\n            if current_character < max_character:\n                max_character = current_character\n    return max_character", "input": "'baabaa'", "output": "'a'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    unique_characters = set()\n    max_character = ''\n    max_length = 0\n    for i in range(len(input_string)):\n        current_character = input_string[i]\n        character_count = 1\n        j = i + 1\n        while j < len(input_string) and input_string[j] == current_character:\n            character_count += 1\n            j += 1\n        if character_count > max_length:\n            max_length = character_count\n            max_character = current_character\n        elif character_count == max_length:\n            if current_character < max_character:\n                max_character = current_character\n    return max_character", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(nums):\n    if not nums or len(nums) < 2:\n        return 0\n    product_sum = 0\n    n = len(nums)\n    for i in range(n):\n        for j in range(i + 1, n):\n            product = nums[i] * nums[j]\n            if product < 0:\n                product_sum += product\n            else:\n                break\n    return product_sum", "input": "[1, 2, 3, 4, 5, 6]", "output": "0", "imports": [], "original_snippet": "def f(nums):\n    if not nums or len(nums) < 2:\n        return 0\n    product_sum = 0\n    n = len(nums)\n    for i in range(n):\n        for j in range(i + 1, n):\n            product = nums[i] * nums[j]\n            if product < 0:\n                product_sum += product\n            else:\n                break\n    return product_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from heapq import heapify, heappush, heappop\nfrom typing import List\ndef f(nums: List[int]) -> List[int]:\n    if not nums:\n        return []\n    nums_copy = [num for num in nums]\n    heapify(nums_copy)\n    unique_nums = []\n    while nums_copy:\n        num = heappop(nums_copy)\n        if num not in unique_nums:\n            unique_nums.append(num)\n    return unique_nums", "input": "[5, 3, 1, 6, 8, 9, 1, 2, 3, 4, 5]", "output": "[1, 2, 3, 4, 5, 6, 8, 9]", "imports": ["from heapq import heapify, heappush, heappop", "from typing import List"], "original_snippet": "from heapq import heapify, heappush, heappop\nfrom typing import List\ndef f(nums: List[int]) -> List[int]:\n    if not nums:\n        return []\n    nums_copy = [num for num in nums]\n    heapify(nums_copy)\n    unique_nums = []\n    while nums_copy:\n        num = heappop(nums_copy)\n        if num not in unique_nums:\n            unique_nums.append(num)\n    return unique_nums", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(string, mapping):\n    length = len(' ' * 1000) - len(''.join((ch for (i, ch) in enumerate(string) if i not in mapping)))\n    output = ''\n    for (i, ch) in enumerate(string):\n        if i not in mapping:\n            output = output + ch\n        else:\n            if len(output) < length // 2:\n                output = output[::-1].encode('utf-8')\n            output = output.decode('utf-8') + ch\n    return (output, len(output))", "input": "'Hello.', {'H': '', 'e': '', 'l': '', 'o': '', '.': ''}", "output": "('Hello.', 6)", "imports": [], "original_snippet": "def f(string, mapping):\n    length = len(' ' * 1000) - len(''.join((ch for (i, ch) in enumerate(string) if i not in mapping)))\n    output = ''\n    for (i, ch) in enumerate(string):\n        if i not in mapping:\n            output = output + ch\n        else:\n            if len(output) < length // 2:\n                output = output[::-1].encode('utf-8')\n            output = output.decode('utf-8') + ch\n    return (output, len(output))", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(stars: str) -> float:\n    stars = stars.replace('*', ',')\n    valid_stars = 0\n    invalid_stars = 0\n    for i in range(1, len(stars)):\n        if stars[i] == ',':\n            continue\n        if stars[i].upper() in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' or stars[i] in 'VMON' or stars[i] == 'ROBINSON':\n            valid_stars += 1\n        else:\n            invalid_stars += 1\n    return (valid_stars - 15.0) / max(len(stars), 10)", "input": "'f*,g#,a!!b, c# d,e,V,ABC,XYZ'", "output": "-0.07142857142857142", "imports": [], "original_snippet": "def f(stars: str) -> float:\n    stars = stars.replace('*', ',')\n    valid_stars = 0\n    invalid_stars = 0\n    for i in range(1, len(stars)):\n        if stars[i] == ',':\n            continue\n        if stars[i].upper() in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' or stars[i] in 'VMON' or stars[i] == 'ROBINSON':\n            valid_stars += 1\n        else:\n            invalid_stars += 1\n    return (valid_stars - 15.0) / max(len(stars), 10)", "composite_functions": [], "_input_type": "str", "_output_type": "float"}
{"snippet": "def f(numbers, dictionary):\n    if len(numbers) == 0:\n        return (0, None)\n    even_numbers = []\n    for num in numbers:\n        if num % 2 == 0:\n            even_numbers.append(num)\n    largest_even_number = max(even_numbers)\n    filtered_dictionary = {k: v for (k, v) in dictionary.items() if v.get('value', 0) == largest_even_number}\n    if len(filtered_dictionary) == 0:\n        return (sum(even_numbers), None)\n    largest_even_value = max(filtered_dictionary.keys(), key=lambda x: filtered_dictionary[x]['value'])\n    return (sum(even_numbers), largest_even_value)", "input": "[2, 4, 6, 8, 10], {'a': {'value': 9, 'name': 'Deep-Thinking', 'role': 'Solver'}, 'b': {'value': 3, 'name': 'Cat', 'role': 'Solver'}, 'c': {'value': 4, 'name': 'Bird', 'role': 'Listener'}}", "output": "(30, None)", "imports": [], "original_snippet": "def f(numbers, dictionary):\n    if len(numbers) == 0:\n        return (0, None)\n    even_numbers = []\n    for num in numbers:\n        if num % 2 == 0:\n            even_numbers.append(num)\n    largest_even_number = max(even_numbers)\n    filtered_dictionary = {k: v for (k, v) in dictionary.items() if v.get('value', 0) == largest_even_number}\n    if len(filtered_dictionary) == 0:\n        return (sum(even_numbers), None)\n    largest_even_value = max(filtered_dictionary.keys(), key=lambda x: filtered_dictionary[x]['value'])\n    return (sum(even_numbers), largest_even_value)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(numbers: list[int]) -> int:\n    n = len(numbers)\n    min_steps = [float('inf')] * n\n    min_steps[0] = 0\n    for i in range(1, n):\n        for j in range(i):\n            if numbers[j] % numbers[i] == 0 or numbers[i] % 2 == 0:\n                min_steps[i] = min(min_steps[i], min_steps[j] + abs(numbers[i] - numbers[j]))\n    return len(min_steps) if min(min_steps) < float('inf') else -1", "input": "[5, 10, 15, 20]", "output": "4", "imports": [], "original_snippet": "def f(numbers: list[int]) -> int:\n    n = len(numbers)\n    min_steps = [float('inf')] * n\n    min_steps[0] = 0\n    for i in range(1, n):\n        for j in range(i):\n            if numbers[j] % numbers[i] == 0 or numbers[i] % 2 == 0:\n                min_steps[i] = min(min_steps[i], min_steps[j] + abs(numbers[i] - numbers[j]))\n    return len(min_steps) if min(min_steps) < float('inf') else -1", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(some_input: str) -> tuple:\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    shift_count = sum((ord(char) for char in some_input))\n    encoded_string = ''\n    for char in some_input:\n        if char in alphabet:\n            index = alphabet.index(char)\n            new_index = (index + int(shift_count) % len(alphabet)) % len(alphabet)\n            encoded_string += alphabet[new_index]\n        else:\n            encoded_string += char\n    return (encoded_string, shift_count)", "input": "'Hello, World!'", "output": "('Hpwwz, Wzcwo!', 1129)", "imports": [], "original_snippet": "def f(some_input: str) -> tuple:\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    shift_count = sum((ord(char) for char in some_input))\n    encoded_string = ''\n    for char in some_input:\n        if char in alphabet:\n            index = alphabet.index(char)\n            new_index = (index + int(shift_count) % len(alphabet)) % len(alphabet)\n            encoded_string += alphabet[new_index]\n        else:\n            encoded_string += char\n    return (encoded_string, shift_count)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "from typing import List, Dict\ndef f(custom_dict: Dict[int, int]) -> List[int]:\n    transformed_numbers = []\n    for (key, value) in sorted(custom_dict.items()):\n        if key % 2 != 0:\n            transformed_numbers.append(key)\n        elif value % 2 == 0:\n            transformed_numbers.append(value)\n    return transformed_numbers", "input": "{1: 3, 4: 2, 5: 7, 8: 5}", "output": "[1, 2, 5]", "imports": ["from typing import List, Dict"], "original_snippet": "from typing import List, Dict\ndef f(custom_dict: Dict[int, int]) -> List[int]:\n    transformed_numbers = []\n    for (key, value) in sorted(custom_dict.items()):\n        if key % 2 != 0:\n            transformed_numbers.append(key)\n        elif value % 2 == 0:\n            transformed_numbers.append(value)\n    return transformed_numbers", "composite_functions": [], "_input_type": "dict", "_output_type": "list"}
{"snippet": "def f(array: list[int]):\n    n = len(array)\n    min_steps = [float('inf')] * n\n    min_steps[0] = 0\n    for i in range(1, n):\n        min_steps[i] = float('inf')\n        if array[i] >= array[i - 1]:\n            min_steps[i] = min(min_steps[i], min_steps[i - 1])\n        else:\n            for j in range(i - 1):\n                if array[j] >= array[i] and array[j] - array[i] <= 1:\n                    min_steps[i] = min(min_steps[i], min_steps[j] + 1)\n    return min_steps[n - 1]", "input": "[1, 2, 3, -4, 5]", "output": "inf", "imports": [], "original_snippet": "def f(array: list[int]):\n    n = len(array)\n    min_steps = [float('inf')] * n\n    min_steps[0] = 0\n    for i in range(1, n):\n        min_steps[i] = float('inf')\n        if array[i] >= array[i - 1]:\n            min_steps[i] = min(min_steps[i], min_steps[i - 1])\n        else:\n            for j in range(i - 1):\n                if array[j] >= array[i] and array[j] - array[i] <= 1:\n                    min_steps[i] = min(min_steps[i], min_steps[j] + 1)\n    return min_steps[n - 1]", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "import math\ndef f(num: int, operators: list[str]) -> int:\n    current_num = num\n    for op in operators:\n        if op == '+':\n            current_num += current_num\n        elif op == '*':\n            current_num *= current_num\n        else:\n            return -1\n    return current_num", "input": "2, ['+', '*', '+']", "output": "32", "imports": ["import math"], "original_snippet": "import math\ndef f(num: int, operators: list[str]) -> int:\n    current_num = num\n    for op in operators:\n        if op == '+':\n            current_num += current_num\n        elif op == '*':\n            current_num *= current_num\n        else:\n            return -1\n    return current_num", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(l: list, r: list) -> list:\n    if not l:\n        return r\n    else:\n        return [x + y for (x, y) in zip(l, r)]", "input": "[1, 2, 3, 4, 5], [10, 20, 30]", "output": "[11, 22, 33]", "imports": [], "original_snippet": "def f(l: list, r: list) -> list:\n    if not l:\n        return r\n    else:\n        return [x + y for (x, y) in zip(l, r)]", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(n: int) -> int:\n    if n == 0:\n        return 1\n    if n < 0:\n        return 0\n    if n in [1, 3, 4]:\n        return 1\n    dp = [0] * (n + 1)\n    dp[0] = 1\n    for i in range(1, n + 1):\n        dp[i] += dp[i - 1]\n        if i >= 1:\n            dp[i] += dp[i - 3]\n        if i >= 3:\n            dp[i] += dp[i - 4]\n    return dp[n]", "input": "5", "output": "6", "imports": [], "original_snippet": "def f(n: int) -> int:\n    if n == 0:\n        return 1\n    if n < 0:\n        return 0\n    if n in [1, 3, 4]:\n        return 1\n    dp = [0] * (n + 1)\n    dp[0] = 1\n    for i in range(1, n + 1):\n        dp[i] += dp[i - 1]\n        if i >= 1:\n            dp[i] += dp[i - 3]\n        if i >= 3:\n            dp[i] += dp[i - 4]\n    return dp[n]", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(numbers: list, signs: list) -> int:\n    result = 0\n    for i in range(len(numbers)):\n        if not numbers[i]:\n            break\n        result += signs[i - 1] * numbers[i]\n    return result", "input": "[1, 2, 3, 0, 5, 6, 7], [1, -1, 1, 1, -1, -1, 1]", "output": "0", "imports": [], "original_snippet": "def f(numbers: list, signs: list) -> int:\n    result = 0\n    for i in range(len(numbers)):\n        if not numbers[i]:\n            break\n        result += signs[i - 1] * numbers[i]\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(input_set: set) -> int:\n    current_input_set = set()\n    value = 0\n    while value != len(input_set):\n        inputs = list(input_set)\n        current_input_set = set()\n        current_input_set.add(inputs[0])\n        for i in range(1, len(inputs)):\n            if inputs[i] - inputs[i - 1] == 1:\n                current_input_set.add(inputs[i])\n        input_set = current_input_set\n        value = len(input_set)\n    for s in current_input_set:\n        print(s)", "input": "{1, 2, 3, 4, 5, 7}", "output": "None", "imports": [], "original_snippet": "def f(input_set: set) -> int:\n    current_input_set = set()\n    value = 0\n    while value != len(input_set):\n        inputs = list(input_set)\n        current_input_set = set()\n        current_input_set.add(inputs[0])\n        for i in range(1, len(inputs)):\n            if inputs[i] - inputs[i - 1] == 1:\n                current_input_set.add(inputs[i])\n        input_set = current_input_set\n        value = len(input_set)\n    for s in current_input_set:\n        print(s)", "composite_functions": [], "_input_type": "set", "_output_type": "NoneType"}
{"snippet": "def f(input_string: str) -> str:\n    reversed_string = input_string[::-1]\n    pieces = [reversed_string[i:i + 4] for i in range(0, len(reversed_string), 4)]\n    reversed_pieces = [piece[::-1] for piece in pieces]\n    result = ''.join(reversed_pieces)\n    return result", "input": "'Hello world!'", "output": "'rld!o woHell'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    reversed_string = input_string[::-1]\n    pieces = [reversed_string[i:i + 4] for i in range(0, len(reversed_string), 4)]\n    reversed_pieces = [piece[::-1] for piece in pieces]\n    result = ''.join(reversed_pieces)\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(n):\n    return sum((pow(i, 2) for i in range(1, n + 1)))", "input": "5", "output": "55", "imports": [], "original_snippet": "def f(n):\n    return sum((pow(i, 2) for i in range(1, n + 1)))", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "import os\ndef f(test_suite_output: str) -> str:\n    result = test_suite_output\n    while result.startswith('0'):\n        result = result[1:]\n    if len(result) >= 5:\n        result = result[1:]\n    result = result.lower()\n    while len(result) > 2 and result[0] == result[1] == result[2]:\n        result = result[1:]\n    if len(result) >= 5 and len(set(result.replace('0', '').replace('1', ''))) >= 5:\n        return result\n    else:\n        return 'Input is not a valid challenge sequence.'", "input": "'1111100000000001111100000011111100100100000011111001000100111111111111111111111001000000'", "output": "'Input is not a valid challenge sequence.'", "imports": ["import os"], "original_snippet": "import os\ndef f(test_suite_output: str) -> str:\n    result = test_suite_output\n    while result.startswith('0'):\n        result = result[1:]\n    if len(result) >= 5:\n        result = result[1:]\n    result = result.lower()\n    while len(result) > 2 and result[0] == result[1] == result[2]:\n        result = result[1:]\n    if len(result) >= 5 and len(set(result.replace('0', '').replace('1', ''))) >= 5:\n        return result\n    else:\n        return 'Input is not a valid challenge sequence.'", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(data: list, weights: list) -> float:\n    total_weighted_sum = 0.0\n    for (i, (value, weight)) in enumerate(zip(data, weights)):\n        contribution = value * weight\n        total_weighted_sum += contribution\n    return total_weighted_sum", "input": "[6, 3, 8, 5], [0.1, 0.2, 0.3, 0.4]", "output": "5.6", "imports": [], "original_snippet": "def f(data: list, weights: list) -> float:\n    total_weighted_sum = 0.0\n    for (i, (value, weight)) in enumerate(zip(data, weights)):\n        contribution = value * weight\n        total_weighted_sum += contribution\n    return total_weighted_sum", "composite_functions": [], "_input_type": "tuple", "_output_type": "float"}
{"snippet": "def f(numbers: list, sum_value: int=1) -> int:\n    if not numbers:\n        raise ValueError('No numbers provided.')\n    else:\n        total_sum = 0\n        for num in numbers:\n            total_sum += num\n        return total_sum * sum_value", "input": "numbers = [1, 2, 3, 4],\nsum_value = 5", "output": "50", "imports": [], "original_snippet": "def f(numbers: list, sum_value: int=1) -> int:\n    if not numbers:\n        raise ValueError('No numbers provided.')\n    else:\n        total_sum = 0\n        for num in numbers:\n            total_sum += num\n        return total_sum * sum_value", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(numbers):\n    for index in range(len(numbers)):\n        if numbers[index] % 3 == 0:\n            numbers[index] = numbers[index] - 2\n    return numbers", "input": "[1, 5, 6, 2]", "output": "[1, 5, 4, 2]", "imports": [], "original_snippet": "def f(numbers):\n    for index in range(len(numbers)):\n        if numbers[index] % 3 == 0:\n            numbers[index] = numbers[index] - 2\n    return numbers", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list: list) -> list:\n    total = 0\n    lst = [i ** 2 for i in input_list]\n    for x in lst:\n        total += int(str(x)[::-1])\n    return [str(total)[::-1]]", "input": "[1, 2, 3, 4]", "output": "['57']", "imports": [], "original_snippet": "def f(input_list: list) -> list:\n    total = 0\n    lst = [i ** 2 for i in input_list]\n    for x in lst:\n        total += int(str(x)[::-1])\n    return [str(total)[::-1]]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
