#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Python code execution utilities for the Absolute Zero project.

This module provides a set of classes and functions for safely executing
Python code in a controlled environment. It includes a generic runtime
environment, specialized runtimes for different use cases, and a Python
executor that can handle code execution with various configurations.

SECURITY NOTE: This module has been enhanced with secure execution capabilities
to prevent code injection attacks and provide proper sandboxing.

Original source:
    https://github.com/QwenLM/QwQ/blob/main/eval/eval/math_opensource_utils/python_executor.py
"""

import copy
import datetime
import io
import logging
import pickle
import time
import subprocess
import tempfile
import os
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)
import ast
import time
import traceback
from concurrent.futures import TimeoutError
from contextlib import redirect_stdout
from functools import partial
from typing import Any, Dict, List, Optional, Tuple, Union

import dateutil.relativedelta
import numpy as np
import regex

# Optional imports with fallbacks
try:
    from pebble import ProcessPool
except ImportError:
    ProcessPool = None
    logger.warning("pebble not available - parallel execution disabled")

try:
    from timeout_decorator import timeout
except ImportError:
    # Simple timeout fallback using threading
    import threading

    def timeout(seconds):
        def decorator(func):
            def wrapper(*args, **kwargs):
                result = [None]
                exception = [None]

                def target():
                    try:
                        result[0] = func(*args, **kwargs)
                    except Exception as e:
                        exception[0] = e

                thread = threading.Thread(target=target)
                thread.daemon = True
                thread.start()
                thread.join(seconds)

                if thread.is_alive():
                    # Thread is still running, timeout occurred
                    raise TimeoutError(f"Function timed out after {seconds} seconds")

                if exception[0]:
                    raise exception[0]

                return result[0]
            return wrapper
        return decorator
    logger.warning("timeout_decorator not available - using threading fallback")

try:
    from tqdm import tqdm
except ImportError:
    # Simple tqdm fallback
    def tqdm(iterable, *_args, **_kwargs):
        return iterable
    logger.warning("tqdm not available - progress bars disabled")

from absolute_zero_reasoner.utils.code_utils.checks import \
    contains_banned_imports
from absolute_zero_reasoner.utils.code_utils.parsers import parse_error
from absolute_zero_reasoner.utils.code_utils.templates import (
    CHECK_DETERMINISM_TEMPLATE, EVAL_INPUT_PREDICTION_TEMPLATE,
    EVAL_K_INPUT_PREDICTION_TEMPLATE, EVAL_K_OUTPUT_PREDICTION_TEMPLATE,
    EVAL_OUTPUT_PREDICTION_TEMPLATE, RUN_CODE_TEMPLATE, VALIDATE_CODE_TEMPLATE)


# Security enhancements
class SecurityError(Exception):
    """Exception raised for security-related issues in code execution."""
    pass


class SecureExecutor(ABC):
    """Abstract base class for secure code execution."""

    @abstractmethod
    def execute_code(self, code: str, inputs: str = "", timeout: int = 10) -> Tuple[str, str]:
        """Execute code securely and return (output, status)."""
        pass


def sanitize_code(code: str) -> str:
    """Sanitize code input to prevent injection attacks."""
    # Remove dangerous imports
    dangerous_imports = [
        'os', 'sys', 'subprocess', 'socket', 'urllib',
        'requests', 'http', 'ftplib', 'smtplib', 'shutil',
        'glob', 'pathlib', 'importlib', '__import__',
        'eval', 'exec', 'compile', 'open'
    ]

    lines = code.split('\n')
    sanitized_lines = []

    for line in lines:
        line_stripped = line.strip()
        if line_stripped.startswith('import ') or line_stripped.startswith('from '):
            for dangerous in dangerous_imports:
                if dangerous in line_stripped:
                    raise SecurityError(f"Dangerous import detected: {line_stripped}")

        # Check for dangerous function calls
        for dangerous in ['eval(', 'exec(', 'compile(', '__import__(']:
            if dangerous in line_stripped:
                raise SecurityError(f"Dangerous function call detected: {line_stripped}")

        sanitized_lines.append(line)

    return '\n'.join(sanitized_lines)


class ContainerizedExecutor(SecureExecutor):
    """Execute code in a containerized environment using Docker."""

    def __init__(self, memory_limit: str = "256m", cpu_limit: str = "1.0", use_docker: bool = True):
        self.memory_limit = memory_limit
        self.cpu_limit = cpu_limit
        self.use_docker = use_docker

        # Check if Docker is available
        if self.use_docker:
            try:
                subprocess.run(['docker', '--version'], capture_output=True, check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.warning("Docker not available, falling back to local execution with restrictions")
                self.use_docker = False

    def execute_code(self, code: str, inputs: str = "", timeout: int = 10) -> Tuple[str, str]:
        """Execute code in Docker container or restricted local environment."""
        if self.use_docker:
            return self._execute_in_docker(code, inputs, timeout)
        else:
            return self._execute_local_restricted(code, inputs, timeout)

    def _execute_in_docker(self, code: str, inputs: str, timeout: int) -> Tuple[str, str]:
        """Execute code in Docker container."""
        try:
            # Create temporary files
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                code_file = f.name

            # Docker command with security restrictions
            cmd = [
                'docker', 'run', '--rm',
                '--memory', self.memory_limit,
                '--cpus', self.cpu_limit,
                '--network', 'none',  # No network access
                '--read-only',        # Read-only filesystem
                '--tmpfs', '/tmp',    # Temporary filesystem
                '-v', f'{code_file}:/code.py:ro',
                'python:3.10-alpine',
                'timeout', str(timeout),
                'python', '/code.py'
            ]

            # Execute with timeout
            result = subprocess.run(
                cmd,
                input=inputs,
                capture_output=True,
                text=True,
                timeout=timeout + 5  # Extra buffer
            )

            os.unlink(code_file)  # Cleanup

            if result.returncode == 0:
                return result.stdout, "success"
            else:
                return result.stderr, "error"

        except subprocess.TimeoutExpired:
            return "", "timeout"
        except Exception as e:
            return str(e), "error"

    def _execute_local_restricted(self, code: str, _inputs: str, timeout_seconds: int) -> Tuple[str, str]:
        """Execute code locally with restrictions (fallback when Docker unavailable)."""
        try:
            # Create a restricted environment
            restricted_globals = {
                '__builtins__': {
                    'print': print,
                    'len': len,
                    'range': range,
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'list': list,
                    'dict': dict,
                    'tuple': tuple,
                    'set': set,
                    'abs': abs,
                    'max': max,
                    'min': min,
                    'sum': sum,
                    'sorted': sorted,
                    'enumerate': enumerate,
                    'zip': zip,
                    'map': map,
                    'filter': filter,
                    'any': any,
                    'all': all,
                }
            }

            # Capture stdout
            old_stdout = io.StringIO()
            with redirect_stdout(old_stdout):
                # Execute with timeout
                def execute_restricted():
                    exec(code, restricted_globals)

                timeout_func = timeout(timeout_seconds)(execute_restricted)
                timeout_func()

            output = old_stdout.getvalue()
            return output, "success"

        except Exception as e:
            return str(e), "error"


class LocalExecutor(SecureExecutor):
    """Local executor with enhanced security checks (legacy fallback)."""

    def __init__(self):
        self.runtime = GenericRuntime()

    def execute_code(self, code: str, _inputs: str = "", timeout_seconds: int = 10) -> Tuple[str, str]:
        """Execute code locally with security checks."""
        try:
            # Sanitize code first
            sanitized_code = sanitize_code(code)

            # Execute with timeout
            old_stdout = io.StringIO()
            with redirect_stdout(old_stdout):
                def execute_safe():
                    self.runtime.exec_code(sanitized_code)

                timeout_func = timeout(timeout_seconds)(execute_safe)
                timeout_func()

            output = old_stdout.getvalue()
            return output, "success"

        except SecurityError as e:
            return str(e), "security_error"
        except Exception as e:
            return str(e), "error"


class GenericRuntime:
    """A generic runtime environment for executing Python code in a controlled manner.
    
    This class provides a sandboxed environment for executing Python code with
    configurable global and local namespaces. It serves as a base class for more
    specialized runtime environments and includes basic security measures.
    
    Class Attributes:
        GLOBAL_DICT: Dictionary containing the default global namespace for the runtime.
        LOCAL_DICT: Optional dictionary for the local namespace (defaults to None).
        HEADERS: List of code strings to be executed upon initialization.
    """

    GLOBAL_DICT = {}
    LOCAL_DICT = None
    HEADERS = []

    def __init__(self):
        """Initialize the GenericRuntime with default global and local namespaces.
        
        This constructor sets up the runtime environment by:
        1. Creating copies of the class-defined GLOBAL_DICT and LOCAL_DICT
        2. Executing any header code specified in the HEADERS class variable
        
        Attributes:
            _global_vars: A copy of the class's GLOBAL_DICT for this instance
            _local_vars: A copy of the class's LOCAL_DICT for this instance,
                       or None if LOCAL_DICT is not defined
        """
        self._global_vars = copy.copy(self.GLOBAL_DICT)
        self._local_vars = copy.copy(self.LOCAL_DICT) if self.LOCAL_DICT else None

        for c in self.HEADERS:
            self.exec_code(c)

    def exec_code(self, code_piece: str) -> None:
        """Execute a piece of Python code in the runtime's global namespace.

        SECURITY WARNING: This method still uses direct exec() for backward compatibility.
        For secure execution, use the SecureExecutor classes instead.

        Args:
            code_piece: A string containing valid Python code to be executed.

        Raises:
            RuntimeError: If the code contains potentially dangerous operations.
            SecurityError: If dangerous code patterns are detected.

        Note:
            This method includes enhanced security checks but still uses exec().
            Consider migrating to ContainerizedExecutor for better security.
        """
        # Enhanced security checks
        try:
            sanitize_code(code_piece)
        except SecurityError as e:
            logger.error(f"Security violation in code execution: {e}")
            raise RuntimeError(f"Security violation: {e}")

        # Original basic check
        if regex.search(r"(\s|^)?input\(", code_piece):
            raise RuntimeError("Direct input() calls are not allowed")

        # Execute with restricted builtins
        restricted_globals = self._global_vars.copy()
        restricted_globals['__builtins__'] = {
            'print': print, 'len': len, 'range': range, 'str': str, 'int': int,
            'float': float, 'bool': bool, 'list': list, 'dict': dict, 'tuple': tuple,
            'set': set, 'abs': abs, 'max': max, 'min': min, 'sum': sum,
            'sorted': sorted, 'enumerate': enumerate, 'zip': zip, 'map': map,
            'filter': filter, 'any': any, 'all': all,
        }

        exec(code_piece, restricted_globals)

        # Update global vars with safe changes
        for key, value in restricted_globals.items():
            if key not in ['__builtins__'] and not key.startswith('__'):
                self._global_vars[key] = value

        # TODO: use: https://github.com/shroominic/codebox-api
        # @high safe exec in sandbox
        # byte_code = compile_restricted(
        #     code_piece,
        #     filename='<inline code>',
        #     mode='exec'
        # )
        # print("global vars:", self._global_vars)
        # _print_ = PrintCollector
        # exec(byte_code, {'__builtins__': utility_builtins}, None)

    def eval_code(self, expr: str) -> Any:
        """Evaluate a Python expression in the runtime's global namespace.

        SECURITY WARNING: This method still uses direct eval() for backward compatibility.
        Enhanced with security checks but consider using SecureExecutor for better security.

        Args:
            expr: A string containing a valid Python expression to be evaluated.

        Returns:
            The result of evaluating the expression in the runtime's global namespace.

        Raises:
            SecurityError: If dangerous code patterns are detected.
            RuntimeError: If security violations are found.
        """
        # Security checks for eval
        try:
            sanitize_code(expr)
        except SecurityError as e:
            logger.error(f"Security violation in expression evaluation: {e}")
            raise RuntimeError(f"Security violation: {e}")

        # Additional checks for eval-specific dangers
        dangerous_patterns = ['__', 'import', 'exec', 'eval', 'compile']
        for pattern in dangerous_patterns:
            if pattern in expr:
                raise RuntimeError(f"Dangerous pattern '{pattern}' detected in expression")

        # Use restricted globals for eval
        restricted_globals = self._global_vars.copy()
        restricted_globals['__builtins__'] = {
            'len': len, 'str': str, 'int': int, 'float': float, 'bool': bool,
            'abs': abs, 'max': max, 'min': min, 'sum': sum,
        }

        return eval(expr, restricted_globals)

    def inject(self, var_dict: Dict[str, Any]) -> None:
        """Inject variables into the runtime's global namespace.
        
        This method updates the runtime's global variables with the provided
        dictionary of variable names and their corresponding values.
        
        Args:
            var_dict: A dictionary where keys are variable names (as strings) and
                    values are the corresponding values to be injected into the
                    global namespace.
                    
        Returns:
            None
            
        Note:
            This method modifies the runtime's global variables in-place.
            Any existing variables with the same names will be overwritten.
        """
        for k, v in var_dict.items():
            self._global_vars[k] = v

    @property
    def answer(self):
        """Get the value of the 'answer' variable from the global namespace.
        
        Returns:
            The value of the 'answer' variable from the global namespace.
            
        Note:
            This property assumes that the 'answer' variable exists in the global
            namespace. If it doesn't exist, this will raise a KeyError.
        """
        return self._global_vars["answer"]


class DateRuntime(GenericRuntime):
    """A runtime environment that provides date and time utilities.
    
    This runtime extends GenericRuntime to provide common date and time related
    functionality in the global namespace. It includes the datetime module and
    relativedelta for date arithmetic.
    
    Attributes:
        GLOBAL_DICT: A dictionary containing the following date/time related objects:
                   - 'datetime': The standard datetime module
                   - 'timedelta': A relativedelta object for date arithmetic
                   - 'relativedelta': An alias for timedelta for compatibility
    """

    GLOBAL_DICT = {
        "datetime": datetime.datetime,
        "timedelta": dateutil.relativedelta.relativedelta,
        "relativedelta": dateutil.relativedelta.relativedelta,
    }


class CustomDict(dict):
    """A custom dictionary implementation that provides a list-based iterator.
    
    This class extends the built-in dict type to ensure that iteration
    always returns a list of keys, which can be useful in certain contexts
    where a list-like iteration behavior is required.
    """

    def __iter__(self):
        """Return an iterator over the dictionary's keys as a list.
        
        Returns:
            iterator: An iterator that yields dictionary keys in list form.
            
        Note:
            This overrides the default dictionary iterator to ensure consistent
            behavior across different Python versions and implementations.
        """
        return list(super().__iter__()).__iter__()


class ColorObjectRuntime(GenericRuntime):
    """A runtime environment that provides a custom dictionary implementation.
    
    This runtime extends GenericRuntime to provide a custom dictionary implementation
    (CustomDict) in the global namespace. It's typically used in scenarios where
    custom dictionary behavior is required for code execution.
    
    Attributes:
        GLOBAL_DICT: A dictionary containing the global variables available in this
                   runtime. It includes a custom dictionary implementation under the
                   key 'dict'.
    """

    GLOBAL_DICT = {"dict": CustomDict}


class PythonExecutor:
    """A class for executing Python code in a controlled environment with various execution modes.
    
    This class provides functionality to execute Python code with different runtime configurations,
    capture outputs, and validate code execution. It supports various execution modes including
    capturing output from stdout, evaluating expressions, or retrieving values of specific symbols.
    
    The executor can be configured with different runtime environments, timeouts, and parallel
    execution settings. It also includes features for AST checking, code validation, and process
    management for parallel execution.
    
    Example:
        # Basic usage with stdout capture
        executor = PythonExecutor(get_answer_from_stdout=True)
        output, error = executor.run_code("print('Hello, world!')", "")
        
        # Using with answer symbol
        executor = PythonExecutor(get_answer_symbol='result')
        output, error = executor.run_code("result = 42", "")
        
        # Using with expression evaluation
        executor = PythonExecutor(get_answer_expr='x * 2')
        output, error = executor.run_code("x = 21", "")
    """

    def __init__(
        self,
        runtime: Optional[Any] = None,
        get_answer_symbol: Optional[str] = None,
        get_answer_expr: Optional[str] = None,
        get_answer_from_stdout: bool = False,
        timeout_length: int = 10,
        ast_check: bool = False,
        max_workers: int = 1,
        use_secure_execution: bool = True,
        executor_config: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Initialize the PythonExecutor with the specified configuration.

        This constructor sets up the Python code execution environment with the provided
        parameters that control how code is executed and how results are captured.

        Args:
            runtime: An optional runtime environment to use for code execution.
                   If not provided, a new GenericRuntime instance will be created.
            get_answer_symbol: The name of the variable that contains the answer
                            in the executed code. If provided, this variable's value
                            will be used as the execution result.
            get_answer_expr: A Python expression to evaluate to get the answer.
                          This provides more flexibility than get_answer_symbol.
            get_answer_from_stdout: If True, capture the standard output of the
                                 executed code as the result.
            timeout_length: Maximum time in seconds to allow for code execution
                         before timing out. Defaults to 10 seconds.
            ast_check: If True, perform AST parsing checks on the code before execution.
            max_workers: Maximum number of worker processes to use for parallel
                       execution. Defaults to 1 (no parallelism).
            use_secure_execution: If True, use secure containerized execution.
            executor_config: Configuration for the secure executor.

        Note:
            At least one of get_answer_symbol, get_answer_expr, or get_answer_from_stdout
            should be specified to properly capture the execution result.
        """
        self.runtime = runtime if runtime else GenericRuntime()
        self.answer_symbol = get_answer_symbol
        self.answer_expr = get_answer_expr
        self.get_answer_from_stdout = get_answer_from_stdout
        self.timeout_length = timeout_length
        self.ast_check = ast_check
        self.max_workers = max_workers
        self._process_pool = None

        # Security enhancements
        self.use_secure_execution = use_secure_execution
        self.executor_config = executor_config or {}

        if self.use_secure_execution:
            self.secure_executor = ContainerizedExecutor(
                memory_limit=self.executor_config.get('memory_limit', '256m'),
                cpu_limit=self.executor_config.get('cpu_limit', '1.0'),
                use_docker=self.executor_config.get('use_docker', True)
            )
            logger.info("PythonExecutor initialized with secure execution enabled")
        else:
            self.secure_executor = LocalExecutor()
            logger.warning("PythonExecutor initialized with legacy execution mode - security risks present")

    def __del__(self):
        """Clean up resources when the PythonExecutor is garbage collected.
        
        This method is called when the PythonExecutor object is being destroyed.
        It ensures that the process pool is properly cleaned up to prevent resource leaks.
        Any exceptions during cleanup are silently ignored to prevent issues during
        garbage collection.
        """
        try:
            self.cleanup()
        except:
            # Ignore any errors during cleanup in __del__
            pass

    def cleanup(self):
        """Explicitly clean up the process pool."""
        if self._process_pool is not None:
            self._process_pool.close()
            self._process_pool.join()
            self._process_pool = None

    def _get_process_pool(self, size_hint):
        """Get or create a ProcessPool with appropriate size."""
        if self._process_pool is None:
            self._process_pool = ProcessPool(
                max_workers=min(size_hint, self.max_workers)
            )
        return self._process_pool

    def process_generation_to_code(self, gens: str) -> List[List[str]]:
        r"""Process a string of generated code into a list of code lines.
        
        This method takes a string containing generated code, typically with multiple
        code blocks separated by double newlines, and splits it into a list of code
        blocks, where each block is itself a list of lines.
        
        Args:
            gens: A string containing the generated code, with code blocks
                 separated by double newlines.
                 
        Returns:
            List[List[str]]: A list where each element is a list of strings representing
                          the lines of a code block. Each line is stripped of leading
                          and trailing whitespace.
                          
        Example:
            Input: "def foo():\n    return 1\n\ndef bar():\n    return 2"
            Output: [['def foo():', '    return 1'], ['def bar():', '    return 2']]
        """
        return [g.strip().split("\n") for g in gens]

    def run_code(
        self, code: str, inputs: str, imports: List[str] = []
    ) -> Tuple[str, str]:
        """Execute the provided Python code with the given inputs and return the result.
        
        This method executes the provided code in a controlled environment, with optional
        imports prepended to the code. It performs AST parsing if AST checking is enabled
        and returns both the output and any error messages.
        
        Args:
            code: The Python code to execute.
            inputs: Input values to pass to the code (as a string that will be
                  formatted into the code template).
            imports: Optional list of import statements to prepend to the code.
            
        Returns:
            A tuple containing:
                - str: The output of the code execution (if successful).
                - str: Error message if execution failed, otherwise an empty string.
                
        Note:
            - The method performs an AST check if self.ast_check is True.
            - If the code fails to parse, returns an empty string and 'error'.
            - The actual execution is delegated to the apply() method.
        """
        if isinstance(imports, np.ndarray):
            imports = imports.tolist()
        if imports:
            code = "\n".join(imports) + "\n" + code
        code_snippet = RUN_CODE_TEMPLATE.format(code=code, inputs=inputs)
        # print(code_snippet)
        if self.ast_check:
            try:
                ast.parse(code_snippet)
            except:
                return "", "error"
        return self.apply(code_snippet)

    def validate_code(self, code: str, inputs: str, imports: List[str] = []) -> bool:
        """Validate that the provided Python code executes without errors for the given inputs.
        
        This method checks if the code can be successfully parsed and executed with the
        provided inputs. It can also validate against a list of imports that should be
        included in the code.
        
        Args:
            code: The Python code to validate.
            inputs: Input values to pass to the code (as a string that will be
                  formatted into the code template).
            imports: Optional list of import statements to prepend to the code.
            
        Returns:
            bool: True if the code executes without errors, False otherwise.
                  Returns False if the code fails to parse or raises an exception.
                  
        Note:
            - The method performs an AST check if self.ast_check is True.
            - The code is considered valid if it doesn't produce any errors.
        """
        if isinstance(imports, np.ndarray):
            imports = imports.tolist()
        if imports:
            code = "\n".join(imports) + "\n" + code
        code_snippet = VALIDATE_CODE_TEMPLATE.format(code=code, inputs=inputs)
        if self.ast_check:
            try:
                ast.parse(code_snippet)
            except:
                return False
        _, status = self.apply(code_snippet)
        return not "error" in status.lower()

    def eval_input_prediction(
        self, code: str, gold_output: str, agent_input: str, imports: List[str] = []
    ) -> float:
        """Evaluate if an agent's input produces the expected gold output when executed.
        
        This method tests whether executing the provided code with the agent's input
        produces the expected gold output. It includes retry logic with exponential backoff
        for robustness against transient failures.
        
        Args:
            code: The Python code to be executed, which should define a function 'f'.
            gold_output: The expected output as a string that can be evaluated.
            agent_input: The input to pass to the function defined in the code.
            imports: Optional list of import statements to prepend to the code.
            
        Returns:
            float: 1.0 if the output matches the gold standard, 0.0 otherwise or if there's an error.
                  Returns None if all retries fail with an error.
                  
        Note:
            - The method includes retry logic with exponential backoff for robustness.
            - Returns None if all retries fail with an error.
            - The code should define a function 'f' that takes the agent_input as an argument.
        """
        if isinstance(imports, np.ndarray):
            imports = imports.tolist()
        if imports:
            code = "\n".join(imports) + "\n" + code
        code_snippet = EVAL_INPUT_PREDICTION_TEMPLATE.format(
            code=code, gold_output=gold_output, agent_input=agent_input
        )
        if self.ast_check:
            try:
                ast.parse(code_snippet)
            except:
                return 0.0
        max_retries = 3
        for retry in range(max_retries):
            try:
                correct, status = self.apply(code_snippet)
                return 0.0 if "error" in status.lower() or not eval(correct) else 1.0
            except Exception as e:
                if retry == max_retries - 1:
                    error_details = traceback.format_exc()
                    print(f"Error in eval_input_prediction: {e}\n{error_details}")
                    return
                time.sleep(0.1 * (retry + 1))  # Exponential backoff

    def eval_output_prediction(
        self, code: str, gold_output: str, agent_output: str, imports: List[str] = []
    ) -> float:
        """Evaluate if an agent's output matches the expected gold output.
        
        This method compares an agent's output against a gold standard output.
        It first attempts a direct evaluation of both outputs. If that fails,
        it executes the provided code with the agent's output and compares
        the results to the gold standard.
        
        Args:
            code: The Python code that defines the context for evaluation.
            gold_output: The expected output as a string that can be evaluated.
            agent_output: The agent-generated output to evaluate.
            imports: Optional list of import statements to prepend to the code.
            
        Returns:
            float: 1.0 if the outputs match, 0.0 otherwise or if there's an error.
            
        Note:
            - The method includes retry logic with exponential backoff for robustness.
            - Returns None if all retries fail with an error.
        """
        try:  # fast check if we dont need to run the code
            if eval(gold_output) == eval(agent_output):
                return 1.0
        except:
            pass
        if isinstance(imports, np.ndarray):
            imports = imports.tolist()
        if imports:
            code = "\n".join(imports) + "\n" + code
        code_snippet = EVAL_OUTPUT_PREDICTION_TEMPLATE.format(
            code=code, gold_output=gold_output, agent_output=agent_output
        )
        if self.ast_check:
            try:
                ast.parse(code_snippet)
            except:
                return 0.0
        max_retries = 3
        for retry in range(max_retries):
            try:
                correct, status = self.apply(code_snippet)
                return 0.0 if "error" in status.lower() or not eval(correct) else 1.0
            except Exception as e:
                if retry == max_retries - 1:
                    error_details = traceback.format_exc()
                    logger.error(
                        f"Error in eval_output_prediction: {e}\n{error_details}"
                    )
                    return
                time.sleep(0.1 * (retry + 1))  # Exponential backoff

    def eval_k_input_prediction(
        self,
        code: str,
        gold_output: str,
        k_agent_inputs: List[str],
        imports: List[str] = [],
    ) -> List[float]:
        """Evaluate multiple input predictions against a gold output.
        
        This method takes a list of agent inputs, executes the provided code with each input,
        and compares the results against the gold output. Invalid inputs are assigned a score
        of 0.0. The evaluation is done by executing a generated code template that tests
        each input against the gold standard.
        
        Args:
            code: The Python code that defines a function 'f' to be tested.
            gold_output: The expected output as a string that can be evaluated.
            k_agent_inputs: List of agent-generated inputs to test against the gold output.
            imports: Optional list of import statements to prepend to the code.
            
        Returns:
            A list of float scores (1.0 for correct, 0.0 for incorrect or invalid)
            corresponding to each input in k_agent_inputs.
            
        Note:
            - Invalid inputs (those that can't be parsed) are assigned a score of 0.0.
            - The method ensures the returned list has the same length as k_agent_inputs.
        """
        if isinstance(imports, np.ndarray):
            imports = imports.tolist()
        if imports:
            code = "\n".join(imports) + "\n" + code
        invalid_lists = []
        valid_k_agent_inputs = []
        for k_agent_input in k_agent_inputs:
            try:
                ast.parse(f"f({k_agent_input})")
                valid_k_agent_inputs.append(k_agent_input)
            except:
                invalid_lists.append(0.0)
        acc_list, status = self.apply(
            EVAL_K_INPUT_PREDICTION_TEMPLATE(
                code=code, gold_output=gold_output, k_agent_inputs=valid_k_agent_inputs
            )
        )
        assert "error" not in status.lower()
        output_acc = eval(acc_list) + invalid_lists
        assert len(output_acc) == len(k_agent_inputs)
        return output_acc

    def eval_k_output_prediction(
        self,
        code: str,
        gold_output: str,
        k_agent_outputs: List[str],
        imports: List[str] = [],
    ) -> List[float]:
        """Evaluate multiple output predictions against a gold output.
        
        This method takes a list of agent outputs and evaluates each one against
        the gold output. It handles invalid outputs by assigning them a score of 0.0.
        The evaluation is done by executing a generated code template that compares
        each output to the gold standard.
        
        Args:
            code: The Python code that defines the context for the evaluation.
            gold_output: The expected output as a string that can be evaluated.
            k_agent_outputs: List of agent-generated outputs to evaluate.
            imports: Optional list of import statements to prepend to the code.
            
        Returns:
            A list of float scores (1.0 for correct, 0.0 for incorrect or invalid)
            corresponding to each output in k_agent_outputs.
            
        Note:
            - Invalid outputs (those that can't be parsed) are assigned a score of 0.0.
            - The method ensures the returned list has the same length as k_agent_outputs.
        """
        if isinstance(imports, np.ndarray):
            imports = imports.tolist()
        if imports:
            code = "\n".join(imports) + "\n" + code
        invalid_lists = []
        valid_k_agent_outputs = []
        for k_agent_output in k_agent_outputs:
            try:
                if k_agent_output != "":
                    ast.parse(f"f({k_agent_output})")
                    valid_k_agent_outputs.append(k_agent_output)
                else:
                    invalid_lists.append(0.0)
            except:
                invalid_lists.append(0.0)
        acc_list, status = self.apply(
            EVAL_K_OUTPUT_PREDICTION_TEMPLATE(
                code=code,
                gold_output=gold_output,
                k_agent_outputs=valid_k_agent_outputs,
            )
        )
        assert "error" not in status.lower()
        output_acc = eval(acc_list) + invalid_lists
        assert len(output_acc) == len(k_agent_outputs)
        return output_acc

    def check_all(
        self,
        code: str,
        inputs: str,
        banned_keywords: List[str] = [],
        check_determinism: bool = True,
        imports: List[str] = [],
        check_error: bool = False,
        banned_keywords_for_errors_and_exceptions: List[str] = [],
    ) -> Tuple[bool, str]:
        """Check if the code passes all validation checks.
        
        This method performs several validation checks on the provided code:
        - Checks for banned imports
        - Validates code syntax using AST
        - Optionally checks for determinism (same output for same input)
        - Optionally checks for specific error conditions
        
        Args:
            code: The Python code to validate.
            inputs: Input values to pass to the code (as a string that will be
                  formatted into the code template).
            banned_keywords: List of module/function names that are not allowed
                          in the code.
            check_determinism: If True, verifies that the code produces the same
                            output for the same input when run multiple times.
            imports: List of import statements to prepend to the code.
            check_error: If True, checks for errors in the code execution.
            banned_keywords_for_errors_and_exceptions: Additional keywords that are
                                                    not allowed when checking for
                                                    errors and exceptions.
            
        Returns:
            A tuple containing:
                - bool: True if all checks pass, False otherwise.
                - str: Additional status information (e.g., error message or "NoError").
        """
        if isinstance(imports, np.ndarray):
            imports = imports.tolist()
        if imports:
            code = "\n".join(imports) + "\n" + code
        if contains_banned_imports(
            code=code,
            banned_keywords=banned_keywords,
            banned_keywords_for_errors_and_exceptions=(
                banned_keywords_for_errors_and_exceptions if check_error else []
            ),
        ):
            return False, None
        if check_error:
            code_snippet = RUN_CODE_TEMPLATE.format(code=code, inputs=inputs)
            try:
                ast.parse(code_snippet)
            except:
                return False, "error"
            output, status = self.apply(code_snippet)
            if check_determinism:  # run the code again, see if outputs are same
                output_2, status_2 = self.apply(code_snippet)
                if status_2.lower() != status.lower() and output != output_2:
                    return False, "error"
            # True if the code is valid code but might have error, output no error if the code returns something
            return True, "NoError" if status.lower() == "done" else parse_error(status)
        else:
            if check_determinism:
                code_snippet = CHECK_DETERMINISM_TEMPLATE.format(
                    code=code, inputs=inputs
                )
            else:
                code_snippet = RUN_CODE_TEMPLATE.format(code=code, inputs=inputs)
            if self.ast_check:
                try:
                    ast.parse(code_snippet)
                except:
                    return False, "error"
            output, status = self.apply(code_snippet)
            return not "error" in status.lower(), output

    @staticmethod
    def execute(
        code: Union[str, List[str]],
        get_answer_from_stdout: Optional[bool] = None,
        runtime: Optional[Any] = None,
        answer_symbol: Optional[str] = None,
        answer_expr: Optional[str] = None,
        timeout_length: int = 10,
        auto_mode: bool = False,
    ) -> Tuple[Any, str]:
        """Execute Python code with the specified runtime and capture the result.
        
        This method executes the provided code using the specified runtime, with
        support for different execution modes (stdout capture, answer symbol,
        or expression evaluation). It includes timeout handling and error capture.
        
        Args:
            code: The code to execute, either as a string or a list of code lines.
            get_answer_from_stdout: If True, capture the output from stdout.
            runtime: The runtime environment to execute the code in.
            answer_symbol: If provided, get the result from this global variable.
            answer_expr: If provided, evaluate this expression to get the result.
            timeout_length: Maximum execution time in seconds. Defaults to 10.
            auto_mode: If True, auto-detect the execution mode based on the code.
            
        Returns:
            A tuple containing:
                - The execution result (type depends on the code and mode)
                - A status message ("Done" if successful, error message otherwise)
                
        Note:
            The method performs serialization and string conversion checks on the
            result to ensure it can be properly handled by the caller.
        """
        try:
            if auto_mode:
                if "print(" in code[-1]:
                    program_io = io.StringIO()
                    with redirect_stdout(program_io):
                        timeout(timeout_length)(runtime.exec_code)("\n".join(code))
                    program_io.seek(0)
                    result = program_io.read()
                else:
                    # print(code)
                    timeout(timeout_length)(runtime.exec_code)("\n".join(code[:-1]))
                    result = timeout(timeout_length)(runtime.eval_code)(code[-1])
            else:
                if get_answer_from_stdout:
                    program_io = io.StringIO()
                    with redirect_stdout(program_io):
                        timeout(timeout_length)(runtime.exec_code)("\n".join(code))
                    program_io.seek(0)
                    result = program_io.read()
                elif answer_symbol:
                    timeout(timeout_length)(runtime.exec_code)("\n".join(code))
                    result = runtime._global_vars[answer_symbol]
                elif answer_expr:
                    timeout(timeout_length)(runtime.exec_code)("\n".join(code))
                    result = timeout(timeout_length)(runtime.eval_code)(answer_expr)
                else:
                    timeout(timeout_length)(runtime.exec_code)("\n".join(code[:-1]))
                    result = timeout(timeout_length)(runtime.eval_code)(code[-1])
            report = "Done"
            str(result)  # codec check
            pickle.dumps(result)  # serialization check
        except:
            result = ""
            report = traceback.format_exc().split("\n")[-2]
        return result, report

    def apply_secure(self, code: str, inputs: str = "") -> Tuple[str, str]:
        """Apply secure execution to a single code snippet.

        This method uses the secure executor to run code in a sandboxed environment,
        providing protection against code injection and other security vulnerabilities.

        Args:
            code: The code snippet to be executed.
            inputs: Optional input string for the code.

        Returns:
            A tuple containing (output, status) where status can be:
            - "success": Code executed successfully
            - "error": Runtime error occurred
            - "timeout": Code execution timed out
            - "security_error": Security violation detected
        """
        try:
            if self.use_secure_execution:
                return self.secure_executor.execute_code(code, inputs, self.timeout_length)
            else:
                # Fallback to legacy execution with enhanced security checks
                try:
                    sanitize_code(code)
                except SecurityError as e:
                    return str(e), "security_error"

                # Use the legacy apply method
                result = self.apply(code)
                if isinstance(result, tuple) and len(result) == 2:
                    return result
                else:
                    return str(result), "success"
        except Exception as e:
            logger.error(f"Secure execution failed: {e}")
            return str(e), "error"

    def apply(self, code: str) -> Any:
        """Apply the executor to a single code snippet.
        
        This is a convenience method that wraps a single code snippet in a list
        and calls batch_apply with it, returning the first (and only) result.
        
        Args:
            code: The code snippet to be executed.
            
        Returns:
            The execution result of the code snippet, which includes the output,
            error message (if any), and other execution metadata.
        """
        return self.batch_apply([code])[0]

    @staticmethod
    def truncate(s: str, max_length: int = 400) -> str:
        """Truncate a string to a maximum length, adding ellipsis in the middle.
        
        If the string is longer than max_length, it will be truncated to max_length
        characters, with the middle replaced by '...'. The beginning and end of the
        string will be preserved as much as possible.
        
        Args:
            s: The input string to be truncated.
            max_length: Maximum allowed length of the output string.
                     Defaults to 400 characters.
                     
        Returns:
            str: The truncated string with ellipsis in the middle if it was longer
                 than max_length, otherwise the original string.
        """
        half = max_length // 2
        if len(s) > max_length:
            s = s[:half] + "..." + s[-half:]
        return s

    def batch_apply(self, batch_code):
        """Apply the executor to a batch of code snippets in parallel.
        
        This method processes a batch of code snippets, executes them in parallel
        using a process pool, and returns their execution results.
        
        Args:
            batch_code: A list of code snippets or a single code snippet string
                      to be executed.
                      
        Returns:
            list: A list of execution results corresponding to each code snippet.
                 Each result contains the output, error message (if any), and
                 other execution metadata.
        """
        all_code_snippets = self.process_generation_to_code(batch_code)

        timeout_cnt = 0
        all_exec_results = []

        pool = self._get_process_pool(len(all_code_snippets))
        executor = partial(
            self.execute,
            get_answer_from_stdout=self.get_answer_from_stdout,
            runtime=self.runtime,
            answer_symbol=self.answer_symbol,
            answer_expr=self.answer_expr,
            timeout_length=self.timeout_length,
            auto_mode=True,
        )

        try:
            future = pool.map(executor, all_code_snippets, timeout=self.timeout_length)
            iterator = future.result()

            if len(all_code_snippets) > 100:
                progress_bar = tqdm(total=len(all_code_snippets), desc="Execute")
            else:
                progress_bar = None

            while True:
                try:
                    result = next(iterator)
                    all_exec_results.append(result)
                except StopIteration:
                    break
                except TimeoutError as error:
                    logging.warning(f"Timeout error in code execution: {error}")
                    all_exec_results.append(("", "Timeout Error"))
                    timeout_cnt += 1
                except Exception as error:
                    logging.warning(f"Error in code execution: {error}")
                    all_exec_results.append(("", f"Error: {str(error)}"))
                if progress_bar is not None:
                    progress_bar.update(1)

            if progress_bar is not None:
                progress_bar.close()
        except Exception as e:
            logging.error(f"Critical error in batch execution: {e}")
            # Make sure we have results for all snippets
            while len(all_exec_results) < len(all_code_snippets):
                all_exec_results.append(("", f"Critical Error: {str(e)}"))

            # Cleanup the pool on critical errors
            self.cleanup()

        batch_results = []
        for code, (res, report) in zip(all_code_snippets, all_exec_results):
            # post processing
            res, report = str(res).strip(), str(report).strip()
            res, report = self.truncate(res), self.truncate(report)
            batch_results.append((res, report))
        return batch_results


def _test():
    batch_code = [
        """
def f(a):
    return a
print(f(1,2))
"""
    ]

    executor = PythonExecutor(get_answer_from_stdout=True)
    predictions = executor.apply(batch_code[0])
    logger.info(predictions)


if __name__ == "__main__":
    _test()
