{"snippet": "def f(a):\n    return a", "input": "\"Hello world\"", "output": "\"Hello world\"", "imports": [], "original_snippet": "def f(a):\n    return a", "composite_functions": []}
{"snippet": "def f(a: str) -> str:\n    fix_string = ' secret key'\n    result = (a + fix_string) * 3\n    return result", "input": "\"A test string\"", "output": "'A test string secret keyA test string secret keyA test string secret key'", "imports": [], "original_snippet": "def f(a: str) -> str:\n    fix_string = ' secret key'\n    result = (a + fix_string) * 3\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_list):\n    result_string = ''\n    for i in input_list:\n        result_string += str(i) + ' '\n    return result_string", "input": "[[1, 'a'], ['b', 2], [3]]", "output": "\"[1, 'a'] ['b', 2] [3] \"", "imports": [], "original_snippet": "def f(input_list):\n    result_string = ''\n    for i in input_list:\n        result_string += str(i) + ' '\n    return result_string", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(x: str, y: str, z: str):\n    return x + y + z[::-1]", "input": "'Lab', 'retest', 'gnelloH'", "output": "'LabretestHolleng'", "imports": [], "original_snippet": "def f(x: str, y: str, z: str):\n    return x + y + z[::-1]", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(input_string: str) -> str:\n    parts = input_string.split('.')\n    for p in parts:\n        print(p)\n    print('end metrics string')\n    str_parts = [str(part) for part in parts]\n    compact_string = '.'.join(str_parts)\n    return compact_string", "input": "'R2-D2.I am a metric-string.that always ends.php.html'", "output": "'R2-D2.I am a metric-string.that always ends.php.html'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    parts = input_string.split('.')\n    for p in parts:\n        print(p)\n    print('end metrics string')\n    str_parts = [str(part) for part in parts]\n    compact_string = '.'.join(str_parts)\n    return compact_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import collections\ndef f(input_list):\n    output_list = []\n    unique_number_counts = collections.Counter([i[1] for i in input_list if isinstance(i[1], int)])\n    for i in input_list:\n        if isinstance(i[1], int):\n            output_list.append((i[0], i[1] * unique_number_counts[i[1]]))\n        else:\n            output_list.append(i)\n    return output_list", "input": "[['Name', 'Alice'], ['Age', 25], ['Favourite color', 'Blue'], ['Name', 'Bob'], ['Age', 25], ['Favourite color', 'Green'], ['Name', 'Charlie'], ['Age', 30]]", "output": "[['Name', 'Alice'], ('Age', 50), ['Favourite color', 'Blue'], ['Name', 'Bob'], ('Age', 50), ['Favourite color', 'Green'], ['Name', 'Charlie'], ('Age', 30)]", "imports": ["import collections"], "original_snippet": "import collections\ndef f(input_list):\n    output_list = []\n    unique_number_counts = collections.Counter([i[1] for i in input_list if isinstance(i[1], int)])\n    for i in input_list:\n        if isinstance(i[1], int):\n            output_list.append((i[0], i[1] * unique_number_counts[i[1]]))\n        else:\n            output_list.append(i)\n    return output_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_num):\n    for x in range(32, -1, -1):\n        if not x & input_num:\n            return x\n    return 0", "input": "36", "output": "27", "imports": [], "original_snippet": "def f(input_num):\n    for x in range(32, -1, -1):\n        if not x & input_num:\n            return x\n    return 0", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(numbers: str) -> list:\n    odd_sum = 0\n    even_sum = 0\n    numbers = list(map(int, numbers.split(',')))\n    for (idx, val) in enumerate(numbers):\n        if idx % 2 == 0:\n            if val % 2 == 0:\n                odd_sum += val\n        elif val % 2 != 0:\n            even_sum += val\n    return [odd_sum, even_sum]", "input": "\"1, 2, 3, 4, 5\"", "output": "[0, 0]", "imports": [], "original_snippet": "def f(numbers: str) -> list:\n    odd_sum = 0\n    even_sum = 0\n    numbers = list(map(int, numbers.split(',')))\n    for (idx, val) in enumerate(numbers):\n        if idx % 2 == 0:\n            if val % 2 == 0:\n                odd_sum += val\n        elif val % 2 != 0:\n            even_sum += val\n    return [odd_sum, even_sum]", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(numbers):\n    even_sum = 0\n    odd_product = 1\n    for num in numbers:\n        if num % 2 == 0:\n            even_sum += num\n        else:\n            odd_product *= num\n    return (even_sum, odd_product)", "input": "[1, 2, 3, 4, 5, 6]", "output": "(12, 15)", "imports": [], "original_snippet": "def f(numbers):\n    even_sum = 0\n    odd_product = 1\n    for num in numbers:\n        if num % 2 == 0:\n            even_sum += num\n        else:\n            odd_product *= num\n    return (even_sum, odd_product)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "import re\ndef f(input_data):\n    patterns = [('[aeiou]+', 'X'), ('foo', 'bar'), ('hello', 'world')]\n    for (pat, repl) in patterns:\n        input_data = re.subn(re.escape(pat), repl, input_data)[0]\n        input_data = re.subn('X', 'Y', input_data)[0]\n    return input_data", "input": "'me'", "output": "'me'", "imports": ["import re"], "original_snippet": "import re\ndef f(input_data):\n    patterns = [('[aeiou]+', 'X'), ('foo', 'bar'), ('hello', 'world')]\n    for (pat, repl) in patterns:\n        input_data = re.subn(re.escape(pat), repl, input_data)[0]\n        input_data = re.subn('X', 'Y', input_data)[0]\n    return input_data", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_string):\n    count = 0\n    reversed_string = ''\n    for char in input_string[::-1]:\n        if char.isalpha():\n            count += 1\n            reversed_string += char\n        elif char.isspace():\n            count += 1\n            reversed_string += ' '\n    return (reversed_string, count)", "input": "\"Hello world\"", "output": "('dlrow olleH', 11)", "imports": [], "original_snippet": "def f(input_string):\n    count = 0\n    reversed_string = ''\n    for char in input_string[::-1]:\n        if char.isalpha():\n            count += 1\n            reversed_string += char\n        elif char.isspace():\n            count += 1\n            reversed_string += ' '\n    return (reversed_string, count)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(input_string):\n    result = ''\n    for char in input_string:\n        if char.lower() in 'aeiou':\n            result += str(ord(char.lower()) - ord('a') + 1)\n        else:\n            result += char\n    vowel_count = sum((1 for char in result if char.lower() in 'aeiou'))\n    consonant_count = sum((1 for char in result if char.lower() in 'bcdfghjklmnpqrstvwxyz'))\n    final_result = vowel_count * 'V' + consonant_count * 'C'\n    return final_result", "input": "'abcdefg hijklmnop'", "output": "'CCCCCCCCCCCC'", "imports": [], "original_snippet": "def f(input_string):\n    result = ''\n    for char in input_string:\n        if char.lower() in 'aeiou':\n            result += str(ord(char.lower()) - ord('a') + 1)\n        else:\n            result += char\n    vowel_count = sum((1 for char in result if char.lower() in 'aeiou'))\n    consonant_count = sum((1 for char in result if char.lower() in 'bcdfghjklmnpqrstvwxyz'))\n    final_result = vowel_count * 'V' + consonant_count * 'C'\n    return final_result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from collections import defaultdict\ndef f(input_pairs):\n    label_frequencies = defaultdict(int)\n    word_frequencies = defaultdict(int)\n    for (label, word) in input_pairs:\n        label_frequencies[label] += 1\n        if word[0] in 'aeiouAEIOU':\n            word_frequencies[word] += 1\n    result = []\n    for (label, count) in label_frequencies.items():\n        result.append((label, count, word_frequencies[label]))\n    return result", "input": "[('Label1', 'Apple'), ('Label2', 'Elephant'), ('Label1', 'Orange'), ('Label2', 'Aardvark'), ('Label1', 'Elephant'), ('Label2', 'Banana')]", "output": "[('Label1', 3, 0), ('Label2', 3, 0)]", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(input_pairs):\n    label_frequencies = defaultdict(int)\n    word_frequencies = defaultdict(int)\n    for (label, word) in input_pairs:\n        label_frequencies[label] += 1\n        if word[0] in 'aeiouAEIOU':\n            word_frequencies[word] += 1\n    result = []\n    for (label, count) in label_frequencies.items():\n        result.append((label, count, word_frequencies[label]))\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_str: str, l_list: list):\n    w_list = [s[::-1] for s in l_list]\n    concat_str = ' '.join(w_list) + input_str[::-1]\n    char_count = {}\n    for char in concat_str:\n        if char not in char_count:\n            char_count[char] = 1\n        else:\n            char_count[char] += 1\n    return (concat_str, char_count)", "input": "\"Hello World!\", ['Python', 'Programming', 'is', 'a', 'powerful', 'tool']", "output": "('nohtyP gnimmargorP si a lufrewop loot!dlroW olleH', {'n': 2, 'o': 7, 'h': 1, 't': 2, 'y': 1, 'P': 2, ' ': 6, 'g': 2, 'i': 2, 'm': 2, 'a': 2, 'r': 4, 's': 1, 'l': 5, 'u': 1, 'f': 1, 'e': 2, 'w': 1, 'p': 1, '!': 1, 'd': 1, 'W': 1, 'H': 1})", "imports": [], "original_snippet": "def f(input_str: str, l_list: list):\n    w_list = [s[::-1] for s in l_list]\n    concat_str = ' '.join(w_list) + input_str[::-1]\n    char_count = {}\n    for char in concat_str:\n        if char not in char_count:\n            char_count[char] = 1\n        else:\n            char_count[char] += 1\n    return (concat_str, char_count)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(n: int) -> int:\n    return n", "input": "3", "output": "3", "imports": [], "original_snippet": "def f(n: int) -> int:\n    return n", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(input_list):\n    count = 0\n    result = 0\n    for i in range(len(input_list)):\n        if i >= 1:\n            index_mult = input_list[i - 1]\n        else:\n            index_mult = 1\n        if i % 2 == 0:\n            if input_list[i] >= 0 and input_list[i] <= 4:\n                result += input_list[i] * index_mult\n            else:\n                result += input_list[i] - index_mult\n            selected_val = input_list[i]\n            count += 1\n    return [selected_val, result, count]", "input": "[-4, 2, 3, 6]", "output": "[3, 1, 2]", "imports": [], "original_snippet": "def f(input_list):\n    count = 0\n    result = 0\n    for i in range(len(input_list)):\n        if i >= 1:\n            index_mult = input_list[i - 1]\n        else:\n            index_mult = 1\n        if i % 2 == 0:\n            if input_list[i] >= 0 and input_list[i] <= 4:\n                result += input_list[i] * index_mult\n            else:\n                result += input_list[i] - index_mult\n            selected_val = input_list[i]\n            count += 1\n    return [selected_val, result, count]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_string):\n    vowels = ['a', 'e', 'i', 'o', 'u']\n    consonants = 'bcdfghjklmnpqrstvwxyz'\n    reversed_string = ''\n    vowel_count = 0\n    consonant_count = 0\n    for char in input_string:\n        if char.isalpha():\n            reversed_string = char + reversed_string\n            if char.lower() in vowels:\n                vowel_count += 1\n            elif char.lower() in consonants:\n                consonant_count += 1\n    return (reversed_string, vowel_count * consonant_count)", "input": "'abc'", "output": "('cba', 2)", "imports": [], "original_snippet": "def f(input_string):\n    vowels = ['a', 'e', 'i', 'o', 'u']\n    consonants = 'bcdfghjklmnpqrstvwxyz'\n    reversed_string = ''\n    vowel_count = 0\n    consonant_count = 0\n    for char in input_string:\n        if char.isalpha():\n            reversed_string = char + reversed_string\n            if char.lower() in vowels:\n                vowel_count += 1\n            elif char.lower() in consonants:\n                consonant_count += 1\n    return (reversed_string, vowel_count * consonant_count)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "import re\ndef f(input_string: str, pattern: str, replacement: str) -> str:\n    output_string = re.sub(pattern, replacement, input_string)\n    if output_string == '':\n        output_string = input_string\n    return output_string", "input": "'The quick brown fox jumps over the lazy dog', 'blue', 'green'", "output": "'The quick brown fox jumps over the lazy dog'", "imports": ["import re"], "original_snippet": "import re\ndef f(input_string: str, pattern: str, replacement: str) -> str:\n    output_string = re.sub(pattern, replacement, input_string)\n    if output_string == '':\n        output_string = input_string\n    return output_string", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "import collections\ndef f(input_dict):\n    total_age = 0\n    count_ages = collections.Counter([person['age'] for person in input_dict if 'age' in person])\n    for person in input_dict:\n        if 'age' not in person and person['city'] == 'New York':\n            age_key = next((x for x in count_ages.keys() if x > 100), 101)\n            person['age'] = age_key\n            total_age += age_key\n        elif 'age' not in person:\n            total_age += 101\n        else:\n            total_age += person['age']\n    return total_age", "input": "[{'name': 'Alice', 'age': 25}, {'name': 'Bob', 'age': 35}, {'name': 'Charlie', 'city': 'New York'}]", "output": "161", "imports": ["import collections"], "original_snippet": "import collections\ndef f(input_dict):\n    total_age = 0\n    count_ages = collections.Counter([person['age'] for person in input_dict if 'age' in person])\n    for person in input_dict:\n        if 'age' not in person and person['city'] == 'New York':\n            age_key = next((x for x in count_ages.keys() if x > 100), 101)\n            person['age'] = age_key\n            total_age += age_key\n        elif 'age' not in person:\n            total_age += 101\n        else:\n            total_age += person['age']\n    return total_age", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_list: list):\n    sorted_elements = [''.join(sorted(element)) for element in input_list]\n    compact_string = '(' + ', '.join(sorted_elements) + ')[]{'\n    return compact_string", "input": "['hello']", "output": "'(ehllo)[]{'", "imports": [], "original_snippet": "def f(input_list: list):\n    sorted_elements = [''.join(sorted(element)) for element in input_list]\n    compact_string = '(' + ', '.join(sorted_elements) + ')[]{'\n    return compact_string", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(input_str: str) -> str:\n    replacements = {'a': '1', 'b': '2', 'c': '3', 'd': '4', 'e': '5', 'f': '6', 'g': '7', 'h': '8', 'i': '9', 'j': '0'}\n    result_str = ''\n    for char in input_str:\n        if char in replacements:\n            result_str += replacements[char]\n        else:\n            result_str += char\n    return result_str", "input": "\"Hel1lo, 2orld!\"", "output": "'H5l1lo, 2orl4!'", "imports": [], "original_snippet": "def f(input_str: str) -> str:\n    replacements = {'a': '1', 'b': '2', 'c': '3', 'd': '4', 'e': '5', 'f': '6', 'g': '7', 'h': '8', 'i': '9', 'j': '0'}\n    result_str = ''\n    for char in input_str:\n        if char in replacements:\n            result_str += replacements[char]\n        else:\n            result_str += char\n    return result_str", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_list):\n    result = []\n    for i in range(len(input_list)):\n        if i >= 1:\n            prev_value = input_list[i - 1]\n        else:\n            prev_value = 0\n        if i % 2 == 0:\n            result.append(input_list[i] - prev_value)\n        else:\n            result.append(input_list[i] + prev_value)\n    return result", "input": "[1, 2, 3, 4, 5, 6]", "output": "[1, 3, 1, 7, 1, 11]", "imports": [], "original_snippet": "def f(input_list):\n    result = []\n    for i in range(len(input_list)):\n        if i >= 1:\n            prev_value = input_list[i - 1]\n        else:\n            prev_value = 0\n        if i % 2 == 0:\n            result.append(input_list[i] - prev_value)\n        else:\n            result.append(input_list[i] + prev_value)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list):\n    output_dict = {chr(i): 0 for i in range(ord('a'), ord('z') + 1)}\n    processed_strings = set(input_list)\n    for s in processed_strings:\n        for c in s:\n            if c.isalpha():\n                output_dict[c.lower()] += 1\n    return output_dict", "input": "['hello', 'world', 'hello', 'earth', 'world', 'world']", "output": "{'a': 1, 'b': 0, 'c': 0, 'd': 1, 'e': 2, 'f': 0, 'g': 0, 'h': 2, 'i': 0, 'j': 0, 'k': 0, 'l': 3, 'm': 0, 'n': 0, 'o': 2, 'p': 0, 'q': 0, 'r': 2, 's': 0, 't': 1, 'u': 0, 'v': 0, 'w': 1, 'x': 0, 'y': 0, 'z': 0}", "imports": [], "original_snippet": "def f(input_list):\n    output_dict = {chr(i): 0 for i in range(ord('a'), ord('z') + 1)}\n    processed_strings = set(input_list)\n    for s in processed_strings:\n        for c in s:\n            if c.isalpha():\n                output_dict[c.lower()] += 1\n    return output_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "import math\ndef f(lst):\n    result = []\n    current_sum = 0\n    max_count = 0\n    for num in lst:\n        current_sum += num\n        if num == 1:\n            count = int(math.log2(current_sum))\n            result += count * ['0b1']\n            max_count = max(max_count, count)\n    for _ in range(max_count - len(result)):\n        result.append('0b0')\n    return result", "input": "[1, 2, 4, 3, 5, 8, 2, 3]", "output": "[]", "imports": ["import math"], "original_snippet": "import math\ndef f(lst):\n    result = []\n    current_sum = 0\n    max_count = 0\n    for num in lst:\n        current_sum += num\n        if num == 1:\n            count = int(math.log2(current_sum))\n            result += count * ['0b1']\n            max_count = max(max_count, count)\n    for _ in range(max_count - len(result)):\n        result.append('0b0')\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(nums):\n    ans = 0\n    for i in range(len(nums)):\n        for j in range(i + 1, len(nums)):\n            if nums[i] < nums[j]:\n                for k in range(j + 1, len(nums)):\n                    if nums[j] < nums[k]:\n                        ans += 1\n    return ans", "input": "[1, 2, 3, 4, 5]", "output": "10", "imports": [], "original_snippet": "def f(nums):\n    ans = 0\n    for i in range(len(nums)):\n        for j in range(i + 1, len(nums)):\n            if nums[i] < nums[j]:\n                for k in range(j + 1, len(nums)):\n                    if nums[j] < nums[k]:\n                        ans += 1\n    return ans", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_data: dict):\n    return {...}", "input": "{\n    'Group': {\n        'Member1': {'age': 25, 'info': 'a'},\n        'Member2': {'age': 30, 'info': 'b'},\n        'Member3': {'age': 35, 'info': 'c'},\n    }\n}", "output": "{Ellipsis}", "imports": [], "original_snippet": "def f(input_data: dict):\n    return {...}", "composite_functions": [], "_input_type": "dict", "_output_type": "str"}
{"snippet": "def f(input_list):\n    processed_list = []\n    even_count = 0\n    for num in input_list:\n        if num % 2 == 0:\n            processed_list.append(num * 2)\n            even_count += 1\n        elif num % 3 == 0:\n            processed_list.append(num + 10)\n        elif num % 5 == 0:\n            processed_list.append(num - 10)\n        else:\n            processed_list.append(num)\n    processed_list.append(even_count)\n    return processed_list", "input": "[6, 9, 10, 15, 20]", "output": "[12, 19, 20, 25, 40, 3]", "imports": [], "original_snippet": "def f(input_list):\n    processed_list = []\n    even_count = 0\n    for num in input_list:\n        if num % 2 == 0:\n            processed_list.append(num * 2)\n            even_count += 1\n        elif num % 3 == 0:\n            processed_list.append(num + 10)\n        elif num % 5 == 0:\n            processed_list.append(num - 10)\n        else:\n            processed_list.append(num)\n    processed_list.append(even_count)\n    return processed_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_string: str) -> str:\n    words = input_string.split()\n    sentence = ' '.join(words[::-1])\n    final_sentence = sentence[0].upper() + sentence[1:].replace(' ', '') if sentence else ''\n    return final_sentence", "input": "'my name is ai'", "output": "'Aiisnamemy'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    words = input_string.split()\n    sentence = ' '.join(words[::-1])\n    final_sentence = sentence[0].upper() + sentence[1:].replace(' ', '') if sentence else ''\n    return final_sentence", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_string: str) -> str:\n    result_str = ''\n    state = {'character_count': {}, 'special_character_count': 0}\n    for (i, char) in enumerate(input_string):\n        if char.isalpha():\n            if char.lower() != char.upper():\n                state['character_count']['lower'] = state['character_count'].get('lower', 0) + 1\n            else:\n                state['character_count']['upper'] = state['character_count'].get('upper', 0) + 1\n                result_str += char.capitalize()\n        elif char.isdigit():\n            result_str += str(i)\n            state['character_count']['digit'] = state['character_count'].get('digit', 0) + 1\n        else:\n            state['special_character_count'] += 1\n            result_str += char\n    state['special_character_count'] = 2 * state['special_character_count']\n    for (key, value) in state['character_count'].items():\n        state['special_character_count'] += value % 2\n    try:\n        final_char = input_string[0]\n    except IndexError:\n        final_char = ''\n    for char in result_str[:state['special_character_count']]:\n        if char.isalpha():\n            final_char += char\n    if final_char[-1].isupper():\n        final_char = final_char[:-1] + final_char[-1].lower()\n    return final_char", "input": "\"Hello, World! 123\"", "output": "'h'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    result_str = ''\n    state = {'character_count': {}, 'special_character_count': 0}\n    for (i, char) in enumerate(input_string):\n        if char.isalpha():\n            if char.lower() != char.upper():\n                state['character_count']['lower'] = state['character_count'].get('lower', 0) + 1\n            else:\n                state['character_count']['upper'] = state['character_count'].get('upper', 0) + 1\n                result_str += char.capitalize()\n        elif char.isdigit():\n            result_str += str(i)\n            state['character_count']['digit'] = state['character_count'].get('digit', 0) + 1\n        else:\n            state['special_character_count'] += 1\n            result_str += char\n    state['special_character_count'] = 2 * state['special_character_count']\n    for (key, value) in state['character_count'].items():\n        state['special_character_count'] += value % 2\n    try:\n        final_char = input_string[0]\n    except IndexError:\n        final_char = ''\n    for char in result_str[:state['special_character_count']]:\n        if char.isalpha():\n            final_char += char\n    if final_char[-1].isupper():\n        final_char = final_char[:-1] + final_char[-1].lower()\n    return final_char", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(numbers):\n    result = 0\n    counted_locations = {}\n    for (index, num) in enumerate(numbers):\n        if index % 2 == 0:\n            result += num\n        else:\n            result *= num\n        counted_locations[index] = num\n    return (result, counted_locations)", "input": "[1, 2, 3, 4, 5, 6]", "output": "(150, {0: 1, 1: 2, 2: 3, 3: 4, 4: 5, 5: 6})", "imports": [], "original_snippet": "def f(numbers):\n    result = 0\n    counted_locations = {}\n    for (index, num) in enumerate(numbers):\n        if index % 2 == 0:\n            result += num\n        else:\n            result *= num\n        counted_locations[index] = num\n    return (result, counted_locations)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(input_dict: dict) -> dict:\n    original_keys = set(input_dict.keys())\n    original_values = set(input_dict.values())\n    reversed_dict = {}\n    for (key, value) in input_dict.items():\n        reversed_dict[value] = key\n    return reversed_dict", "input": "{'a': 'x', 'b': 'y', 'c': 'z'}", "output": "{'x': 'a', 'y': 'b', 'z': 'c'}", "imports": [], "original_snippet": "def f(input_dict: dict) -> dict:\n    original_keys = set(input_dict.keys())\n    original_values = set(input_dict.values())\n    reversed_dict = {}\n    for (key, value) in input_dict.items():\n        reversed_dict[value] = key\n    return reversed_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(boxes: list) -> list:\n    fib_sequence = [0, 1]\n    for i in range(2, max(boxes) + 1):\n        fib_sequence.append(fib_sequence[i - 1] + fib_sequence[i - 2])\n    return fib_sequence", "input": "[2, 3, 5, 8]", "output": "[0, 1, 1, 2, 3, 5, 8, 13, 21]", "imports": [], "original_snippet": "def f(boxes: list) -> list:\n    fib_sequence = [0, 1]\n    for i in range(2, max(boxes) + 1):\n        fib_sequence.append(fib_sequence[i - 1] + fib_sequence[i - 2])\n    return fib_sequence", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(numbers):\n    associations = {}\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            if numbers[i] not in associations:\n                associations[numbers[i]] = set()\n            associations[numbers[i]].add((numbers[j], i))\n        if numbers[i] not in associations:\n            associations[numbers[i]] = set()\n        associations[numbers[i]].add((numbers[i], i))\n    return associations", "input": "[1, 2, 3, 1, 4]", "output": "{1: {(4, 0), (4, 3), (2, 0), (3, 0), (1, 0), (1, 3)}, 2: {(3, 1), (1, 1), (4, 1), (2, 1)}, 3: {(3, 2), (1, 2), (4, 2)}, 4: {(4, 4)}}", "imports": [], "original_snippet": "def f(numbers):\n    associations = {}\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            if numbers[i] not in associations:\n                associations[numbers[i]] = set()\n            associations[numbers[i]].add((numbers[j], i))\n        if numbers[i] not in associations:\n            associations[numbers[i]] = set()\n        associations[numbers[i]].add((numbers[i], i))\n    return associations", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(input_char: str) -> str:\n    replacements = {'a': '1', 'b': '3', 'c': '5', 'd': '7', 'e': '9', 'f': '11', 'g': '13', 'h': '15', 'i': '17', 'j': '19'}\n    if input_char in replacements:\n        return replacements[input_char]\n    output_number = 0\n    for char_number in map(replacements.get, input_char):\n        if char_number:\n            output_number += 2\n            output_number *= int(char_number)\n    return str(output_number)", "input": "'e'", "output": "'9'", "imports": [], "original_snippet": "def f(input_char: str) -> str:\n    replacements = {'a': '1', 'b': '3', 'c': '5', 'd': '7', 'e': '9', 'f': '11', 'g': '13', 'h': '15', 'i': '17', 'j': '19'}\n    if input_char in replacements:\n        return replacements[input_char]\n    output_number = 0\n    for char_number in map(replacements.get, input_char):\n        if char_number:\n            output_number += 2\n            output_number *= int(char_number)\n    return str(output_number)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(family_tree):\n    parent_ages = {}\n    for relationship in family_tree:\n        person = relationship[1]\n        if relationship[0] == 'parent':\n            parent_index = len(relationship)\n            parent_ages[person] = sum([relationship[parent_index] for relationship in family_tree if relationship[0] == 'child' and relationship[1] == person])\n    return [[person, parent_ages[person]] for person in parent_ages]", "input": "[[\"parent\", \"John\"], [\"child\", \"Bob\", 10], [\"child\", \"Alice\", 15], [\"parent\", \"Emma\"], [\"child\", \"Connor\", 25], [\"child\", \"Mia\", 8]]", "output": "[['John', 0], ['Emma', 0]]", "imports": [], "original_snippet": "def f(family_tree):\n    parent_ages = {}\n    for relationship in family_tree:\n        person = relationship[1]\n        if relationship[0] == 'parent':\n            parent_index = len(relationship)\n            parent_ages[person] = sum([relationship[parent_index] for relationship in family_tree if relationship[0] == 'child' and relationship[1] == person])\n    return [[person, parent_ages[person]] for person in parent_ages]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_string):\n    letter_counts = {}\n    for char in input_string:\n        letter_counts[char] = letter_counts.get(char, 0) + 1\n    if letter_counts:\n        min_count = min(letter_counts.values())\n        max_count = max(letter_counts.values())\n        return (min_count, max_count)\n    else:\n        return (None, None)", "input": "'hello world'", "output": "(1, 3)", "imports": [], "original_snippet": "def f(input_string):\n    letter_counts = {}\n    for char in input_string:\n        letter_counts[char] = letter_counts.get(char, 0) + 1\n    if letter_counts:\n        min_count = min(letter_counts.values())\n        max_count = max(letter_counts.values())\n        return (min_count, max_count)\n    else:\n        return (None, None)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(sentence):\n    counts = {}\n    for word in sentence.split():\n        unique_words = set(word.lower())\n        unique_chars = set(word)\n        counts[word] = len(unique_words) * len(unique_chars)\n    return sum(counts.values())", "input": "'I love AI. AI is the best. AI is the future.'", "output": "104", "imports": [], "original_snippet": "def f(sentence):\n    counts = {}\n    for word in sentence.split():\n        unique_words = set(word.lower())\n        unique_chars = set(word)\n        counts[word] = len(unique_words) * len(unique_chars)\n    return sum(counts.values())", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_str: str) -> str:\n    result_str = ''\n    stack = []\n    num_digits = 0\n    digits = ''\n    for char in input_str:\n        if char.isdigit():\n            digits += char\n        else:\n            if digits:\n                num_digits = int(digits[::-1])\n                digits = ''\n            if char.isalpha():\n                if num_digits > 0:\n                    result_str += char * num_digits\n                    stack.extend([char] * (num_digits - 1))\n                    num_digits = 0\n                else:\n                    result_str += char\n            if char.isupper():\n                stack.append(char.lower())\n            elif char.islower():\n                stack.append(char.upper())\n    return result_str + ''.join(stack[::-1])", "input": "\"3hello298 should67  5F1.0mallblackBoyRed\"", "output": "'hhhellossssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss...ssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssOLLEHhh'", "imports": [], "original_snippet": "def f(input_str: str) -> str:\n    result_str = ''\n    stack = []\n    num_digits = 0\n    digits = ''\n    for char in input_str:\n        if char.isdigit():\n            digits += char\n        else:\n            if digits:\n                num_digits = int(digits[::-1])\n                digits = ''\n            if char.isalpha():\n                if num_digits > 0:\n                    result_str += char * num_digits\n                    stack.extend([char] * (num_digits - 1))\n                    num_digits = 0\n                else:\n                    result_str += char\n            if char.isupper():\n                stack.append(char.lower())\n            elif char.islower():\n                stack.append(char.upper())\n    return result_str + ''.join(stack[::-1])", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_list: list) -> dict:\n    counts = {'even_count': 0, 'odd_count': 0, 'even_even': 0, 'even_odd': 0, 'odd_even': 0, 'odd_odd': 0}\n    operations = {'sum_even': 0, 'sum_odd': 0, 'odd_list': []}\n    for (idx, num) in enumerate(input_list):\n        if idx % 2 == 0:\n            if num % 2 == 0:\n                counts['even_count'] += 1\n                counts['even_even'] += num\n                operations['sum_even'] += num\n            else:\n                counts['even_count'] += 1\n                counts['even_odd'] += num\n                operations['sum_even'] += num\n        elif num % 2 == 0:\n            counts['odd_count'] += 1\n            counts['odd_even'] += num\n            operations['sum_odd'] += num\n            operations['odd_list'].append(num)\n        else:\n            counts['odd_count'] += 1\n            counts['odd_odd'] += num\n            operations['sum_odd'] += num\n            operations['odd_list'].append(num)\n    answer_dict = {'counts': counts, 'operations': operations}\n    return answer_dict", "input": "[10, 15, 2, -5, 8, 0, 3, 17, 4, -4, -12]", "output": "{'counts': {'even_count': 6, 'odd_count': 5, 'even_even': 12, 'even_odd': 3, 'odd_even': -4, 'odd_odd': 27}, 'operations': {'sum_even': 15, 'sum_odd': 23, 'odd_list': [15, -5, 0, 17, -4]}}", "imports": [], "original_snippet": "def f(input_list: list) -> dict:\n    counts = {'even_count': 0, 'odd_count': 0, 'even_even': 0, 'even_odd': 0, 'odd_even': 0, 'odd_odd': 0}\n    operations = {'sum_even': 0, 'sum_odd': 0, 'odd_list': []}\n    for (idx, num) in enumerate(input_list):\n        if idx % 2 == 0:\n            if num % 2 == 0:\n                counts['even_count'] += 1\n                counts['even_even'] += num\n                operations['sum_even'] += num\n            else:\n                counts['even_count'] += 1\n                counts['even_odd'] += num\n                operations['sum_even'] += num\n        elif num % 2 == 0:\n            counts['odd_count'] += 1\n            counts['odd_even'] += num\n            operations['sum_odd'] += num\n            operations['odd_list'].append(num)\n        else:\n            counts['odd_count'] += 1\n            counts['odd_odd'] += num\n            operations['sum_odd'] += num\n            operations['odd_list'].append(num)\n    answer_dict = {'counts': counts, 'operations': operations}\n    return answer_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(numbers):\n    sum_of_cube_roots = sum([x ** (1 / 3) for x in numbers])\n    max_value = max(numbers)\n    result = [max_value - x for x in numbers]\n    return sorted(result, reverse=True)", "input": "numbers = [2, 8, 9, 5, 6]", "output": "[7, 4, 3, 1, 0]", "imports": [], "original_snippet": "def f(numbers):\n    sum_of_cube_roots = sum([x ** (1 / 3) for x in numbers])\n    max_value = max(numbers)\n    result = [max_value - x for x in numbers]\n    return sorted(result, reverse=True)", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(input_list):\n    result = []\n    even_count = 0\n    current_sum = 0\n    for num in input_list:\n        if num % 2 == 0:\n            result.append(num * 3)\n            even_count += 1\n        elif num % 3 == 0:\n            result.append(num + 5)\n        elif num % 5 == 0:\n            result.append(num - 5)\n        else:\n            result.append(num)\n    result.append(even_count)\n    return result", "input": "[6, 9, 10, 15, 20]", "output": "[18, 14, 30, 20, 60, 3]", "imports": [], "original_snippet": "def f(input_list):\n    result = []\n    even_count = 0\n    current_sum = 0\n    for num in input_list:\n        if num % 2 == 0:\n            result.append(num * 3)\n            even_count += 1\n        elif num % 3 == 0:\n            result.append(num + 5)\n        elif num % 5 == 0:\n            result.append(num - 5)\n        else:\n            result.append(num)\n    result.append(even_count)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_string: str) -> str:\n    result_str = ''\n    state = {'l_lower_c': 0, 'l_upper_c': 0, 'd_count': 0}\n    for (i, char) in enumerate(input_string):\n        if char.isalpha():\n            if char.lower() != char.upper():\n                state['l_lower_c'] += 1\n            else:\n                result_str += char.capitalize()\n        elif char.isdigit():\n            result_str += str(i)\n            state['d_count'] += 1\n        else:\n            result_str += char\n    final_char = ''\n    if state['l_lower_c'] != 0:\n        final_char += 'a'\n    else:\n        final_char += 'b'\n    if state['l_upper_c'] % 2 == 0:\n        final_char += 'e'\n    else:\n        final_char += 'f'\n    if state['d_count'] <= 1:\n        final_char += 'g'\n    else:\n        final_char += 'h'\n    return final_char", "input": "\"Hello World\"", "output": "'aeg'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    result_str = ''\n    state = {'l_lower_c': 0, 'l_upper_c': 0, 'd_count': 0}\n    for (i, char) in enumerate(input_string):\n        if char.isalpha():\n            if char.lower() != char.upper():\n                state['l_lower_c'] += 1\n            else:\n                result_str += char.capitalize()\n        elif char.isdigit():\n            result_str += str(i)\n            state['d_count'] += 1\n        else:\n            result_str += char\n    final_char = ''\n    if state['l_lower_c'] != 0:\n        final_char += 'a'\n    else:\n        final_char += 'b'\n    if state['l_upper_c'] % 2 == 0:\n        final_char += 'e'\n    else:\n        final_char += 'f'\n    if state['d_count'] <= 1:\n        final_char += 'g'\n    else:\n        final_char += 'h'\n    return final_char", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from fractions import Fraction\ndef f(num1, num2, num3, num4):\n    if isinstance(num1, str):\n        num1 = float(num1)\n    if isinstance(num2, str):\n        num2 = Fraction(num2)\n    if isinstance(num3, str):\n        num3 = float(num3)\n    if isinstance(num4, str):\n        num4 = Fraction(num4)\n    result_1 = num1 + num2\n    result_2 = num3 - num4\n    result_3 = num1 * num2 * num3 * num4\n    result_4 = num1 / num2 / num3 / num4\n    result_5 = result_1 + result_2 + result_3 + result_4\n    return (result_1, result_2, result_3, result_4, result_5)", "input": "'3', '4/5', '6', '7/8'", "output": "(3.8, 5.125, 12.600000000000001, 0.7142857142857143, 22.239285714285717)", "imports": ["from fractions import Fraction"], "original_snippet": "from fractions import Fraction\ndef f(num1, num2, num3, num4):\n    if isinstance(num1, str):\n        num1 = float(num1)\n    if isinstance(num2, str):\n        num2 = Fraction(num2)\n    if isinstance(num3, str):\n        num3 = float(num3)\n    if isinstance(num4, str):\n        num4 = Fraction(num4)\n    result_1 = num1 + num2\n    result_2 = num3 - num4\n    result_3 = num1 * num2 * num3 * num4\n    result_4 = num1 / num2 / num3 / num4\n    result_5 = result_1 + result_2 + result_3 + result_4\n    return (result_1, result_2, result_3, result_4, result_5)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(inventory: list, sales_data: list) -> dict:\n    top_selling_items = {}\n    remaining_products = {}\n    inventory_set = set(inventory)\n    for item in sales_data:\n        if item in inventory_set:\n            if item in top_selling_items:\n                top_selling_items[item] += 1\n            else:\n                top_selling_items[item] = 1\n        elif item in remaining_products:\n            continue\n        else:\n            remaining_products[item] = True\n            inventory_set.add(item)\n    return top_selling_items", "input": "['apple', 'banana', 'orange', 'grape'],\n['apple', 'banana', 'banana', 'orange', 'apple', 'orange', 'orange', 'kiwi', 'mango']", "output": "{'apple': 2, 'banana': 2, 'orange': 3}", "imports": [], "original_snippet": "def f(inventory: list, sales_data: list) -> dict:\n    top_selling_items = {}\n    remaining_products = {}\n    inventory_set = set(inventory)\n    for item in sales_data:\n        if item in inventory_set:\n            if item in top_selling_items:\n                top_selling_items[item] += 1\n            else:\n                top_selling_items[item] = 1\n        elif item in remaining_products:\n            continue\n        else:\n            remaining_products[item] = True\n            inventory_set.add(item)\n    return top_selling_items", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(input_list: list) -> list:\n    total_positives = 0\n    total_evens = 0\n    output_values = []\n    for i in range(len(input_list) - 1, -1, -2):\n        pair_value = input_list[i] + input_list[i - 1]\n        output_values.insert(0, pair_value)\n    for i in range(len(output_values)):\n        if output_values[i] > 0:\n            total_positives += 1\n        if output_values[i] % 2 == 0:\n            total_evens += 1\n    output_values = [n for n in output_values if n % total_positives != 0]\n    output_value_mult = 1\n    for value in output_values:\n        output_value_mult *= value\n    for i in range(len(output_values) - 1, -1, -1):\n        output_values[i] = output_value_mult\n    return output_values", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]", "output": "[1729986783525, 1729986783525, 1729986783525, 1729986783525, 1729986783525, 1729986783525, 1729986783525, 1729986783525, 1729986783525, 1729986783525]", "imports": [], "original_snippet": "def f(input_list: list) -> list:\n    total_positives = 0\n    total_evens = 0\n    output_values = []\n    for i in range(len(input_list) - 1, -1, -2):\n        pair_value = input_list[i] + input_list[i - 1]\n        output_values.insert(0, pair_value)\n    for i in range(len(output_values)):\n        if output_values[i] > 0:\n            total_positives += 1\n        if output_values[i] % 2 == 0:\n            total_evens += 1\n    output_values = [n for n in output_values if n % total_positives != 0]\n    output_value_mult = 1\n    for value in output_values:\n        output_value_mult *= value\n    for i in range(len(output_values) - 1, -1, -1):\n        output_values[i] = output_value_mult\n    return output_values", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_data: dict):\n    group_info = input_data.get('Group', {})\n    result_dict = {}\n    for (name, member) in group_info.items():\n        info = member.get('info', '')\n        result_dict[info] = result_dict.get(info, 0) + 1\n    return result_dict", "input": "{\n    'Group': {\n        'Member1': {'age': 25, 'info': 'a'},\n        'Member2': {'age': 30, 'info': 'b'},\n        'Member3': {'age': 35, 'info': 'a'},\n    }\n}", "output": "{'a': 2, 'b': 1}", "imports": [], "original_snippet": "def f(input_data: dict):\n    group_info = input_data.get('Group', {})\n    result_dict = {}\n    for (name, member) in group_info.items():\n        info = member.get('info', '')\n        result_dict[info] = result_dict.get(info, 0) + 1\n    return result_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(l):\n    smallest = l[0]\n    largest = l[0]\n    sum_elements = 0\n    for i in l:\n        if i < smallest:\n            smallest = i\n        elif i > largest:\n            largest = i\n        sum_elements += i\n    return sum_elements - smallest * largest", "input": "[3, 5, 8, 1]", "output": "9", "imports": [], "original_snippet": "def f(l):\n    smallest = l[0]\n    largest = l[0]\n    sum_elements = 0\n    for i in l:\n        if i < smallest:\n            smallest = i\n        elif i > largest:\n            largest = i\n        sum_elements += i\n    return sum_elements - smallest * largest", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(sentence):\n    counts = {}\n    for word in sentence.split():\n        unique_words = set(word.lower())\n        unique_chars = set(word)\n        counts[word] = len(unique_words) * len(unique_chars)\n    return sum(counts.values())", "input": "\"my bare value\"", "output": "45", "imports": [], "original_snippet": "def f(sentence):\n    counts = {}\n    for word in sentence.split():\n        unique_words = set(word.lower())\n        unique_chars = set(word)\n        counts[word] = len(unique_words) * len(unique_chars)\n    return sum(counts.values())", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_string):\n    letter_counts = {}\n    for char in input_string:\n        letter_counts[char] = letter_counts.get(char, 0) + 1\n    if letter_counts:\n        min_count = min(letter_counts.values())\n        max_count = max(letter_counts.values())\n        return (min_count, max_count)\n    else:\n        return (None, None)", "input": "'apple bannana cat dove'", "output": "(1, 5)", "imports": [], "original_snippet": "def f(input_string):\n    letter_counts = {}\n    for char in input_string:\n        letter_counts[char] = letter_counts.get(char, 0) + 1\n    if letter_counts:\n        min_count = min(letter_counts.values())\n        max_count = max(letter_counts.values())\n        return (min_count, max_count)\n    else:\n        return (None, None)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(transactions: list) -> float:\n    balance = 0.0\n    for transaction in transactions:\n        if transaction[0] == 'deposit':\n            balance += transaction[1]\n        elif transaction[0] == 'withdrawal':\n            balance -= transaction[1]\n    return round(balance, 2)", "input": "[[\"deposit\", 500.00], [\"withdrawal\", 300.00], [\"deposit\", 200.00]]", "output": "400.0", "imports": [], "original_snippet": "def f(transactions: list) -> float:\n    balance = 0.0\n    for transaction in transactions:\n        if transaction[0] == 'deposit':\n            balance += transaction[1]\n        elif transaction[0] == 'withdrawal':\n            balance -= transaction[1]\n    return round(balance, 2)", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(input_str):\n    brackets = []\n    result = 0\n    for char in input_str:\n        if char == '{':\n            brackets.append(char)\n        elif char == '}':\n            if brackets and brackets[-1] == char:\n                result += 2\n                brackets.pop()\n            else:\n                brackets.append(char)\n    return result", "input": "'{}{{}{{}}{{}}}'", "output": "4", "imports": [], "original_snippet": "def f(input_str):\n    brackets = []\n    result = 0\n    for char in input_str:\n        if char == '{':\n            brackets.append(char)\n        elif char == '}':\n            if brackets and brackets[-1] == char:\n                result += 2\n                brackets.pop()\n            else:\n                brackets.append(char)\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_str: str) -> str:\n    words = input_str.split()\n    total_length = 0\n    result = []\n    for word in words:\n        total_length += len(word)\n    for word in words:\n        reversed_word = word[::-1]\n        reversed_vowels = reversed_word[::2]\n        result_word = reversed_word + reversed_vowels + reversed_word[::2][len(reversed_word):]\n        result.append(result_word)\n    return (' '.join(result), total_length)", "input": "input_str = \"I love computer science\"", "output": "('II evoleo retupmocrtpo ecneicsenis', 20)", "imports": [], "original_snippet": "def f(input_str: str) -> str:\n    words = input_str.split()\n    total_length = 0\n    result = []\n    for word in words:\n        total_length += len(word)\n    for word in words:\n        reversed_word = word[::-1]\n        reversed_vowels = reversed_word[::2]\n        result_word = reversed_word + reversed_vowels + reversed_word[::2][len(reversed_word):]\n        result.append(result_word)\n    return (' '.join(result), total_length)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(input_list):\n    state = {'total': 0}\n    for (idx, val) in enumerate(input_list):\n        if val % 2 == 0:\n            if val > 10:\n                state[f'even_{idx}'] = val ** 2\n                state['total'] += state[f'even_{idx}']\n            else:\n                state[f'even_{idx}'] = val\n                state['total'] += state[f'even_{idx}']\n        else:\n            state[f'odd_{idx}'] = val ** 3\n            state['total'] += state[f'odd_{idx}']\n    return state['total']", "input": "[7, 8, 9, 10, 11, 12]", "output": "2565", "imports": [], "original_snippet": "def f(input_list):\n    state = {'total': 0}\n    for (idx, val) in enumerate(input_list):\n        if val % 2 == 0:\n            if val > 10:\n                state[f'even_{idx}'] = val ** 2\n                state['total'] += state[f'even_{idx}']\n            else:\n                state[f'even_{idx}'] = val\n                state['total'] += state[f'even_{idx}']\n        else:\n            state[f'odd_{idx}'] = val ** 3\n            state['total'] += state[f'odd_{idx}']\n    return state['total']", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(s1: str, s2: str):\n    result = ''\n    for i in range(len(s1)):\n        if s1[i] == s2[i]:\n            result += chr((ord(s2[i]) + i) % 128)\n        else:\n            result += chr((ord(s2[i]) + i) // 10)\n    return result", "input": "'hello', 'world'", "output": "'\\x0b\\x0b\\x0bo\\n'", "imports": [], "original_snippet": "def f(s1: str, s2: str):\n    result = ''\n    for i in range(len(s1)):\n        if s1[i] == s2[i]:\n            result += chr((ord(s2[i]) + i) % 128)\n        else:\n            result += chr((ord(s2[i]) + i) // 10)\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(dag: dict):\n    paths = {node: [] for node in dag}\n    weights = {node: 0 for node in dag}\n    for node in dag:\n        for (adj_node, weight) in dag[node].items():\n            new_path = paths[node] + [adj_node]\n            new_weight = weights[node] + weight\n            if new_weight > weights[adj_node]:\n                paths[adj_node] = new_path\n                weights[adj_node] = new_weight\n    max_weight_node = max(weights, key=lambda k: weights[k])\n    return (max_weight_node, weights[max_weight_node], len(paths[max_weight_node]))", "input": "dag = {\n    'A': {'B': 5, 'C': 1},\n    'B': {'D': 7, 'E': 4},\n    'C': {'F': 3, 'G': 9},\n    'D': {'H': 2, 'I': 6},\n    'E': {'J': 8},\n    'F': {},\n    'G': {'J': 2},\n    'H': {},\n    'I': {'J': 3},\n    'J': {}\n}", "output": "('J', 21, 4)", "imports": [], "original_snippet": "def f(dag: dict):\n    paths = {node: [] for node in dag}\n    weights = {node: 0 for node in dag}\n    for node in dag:\n        for (adj_node, weight) in dag[node].items():\n            new_path = paths[node] + [adj_node]\n            new_weight = weights[node] + weight\n            if new_weight > weights[adj_node]:\n                paths[adj_node] = new_path\n                weights[adj_node] = new_weight\n    max_weight_node = max(weights, key=lambda k: weights[k])\n    return (max_weight_node, weights[max_weight_node], len(paths[max_weight_node]))", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(numbers: list) -> tuple:\n    sum = 0\n    product = 1\n    for num in numbers:\n        sum += num\n        product *= num\n    return (sum, product)", "input": "[1, 2, 3, 4, 5, 6]", "output": "(21, 720)", "imports": [], "original_snippet": "def f(numbers: list) -> tuple:\n    sum = 0\n    product = 1\n    for num in numbers:\n        sum += num\n        product *= num\n    return (sum, product)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(input_string: str, n: int) -> str:\n    result = ''\n    char_counter = {}\n    for char in input_string:\n        if char not in char_counter:\n            char_counter[char] = 1\n        else:\n            char_counter[char] += 1\n    sorted_chars = sorted(char_counter, key=char_counter.get, reverse=True)\n    for char in sorted_chars:\n        result += char * char_counter[char]\n    if len(result) < n:\n        return result + 'A' * (n - len(result))\n    return result", "input": "\"abcaabcb\", 7", "output": "'aaabbbcc'", "imports": [], "original_snippet": "def f(input_string: str, n: int) -> str:\n    result = ''\n    char_counter = {}\n    for char in input_string:\n        if char not in char_counter:\n            char_counter[char] = 1\n        else:\n            char_counter[char] += 1\n    sorted_chars = sorted(char_counter, key=char_counter.get, reverse=True)\n    for char in sorted_chars:\n        result += char * char_counter[char]\n    if len(result) < n:\n        return result + 'A' * (n - len(result))\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(numbers: list) -> list:\n    result = []\n    for number in numbers:\n        cube_root = number ** (1 / 3) * 2 ** (1 / 6) / (2 ** (1 / 6) - 1)\n        result.append(round(cube_root, 2))\n    return result", "input": "[1, 2, 3, 4, 5, 6, 7, 8]", "output": "[9.17, 11.55, 13.22, 14.55, 15.67, 16.66, 17.53, 18.33]", "imports": [], "original_snippet": "def f(numbers: list) -> list:\n    result = []\n    for number in numbers:\n        cube_root = number ** (1 / 3) * 2 ** (1 / 6) / (2 ** (1 / 6) - 1)\n        result.append(round(cube_root, 2))\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(strings: list) -> tuple:\n    output_list = []\n    total_count = 0\n    for string in strings:\n        alpha_char_count = len([char for char in string if char.isalpha() or char.isdigit()])\n        total_count += alpha_char_count\n        characters = sorted([char.lower() for char in string if char.isalpha()])\n        char_count_dict = {}\n        for char in characters:\n            char_count = characters.count(char)\n            key = char_count_dict.keys()\n            if char in key:\n                char_count_dict[char] += char_count\n            else:\n                char_count_dict[char] = char_count\n        pairs = [(char, char_count_dict[char]) for char in char_count_dict]\n        output_list.append(pairs)\n    return (output_list, total_count)", "input": "[ \"Hello#World\", \"xyz123!!\", \"Python-Programming!222\" ]", "output": "([[('d', 1), ('e', 1), ('h', 1), ('l', 9), ('o', 4), ('r', 1), ('w', 1)], [('x', 1), ('y', 1), ('z', 1)], [('a', 1), ('g', 4), ('h', 1), ('i', 1), ('m', 4), ('n', 4), ('o', 4), ('p', 4), ('r', 4), ('t', 1), ('y', 1)]], 36)", "imports": [], "original_snippet": "def f(strings: list) -> tuple:\n    output_list = []\n    total_count = 0\n    for string in strings:\n        alpha_char_count = len([char for char in string if char.isalpha() or char.isdigit()])\n        total_count += alpha_char_count\n        characters = sorted([char.lower() for char in string if char.isalpha()])\n        char_count_dict = {}\n        for char in characters:\n            char_count = characters.count(char)\n            key = char_count_dict.keys()\n            if char in key:\n                char_count_dict[char] += char_count\n            else:\n                char_count_dict[char] = char_count\n        pairs = [(char, char_count_dict[char]) for char in char_count_dict]\n        output_list.append(pairs)\n    return (output_list, total_count)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(data_items: list, stack_items: list, queue_items: list):\n    while queue_items or stack_items:\n        item = queue_items.pop() if queue_items else stack_items.pop()\n        data_items.append(item)\n    for i in range(len(data_items)):\n        for j in range(len(data_items) - 1 - i):\n            if data_items[j] > data_items[j + 1]:\n                (data_items[j], data_items[j + 1]) = (data_items[j + 1], data_items[j])\n    return data_items", "input": "[23, 85, 1776, 21], [225, 12], [373]", "output": "[12, 21, 23, 85, 225, 373, 1776]", "imports": [], "original_snippet": "def f(data_items: list, stack_items: list, queue_items: list):\n    while queue_items or stack_items:\n        item = queue_items.pop() if queue_items else stack_items.pop()\n        data_items.append(item)\n    for i in range(len(data_items)):\n        for j in range(len(data_items) - 1 - i):\n            if data_items[j] > data_items[j + 1]:\n                (data_items[j], data_items[j + 1]) = (data_items[j + 1], data_items[j])\n    return data_items", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(input_string):\n    input_string = input_string.lower()\n    letter_counts = {}\n    for char in input_string:\n        letter_counts[char] = letter_counts.get(char, 0) + 1\n    highest_freq = 0\n    lowest_freq = float('inf')\n    highest_chars = []\n    lowest_chars = []\n    for (char, freq) in letter_counts.items():\n        if freq > highest_freq:\n            highest_freq = freq\n            highest_chars = [char]\n        elif freq == highest_freq:\n            highest_chars.append(char)\n        if freq < lowest_freq:\n            lowest_freq = freq\n            lowest_chars = [char]\n        elif freq == lowest_freq:\n            lowest_chars.append(char)\n    result_string = ''.join(highest_chars) + ''.join(lowest_chars)\n    return result_string", "input": "'tHE rAy blur rAY'", "output": "' rtheblu'", "imports": [], "original_snippet": "def f(input_string):\n    input_string = input_string.lower()\n    letter_counts = {}\n    for char in input_string:\n        letter_counts[char] = letter_counts.get(char, 0) + 1\n    highest_freq = 0\n    lowest_freq = float('inf')\n    highest_chars = []\n    lowest_chars = []\n    for (char, freq) in letter_counts.items():\n        if freq > highest_freq:\n            highest_freq = freq\n            highest_chars = [char]\n        elif freq == highest_freq:\n            highest_chars.append(char)\n        if freq < lowest_freq:\n            lowest_freq = freq\n            lowest_chars = [char]\n        elif freq == lowest_freq:\n            lowest_chars.append(char)\n    result_string = ''.join(highest_chars) + ''.join(lowest_chars)\n    return result_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_dict: dict) -> dict:\n    if 'data' in input_dict:\n        transformed_data = {}\n        for item in input_dict['data']:\n            if isinstance(item, dict) and sorted(item.keys()) == ['id', 'value']:\n                (id_key, value) = (item['id'], item['value'])\n                transformed_data[id_key] = {'value': value}\n                if isinstance(value, str):\n                    transformed_data[id_key]['value'] = value.upper()\n        return transformed_data\n    else:\n        return {'error': \"Invalid input format: Missing 'data' key\"}", "input": "{'data': [{'id': 'A', 'value': 'apple'}, {'id': 'B', 'value': 'banana'}, {'id': 'C', 'value': 'orange'}]}", "output": "{'A': {'value': 'APPLE'}, 'B': {'value': 'BANANA'}, 'C': {'value': 'ORANGE'}}", "imports": [], "original_snippet": "def f(input_dict: dict) -> dict:\n    if 'data' in input_dict:\n        transformed_data = {}\n        for item in input_dict['data']:\n            if isinstance(item, dict) and sorted(item.keys()) == ['id', 'value']:\n                (id_key, value) = (item['id'], item['value'])\n                transformed_data[id_key] = {'value': value}\n                if isinstance(value, str):\n                    transformed_data[id_key]['value'] = value.upper()\n        return transformed_data\n    else:\n        return {'error': \"Invalid input format: Missing 'data' key\"}", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(numbers: list) -> int:\n    total = 0\n    for (i, num) in enumerate(numbers):\n        base = num - 1\n        if base > 1:\n            total += base * base\n    return total", "input": "[3, 5, 7, 9]", "output": "120", "imports": [], "original_snippet": "def f(numbers: list) -> int:\n    total = 0\n    for (i, num) in enumerate(numbers):\n        base = num - 1\n        if base > 1:\n            total += base * base\n    return total", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_string: str):\n    if 'K' in input_string or 'k' in input_string:\n        input_string = input_string.replace('K', 'K').replace('k', 'k')\n        input_string += input_string[::-1]\n    else:\n        input_string = input_string[::-1]\n    return input_string", "input": "'The firefox live'", "output": "'evil xoferif ehT'", "imports": [], "original_snippet": "def f(input_string: str):\n    if 'K' in input_string or 'k' in input_string:\n        input_string = input_string.replace('K', 'K').replace('k', 'k')\n        input_string += input_string[::-1]\n    else:\n        input_string = input_string[::-1]\n    return input_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(nums, k):\n    (min_num, max_num) = (min(nums), max(nums))\n    (min_diff, max_diff) = (min_num - k, max_num - k)\n    (min_diff_cnt, max_diff_cnt) = (nums.count(min_diff), nums.count(max_diff))\n    output = max_diff * max_diff_cnt + min_diff * (nums.count(min_num) - min_diff_cnt)\n    return output", "input": "[7, 2, 9, 5], 3", "output": "-1", "imports": [], "original_snippet": "def f(nums, k):\n    (min_num, max_num) = (min(nums), max(nums))\n    (min_diff, max_diff) = (min_num - k, max_num - k)\n    (min_diff_cnt, max_diff_cnt) = (nums.count(min_diff), nums.count(max_diff))\n    output = max_diff * max_diff_cnt + min_diff * (nums.count(min_num) - min_diff_cnt)\n    return output", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(input_list):\n    result = []\n    state = []\n    for (idx, num) in enumerate(input_list):\n        if idx % 3 == 0:\n            state.append(num)\n        else:\n            result.append(num)\n    result = sorted(result)\n    if len(state) > 1:\n        sum_of_states = sum(state)\n        avg_of_states = sum_of_states / len(state)\n        result.append(avg_of_states)\n    return result", "input": "[4, 1, 5, 2, 3, 6]", "output": "[1, 3, 5, 6, 3.0]", "imports": [], "original_snippet": "def f(input_list):\n    result = []\n    state = []\n    for (idx, num) in enumerate(input_list):\n        if idx % 3 == 0:\n            state.append(num)\n        else:\n            result.append(num)\n    result = sorted(result)\n    if len(state) > 1:\n        sum_of_states = sum(state)\n        avg_of_states = sum_of_states / len(state)\n        result.append(avg_of_states)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(lst):\n    return sorted(lst)", "input": "[7, 2, 9, 10, 1, 12, 11, 8, 3, 6, 4, 5]", "output": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]", "imports": [], "original_snippet": "def f(lst):\n    return sorted(lst)", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(data: dict) -> dict:\n    state = {'count': {}, 'replacement_map': {}, 'duplicates': []}\n    state['duplicates'] = {key: value for (key, value) in data.items() if len(value['values']) > 1}\n    for (key, value) in data.items():\n        for val in value['values']:\n            state['count'][val] = state['count'].get(val, 0) + 1\n            if val == key:\n                state['replacement_map'][val] = key.upper() + '_' + str(state['count'][val])\n            else:\n                state['replacement_map'][val] = key + '_' + str(state['count'][val])\n    for (key, value) in data.items():\n        new_values = [state['replacement_map'][val] for val in value['values']]\n        data[key]['values'] = list(filter(lambda x: x != key, new_values))\n        data[key]['values'] = list(dict.fromkeys(data[key]['values']))\n    return (data, state['duplicates'])", "input": "{'A': {'values': ['A', 'A', 'A', 'B', 'C']}, 'B': {'values': ['B', 'B', 'B', 'C']}, 'C': {'values': ['C', 'C', 'C', 'A']}}", "output": "({'A': {'values': ['C_4', 'B_4', 'C_5']}, 'B': {'values': ['B_4', 'C_5']}, 'C': {'values': ['C_5', 'C_4']}}, {'A': {'values': ['C_4', 'B_4', 'C_5']}, 'B': {'values': ['B_4', 'C_5']}, 'C': {'values': ['C_5', 'C_4']}})", "imports": [], "original_snippet": "def f(data: dict) -> dict:\n    state = {'count': {}, 'replacement_map': {}, 'duplicates': []}\n    state['duplicates'] = {key: value for (key, value) in data.items() if len(value['values']) > 1}\n    for (key, value) in data.items():\n        for val in value['values']:\n            state['count'][val] = state['count'].get(val, 0) + 1\n            if val == key:\n                state['replacement_map'][val] = key.upper() + '_' + str(state['count'][val])\n            else:\n                state['replacement_map'][val] = key + '_' + str(state['count'][val])\n    for (key, value) in data.items():\n        new_values = [state['replacement_map'][val] for val in value['values']]\n        data[key]['values'] = list(filter(lambda x: x != key, new_values))\n        data[key]['values'] = list(dict.fromkeys(data[key]['values']))\n    return (data, state['duplicates'])", "composite_functions": [], "_input_type": "dict", "_output_type": "tuple"}
{"snippet": "def f(starting_list: list, input_list: list):\n    choose = sorted([elem for elem in starting_list if elem not in input_list], reverse=True)\n    new = [elem for elem in input_list if elem not in starting_list]\n    common_elems = [elem for elem in starting_list if elem in input_list]\n    count = 0\n    for elem in input_list:\n        if elem in common_elems and elem not in choose:\n            count += 1\n    output_dict = {'choose': choose, 'new': new, 'elements': common_elems, 'count': count}\n    return output_dict", "input": "[1, 2, 3, 4, 5], [3, 4, 5, 6, 7]", "output": "{'choose': [2, 1], 'new': [6, 7], 'elements': [3, 4, 5], 'count': 3}", "imports": [], "original_snippet": "def f(starting_list: list, input_list: list):\n    choose = sorted([elem for elem in starting_list if elem not in input_list], reverse=True)\n    new = [elem for elem in input_list if elem not in starting_list]\n    common_elems = [elem for elem in starting_list if elem in input_list]\n    count = 0\n    for elem in input_list:\n        if elem in common_elems and elem not in choose:\n            count += 1\n    output_dict = {'choose': choose, 'new': new, 'elements': common_elems, 'count': count}\n    return output_dict", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(numbers):\n    result = []\n    for (i, num) in enumerate(numbers):\n        if i <= len(numbers) - 2:\n            result.append(num + numbers[i + 1])\n    return result", "input": "[1, 2, 3, 4, 5]", "output": "[3, 5, 7, 9]", "imports": [], "original_snippet": "def f(numbers):\n    result = []\n    for (i, num) in enumerate(numbers):\n        if i <= len(numbers) - 2:\n            result.append(num + numbers[i + 1])\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(output: int) -> int:\n    return output", "input": "2772", "output": "2772", "imports": [], "original_snippet": "def f(output: int) -> int:\n    return output", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(my_string):\n    transformed_string = my_string\n    even_letters_sum = 0\n    odd_letters_sum = 0\n    for (pos, letter) in enumerate(transformed_string):\n        if letter.isalnum():\n            if ord(letter.lower()) % 2 == 0:\n                even_letters_sum += ord(letter.lower())\n            else:\n                odd_letters_sum += ord(letter.lower())\n        else:\n            continue\n    my_int = odd_letters_sum - even_letters_sum // 10\n    return my_int", "input": "\"05gtw89sdl37dfg7348833724b837b92ydf\"", "output": "1063", "imports": [], "original_snippet": "def f(my_string):\n    transformed_string = my_string\n    even_letters_sum = 0\n    odd_letters_sum = 0\n    for (pos, letter) in enumerate(transformed_string):\n        if letter.isalnum():\n            if ord(letter.lower()) % 2 == 0:\n                even_letters_sum += ord(letter.lower())\n            else:\n                odd_letters_sum += ord(letter.lower())\n        else:\n            continue\n    my_int = odd_letters_sum - even_letters_sum // 10\n    return my_int", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_list):\n    hashed = {}\n    final = 0\n    for (idx, val) in enumerate(input_list):\n        if val % 2 == 0:\n            if val > 10:\n                hashed[f'even_{idx}'] = val ** 2\n                final += hashed[f'even_{idx}']\n            else:\n                hashed[f'even_{idx}'] = val\n                final += hashed[f'even_{idx}']\n        else:\n            hashed[f'odd_{idx}'] = val ** 3\n            final += hashed[f'odd_{idx}']\n    return final", "input": "[7, 8, 9, 10, 11, 12]", "output": "2565", "imports": [], "original_snippet": "def f(input_list):\n    hashed = {}\n    final = 0\n    for (idx, val) in enumerate(input_list):\n        if val % 2 == 0:\n            if val > 10:\n                hashed[f'even_{idx}'] = val ** 2\n                final += hashed[f'even_{idx}']\n            else:\n                hashed[f'even_{idx}'] = val\n                final += hashed[f'even_{idx}']\n        else:\n            hashed[f'odd_{idx}'] = val ** 3\n            final += hashed[f'odd_{idx}']\n    return final", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(n: int, count_stack: int):\n    record_stack = []\n    stack = [(n, count_stack)]\n    while stack:\n        (n, count_stack) = stack.pop()\n        if count_stack > 1:\n            record_stack.append((1, n))\n            count_stack -= 1\n        else:\n            break\n    return (len(record_stack), n)", "input": "4,7", "output": "(1, 4)", "imports": [], "original_snippet": "def f(n: int, count_stack: int):\n    record_stack = []\n    stack = [(n, count_stack)]\n    while stack:\n        (n, count_stack) = stack.pop()\n        if count_stack > 1:\n            record_stack.append((1, n))\n            count_stack -= 1\n        else:\n            break\n    return (len(record_stack), n)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(input_list):\n    output_dict = {}\n    even_count = 0\n    odd_count = 0\n    multiple_of_3_count = 0\n    multiple_of_5_count = 0\n    for num in input_list:\n        if num % 2 == 0:\n            processed_num = num * 3\n            even_count += 1\n        elif num % 3 == 0:\n            processed_num = num * 2\n            multiple_of_3_count += 1\n        elif num % 5 == 0:\n            processed_num = num * 5\n            multiple_of_5_count += 1\n        else:\n            processed_num = num\n        if processed_num % 2 == 1:\n            odd_count += 1\n        output_dict[num] = processed_num\n    output_dict['counts'] = {'even_count': even_count, 'odd_count': odd_count, 'multiple_of_3_count': multiple_of_3_count, 'multiple_of_5_count': multiple_of_5_count}\n    return output_dict", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "{1: 1, 2: 6, 3: 6, 4: 12, 5: 25, 6: 18, 7: 7, 8: 24, 9: 18, 10: 30, 'counts': {'even_count': 5, 'odd_count': 3, 'multiple_of_3_count': 2, 'multiple_of_5_count': 1}}", "imports": [], "original_snippet": "def f(input_list):\n    output_dict = {}\n    even_count = 0\n    odd_count = 0\n    multiple_of_3_count = 0\n    multiple_of_5_count = 0\n    for num in input_list:\n        if num % 2 == 0:\n            processed_num = num * 3\n            even_count += 1\n        elif num % 3 == 0:\n            processed_num = num * 2\n            multiple_of_3_count += 1\n        elif num % 5 == 0:\n            processed_num = num * 5\n            multiple_of_5_count += 1\n        else:\n            processed_num = num\n        if processed_num % 2 == 1:\n            odd_count += 1\n        output_dict[num] = processed_num\n    output_dict['counts'] = {'even_count': even_count, 'odd_count': odd_count, 'multiple_of_3_count': multiple_of_3_count, 'multiple_of_5_count': multiple_of_5_count}\n    return output_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(numbers: list, input_string: str) -> str:\n    result = ''\n    for i in range(len(numbers)):\n        if numbers[i] % 2 == 0:\n            result += chr((ord(input_string[i]) + i) // 2)\n        else:\n            result += chr((ord(input_string[i]) + 49) ** 2 // numbers[i])\n    return result", "input": "[1, 2, 3, 4], 'Hello'", "output": "'\u39313\u20187'", "imports": [], "original_snippet": "def f(numbers: list, input_string: str) -> str:\n    result = ''\n    for i in range(len(numbers)):\n        if numbers[i] % 2 == 0:\n            result += chr((ord(input_string[i]) + i) // 2)\n        else:\n            result += chr((ord(input_string[i]) + 49) ** 2 // numbers[i])\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(n):\n    return [i for i in range(n, 1000) if i % n != 0]", "input": "5", "output": "[6, 7, 8, 9, 11, 12, 13, 14, 16, 17, 18, 19, 21, 22, 23, 24, 26, 27, 28, 29, 31, 32, 33, 34, 36, 37, 38, 39, 41, 42, 43, 44, 46, 47, 48, 49, 51, 52, 53, 54, 56, 57, 58, 59, 61, 62, 63, 64, 66, 67, 68,... 951, 952, 953, 954, 956, 957, 958, 959, 961, 962, 963, 964, 966, 967, 968, 969, 971, 972, 973, 974, 976, 977, 978, 979, 981, 982, 983, 984, 986, 987, 988, 989, 991, 992, 993, 994, 996, 997, 998, 999]", "imports": [], "original_snippet": "def f(n):\n    return [i for i in range(n, 1000) if i % n != 0]", "composite_functions": [], "_input_type": "int", "_output_type": "str"}
{"snippet": "def f(input_list):\n    counter = 0\n    for i in range(len(input_list)):\n        if counter % 3 == 0:\n            input_list[i] = input_list[i] * 11\n        elif counter % 3 == 1:\n            input_list[i] = input_list[i] ** 2\n        elif counter % 3 == 2:\n            input_list[i] = input_list[i] - 5\n        counter += 1\n    return (input_list, counter)", "input": "[-5, 6, 5, 2, 1]", "output": "([-55, 36, 0, 22, 1], 5)", "imports": [], "original_snippet": "def f(input_list):\n    counter = 0\n    for i in range(len(input_list)):\n        if counter % 3 == 0:\n            input_list[i] = input_list[i] * 11\n        elif counter % 3 == 1:\n            input_list[i] = input_list[i] ** 2\n        elif counter % 3 == 2:\n            input_list[i] = input_list[i] - 5\n        counter += 1\n    return (input_list, counter)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(input_str: str, num: int):\n    reversed_str = input_str[::-1]\n    num_sequence = []\n    for (i, char) in enumerate(reversed_str):\n        char_ascii = ord(char.lower())\n        char_sqrt = char_ascii // 10\n        result = char_sqrt + num\n        num_sequence.append(result)\n    char_sequence = [chr(num) for num in num_sequence]\n    concatenated_str = ''.join(char_sequence)\n    return concatenated_str[::-1]", "input": "\"The quick brown fox jumped over the lazy dog\", 7", "output": "'\\x12\\x11\\x11\\n\\x12\\x12\\x11\\x10\\x11\\n\\x10\\x12\\x12\\x12\\x12\\n\\x11\\x12\\x13\\n\\x11\\x12\\x11\\x12\\x11\\x11\\n\\x12\\x12\\x11\\x12\\n\\x12\\x11\\x11\\n\\x11\\x10\\x13\\x13\\n\\x11\\x12\\x11'", "imports": [], "original_snippet": "def f(input_str: str, num: int):\n    reversed_str = input_str[::-1]\n    num_sequence = []\n    for (i, char) in enumerate(reversed_str):\n        char_ascii = ord(char.lower())\n        char_sqrt = char_ascii // 10\n        result = char_sqrt + num\n        num_sequence.append(result)\n    char_sequence = [chr(num) for num in num_sequence]\n    concatenated_str = ''.join(char_sequence)\n    return concatenated_str[::-1]", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(input_str):\n    tracker = {}\n    for char in input_str:\n        tracker[char] = tracker.get(char, 0) + 1\n    max_count = max(tracker.values())\n    min_count = min(tracker.values())\n    most_occurring_chars = [key for (key, value) in tracker.items() if value == max_count]\n    least_occurring_chars = [key for (key, value) in tracker.items() if value == min_count]\n    max_char = max(most_occurring_chars, key=lambda x: ord(x))\n    min_char = min(least_occurring_chars, key=lambda x: ord(x))\n    if tracker[max_char] < 2:\n        output = input_str.replace(max_char, min_char)\n    else:\n        output = input_str.replace(min_char, max_char)\n    return output", "input": "'Bumblebee and the Beetles delight'", "output": "'Bumblebee end the Beetles delight'", "imports": [], "original_snippet": "def f(input_str):\n    tracker = {}\n    for char in input_str:\n        tracker[char] = tracker.get(char, 0) + 1\n    max_count = max(tracker.values())\n    min_count = min(tracker.values())\n    most_occurring_chars = [key for (key, value) in tracker.items() if value == max_count]\n    least_occurring_chars = [key for (key, value) in tracker.items() if value == min_count]\n    max_char = max(most_occurring_chars, key=lambda x: ord(x))\n    min_char = min(least_occurring_chars, key=lambda x: ord(x))\n    if tracker[max_char] < 2:\n        output = input_str.replace(max_char, min_char)\n    else:\n        output = input_str.replace(min_char, max_char)\n    return output", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_data: dict):\n    input_data['Member1']['info'] = 'A'\n    return input_data", "input": "{\n    'Member1': {'age': 25, 'info': 'a'},\n    'Member2': {'age': 30, 'info': 'b'},\n    'Member3': {'age': 35, 'info': 'c'},\n}", "output": "{'Member1': {'age': 25, 'info': 'A'}, 'Member2': {'age': 30, 'info': 'b'}, 'Member3': {'age': 35, 'info': 'c'}}", "imports": [], "original_snippet": "def f(input_data: dict):\n    input_data['Member1']['info'] = 'A'\n    return input_data", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(input_string: str, input_list: list) -> dict:\n    final_dict = {'string': input_string}\n    for dictionary in input_list:\n        transformed_dict = {}\n        values = [value for (_, value) in sorted(dictionary.items(), key=lambda item: item[1], reverse=True)]\n        final_dict.update({k: v for (k, v) in zip(range(len(values)), values)})\n        transformed_dict = {key: {key: value} for (key, value) in zip(range(len(values)), values)}\n        final_dict['keys'] = list(transformed_dict.keys())\n    return final_dict", "input": "\"Python Programming\", [{\"a\": 3}, {\"b\": 1}, {\"c\": 5}, {}]", "output": "{'string': 'Python Programming', 0: 5, 'keys': []}", "imports": [], "original_snippet": "def f(input_string: str, input_list: list) -> dict:\n    final_dict = {'string': input_string}\n    for dictionary in input_list:\n        transformed_dict = {}\n        values = [value for (_, value) in sorted(dictionary.items(), key=lambda item: item[1], reverse=True)]\n        final_dict.update({k: v for (k, v) in zip(range(len(values)), values)})\n        transformed_dict = {key: {key: value} for (key, value) in zip(range(len(values)), values)}\n        final_dict['keys'] = list(transformed_dict.keys())\n    return final_dict", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(input_char: str) -> str:\n    replacements = {'a': '1', 'b': '3', 'c': '5', 'd': '7', 'e': '9', 'f': '11', 'g': '13', 'h': '15', 'i': '17', 'j': '19'}\n    if input_char in replacements:\n        return replacements[input_char]\n    output_number = 0\n    for char_number in map(replacements.get, input_char):\n        if char_number:\n            output_number += 2\n            output_number *= int(char_number)\n    return str(output_number)", "input": "'d'", "output": "'7'", "imports": [], "original_snippet": "def f(input_char: str) -> str:\n    replacements = {'a': '1', 'b': '3', 'c': '5', 'd': '7', 'e': '9', 'f': '11', 'g': '13', 'h': '15', 'i': '17', 'j': '19'}\n    if input_char in replacements:\n        return replacements[input_char]\n    output_number = 0\n    for char_number in map(replacements.get, input_char):\n        if char_number:\n            output_number += 2\n            output_number *= int(char_number)\n    return str(output_number)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(binary_string):\n    decimal_value = 0\n    binary_length = len(binary_string)\n    for i in range(binary_length):\n        bit_value = binary_string[binary_length - 1 - i]\n        decimal_value += 16 ** i * int(bit_value)\n    return decimal_value", "input": "'101011'", "output": "1052689", "imports": [], "original_snippet": "def f(binary_string):\n    decimal_value = 0\n    binary_length = len(binary_string)\n    for i in range(binary_length):\n        bit_value = binary_string[binary_length - 1 - i]\n        decimal_value += 16 ** i * int(bit_value)\n    return decimal_value", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_number):\n    digit_counts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}\n    str_input = str(input_number)\n    for digit in str_input:\n        if digit in digit_counts:\n            digit_counts[len(digit)] += 1\n    return digit_counts", "input": "1920", "output": "{1: 0, 2: 0, 3: 0, 4: 0, 5: 0}", "imports": [], "original_snippet": "def f(input_number):\n    digit_counts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}\n    str_input = str(input_number)\n    for digit in str_input:\n        if digit in digit_counts:\n            digit_counts[len(digit)] += 1\n    return digit_counts", "composite_functions": [], "_input_type": "int", "_output_type": "dict"}
{"snippet": "def f(input_list):\n    output_list = [n * 2 for n in input_list]\n    return sum(output_list)", "input": "[1, 2, 3, 4, 5]", "output": "30", "imports": [], "original_snippet": "def f(input_list):\n    output_list = [n * 2 for n in input_list]\n    return sum(output_list)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(output: str) -> str:\n    decoded_string = output\n    even_letters_sum = 0\n    odd_letters_sum = 0\n    for (pos, l) in enumerate(decoded_string):\n        if l.isalnum():\n            if (ord(l.lower()) + 10) % 2 == 0:\n                even_letters_sum += ord(l.lower()) + 10\n            else:\n                odd_letters_sum += ord(l.lower()) + 10\n        else:\n            continue\n    my_int = odd_letters_sum - even_letters_sum\n    return chr(my_int)", "input": "\"Discrete Math: Trees and Data Structures ,1382944945,3078096,944512875\"", "output": "'\u0188'", "imports": [], "original_snippet": "def f(output: str) -> str:\n    decoded_string = output\n    even_letters_sum = 0\n    odd_letters_sum = 0\n    for (pos, l) in enumerate(decoded_string):\n        if l.isalnum():\n            if (ord(l.lower()) + 10) % 2 == 0:\n                even_letters_sum += ord(l.lower()) + 10\n            else:\n                odd_letters_sum += ord(l.lower()) + 10\n        else:\n            continue\n    my_int = odd_letters_sum - even_letters_sum\n    return chr(my_int)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(s: str):\n    result = ''\n    for char in s:\n        if char == 'a':\n            result += 'b'\n        elif char == 'b':\n            result += 'a'\n        elif char == 'c':\n            result += 'd'\n        elif char == 'd':\n            result += 'e'\n        elif char == 'e':\n            result += 'c'\n        else:\n            result += char\n    return result", "input": "'abcde'", "output": "'badec'", "imports": [], "original_snippet": "def f(s: str):\n    result = ''\n    for char in s:\n        if char == 'a':\n            result += 'b'\n        elif char == 'b':\n            result += 'a'\n        elif char == 'c':\n            result += 'd'\n        elif char == 'd':\n            result += 'e'\n        elif char == 'e':\n            result += 'c'\n        else:\n            result += char\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(num: int) -> int:\n    return num * num + num * num * num", "input": "f(3) + f(33)", "output": "50909434514172", "imports": [], "original_snippet": "def f(num: int) -> int:\n    return num * num + num * num * num", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_string: str):\n    result = {}\n    letter_counts = {}\n    for char in input_string:\n        if char.isalpha():\n            if char not in letter_counts:\n                letter_counts[char] = 1\n            else:\n                letter_counts[char] += 1\n    avg_count = sum(letter_counts.values()) / len(letter_counts)\n    closest_letter = min(letter_counts, key=lambda letter: abs(letter_counts[letter] - avg_count))\n    for char in letter_counts:\n        if letter_counts[char] > 3:\n            result[char] = letter_counts[char]\n    if len(result) > 1:\n        result[closest_letter] = letter_counts[closest_letter]\n    return result", "input": "'aahdaahhaaad'", "output": "{'a': 7}", "imports": [], "original_snippet": "def f(input_string: str):\n    result = {}\n    letter_counts = {}\n    for char in input_string:\n        if char.isalpha():\n            if char not in letter_counts:\n                letter_counts[char] = 1\n            else:\n                letter_counts[char] += 1\n    avg_count = sum(letter_counts.values()) / len(letter_counts)\n    closest_letter = min(letter_counts, key=lambda letter: abs(letter_counts[letter] - avg_count))\n    for char in letter_counts:\n        if letter_counts[char] > 3:\n            result[char] = letter_counts[char]\n    if len(result) > 1:\n        result[closest_letter] = letter_counts[closest_letter]\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "dict"}
{"snippet": "def f(input_string: str) -> dict:\n    result_dict = {}\n    count_dict = {}\n    for (i, char) in enumerate(input_string):\n        if char.isalpha():\n            if char.lower() != char.upper():\n                count_dict[char, 'lower'] = count_dict.get((char, 'lower'), 0) + 1\n            else:\n                count_dict[char, 'upper'] = count_dict.get((char, 'upper'), 0) + 1\n        else:\n            count_dict[f'special_char_{char}'] = count_dict.get(f'special_char_{char}', 0) + 1\n    for (key, value) in count_dict.items():\n        if key[0] in ('A', 'B', 'C', 'D'):\n            result_dict[key] = f\"Generated from '{key}'\"\n        else:\n            result_dict[key] = f\"Generated from '(key)'\"\n    return result_dict", "input": "'ABCD!hello@world!'", "output": "{('A', 'lower'): \"Generated from '('A', 'lower')'\", ('B', 'lower'): \"Generated from '('B', 'lower')'\", ('C', 'lower'): \"Generated from '('C', 'lower')'\", ('D', 'lower'): \"Generated from '('D', 'lower'...r'): \"Generated from '(key)'\", 'special_char_@': \"Generated from '(key)'\", ('w', 'lower'): \"Generated from '(key)'\", ('r', 'lower'): \"Generated from '(key)'\", ('d', 'lower'): \"Generated from '(key)'\"}", "imports": [], "original_snippet": "def f(input_string: str) -> dict:\n    result_dict = {}\n    count_dict = {}\n    for (i, char) in enumerate(input_string):\n        if char.isalpha():\n            if char.lower() != char.upper():\n                count_dict[char, 'lower'] = count_dict.get((char, 'lower'), 0) + 1\n            else:\n                count_dict[char, 'upper'] = count_dict.get((char, 'upper'), 0) + 1\n        else:\n            count_dict[f'special_char_{char}'] = count_dict.get(f'special_char_{char}', 0) + 1\n    for (key, value) in count_dict.items():\n        if key[0] in ('A', 'B', 'C', 'D'):\n            result_dict[key] = f\"Generated from '{key}'\"\n        else:\n            result_dict[key] = f\"Generated from '(key)'\"\n    return result_dict", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(s: str):\n    reverse = s[::-1]\n    len_s = len(s)\n    dp = [[0 for i in range(len_s + 1)] for j in range(len_s + 1)]\n    for i in reversed(range(len_s)):\n        for j in reversed(range(len_s)):\n            if s[i] == reverse[j]:\n                dp[i][j] = 1 + dp[i + 1][j + 1]\n            else:\n                dp[i][j] = max(dp[i][j + 1], dp[i + 1][j])\n    return len_s - dp[0][0]", "input": "'level'", "output": "0", "imports": [], "original_snippet": "def f(s: str):\n    reverse = s[::-1]\n    len_s = len(s)\n    dp = [[0 for i in range(len_s + 1)] for j in range(len_s + 1)]\n    for i in reversed(range(len_s)):\n        for j in reversed(range(len_s)):\n            if s[i] == reverse[j]:\n                dp[i][j] = 1 + dp[i + 1][j + 1]\n            else:\n                dp[i][j] = max(dp[i][j + 1], dp[i + 1][j])\n    return len_s - dp[0][0]", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_string: str, input_integer: int) -> int:\n    data_structure = {}\n    for char in input_string:\n        data_structure[char] = data_structure.get(char, 0) + 1\n    result = 0\n    for value in data_structure.values():\n        if value % input_integer == 0:\n            result += value // input_integer\n        else:\n            result += value // input_integer + 1\n    return result", "input": "'abcabcabcabcabcabcabcabcabc', 3", "output": "9", "imports": [], "original_snippet": "def f(input_string: str, input_integer: int) -> int:\n    data_structure = {}\n    for char in input_string:\n        data_structure[char] = data_structure.get(char, 0) + 1\n    result = 0\n    for value in data_structure.values():\n        if value % input_integer == 0:\n            result += value // input_integer\n        else:\n            result += value // input_integer + 1\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(list):\n    n = len(list)\n    (max_num, min_num) = (float('-inf'), float('inf'))\n    (maxnum_indeces, minnum_indeces) = ([], [])\n    for i in range(n):\n        if list[i] > max_num:\n            maxnum_indeces.append(i)\n            max_num = list[i]\n        if list[i] < min_num:\n            minnum_indeces.append(i)\n            min_num = list[i]\n    return [min_num, max_num]", "input": "[1, 2, 3, 4, 5, 6]", "output": "[1, 6]", "imports": [], "original_snippet": "def f(list):\n    n = len(list)\n    (max_num, min_num) = (float('-inf'), float('inf'))\n    (maxnum_indeces, minnum_indeces) = ([], [])\n    for i in range(n):\n        if list[i] > max_num:\n            maxnum_indeces.append(i)\n            max_num = list[i]\n        if list[i] < min_num:\n            minnum_indeces.append(i)\n            min_num = list[i]\n    return [min_num, max_num]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(list):\n    if not list:\n        return None\n    sum_values = 0\n    for number in list:\n        if number < 0:\n            return None\n        elif number % 2 == 0:\n            sum_values = 2 * (sum_values + number)\n        else:\n            sum_values = 2 * sum_values + number\n    return sum_values", "input": "[-1, 0, 2, 4]", "output": "None", "imports": [], "original_snippet": "def f(list):\n    if not list:\n        return None\n    sum_values = 0\n    for number in list:\n        if number < 0:\n            return None\n        elif number % 2 == 0:\n            sum_values = 2 * (sum_values + number)\n        else:\n            sum_values = 2 * sum_values + number\n    return sum_values", "composite_functions": [], "_input_type": "list", "_output_type": "NoneType"}
{"snippet": "def f(input_list):\n    sorted_list = sorted(input_list)\n    total_products = 1\n    for i in range(len(sorted_list)):\n        total_products *= sorted_list[i]\n    return total_products", "input": "[2, 3, 4]", "output": "24", "imports": [], "original_snippet": "def f(input_list):\n    sorted_list = sorted(input_list)\n    total_products = 1\n    for i in range(len(sorted_list)):\n        total_products *= sorted_list[i]\n    return total_products", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_number):\n    digit_counts = [0] * 10\n    place_values = [1, 10, 100, 1000, 10000, 100000, 1000000, 10000000, 100000000, 1000000000]\n    placeholder_value = 1\n    while placeholder_value <= input_number:\n        divisor = input_number // placeholder_value\n        digit_counts[len(str(divisor))] += 1\n        input_number %= placeholder_value\n        placeholder_value *= 10\n    return digit_counts", "input": "123456", "output": "[0, 0, 0, 0, 0, 0, 1, 0, 0, 0]", "imports": [], "original_snippet": "def f(input_number):\n    digit_counts = [0] * 10\n    place_values = [1, 10, 100, 1000, 10000, 100000, 1000000, 10000000, 100000000, 1000000000]\n    placeholder_value = 1\n    while placeholder_value <= input_number:\n        divisor = input_number // placeholder_value\n        digit_counts[len(str(divisor))] += 1\n        input_number %= placeholder_value\n        placeholder_value *= 10\n    return digit_counts", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(team_points: dict):\n    sorted_teams = {}\n    for (team, points) in team_points.items():\n        sorted_teams[team] = points\n    sorted_teams = dict(sorted(sorted_teams.items(), key=lambda x: x[1], reverse=True))\n    return sorted_teams", "input": "{'team1': 10, 'team2': 5, 'team3': 25, 'team4': 7}", "output": "{'team3': 25, 'team1': 10, 'team4': 7, 'team2': 5}", "imports": [], "original_snippet": "def f(team_points: dict):\n    sorted_teams = {}\n    for (team, points) in team_points.items():\n        sorted_teams[team] = points\n    sorted_teams = dict(sorted(sorted_teams.items(), key=lambda x: x[1], reverse=True))\n    return sorted_teams", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(text: str):\n    text = text.replace('\\n', ' ').lower()\n    char_frequency = {}\n    for char in text:\n        if char not in [' ', ',', '.', '?', '!', \"'\", '\"']:\n            if char in char_frequency:\n                char_frequency[char] += 1\n            else:\n                char_frequency[char] = 1\n    return char_frequency", "input": "\"Hello there! How are you doing today? I hope things are great.\"", "output": "{'h': 5, 'e': 7, 'l': 2, 'o': 6, 't': 4, 'r': 4, 'w': 1, 'a': 4, 'y': 2, 'u': 1, 'd': 2, 'i': 3, 'n': 2, 'g': 3, 'p': 1, 's': 1}", "imports": [], "original_snippet": "def f(text: str):\n    text = text.replace('\\n', ' ').lower()\n    char_frequency = {}\n    for char in text:\n        if char not in [' ', ',', '.', '?', '!', \"'\", '\"']:\n            if char in char_frequency:\n                char_frequency[char] += 1\n            else:\n                char_frequency[char] = 1\n    return char_frequency", "composite_functions": [], "_input_type": "str", "_output_type": "dict"}
{"snippet": "def f(input_string: str) -> str:\n    result_str = ''\n    state = {'chars': 0}\n    for char in input_string:\n        if char.isalpha():\n            state['chars'] += 1\n            if state['chars'] % 2 == 0:\n                result_str += char.upper()\n            else:\n                result_str += char.lower()\n        elif char.isdigit():\n            result_str += char\n    return result_str", "input": "'abcdefg123456'", "output": "'aBcDeFg123456'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    result_str = ''\n    state = {'chars': 0}\n    for char in input_string:\n        if char.isalpha():\n            state['chars'] += 1\n            if state['chars'] % 2 == 0:\n                result_str += char.upper()\n            else:\n                result_str += char.lower()\n        elif char.isdigit():\n            result_str += char\n    return result_str", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(data):\n    count = 0\n    result = ''\n    for i in range(len(data)):\n        if i % 2 == 0:\n            if data[i] not in ['a', 'e', 'i', 'o', 'u']:\n                result += data[i]\n                count += 1\n            else:\n                result += data[i].upper()\n    return [count, result]", "input": "'apple orange pineapple pinapple cucumber grapes'", "output": "[14, 'ApEOAg IEpl IApEccmE rps']", "imports": [], "original_snippet": "def f(data):\n    count = 0\n    result = ''\n    for i in range(len(data)):\n        if i % 2 == 0:\n            if data[i] not in ['a', 'e', 'i', 'o', 'u']:\n                result += data[i]\n                count += 1\n            else:\n                result += data[i].upper()\n    return [count, result]", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(x):\n    a = x[::-1]\n    b = ''\n    for i in range(len(x)):\n        if x[i].isalpha():\n            a = a[1:] + x[i]\n    for char in a:\n        if char == ' ':\n            b += char\n        elif char.isdigit():\n            b += str(a.index(char))\n    try:\n        c = b[:len(b) // 2]\n        d = b[len(b) // 2:]\n        e = ''\n        for i in range(len(c)):\n            e += c[i]\n            if i < len(d):\n                e += d[i]\n        return e\n    except IndexError:\n        return ''", "input": "'Hello World!'", "output": "''", "imports": [], "original_snippet": "def f(x):\n    a = x[::-1]\n    b = ''\n    for i in range(len(x)):\n        if x[i].isalpha():\n            a = a[1:] + x[i]\n    for char in a:\n        if char == ' ':\n            b += char\n        elif char.isdigit():\n            b += str(a.index(char))\n    try:\n        c = b[:len(b) // 2]\n        d = b[len(b) // 2:]\n        e = ''\n        for i in range(len(c)):\n            e += c[i]\n            if i < len(d):\n                e += d[i]\n        return e\n    except IndexError:\n        return ''", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_list):\n    state = [0, 0]\n    for (num_idx, num) in enumerate(input_list):\n        state[0] = num_idx\n        if num % 3 == 0 or num % 5 == 0:\n            state[1] += num ** 2\n    return state[1]", "input": "[3, 5, 6, 10, 15]", "output": "395", "imports": [], "original_snippet": "def f(input_list):\n    state = [0, 0]\n    for (num_idx, num) in enumerate(input_list):\n        state[0] = num_idx\n        if num % 3 == 0 or num % 5 == 0:\n            state[1] += num ** 2\n    return state[1]", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(fraction, input_string):\n    (base_13_num, denominator) = fraction.split('/')\n    modified_string = ''\n    denominator_int = int(denominator)\n    fractional = int(base_13_num) / denominator_int\n    base_13_fraction = [int(char) if char in '0123456789' else 9 - int(char) for char in base_13_num[::-1]]\n    while fractional > 0:\n        fractional *= denominator_int\n        digit = int(fractional)\n        fractional -= digit\n        modified_string = str(digit) + modified_string\n    input_string_list = list(input_string)\n    for (i, char) in enumerate(modified_string):\n        if char != '0':\n            if input_string_list[i] != '1':\n                input_string_list[i + 1] = str(1 - (int(input_string_list[i]) + int(char)) % 2)\n    return ''.join(input_string_list)", "input": "'1/16', '0101011'", "output": "'0001011'", "imports": [], "original_snippet": "def f(fraction, input_string):\n    (base_13_num, denominator) = fraction.split('/')\n    modified_string = ''\n    denominator_int = int(denominator)\n    fractional = int(base_13_num) / denominator_int\n    base_13_fraction = [int(char) if char in '0123456789' else 9 - int(char) for char in base_13_num[::-1]]\n    while fractional > 0:\n        fractional *= denominator_int\n        digit = int(fractional)\n        fractional -= digit\n        modified_string = str(digit) + modified_string\n    input_string_list = list(input_string)\n    for (i, char) in enumerate(modified_string):\n        if char != '0':\n            if input_string_list[i] != '1':\n                input_string_list[i + 1] = str(1 - (int(input_string_list[i]) + int(char)) % 2)\n    return ''.join(input_string_list)", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(input_string: str) -> str:\n    new_string = ''\n    for char in input_string:\n        if char == 'a':\n            new_string += 'z'\n        elif char == 'z':\n            new_string += 'a'\n        elif char == 'b':\n            new_string += 'y'\n        elif char == 'y':\n            new_string += 'b'\n        elif char == 'c':\n            new_string += 'x'\n        elif char == 'x':\n            new_string += 'c'\n        elif char == 'd':\n            new_string += 'w'\n        elif char == 'w':\n            new_string += 'd'\n        elif char == 'e':\n            new_string += 'v'\n        elif char == 'v':\n            new_string += 'e'\n        elif char == 'f':\n            new_string += 'u'\n        elif char == 'u':\n            new_string += 'f'\n        elif char == 'g':\n            new_string += 't'\n        elif char == 't':\n            new_string += 'g'\n        elif char == 'h':\n            new_string += 's'\n        elif char == 's':\n            new_string += 'h'\n        elif char == 'i':\n            new_string += 'r'\n        elif char == 'r':\n            new_string += 'i'\n        elif char == 'j':\n            new_string += 'q'\n        elif char == 'q':\n            new_string += 'j'\n        elif char == 'k':\n            new_string += 'p'\n        elif char == 'p':\n            new_string += 'k'\n        elif char == 'l':\n            new_string += 'o'\n        elif char == 'o':\n            new_string += 'l'\n        elif char == 'm':\n            new_string += 'n'\n        elif char == 'n':\n            new_string += 'm'\n        else:\n            new_string += char\n    return new_string", "input": "'abcdefghijkmnopqrstuvwxyz'", "output": "'zyxwvutsrqpnmlkjihgfedcba'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    new_string = ''\n    for char in input_string:\n        if char == 'a':\n            new_string += 'z'\n        elif char == 'z':\n            new_string += 'a'\n        elif char == 'b':\n            new_string += 'y'\n        elif char == 'y':\n            new_string += 'b'\n        elif char == 'c':\n            new_string += 'x'\n        elif char == 'x':\n            new_string += 'c'\n        elif char == 'd':\n            new_string += 'w'\n        elif char == 'w':\n            new_string += 'd'\n        elif char == 'e':\n            new_string += 'v'\n        elif char == 'v':\n            new_string += 'e'\n        elif char == 'f':\n            new_string += 'u'\n        elif char == 'u':\n            new_string += 'f'\n        elif char == 'g':\n            new_string += 't'\n        elif char == 't':\n            new_string += 'g'\n        elif char == 'h':\n            new_string += 's'\n        elif char == 's':\n            new_string += 'h'\n        elif char == 'i':\n            new_string += 'r'\n        elif char == 'r':\n            new_string += 'i'\n        elif char == 'j':\n            new_string += 'q'\n        elif char == 'q':\n            new_string += 'j'\n        elif char == 'k':\n            new_string += 'p'\n        elif char == 'p':\n            new_string += 'k'\n        elif char == 'l':\n            new_string += 'o'\n        elif char == 'o':\n            new_string += 'l'\n        elif char == 'm':\n            new_string += 'n'\n        elif char == 'n':\n            new_string += 'm'\n        else:\n            new_string += char\n    return new_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_list: list) -> tuple:\n    if not input_list:\n        raise ValueError('Input list cannot be empty')\n    state = {'sorted_strings': [], 'vowel_count': 0}\n    for string in input_list:\n        sorted_string = ''.join(sorted(string))\n        state['sorted_strings'].append(sorted_string[::-1])\n    concatenated_string = ''.join(state['sorted_strings'][::-1])\n    for char in concatenated_string.lower():\n        if char in 'aeiou':\n            state['vowel_count'] += 1\n    result = (concatenated_string, state['vowel_count'])\n    return result", "input": "['stack', 'exchange', 'programmers']", "output": "('srrrpommgeaxnhgeecatskca', 7)", "imports": [], "original_snippet": "def f(input_list: list) -> tuple:\n    if not input_list:\n        raise ValueError('Input list cannot be empty')\n    state = {'sorted_strings': [], 'vowel_count': 0}\n    for string in input_list:\n        sorted_string = ''.join(sorted(string))\n        state['sorted_strings'].append(sorted_string[::-1])\n    concatenated_string = ''.join(state['sorted_strings'][::-1])\n    for char in concatenated_string.lower():\n        if char in 'aeiou':\n            state['vowel_count'] += 1\n    result = (concatenated_string, state['vowel_count'])\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(key: str, message: str):\n    result = ''\n    for (i, char) in enumerate(message):\n        if char.isalpha():\n            char_ord = ord(char)\n            key_ord = ord(key[i % len(key)])\n            result += chr(char_ord ^ key_ord)\n        else:\n            result += char\n    return result", "input": "'he11o', 'helloworldhe11o'", "output": "'\\x00\\x00]]\\x00\\x1f\\nC]\\x0b\\x00\\x0011\\x00'", "imports": [], "original_snippet": "def f(key: str, message: str):\n    result = ''\n    for (i, char) in enumerate(message):\n        if char.isalpha():\n            char_ord = ord(char)\n            key_ord = ord(key[i % len(key)])\n            result += chr(char_ord ^ key_ord)\n        else:\n            result += char\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(numbers):\n    result = 0\n    counted_locations = {}\n    for (index, num) in enumerate(numbers):\n        if index % 2 == 0:\n            result += num\n        else:\n            result *= num\n        counted_locations[index] = num\n    return (result, counted_locations)", "input": "[1, 2, 3, 6, 5, 7]", "output": "(245, {0: 1, 1: 2, 2: 3, 3: 6, 4: 5, 5: 7})", "imports": [], "original_snippet": "def f(numbers):\n    result = 0\n    counted_locations = {}\n    for (index, num) in enumerate(numbers):\n        if index % 2 == 0:\n            result += num\n        else:\n            result *= num\n        counted_locations[index] = num\n    return (result, counted_locations)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(heap_before_modification: list[int]):\n    heap_after_modification = heap_before_modification.copy()\n    v = heap_after_modification[-1]\n    while v > heap_after_modification[(len(heap_after_modification) - 2) // 2]:\n        (heap_after_modification[(len(heap_after_modification) - 2) // 2], v) = (v, heap_after_modification[(len(heap_after_modification) - 2) // 2])\n        heap_after_modification.pop()\n    return heap_after_modification", "input": "f([2,4,5,7])", "output": "[7]", "imports": [], "original_snippet": "def f(heap_before_modification: list[int]):\n    heap_after_modification = heap_before_modification.copy()\n    v = heap_after_modification[-1]\n    while v > heap_after_modification[(len(heap_after_modification) - 2) // 2]:\n        (heap_after_modification[(len(heap_after_modification) - 2) // 2], v) = (v, heap_after_modification[(len(heap_after_modification) - 2) // 2])\n        heap_after_modification.pop()\n    return heap_after_modification", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(text, op, shift):\n    output = ''\n    for char in text:\n        if '0' <= char <= '9':\n            char_index = ord(char) - 48\n            shifted_index = (char_index + shift) % 10\n            shifted_char = chr(shifted_index + 48)\n            output += shifted_char\n        else:\n            output += char\n    if op == 'decrypt':\n        output = f(output, 'encrypt', -shift)\n    return output", "input": "\"02468\", \"encrypt\", 1", "output": "'13579'", "imports": [], "original_snippet": "def f(text, op, shift):\n    output = ''\n    for char in text:\n        if '0' <= char <= '9':\n            char_index = ord(char) - 48\n            shifted_index = (char_index + shift) % 10\n            shifted_char = chr(shifted_index + 48)\n            output += shifted_char\n        else:\n            output += char\n    if op == 'decrypt':\n        output = f(output, 'encrypt', -shift)\n    return output", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(input_list):\n    removed_elements = []\n    added_elements = []\n    count = 0\n    for elem in input_list:\n        if elem % 2 != 0 and elem > 10:\n            removed_elements.append(elem)\n            count += elem\n        elif elem % 2 == 0 and elem <= 50:\n            added_elements.append(elem + 10)\n            count -= elem\n    for index in range(len(input_list)):\n        if index % 2 == 0:\n            input_list[index] = input_list[index] ** 2\n        else:\n            input_list[index] = input_list[index] // 2\n    output_dict = {'modified_list': input_list, 'removed_elements': removed_elements, 'added_elements': added_elements, 'count': count}\n    return output_dict", "input": "[7, 8, 9, 10, 11, 12]", "output": "{'modified_list': [49, 4, 81, 5, 121, 6], 'removed_elements': [11], 'added_elements': [18, 20, 22], 'count': -19}", "imports": [], "original_snippet": "def f(input_list):\n    removed_elements = []\n    added_elements = []\n    count = 0\n    for elem in input_list:\n        if elem % 2 != 0 and elem > 10:\n            removed_elements.append(elem)\n            count += elem\n        elif elem % 2 == 0 and elem <= 50:\n            added_elements.append(elem + 10)\n            count -= elem\n    for index in range(len(input_list)):\n        if index % 2 == 0:\n            input_list[index] = input_list[index] ** 2\n        else:\n            input_list[index] = input_list[index] // 2\n    output_dict = {'modified_list': input_list, 'removed_elements': removed_elements, 'added_elements': added_elements, 'count': count}\n    return output_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(prices):\n    max_profit = 0\n    left = 0\n    right = 0\n    current_min_price = prices[left]\n    while right < len(prices) - 1:\n        if prices[right] < prices[right + 1]:\n            right += 1\n            continue\n        max_profit = max(prices[right] - current_min_price, max_profit)\n        left = right + 1\n        right = left\n        current_min_price = prices[left]\n    max_profit = max(prices[len(prices) - 1] - current_min_price, max_profit)\n    return max_profit", "input": "[7,1,5,3,6,4]", "output": "4", "imports": [], "original_snippet": "def f(prices):\n    max_profit = 0\n    left = 0\n    right = 0\n    current_min_price = prices[left]\n    while right < len(prices) - 1:\n        if prices[right] < prices[right + 1]:\n            right += 1\n            continue\n        max_profit = max(prices[right] - current_min_price, max_profit)\n        left = right + 1\n        right = left\n        current_min_price = prices[left]\n    max_profit = max(prices[len(prices) - 1] - current_min_price, max_profit)\n    return max_profit", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "import re\ndef f(input_string: str) -> str:\n    result_str = ''\n    state = {'character_count': {}, 'special_character_count': 0}\n    for char in input_string:\n        if char.isalpha():\n            if char.lower() != char.upper():\n                state['character_count']['lower'] = state.get('lower', 0) + 1\n            else:\n                state['character_count']['upper'] = state.get('upper', 0) + 1\n                result_str += char.capitalize()\n        elif char.isdigit():\n            result_str += str(input_string.index(char))\n            state['character_count']['digit'] = state.get('digit', 0) + 1\n        else:\n            state['special_character_count'] += 1\n    state['special_character_count'] = state['special_character_count'] % 2 if state.get('special_character_count', 0) % 2 != 0 else 0\n    for (key, value) in state['character_count'].items():\n        state['special_character_count'] += value % 2\n    final_char = input_string[0]\n    for char in result_str[:state['special_character_count']]:\n        if char.isalpha():\n            final_char += char\n    if final_char[-1].isupper():\n        final_char = final_char[:-1] + final_char[-1].lower()\n    return final_char", "input": "\"Hello, World! 123\"", "output": "'h'", "imports": ["import re"], "original_snippet": "import re\ndef f(input_string: str) -> str:\n    result_str = ''\n    state = {'character_count': {}, 'special_character_count': 0}\n    for char in input_string:\n        if char.isalpha():\n            if char.lower() != char.upper():\n                state['character_count']['lower'] = state.get('lower', 0) + 1\n            else:\n                state['character_count']['upper'] = state.get('upper', 0) + 1\n                result_str += char.capitalize()\n        elif char.isdigit():\n            result_str += str(input_string.index(char))\n            state['character_count']['digit'] = state.get('digit', 0) + 1\n        else:\n            state['special_character_count'] += 1\n    state['special_character_count'] = state['special_character_count'] % 2 if state.get('special_character_count', 0) % 2 != 0 else 0\n    for (key, value) in state['character_count'].items():\n        state['special_character_count'] += value % 2\n    final_char = input_string[0]\n    for char in result_str[:state['special_character_count']]:\n        if char.isalpha():\n            final_char += char\n    if final_char[-1].isupper():\n        final_char = final_char[:-1] + final_char[-1].lower()\n    return final_char", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_string):\n    order_of_chars = {}\n    adjacent_chars = {}\n    for i in range(len(input_string)):\n        order_of_chars[input_string[i]] = i\n    for (char, pos) in order_of_chars.items():\n        if pos > 0:\n            left_adjacent_char = input_string[pos - 1]\n        else:\n            left_adjacent_char = '#'\n        if pos < len(input_string) - 1:\n            right_adjacent_char = input_string[pos + 1]\n        else:\n            right_adjacent_char = '#'\n        adjacent_chars[char] = {'l': left_adjacent_char, 'r': right_adjacent_char}\n    results = {}\n    for c in 'abcdefghijklmnopqrstuvwyz _ABCDEFGHIJKLMNOPQRSTUVWXYZ':\n        if c in input_string:\n            if c in adjacent_chars:\n                results[c] = adjacent_chars[c]\n            else:\n                results[c] = {'l': '#', 'r': '#'}\n    return (input_string, results)", "input": "\"Python is fun to code \"", "output": "('Python is fun to code ', {'c': {'l': ' ', 'r': 'o'}, 'd': {'l': 'o', 'r': 'e'}, 'e': {'l': 'd', 'r': ' '}, 'f': {'l': ' ', 'r': 'u'}, 'h': {'l': 't', 'r': 'o'}, 'i': {'l': ' ', 'r': 's'}, 'n': {'l':...'r': ' '}, 'o': {'l': 'c', 'r': 'd'}, 's': {'l': 'i', 'r': ' '}, 't': {'l': ' ', 'r': 'o'}, 'u': {'l': 'f', 'r': 'n'}, 'y': {'l': 'P', 'r': 't'}, ' ': {'l': 'e', 'r': '#'}, 'P': {'l': '#', 'r': 'y'}})", "imports": [], "original_snippet": "def f(input_string):\n    order_of_chars = {}\n    adjacent_chars = {}\n    for i in range(len(input_string)):\n        order_of_chars[input_string[i]] = i\n    for (char, pos) in order_of_chars.items():\n        if pos > 0:\n            left_adjacent_char = input_string[pos - 1]\n        else:\n            left_adjacent_char = '#'\n        if pos < len(input_string) - 1:\n            right_adjacent_char = input_string[pos + 1]\n        else:\n            right_adjacent_char = '#'\n        adjacent_chars[char] = {'l': left_adjacent_char, 'r': right_adjacent_char}\n    results = {}\n    for c in 'abcdefghijklmnopqrstuvwyz _ABCDEFGHIJKLMNOPQRSTUVWXYZ':\n        if c in input_string:\n            if c in adjacent_chars:\n                results[c] = adjacent_chars[c]\n            else:\n                results[c] = {'l': '#', 'r': '#'}\n    return (input_string, results)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_string: str) -> str:\n    import math\n    chars = list(input_string)\n    char_count = {}\n    for char in chars:\n        char_count[char] = char_count.get(char, 0) + 1\n    sorted_chars = sorted(chars, key=lambda char: char_count[char] * 1.5 + math.log(char_count[char] + 1), reverse=True)\n    importance_scores = {}\n    for char in sorted_chars:\n        importance_scores[char] = char_count[char] * 1.5 + math.log(char_count[char] + 1)\n    output = ''\n    while len(importance_scores) > 0:\n        max_char = max(importance_scores, key=importance_scores.get)\n        output += max_char\n        importance_scores[max_char] = importance_scores[max_char] / 2\n        if importance_scores[max_char] < 1:\n            del importance_scores[max_char]\n    return output", "input": "'Hello, world!'", "output": "'lolHe, wrd!olHe, wrd!o'", "imports": ["import math"], "original_snippet": "def f(input_string: str) -> str:\n    import math\n    chars = list(input_string)\n    char_count = {}\n    for char in chars:\n        char_count[char] = char_count.get(char, 0) + 1\n    sorted_chars = sorted(chars, key=lambda char: char_count[char] * 1.5 + math.log(char_count[char] + 1), reverse=True)\n    importance_scores = {}\n    for char in sorted_chars:\n        importance_scores[char] = char_count[char] * 1.5 + math.log(char_count[char] + 1)\n    output = ''\n    while len(importance_scores) > 0:\n        max_char = max(importance_scores, key=importance_scores.get)\n        output += max_char\n        importance_scores[max_char] = importance_scores[max_char] / 2\n        if importance_scores[max_char] < 1:\n            del importance_scores[max_char]\n    return output", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(numbers: list[int]) -> int:\n    max_diff = 0\n    max_index = 0\n    min_index = 0\n    for (i, num) in enumerate(numbers):\n        if num > numbers[max_index]:\n            max_index = i\n            max_diff = num - numbers[min_index]\n        elif num < numbers[min_index]:\n            min_index = i\n            max_diff = numbers[max_index] - num\n    return max_diff", "input": "[5, 3, 8, 9, 1, 2]", "output": "8", "imports": [], "original_snippet": "def f(numbers: list[int]) -> int:\n    max_diff = 0\n    max_index = 0\n    min_index = 0\n    for (i, num) in enumerate(numbers):\n        if num > numbers[max_index]:\n            max_index = i\n            max_diff = num - numbers[min_index]\n        elif num < numbers[min_index]:\n            min_index = i\n            max_diff = numbers[max_index] - num\n    return max_diff", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_data: dict) -> dict:\n    Group = input_data.get('Group', {})\n    modified_dict = {}\n    modifications = {}\n    for member in Group:\n        modifications[member] = {'age': Group[member].get('age', 0) + 10, 'info': Group[member].get('info', '')}\n    modified_dict['modifiedData'] = modifications\n    return modified_dict", "input": "{\n    'Group': {\n        'Member1': {'age': 25, 'info': 'a'},\n        'Member2': {'age': 30, 'info': 'b'},\n        'Member3': {'age': 35, 'info': 'c'},\n    }\n}", "output": "{'modifiedData': {'Member1': {'age': 35, 'info': 'a'}, 'Member2': {'age': 40, 'info': 'b'}, 'Member3': {'age': 45, 'info': 'c'}}}", "imports": [], "original_snippet": "def f(input_data: dict) -> dict:\n    Group = input_data.get('Group', {})\n    modified_dict = {}\n    modifications = {}\n    for member in Group:\n        modifications[member] = {'age': Group[member].get('age', 0) + 10, 'info': Group[member].get('info', '')}\n    modified_dict['modifiedData'] = modifications\n    return modified_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(input_string):\n    result = ''\n    sub_strings = input_string.split('d') if 'd' in input_string else [input_string]\n    modified = {}\n    for sub_string in sub_strings:\n        if len(sub_string) < 1 or not any((char.isalpha() for char in sub_string)):\n            result += sub_string\n        else:\n            for char in sub_string:\n                if char.isalpha() and char.lower() not in modified:\n                    modified[char.lower()] = chr(ord(char) + modified.get(char.lower(), 0) + 1)\n                    result += modified[char.lower()]\n                elif char.lower() in modified:\n                    result += modified[char.lower()]\n                else:\n                    result += chr(ord(char) + 1)\n    return result", "input": "'Hello dWerty'", "output": "'Ifmmp!Xfsuz'", "imports": [], "original_snippet": "def f(input_string):\n    result = ''\n    sub_strings = input_string.split('d') if 'd' in input_string else [input_string]\n    modified = {}\n    for sub_string in sub_strings:\n        if len(sub_string) < 1 or not any((char.isalpha() for char in sub_string)):\n            result += sub_string\n        else:\n            for char in sub_string:\n                if char.isalpha() and char.lower() not in modified:\n                    modified[char.lower()] = chr(ord(char) + modified.get(char.lower(), 0) + 1)\n                    result += modified[char.lower()]\n                elif char.lower() in modified:\n                    result += modified[char.lower()]\n                else:\n                    result += chr(ord(char) + 1)\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(data: list) -> list:\n    result = []\n    for d in data:\n        d_keys = list(d.keys())\n        max_field = 'max_field'\n        try:\n            max_field_index = d_keys.index(max_field)\n        except:\n            max_field_index = len(d_keys)\n        before = dict(list(d.items())[:max_field_index])\n        after = dict(list(d.items())[max_field_index + 1:])\n        if not after:\n            result.append(d)\n            continue\n        max_key = max(after, key=after.get)\n        before[max_field] = after.pop(max_key)\n        if 'max_value' in before:\n            del before['max_value']\n        result.append({max_field: before[max_field], **before})\n    return result", "input": "[{'a': 1, 'b': 2, 'max_field': 10, 'c': 1}, {'a': 1, 'b': 0, 'max_field': 4, 'c': 3, 'xx': 5, 'max_value': 666}]", "output": "[{'max_field': 1, 'a': 1, 'b': 2}, {'max_field': 666, 'a': 1, 'b': 0}]", "imports": [], "original_snippet": "def f(data: list) -> list:\n    result = []\n    for d in data:\n        d_keys = list(d.keys())\n        max_field = 'max_field'\n        try:\n            max_field_index = d_keys.index(max_field)\n        except:\n            max_field_index = len(d_keys)\n        before = dict(list(d.items())[:max_field_index])\n        after = dict(list(d.items())[max_field_index + 1:])\n        if not after:\n            result.append(d)\n            continue\n        max_key = max(after, key=after.get)\n        before[max_field] = after.pop(max_key)\n        if 'max_value' in before:\n            del before['max_value']\n        result.append({max_field: before[max_field], **before})\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_str: str) -> list:\n    char_list = list(input_str)\n    char_list.reverse()\n    return char_list", "input": "'Hello'", "output": "['o', 'l', 'l', 'e', 'H']", "imports": [], "original_snippet": "def f(input_str: str) -> list:\n    char_list = list(input_str)\n    char_list.reverse()\n    return char_list", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(binary_string: str) -> int:\n    result = 0\n    base = 1\n    for i in range(len(binary_string) - 1, -1, -1):\n        result += int(binary_string[i]) * base\n        base *= 2\n    return result", "input": "'1101'", "output": "13", "imports": [], "original_snippet": "def f(binary_string: str) -> int:\n    result = 0\n    base = 1\n    for i in range(len(binary_string) - 1, -1, -1):\n        result += int(binary_string[i]) * base\n        base *= 2\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_data):\n    if isinstance(input_data, (int, float)):\n        if input_data < 0:\n            return 'Error: Input should be a positive value'\n        elif input_data < 10:\n            return input_data * input_data\n        else:\n            return input_data / 2\n    elif isinstance(input_data, list):\n        if len(input_data) == 0:\n            return 0\n        sum_values = 0\n        min_value = min(input_data)\n        max_value = max(input_data)\n        mean_value = sum(input_data) / len(input_data)\n        for value in input_data:\n            sum_values += abs(value - mean_value)\n        return sum_values / len(input_data)\n    else:\n        return 'Error: Invalid input data type'", "input": "15", "output": "7.5", "imports": [], "original_snippet": "def f(input_data):\n    if isinstance(input_data, (int, float)):\n        if input_data < 0:\n            return 'Error: Input should be a positive value'\n        elif input_data < 10:\n            return input_data * input_data\n        else:\n            return input_data / 2\n    elif isinstance(input_data, list):\n        if len(input_data) == 0:\n            return 0\n        sum_values = 0\n        min_value = min(input_data)\n        max_value = max(input_data)\n        mean_value = sum(input_data) / len(input_data)\n        for value in input_data:\n            sum_values += abs(value - mean_value)\n        return sum_values / len(input_data)\n    else:\n        return 'Error: Invalid input data type'", "composite_functions": [], "_input_type": "int", "_output_type": "float"}
{"snippet": "import math\ndef f(n):\n    points = [(0, 0)] * (n - 1)\n    for i in range(1, n):\n        points[i - 1] = (3 ** i + 1, 11 ** i - 2)\n    position = 0\n    count = 0\n    while position < len(points) - 1:\n        if points[position][1] - points[position + 1][1] == 1:\n            count += 1\n        else:\n            old_position = position\n            try_position = min([j for j in range(old_position + 2, len(points)) if points[j][1] - points[old_position][1] == 1], default=len(points))\n            if try_position != len(points):\n                new_position = try_position\n                position = new_position\n                count += 1\n            else:\n                break\n    if points[-2][1] - points[-1][1] == 1:\n        count += 1\n    return count", "input": "50", "output": "0", "imports": ["import math"], "original_snippet": "import math\ndef f(n):\n    points = [(0, 0)] * (n - 1)\n    for i in range(1, n):\n        points[i - 1] = (3 ** i + 1, 11 ** i - 2)\n    position = 0\n    count = 0\n    while position < len(points) - 1:\n        if points[position][1] - points[position + 1][1] == 1:\n            count += 1\n        else:\n            old_position = position\n            try_position = min([j for j in range(old_position + 2, len(points)) if points[j][1] - points[old_position][1] == 1], default=len(points))\n            if try_position != len(points):\n                new_position = try_position\n                position = new_position\n                count += 1\n            else:\n                break\n    if points[-2][1] - points[-1][1] == 1:\n        count += 1\n    return count", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(numbers):\n    transformed = [num + 1 for num in numbers]\n    transformed.sort(reverse=True)\n    return transformed", "input": "[1, 2, 3, 4, 5]", "output": "[6, 5, 4, 3, 2]", "imports": [], "original_snippet": "def f(numbers):\n    transformed = [num + 1 for num in numbers]\n    transformed.sort(reverse=True)\n    return transformed", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "import re\ndef f(input_string):\n    japanese_dict = {'\u30d0': '\u3070', '\u30d3': '\u3073', '\u30d6': '\u3076', '\u30d9': '\u3079', '\u30dc': '\u307c'}\n    japanese_string = ''.join([japanese_dict.get(char, char) for char in input_string])\n    ouput_string_replaced = re.sub('\u30ab\u30bf\u30ab\u30ca', 'katakana', japanese_string, flags=re.IGNORECASE)\n    ouput_string_replaced = re.sub('\u3072\u3089\u304c\u306a', 'hiragana', ouput_string_replaced, flags=re.IGNORECASE)\n    return ouput_string_replaced", "input": "\"Good morning\"", "output": "'Good morning'", "imports": ["import re"], "original_snippet": "import re\ndef f(input_string):\n    japanese_dict = {'\u30d0': '\u3070', '\u30d3': '\u3073', '\u30d6': '\u3076', '\u30d9': '\u3079', '\u30dc': '\u307c'}\n    japanese_string = ''.join([japanese_dict.get(char, char) for char in input_string])\n    ouput_string_replaced = re.sub('\u30ab\u30bf\u30ab\u30ca', 'katakana', japanese_string, flags=re.IGNORECASE)\n    ouput_string_replaced = re.sub('\u3072\u3089\u304c\u306a', 'hiragana', ouput_string_replaced, flags=re.IGNORECASE)\n    return ouput_string_replaced", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_str: str) -> list:\n    char_list = list(input_str)\n    for i in range(len(char_list)):\n        for j in range(len(char_list)):\n            if ord(char_list[j]) > ord(char_list[i]):\n                (char_list[i], char_list[j]) = (char_list[j], char_list[i])\n    return char_list", "input": "'Hello'", "output": "['H', 'e', 'l', 'l', 'o']", "imports": [], "original_snippet": "def f(input_str: str) -> list:\n    char_list = list(input_str)\n    for i in range(len(char_list)):\n        for j in range(len(char_list)):\n            if ord(char_list[j]) > ord(char_list[i]):\n                (char_list[i], char_list[j]) = (char_list[j], char_list[i])\n    return char_list", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(password: str) -> str:\n    state = {'lowercase': 0, 'uppercase': 0, 'digits': 0, 'special_characters': 0}\n    for char in password:\n        if char.islower():\n            state['lowercase'] += 1\n        elif char.isupper():\n            state['uppercase'] += 1\n        elif char.isdigit():\n            state['digits'] += 1\n        else:\n            state['special_characters'] += 1\n    if state['lowercase'] == 0:\n        return 'Password should contain at least one lowercase letter'\n    if state['uppercase'] == 0:\n        return 'Password should contain at least one uppercase letter'\n    if state['digits'] == 0:\n        return 'Password should contain at least one digit'\n    if state['special_characters'] == 0:\n        return 'Password should contain at least one special character'\n    else:\n        return 'Password is valid'", "input": "'password'", "output": "'Password should contain at least one uppercase letter'", "imports": [], "original_snippet": "def f(password: str) -> str:\n    state = {'lowercase': 0, 'uppercase': 0, 'digits': 0, 'special_characters': 0}\n    for char in password:\n        if char.islower():\n            state['lowercase'] += 1\n        elif char.isupper():\n            state['uppercase'] += 1\n        elif char.isdigit():\n            state['digits'] += 1\n        else:\n            state['special_characters'] += 1\n    if state['lowercase'] == 0:\n        return 'Password should contain at least one lowercase letter'\n    if state['uppercase'] == 0:\n        return 'Password should contain at least one uppercase letter'\n    if state['digits'] == 0:\n        return 'Password should contain at least one digit'\n    if state['special_characters'] == 0:\n        return 'Password should contain at least one special character'\n    else:\n        return 'Password is valid'", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(numbers: list, base_number: int) -> list:\n    result = []\n    for num in numbers:\n        if num < base_number:\n            result.append(num ** 2)\n        else:\n            result.append(num ** 3)\n    return result", "input": "[2, 5, 3, 7, 1], 4", "output": "[4, 125, 9, 343, 1]", "imports": [], "original_snippet": "def f(numbers: list, base_number: int) -> list:\n    result = []\n    for num in numbers:\n        if num < base_number:\n            result.append(num ** 2)\n        else:\n            result.append(num ** 3)\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(s: str) -> str:\n    state = {'unique_chars': set(), 'char_freq': {}}\n    result = ''\n    for char in s:\n        if not (char.isdigit() or char in state['unique_chars']):\n            state['unique_chars'].add(char)\n            state['char_freq'][char] = 1\n            if len(state['char_freq']) % 2 == 0:\n                result += char.lower()\n            else:\n                result += char.upper()\n        elif char.isdigit() and char not in state['unique_chars']:\n            state['unique_chars'].add(char)\n            state['char_freq'][char] = int(char)\n    return result", "input": "'Hello 4 world'", "output": "'HeLo WrD'", "imports": [], "original_snippet": "def f(s: str) -> str:\n    state = {'unique_chars': set(), 'char_freq': {}}\n    result = ''\n    for char in s:\n        if not (char.isdigit() or char in state['unique_chars']):\n            state['unique_chars'].add(char)\n            state['char_freq'][char] = 1\n            if len(state['char_freq']) % 2 == 0:\n                result += char.lower()\n            else:\n                result += char.upper()\n        elif char.isdigit() and char not in state['unique_chars']:\n            state['unique_chars'].add(char)\n            state['char_freq'][char] = int(char)\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_list: list) -> dict:\n    counts = {}\n    transformations = {}\n    transformed_list = []\n    for num in input_list:\n        if num % 2 == 0:\n            transformed_list.append(num * 2)\n        else:\n            transformed_list.append(num * 3)\n        if num % 2 == 0:\n            counts['even_count'] = counts.get('even_count', 0) + 1\n            transformations['sum_even'] = transformations.get('sum_even', 0) + num\n        else:\n            counts['odd_count'] = counts.get('odd_count', 0) + 1\n            transformations['sum_odd'] = transformations.get('sum_odd', 0) + num\n    transformations['transformed_list'] = transformed_list\n    counts['transformed_list'] = transformed_list\n    answer_dict = {'input_list': input_list, 'transformations': transformations, 'counts': counts}\n    return answer_dict", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "{'input_list': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'transformations': {'sum_odd': 25, 'sum_even': 30, 'transformed_list': [3, 4, 9, 8, 15, 12, 21, 16, 27, 20]}, 'counts': {'odd_count': 5, 'even_count': 5, 'transformed_list': [3, 4, 9, 8, 15, 12, 21, 16, 27, 20]}}", "imports": [], "original_snippet": "def f(input_list: list) -> dict:\n    counts = {}\n    transformations = {}\n    transformed_list = []\n    for num in input_list:\n        if num % 2 == 0:\n            transformed_list.append(num * 2)\n        else:\n            transformed_list.append(num * 3)\n        if num % 2 == 0:\n            counts['even_count'] = counts.get('even_count', 0) + 1\n            transformations['sum_even'] = transformations.get('sum_even', 0) + num\n        else:\n            counts['odd_count'] = counts.get('odd_count', 0) + 1\n            transformations['sum_odd'] = transformations.get('sum_odd', 0) + num\n    transformations['transformed_list'] = transformed_list\n    counts['transformed_list'] = transformed_list\n    answer_dict = {'input_list': input_list, 'transformations': transformations, 'counts': counts}\n    return answer_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(lst):\n    result = []\n    for num in lst:\n        result.append(num ** 2 if num % 2 == 0 else num)\n    return sum(result)", "input": "[3, 4, 5, 6]", "output": "60", "imports": [], "original_snippet": "def f(lst):\n    result = []\n    for num in lst:\n        result.append(num ** 2 if num % 2 == 0 else num)\n    return sum(result)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(items, n):\n    counts = {}\n    for item in items:\n        if item in counts:\n            counts[item] += 1\n        else:\n            counts[item] = 1\n    sorted_counts = sorted(counts.items(), key=lambda x: x[1], reverse=True)\n    result = {item: count for (item, count) in sorted_counts[:n]}\n    return result", "input": "['apple', 'banana', 'orange', 'apple', 'banana', 'apple', 'orange', 'kiwi', 'mango', 'apple'],\n5", "output": "{'apple': 4, 'banana': 2, 'orange': 2, 'kiwi': 1, 'mango': 1}", "imports": [], "original_snippet": "def f(items, n):\n    counts = {}\n    for item in items:\n        if item in counts:\n            counts[item] += 1\n        else:\n            counts[item] = 1\n    sorted_counts = sorted(counts.items(), key=lambda x: x[1], reverse=True)\n    result = {item: count for (item, count) in sorted_counts[:n]}\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(input_dict: dict) -> int:\n    state = {'value': input_dict['initial']}\n    for key in input_dict['transformations']:\n        if key.startswith('add'):\n            value = key.split('_')[1]\n            state['value'] += int(value)\n        elif key.startswith('subtract'):\n            value = key.split('_')[1]\n            state['value'] -= int(value)\n        elif key.startswith('double'):\n            state['value'] *= 2\n        elif key.startswith('half'):\n            state['value'] //= 2\n    return state['value']", "input": "{'initial': 5, 'transformations': ['add_3', 'double', 'subtract_2', 'half', 'add_7']}", "output": "14", "imports": [], "original_snippet": "def f(input_dict: dict) -> int:\n    state = {'value': input_dict['initial']}\n    for key in input_dict['transformations']:\n        if key.startswith('add'):\n            value = key.split('_')[1]\n            state['value'] += int(value)\n        elif key.startswith('subtract'):\n            value = key.split('_')[1]\n            state['value'] -= int(value)\n        elif key.startswith('double'):\n            state['value'] *= 2\n        elif key.startswith('half'):\n            state['value'] //= 2\n    return state['value']", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(input_str):\n    output_str = ''\n    counter = 0\n    for char in input_str:\n        if char.isalpha():\n            output_str += chr(ord(char) + counter)\n        else:\n            output_str += char\n        counter += 1\n    return output_str", "input": "'Hello World!'", "output": "'Hfnos ]vzun!'", "imports": [], "original_snippet": "def f(input_str):\n    output_str = ''\n    counter = 0\n    for char in input_str:\n        if char.isalpha():\n            output_str += chr(ord(char) + counter)\n        else:\n            output_str += char\n        counter += 1\n    return output_str", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_char: str) -> str:\n    replacements = {'a': '1', 'b': '3', 'c': '5', 'd': '7', 'e': '9'}\n    output_str = ''\n    for char in input_char:\n        if char in replacements:\n            output_str += replacements[char]\n        else:\n            output_str += '0'\n    while '00' in output_str:\n        output_str = output_str.replace('00', '0')\n    if output_str == '0':\n        output_str = 'No'\n    return output_str", "input": "'1233321'", "output": "'No'", "imports": [], "original_snippet": "def f(input_char: str) -> str:\n    replacements = {'a': '1', 'b': '3', 'c': '5', 'd': '7', 'e': '9'}\n    output_str = ''\n    for char in input_char:\n        if char in replacements:\n            output_str += replacements[char]\n        else:\n            output_str += '0'\n    while '00' in output_str:\n        output_str = output_str.replace('00', '0')\n    if output_str == '0':\n        output_str = 'No'\n    return output_str", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(name: str, info: dict):\n    sorted_info = sorted(info.items())\n    output = name + ', ' + str(sorted_info[0][1]) + ' years old from ' + sorted_info[1][1]\n    return {'output': output}", "input": "'Maria', {'city': 'New York', 'age': 25}", "output": "{'output': 'Maria, 25 years old from New York'}", "imports": [], "original_snippet": "def f(name: str, info: dict):\n    sorted_info = sorted(info.items())\n    output = name + ', ' + str(sorted_info[0][1]) + ' years old from ' + sorted_info[1][1]\n    return {'output': output}", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(input_list: list) -> str:\n    new_list = []\n    sorted_list = sorted(input_list)\n    for string in input_list:\n        name_list = string.split(' ')\n        new_list.append(name_list[0] + ' ' + name_list[-1])\n    return str(new_list) + ' => ' + str(sorted_list)", "input": "['alice bob charlie bob', 'david eve frank', 'grace haley ian herb']", "output": "\"['alice bob', 'david frank', 'grace herb'] => ['alice bob charlie bob', 'david eve frank', 'grace haley ian herb']\"", "imports": [], "original_snippet": "def f(input_list: list) -> str:\n    new_list = []\n    sorted_list = sorted(input_list)\n    for string in input_list:\n        name_list = string.split(' ')\n        new_list.append(name_list[0] + ' ' + name_list[-1])\n    return str(new_list) + ' => ' + str(sorted_list)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(string_list):\n    vowels_count_list = []\n    for string in string_list:\n        count = 0\n        for char in string:\n            if char in 'aeiouAEIOU':\n                count += 1\n        vowels_count_list.append(count)\n    return vowels_count_list", "input": "['Hello', 'World']", "output": "[2, 1]", "imports": [], "original_snippet": "def f(string_list):\n    vowels_count_list = []\n    for string in string_list:\n        count = 0\n        for char in string:\n            if char in 'aeiouAEIOU':\n                count += 1\n        vowels_count_list.append(count)\n    return vowels_count_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_string: str) -> str:\n    input_string_new = ''.join([char for char in input_string if char != '.'])\n    print('end metrics string')\n    return input_string_new", "input": "'R2-D2This is a text string'", "output": "'R2-D2This is a text string'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    input_string_new = ''.join([char for char in input_string if char != '.'])\n    print('end metrics string')\n    return input_string_new", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_list):\n    try:\n        final = 0\n        for (idx, val) in enumerate(input_list):\n            if val % 2 == 0:\n                if val > 10:\n                    final += val ** 2\n                else:\n                    final += val\n        return final\n    except IndexError:\n        return []", "input": "[7, 8, 9, 10, 11]", "output": "18", "imports": [], "original_snippet": "def f(input_list):\n    try:\n        final = 0\n        for (idx, val) in enumerate(input_list):\n            if val % 2 == 0:\n                if val > 10:\n                    final += val ** 2\n                else:\n                    final += val\n        return final\n    except IndexError:\n        return []", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(num_balls: int, colors: list):\n    arrangements = 1\n    for i in range(num_balls):\n        arrangements *= sum(range(len(colors))) / len(colors)\n    return arrangements", "input": "5, ['red', 'green', 'blue']", "output": "1.0", "imports": [], "original_snippet": "def f(num_balls: int, colors: list):\n    arrangements = 1\n    for i in range(num_balls):\n        arrangements *= sum(range(len(colors))) / len(colors)\n    return arrangements", "composite_functions": [], "_input_type": "tuple", "_output_type": "float"}
{"snippet": "def f(numbers):\n    prefix = [0] * (len(numbers) + 1)\n    for i in range(len(numbers)):\n        prefix[i + 1] = prefix[i] + numbers[i]\n    suffix = [0] * (len(numbers) + 1)\n    for i in range(len(numbers) - 1, -1, -1):\n        suffix[i] = suffix[i + 1] + numbers[i]\n    result = [suffix[1]] + [prefix[i + 1] + suffix[i + 2] for i in range(len(numbers) - 1)] + [prefix[-1]]\n    return result", "input": "[2, 3, 4, 5, 6]", "output": "[18, 17, 16, 15, 14, 20]", "imports": [], "original_snippet": "def f(numbers):\n    prefix = [0] * (len(numbers) + 1)\n    for i in range(len(numbers)):\n        prefix[i + 1] = prefix[i] + numbers[i]\n    suffix = [0] * (len(numbers) + 1)\n    for i in range(len(numbers) - 1, -1, -1):\n        suffix[i] = suffix[i + 1] + numbers[i]\n    result = [suffix[1]] + [prefix[i + 1] + suffix[i + 2] for i in range(len(numbers) - 1)] + [prefix[-1]]\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input, memo={}):\n    if isinstance(input, dict):\n        result = {}\n        if input is None:\n            return result\n        for (k, v) in input.items():\n            for (key, value) in f(v).items():\n                result[k + '_' + key] = value\n        leaf_nodes_count = 0\n        leaf_nodes = []\n        tracked_path = []\n        index = 0\n        for (key, value) in input.items():\n            v = f(value, memo)\n            queue = [(key, value)]\n            traversed_path = []\n            while queue:\n                next_queue = []\n                while queue:\n                    (key, val) = queue.pop()\n                    traversed_path.append(key)\n                    if isinstance(val, dict) and val:\n                        for (nested_key, nested_val) in val.items():\n                            next_queue.append((str(nested_key), str(nested_val)))\n                            tracked_path.append(True)\n                    else:\n                        leaf_nodes_count += 1\n                        leaf_nodes.append((val, traversed_path))\n                        tracked_path.append(False)\n                queue = next_queue\n                if next_queue:\n                    indexed_row = []\n                    for row_traversed in tracked_path:\n                        if row_traversed:\n                            parents = ','.join([row for row in indexed_row])\n                            indexed_row.append(parents)\n                            tracked_path.pop()\n                    while len(tracked_path) > len(indexed_row):\n                        tracked_path.pop()\n                    result[','.join(traversed_path)] = tuple(leaf_nodes.pop())\n                else:\n                    result[','.join(traversed_path)] = tuple(leaf_nodes.pop())\n                traversed_path = []\n            return (result, leaf_nodes_count, leaf_nodes)", "input": "[\n    {'a': 1, 'b': {'c': 2, 'd': {'e': {'f': 3}}}},\n    {'g': 4},\n    {'h': {'i': 5, 'j': {'k': 6, 'l': 7}}},\n    {'m': {'n': 8}}\n]", "output": "None", "imports": [], "original_snippet": "def f(input, memo={}):\n    if isinstance(input, dict):\n        result = {}\n        if input is None:\n            return result\n        for (k, v) in input.items():\n            for (key, value) in f(v).items():\n                result[k + '_' + key] = value\n        leaf_nodes_count = 0\n        leaf_nodes = []\n        tracked_path = []\n        index = 0\n        for (key, value) in input.items():\n            v = f(value, memo)\n            queue = [(key, value)]\n            traversed_path = []\n            while queue:\n                next_queue = []\n                while queue:\n                    (key, val) = queue.pop()\n                    traversed_path.append(key)\n                    if isinstance(val, dict) and val:\n                        for (nested_key, nested_val) in val.items():\n                            next_queue.append((str(nested_key), str(nested_val)))\n                            tracked_path.append(True)\n                    else:\n                        leaf_nodes_count += 1\n                        leaf_nodes.append((val, traversed_path))\n                        tracked_path.append(False)\n                queue = next_queue\n                if next_queue:\n                    indexed_row = []\n                    for row_traversed in tracked_path:\n                        if row_traversed:\n                            parents = ','.join([row for row in indexed_row])\n                            indexed_row.append(parents)\n                            tracked_path.pop()\n                    while len(tracked_path) > len(indexed_row):\n                        tracked_path.pop()\n                    result[','.join(traversed_path)] = tuple(leaf_nodes.pop())\n                else:\n                    result[','.join(traversed_path)] = tuple(leaf_nodes.pop())\n                traversed_path = []\n            return (result, leaf_nodes_count, leaf_nodes)", "composite_functions": [], "_input_type": "list", "_output_type": "NoneType"}
{"snippet": "def f(input_list):\n    trie_dict = {}\n    last_element = None\n    total = 0\n    for element in reversed(input_list):\n        current_trie = trie_dict\n        if element not in current_trie:\n            current_trie[element] = {}\n        elif element == last_element:\n            total += 1\n        else:\n            total += 2\n        current_trie = current_trie[element]\n        last_element = element\n    return ('', total)", "input": "['todo', 'data', 'ad', 'ad', 'dat', 'ad', 'dat']", "output": "('', 5)", "imports": [], "original_snippet": "def f(input_list):\n    trie_dict = {}\n    last_element = None\n    total = 0\n    for element in reversed(input_list):\n        current_trie = trie_dict\n        if element not in current_trie:\n            current_trie[element] = {}\n        elif element == last_element:\n            total += 1\n        else:\n            total += 2\n        current_trie = current_trie[element]\n        last_element = element\n    return ('', total)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(number_1: int, number_2: int) -> int:\n    total_1 = number_1 >> 2 * number_2\n    final_number = total_1 + (number_2 & number_2)\n    return final_number", "input": "15, 4", "output": "4", "imports": [], "original_snippet": "def f(number_1: int, number_2: int) -> int:\n    total_1 = number_1 >> 2 * number_2\n    final_number = total_1 + (number_2 & number_2)\n    return final_number", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(input_list):\n    if input_list == []:\n        return 0\n    if isinstance(input_list[0], int):\n        if input_list[0] % 2 == 0:\n            return input_list[0] // 2 + f(input_list[1:])\n        else:\n            return input_list[0] * 3 + f(input_list[1:])\n    else:\n        return f(input_list[0]) + f(input_list[1:])", "input": "[1, [2, [3, [4]]]]", "output": "15", "imports": [], "original_snippet": "def f(input_list):\n    if input_list == []:\n        return 0\n    if isinstance(input_list[0], int):\n        if input_list[0] % 2 == 0:\n            return input_list[0] // 2 + f(input_list[1:])\n        else:\n            return input_list[0] * 3 + f(input_list[1:])\n    else:\n        return f(input_list[0]) + f(input_list[1:])", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers: list):\n    even_numbers = [num for num in numbers if num % 2 == 0]\n    sum_of_abs_even = sum((abs(num) for num in even_numbers))\n    product_of_even_indices = 1\n    for i in range(0, len(numbers) - 1, 2):\n        product_of_even_indices *= numbers[i]\n    return (sum_of_abs_even, product_of_even_indices)", "input": "[4, 2, 1, -2, 0, 8]", "output": "(16, 0)", "imports": [], "original_snippet": "def f(numbers: list):\n    even_numbers = [num for num in numbers if num % 2 == 0]\n    sum_of_abs_even = sum((abs(num) for num in even_numbers))\n    product_of_even_indices = 1\n    for i in range(0, len(numbers) - 1, 2):\n        product_of_even_indices *= numbers[i]\n    return (sum_of_abs_even, product_of_even_indices)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(input_data: list):\n    from collections import Counter\n    result = {}\n    for num in input_data:\n        digits = list(str(num % 10 ** 8))\n        multiplicative_inverse = int(''.join(digits if int(digits[0]) % 2 == 0 else digits[::-1]))\n        key = tuple(Counter(digits).items())\n        result[key] = multiplicative_inverse\n    return result", "input": "[52334, 1057, 1, 83245, 945013, 12, 92, 50]", "output": "{(('5', 1), ('2', 1), ('3', 2), ('4', 1)): 43325, (('1', 1), ('0', 1), ('5', 1), ('7', 1)): 7501, (('1', 1),): 1, (('8', 1), ('3', 1), ('2', 1), ('4', 1), ('5', 1)): 83245, (('9', 1), ('4', 1), ('5', 1), ('0', 1), ('1', 1), ('3', 1)): 310549, (('1', 1), ('2', 1)): 21, (('9', 1), ('2', 1)): 29, (('5', 1), ('0', 1)): 5}", "imports": ["from collections import Counter"], "original_snippet": "def f(input_data: list):\n    from collections import Counter\n    result = {}\n    for num in input_data:\n        digits = list(str(num % 10 ** 8))\n        multiplicative_inverse = int(''.join(digits if int(digits[0]) % 2 == 0 else digits[::-1]))\n        key = tuple(Counter(digits).items())\n        result[key] = multiplicative_inverse\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(nums: list) -> list:\n    factorial = lambda x: 1 if x < 2 else x * factorial(x - 1)\n    factorials = list(map(factorial, nums))\n    return factorials", "input": "[1, 2, 3, 4]", "output": "[1, 2, 6, 24]", "imports": [], "original_snippet": "def f(nums: list) -> list:\n    factorial = lambda x: 1 if x < 2 else x * factorial(x - 1)\n    factorials = list(map(factorial, nums))\n    return factorials", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(data, layer=1):\n    if layer == 0:\n        return data\n    elif isinstance(data, str):\n        if layer % 2 == 0:\n            if data.islower():\n                return data.upper()\n            else:\n                return data.lower()\n        else:\n            return chr((ord(data) - 97 + layer) % 26 + 97)\n    elif isinstance(data, list):\n        return [f(item, layer - 1) for item in data]\n    else:\n        return data", "input": "'Hello World', 2", "output": "'hello world'", "imports": [], "original_snippet": "def f(data, layer=1):\n    if layer == 0:\n        return data\n    elif isinstance(data, str):\n        if layer % 2 == 0:\n            if data.islower():\n                return data.upper()\n            else:\n                return data.lower()\n        else:\n            return chr((ord(data) - 97 + layer) % 26 + 97)\n    elif isinstance(data, list):\n        return [f(item, layer - 1) for item in data]\n    else:\n        return data", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(numbers: list) -> int:\n    if not numbers:\n        raise ValueError('Input list cannot be empty.')\n    filtered_numbers = [num for num in numbers if isinstance(num, (int, float))]\n    if not filtered_numbers:\n        raise ValueError('Input list must contain at least one integer or float.')\n    sorted_numbers = sorted(filtered_numbers)\n    return len(sorted_numbers) * sorted_numbers[0] + (len(filtered_numbers) - 1) * sorted_numbers[-1]", "input": "[10, 20, 30]", "output": "90", "imports": [], "original_snippet": "def f(numbers: list) -> int:\n    if not numbers:\n        raise ValueError('Input list cannot be empty.')\n    filtered_numbers = [num for num in numbers if isinstance(num, (int, float))]\n    if not filtered_numbers:\n        raise ValueError('Input list must contain at least one integer or float.')\n    sorted_numbers = sorted(filtered_numbers)\n    return len(sorted_numbers) * sorted_numbers[0] + (len(filtered_numbers) - 1) * sorted_numbers[-1]", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(integer_list, string_stack, float_queue):\n    sum_of_even_indices = sum((num for (i, num) in enumerate(integer_list) if i % 2 == 0))\n    averaging_queue_items = sum(float_queue, 0.0) / len(float_queue)\n    string_stack_set = set()\n    for item in string_stack:\n        string_stack_set.add(item)\n    string_stack_deduplicated = list(string_stack_set)\n    return (sum_of_even_indices, averaging_queue_items, string_stack_deduplicated)", "input": "[1, 2, 3, 4, 5, 6], ['apple', 'banana', 'apple', 'orange'], [3.5, 2.5, 6.7, 4.2]", "output": "(9, 4.225, ['orange', 'apple', 'banana'])", "imports": [], "original_snippet": "def f(integer_list, string_stack, float_queue):\n    sum_of_even_indices = sum((num for (i, num) in enumerate(integer_list) if i % 2 == 0))\n    averaging_queue_items = sum(float_queue, 0.0) / len(float_queue)\n    string_stack_set = set()\n    for item in string_stack:\n        string_stack_set.add(item)\n    string_stack_deduplicated = list(string_stack_set)\n    return (sum_of_even_indices, averaging_queue_items, string_stack_deduplicated)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(input_string: str):\n    result = {}\n    letter_counts = {}\n    for char in input_string:\n        if char.isalpha():\n            if char not in letter_counts:\n                letter_counts[char] = 1\n            else:\n                letter_counts[char] += 1\n    for (char, count) in letter_counts.items():\n        if count > 3:\n            result[char] = count\n    return result", "input": "'abcdefghabcdefgxxxx'", "output": "{'x': 4}", "imports": [], "original_snippet": "def f(input_string: str):\n    result = {}\n    letter_counts = {}\n    for char in input_string:\n        if char.isalpha():\n            if char not in letter_counts:\n                letter_counts[char] = 1\n            else:\n                letter_counts[char] += 1\n    for (char, count) in letter_counts.items():\n        if count > 3:\n            result[char] = count\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "dict"}
{"snippet": "def f(numbers: list) -> list:\n    result = []\n    index_map = {}\n    for (i, num) in enumerate(numbers):\n        square = num ** 2\n        index_map[square] = []\n        is_divisible = any([dividend % square == 0 for dividend in numbers if dividend != num])\n        result.append((num, square, index_map[square]))\n    for (i, num) in enumerate(numbers):\n        square = num ** 2\n        index_map[square].append(i)\n    return result", "input": "[2, 3, 4, 5, 6, 7, 8, 9]", "output": "[(2, 4, [0]), (3, 9, [1]), (4, 16, [2]), (5, 25, [3]), (6, 36, [4]), (7, 49, [5]), (8, 64, [6]), (9, 81, [7])]", "imports": [], "original_snippet": "def f(numbers: list) -> list:\n    result = []\n    index_map = {}\n    for (i, num) in enumerate(numbers):\n        square = num ** 2\n        index_map[square] = []\n        is_divisible = any([dividend % square == 0 for dividend in numbers if dividend != num])\n        result.append((num, square, index_map[square]))\n    for (i, num) in enumerate(numbers):\n        square = num ** 2\n        index_map[square].append(i)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "import math\ndef f(number):\n    reversed_number = 0\n    while number > 0:\n        reversed_number = reversed_number * 10 + number % 10\n        number //= 10\n    return reversed_number", "input": "1089", "output": "9801", "imports": ["import math"], "original_snippet": "import math\ndef f(number):\n    reversed_number = 0\n    while number > 0:\n        reversed_number = reversed_number * 10 + number % 10\n        number //= 10\n    return reversed_number", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(input_list):\n    output_list = []\n    for s in input_list:\n        words = s.split()\n        output_s = ' '.join(words[::-1])\n        output_s = output_s.replace('e', 'X')\n        output_s = output_s.replace('i', 'e')\n        output_s = output_s.replace('X', 'i')\n        output_list.append(output_s)\n    return output_list", "input": "['okin', 'ppOne', 'he', 'pasted', 'testing', 'escaped', 'theme', 'below', 'Input', 'output', 'pasted', 'keywords']", "output": "['oken', 'ppOni', 'hi', 'pastid', 'tisteng', 'iscapid', 'thimi', 'bilow', 'Input', 'output', 'pastid', 'kiywords']", "imports": [], "original_snippet": "def f(input_list):\n    output_list = []\n    for s in input_list:\n        words = s.split()\n        output_s = ' '.join(words[::-1])\n        output_s = output_s.replace('e', 'X')\n        output_s = output_s.replace('i', 'e')\n        output_s = output_s.replace('X', 'i')\n        output_list.append(output_s)\n    return output_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(initial_values, num_steps: int):\n    outputs = []\n    current_values = initial_values\n    for _ in range(num_steps):\n        states = []\n        for value in current_values:\n            state = {'value': value}\n            states.append(state)\n        outputs.append(states)\n        values = []\n        for state in states:\n            value = state['value']\n            if value % 2 == 0:\n                value // 2\n            else:\n                3 * value + 1\n            values.append(value)\n        current_values = values\n    return outputs", "input": "[4, 8, 16], 5", "output": "[[{'value': 4}, {'value': 8}, {'value': 16}], [{'value': 4}, {'value': 8}, {'value': 16}], [{'value': 4}, {'value': 8}, {'value': 16}], [{'value': 4}, {'value': 8}, {'value': 16}], [{'value': 4}, {'value': 8}, {'value': 16}]]", "imports": [], "original_snippet": "def f(initial_values, num_steps: int):\n    outputs = []\n    current_values = initial_values\n    for _ in range(num_steps):\n        states = []\n        for value in current_values:\n            state = {'value': value}\n            states.append(state)\n        outputs.append(states)\n        values = []\n        for state in states:\n            value = state['value']\n            if value % 2 == 0:\n                value // 2\n            else:\n                3 * value + 1\n            values.append(value)\n        current_values = values\n    return outputs", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(numbers: list) -> dict:\n    result = {}\n    current_sum = 0\n    max_count = 0\n    for num in numbers:\n        current_sum += num\n        if num > 0:\n            count = current_sum % 6 + 1\n            max_count = max(max_count, count)\n    for i in range(max_count):\n        for num in numbers:\n            if num == max_count - i:\n                result[f'count_{num}'] = num ** 2\n    return result", "input": "[3, 6, 2, 5, 1, 4]", "output": "{'count_6': 36, 'count_5': 25, 'count_4': 16, 'count_3': 9, 'count_2': 4, 'count_1': 1}", "imports": [], "original_snippet": "def f(numbers: list) -> dict:\n    result = {}\n    current_sum = 0\n    max_count = 0\n    for num in numbers:\n        current_sum += num\n        if num > 0:\n            count = current_sum % 6 + 1\n            max_count = max(max_count, count)\n    for i in range(max_count):\n        for num in numbers:\n            if num == max_count - i:\n                result[f'count_{num}'] = num ** 2\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(input_str):\n    output_str = input_str[::-1]\n    new_str = ''\n    counter = 0\n    for j in range(len(output_str)):\n        char = output_str[j]\n        if char.isalpha():\n            new_str += char.upper()\n            counter += 1\n            if counter % 3 == 0:\n                new_str += ' '\n        else:\n            new_str += char\n    return new_str.strip()", "input": "\"Hello World!\"", "output": "'!DLR OW O LLE H'", "imports": [], "original_snippet": "def f(input_str):\n    output_str = input_str[::-1]\n    new_str = ''\n    counter = 0\n    for j in range(len(output_str)):\n        char = output_str[j]\n        if char.isalpha():\n            new_str += char.upper()\n            counter += 1\n            if counter % 3 == 0:\n                new_str += ' '\n        else:\n            new_str += char\n    return new_str.strip()", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(code: int) -> tuple:\n    special_codes = {0: 1, 1: 2, 2: 3, 3: 4}\n    binary_code = bin(code)[2:].zfill(8)\n    if binary_code in special_codes:\n        return (special_codes[binary_code], binary_code)\n    else:\n        return ('Not match', binary_code)", "input": "0b00100100", "output": "('Not match', '00100100')", "imports": [], "original_snippet": "def f(code: int) -> tuple:\n    special_codes = {0: 1, 1: 2, 2: 3, 3: 4}\n    binary_code = bin(code)[2:].zfill(8)\n    if binary_code in special_codes:\n        return (special_codes[binary_code], binary_code)\n    else:\n        return ('Not match', binary_code)", "composite_functions": [], "_input_type": "int", "_output_type": "tuple"}
{"snippet": "def f(arr):\n    n = len(arr) + 1\n    return n * (n + 1) // 2 - sum(arr)", "input": "[1, 2, 4, 5, 6]", "output": "3", "imports": [], "original_snippet": "def f(arr):\n    n = len(arr) + 1\n    return n * (n + 1) // 2 - sum(arr)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(bookshelf):\n    tag_counts = {book_id: len(tags) for (book_id, tags) in bookshelf.items()}\n    max_tag_count_books = [book_id for (book_id, count) in tag_counts.items() if count == max(tag_counts.values())]\n    min_tag_count_books = [book_id for (book_id, count) in tag_counts.items() if count == min(tag_counts.values())]\n    return [max_tag_count_books, min_tag_count_books]", "input": "bookshelf = {'book1': {'tag1', 'tag2'}, 'book2': {'tag3'}, 'book3': {'tag4', 'tag5', 'tag6'}}", "output": "[['book3'], ['book2']]", "imports": [], "original_snippet": "def f(bookshelf):\n    tag_counts = {book_id: len(tags) for (book_id, tags) in bookshelf.items()}\n    max_tag_count_books = [book_id for (book_id, count) in tag_counts.items() if count == max(tag_counts.values())]\n    min_tag_count_books = [book_id for (book_id, count) in tag_counts.items() if count == min(tag_counts.values())]\n    return [max_tag_count_books, min_tag_count_books]", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(starting_number, upper_number):\n    candidate = starting_number + 1\n    result = []\n    while candidate <= upper_number:\n        is_prime = True\n        for i in range(2, int(candidate ** 0.5) + 1):\n            if candidate % i == 0:\n                is_prime = False\n                break\n        if is_prime:\n            result.append(candidate)\n        candidate += 1\n    return result", "input": "1, 10", "output": "[2, 3, 5, 7]", "imports": [], "original_snippet": "def f(starting_number, upper_number):\n    candidate = starting_number + 1\n    result = []\n    while candidate <= upper_number:\n        is_prime = True\n        for i in range(2, int(candidate ** 0.5) + 1):\n            if candidate % i == 0:\n                is_prime = False\n                break\n        if is_prime:\n            result.append(candidate)\n        candidate += 1\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(n):\n    if n <= 0:\n        return []\n    result = [0]\n    if n > 1:\n        result.append(1)\n    for i in range(2, n):\n        result.append(result[-1] + result[-2])\n    return result", "input": "10", "output": "[0, 1, 1, 2, 3, 5, 8, 13, 21, 34]", "imports": [], "original_snippet": "def f(n):\n    if n <= 0:\n        return []\n    result = [0]\n    if n > 1:\n        result.append(1)\n    for i in range(2, n):\n        result.append(result[-1] + result[-2])\n    return result", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(input_str: str) -> tuple:\n    original_string = input_str\n    modified_string = input_str\n    result = ''\n    for char in modified_string:\n        if char.isupper():\n            result += char.lower()\n        else:\n            result += char.upper()\n    modified_string = result\n    vowel_count = 0\n    vowels = 'aeiouAEIOU'\n    for char in modified_string:\n        if char in vowels:\n            vowel_count += 1\n    reversed_string = modified_string[::-1]\n    return (original_string, reversed_string, vowel_count)", "input": "'Hello world!'", "output": "('Hello world!', '!DLROW OLLEh', 3)", "imports": [], "original_snippet": "def f(input_str: str) -> tuple:\n    original_string = input_str\n    modified_string = input_str\n    result = ''\n    for char in modified_string:\n        if char.isupper():\n            result += char.lower()\n        else:\n            result += char.upper()\n    modified_string = result\n    vowel_count = 0\n    vowels = 'aeiouAEIOU'\n    for char in modified_string:\n        if char in vowels:\n            vowel_count += 1\n    reversed_string = modified_string[::-1]\n    return (original_string, reversed_string, vowel_count)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(num_attendees: str, num_tickets: str):\n    num_attendees = int(num_attendees)\n    num_tickets = int(num_tickets)\n    return num_attendees // num_tickets", "input": "\"750\", \"15\"", "output": "50", "imports": [], "original_snippet": "def f(num_attendees: str, num_tickets: str):\n    num_attendees = int(num_attendees)\n    num_tickets = int(num_tickets)\n    return num_attendees // num_tickets", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(string_list: list[str]) -> list[str]:\n    transformed_strings = []\n    for string in string_list:\n        transformed_string = ''\n        length = len(string)\n        for i in range(length):\n            if string[i].isalpha():\n                transformed_string += chr(ord(string[i]) + length - i)\n            else:\n                transformed_string += string[i]\n        transformed_strings.append(transformed_string)\n    return transformed_strings", "input": "['cat', 'dog', 'elephant', 'fish']", "output": "['fcu', 'gqh', 'mskuldpu', 'jlui']", "imports": [], "original_snippet": "def f(string_list: list[str]) -> list[str]:\n    transformed_strings = []\n    for string in string_list:\n        transformed_string = ''\n        length = len(string)\n        for i in range(length):\n            if string[i].isalpha():\n                transformed_string += chr(ord(string[i]) + length - i)\n            else:\n                transformed_string += string[i]\n        transformed_strings.append(transformed_string)\n    return transformed_strings", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(list, initial_state):\n    state = initial_state.copy()\n    if sorted(list) != list:\n        state['data'] = state.get('data', []) + sorted([elem for elem in state.get('data', []) if elem not in list])\n        state['data'] = sorted(state['data'] + list)\n    for element in list:\n        if element not in state['data']:\n            state['data'].append(element)\n            state['data'] = sorted(state['data'])\n    return state", "input": "[3, 1, 4, 2], {'data': [2, 5, 6, 3]}", "output": "{'data': [1, 2, 2, 3, 3, 4, 5, 5, 6, 6]}", "imports": [], "original_snippet": "def f(list, initial_state):\n    state = initial_state.copy()\n    if sorted(list) != list:\n        state['data'] = state.get('data', []) + sorted([elem for elem in state.get('data', []) if elem not in list])\n        state['data'] = sorted(state['data'] + list)\n    for element in list:\n        if element not in state['data']:\n            state['data'].append(element)\n            state['data'] = sorted(state['data'])\n    return state", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(n):\n    if n <= 1:\n        return False\n    else:\n        for i in range(2, int(n / 2) + 1):\n            if n % i == 0:\n                return False\n        else:\n            return True", "input": "29", "output": "True", "imports": [], "original_snippet": "def f(n):\n    if n <= 1:\n        return False\n    else:\n        for i in range(2, int(n / 2) + 1):\n            if n % i == 0:\n                return False\n        else:\n            return True", "composite_functions": [], "_input_type": "int", "_output_type": "bool"}
{"snippet": "def f(text: str) -> dict:\n    char_data = {}\n    char_count = dict()\n    for char in text:\n        if char.isalpha():\n            char_data[char] = []\n            char_count[char] = 0\n    for (index, char) in enumerate(text):\n        if char in char_data:\n            char_count[char] = char_count.get(char, 0) + 1\n            char_data[char].append((char_count[char], index))\n    return char_data", "input": "'Hello, world!'", "output": "{'H': [(1, 0)], 'e': [(1, 1)], 'l': [(1, 2), (2, 3), (3, 10)], 'o': [(1, 4), (2, 8)], 'w': [(1, 7)], 'r': [(1, 9)], 'd': [(1, 11)]}", "imports": [], "original_snippet": "def f(text: str) -> dict:\n    char_data = {}\n    char_count = dict()\n    for char in text:\n        if char.isalpha():\n            char_data[char] = []\n            char_count[char] = 0\n    for (index, char) in enumerate(text):\n        if char in char_data:\n            char_count[char] = char_count.get(char, 0) + 1\n            char_data[char].append((char_count[char], index))\n    return char_data", "composite_functions": [], "_input_type": "str", "_output_type": "dict"}
{"snippet": "def f(sentence: str, word_list: list):\n    split_sentence = sentence.split(' ')\n    for i in range(len(split_sentence)):\n        split_sentence[i] = split_sentence[i] + ':' + str(split_sentence.count(split_sentence[i]))\n    return ' '.join(split_sentence)", "input": "'apple orange apple lemon', ['apple', 'orange', 'banana', 'lemon', 'grape', 'kiwi']", "output": "'apple:2 orange:1 apple:1 lemon:1'", "imports": [], "original_snippet": "def f(sentence: str, word_list: list):\n    split_sentence = sentence.split(' ')\n    for i in range(len(split_sentence)):\n        split_sentence[i] = split_sentence[i] + ':' + str(split_sentence.count(split_sentence[i]))\n    return ' '.join(split_sentence)", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(color_dictionary):\n    smallest_key_length = float('inf')\n    matching_key = None\n    visited = set()\n    for (color, syllable_combination) in color_dictionary.items():\n        for (_, pair_element) in enumerate(syllable_combination):\n            if pair_element in visited:\n                count_this_color = sum((1 for other_color in color_dictionary if color_dictionary[other_color] == syllable_combination))\n                if count_this_color <= len(syllable_combination) and count_this_color <= smallest_key_length:\n                    smallest_key_length = count_this_color\n                    matching_key = color\n                break\n        visited.add(syllable_combination[0])\n    return matching_key", "input": "{\n    'Night': ('Hol', 'Syt'),\n    'Sea': ('Hol', 'Syt'),\n    'Sky': ('Hol', 'Syt'),\n    'Baby': ('Baby', 'BAb', 'Bi'),\n    'Disc': ('Dis', 'Disc', 'dis'),\n    'Ocean': ('Oac', 'On',)\n}", "output": "None", "imports": [], "original_snippet": "def f(color_dictionary):\n    smallest_key_length = float('inf')\n    matching_key = None\n    visited = set()\n    for (color, syllable_combination) in color_dictionary.items():\n        for (_, pair_element) in enumerate(syllable_combination):\n            if pair_element in visited:\n                count_this_color = sum((1 for other_color in color_dictionary if color_dictionary[other_color] == syllable_combination))\n                if count_this_color <= len(syllable_combination) and count_this_color <= smallest_key_length:\n                    smallest_key_length = count_this_color\n                    matching_key = color\n                break\n        visited.add(syllable_combination[0])\n    return matching_key", "composite_functions": [], "_input_type": "dict", "_output_type": "NoneType"}
{"snippet": "def f(num: int) -> list:\n    result = []\n    pr_factor = 1\n    pr_i = 2\n    while num > 1:\n        if num % pr_i == 0:\n            if result and result[-1][0] == pr_i:\n                result[-1][1] += 1\n            else:\n                result.append([pr_i, 1])\n            pr_factor *= pr_i\n            num //= pr_i\n        else:\n            pr_i += 1\n    if result:\n        return result\n    else:\n        return [[num, 1]]", "input": "64", "output": "[[2, 6]]", "imports": [], "original_snippet": "def f(num: int) -> list:\n    result = []\n    pr_factor = 1\n    pr_i = 2\n    while num > 1:\n        if num % pr_i == 0:\n            if result and result[-1][0] == pr_i:\n                result[-1][1] += 1\n            else:\n                result.append([pr_i, 1])\n            pr_factor *= pr_i\n            num //= pr_i\n        else:\n            pr_i += 1\n    if result:\n        return result\n    else:\n        return [[num, 1]]", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(string: str) -> dict:\n    state = {'character_mapping': {}, 'word_count': {}, 'character_list': []}\n    for (index, character) in enumerate(string):\n        if character not in state['character_mapping']:\n            state['character_mapping'][character] = index\n        else:\n            state['character_mapping'][character] = state['character_mapping'][character]\n    current_word = ''\n    for character in string:\n        if character == ' ':\n            if current_word != '':\n                state['word_count'][current_word] = state['word_count'].get(current_word, 0) + 1\n            current_word = ''\n        else:\n            current_word += character\n    for (index, character) in enumerate(string):\n        if character not in state['character_list']:\n            state['character_list'].append(character)\n        else:\n            state['character_list'] = state['character_list']\n    return state", "input": "'hello world'", "output": "{'character_mapping': {'h': 0, 'e': 1, 'l': 2, 'o': 4, ' ': 5, 'w': 6, 'r': 8, 'd': 10}, 'word_count': {'hello': 1}, 'character_list': ['h', 'e', 'l', 'o', ' ', 'w', 'r', 'd']}", "imports": [], "original_snippet": "def f(string: str) -> dict:\n    state = {'character_mapping': {}, 'word_count': {}, 'character_list': []}\n    for (index, character) in enumerate(string):\n        if character not in state['character_mapping']:\n            state['character_mapping'][character] = index\n        else:\n            state['character_mapping'][character] = state['character_mapping'][character]\n    current_word = ''\n    for character in string:\n        if character == ' ':\n            if current_word != '':\n                state['word_count'][current_word] = state['word_count'].get(current_word, 0) + 1\n            current_word = ''\n        else:\n            current_word += character\n    for (index, character) in enumerate(string):\n        if character not in state['character_list']:\n            state['character_list'].append(character)\n        else:\n            state['character_list'] = state['character_list']\n    return state", "composite_functions": [], "_input_type": "str", "_output_type": "dict"}
{"snippet": "def f(string_list):\n    final_results = []\n    for string in string_list:\n        current_count = 1\n        for char in string:\n            current_count = current_count * 2 ** ord(char)\n        unicode_values = [ord(char) for char in string]\n        skippable_character_count = min(len(unicode_values), len(string))\n        current_uncounted = 0\n        for num in unicode_values[:skippable_character_count]:\n            current_uncounted = current_uncounted + num\n        current_count += current_uncounted\n        final_results.append(current_count)\n    return final_results", "input": "['abc', 'defg']", "output": "[31828687130226345097944463881396533766429193651030253916189694521162207808802136034115878, 165263992197562149737978827008192759957101170741070304821162198818601447809077836456297302609928821211897803006255839576470]", "imports": [], "original_snippet": "def f(string_list):\n    final_results = []\n    for string in string_list:\n        current_count = 1\n        for char in string:\n            current_count = current_count * 2 ** ord(char)\n        unicode_values = [ord(char) for char in string]\n        skippable_character_count = min(len(unicode_values), len(string))\n        current_uncounted = 0\n        for num in unicode_values[:skippable_character_count]:\n            current_uncounted = current_uncounted + num\n        current_count += current_uncounted\n        final_results.append(current_count)\n    return final_results", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "import secrets\ndef f(input_string: str):\n    output_string = ''\n    for i in range(len(input_string)):\n        random_num = secrets.randbelow(len(input_string))\n        output_string += input_string[random_num]\n    return output_string", "input": "'hello'", "output": "", "imports": ["import secrets"], "original_snippet": "import secrets\ndef f(input_string: str):\n    output_string = ''\n    for i in range(len(input_string)):\n        random_num = secrets.randbelow(len(input_string))\n        output_string += input_string[random_num]\n    return output_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_data: list) -> tuple:\n    result = []\n    processed_input = []\n    for (idx, item) in enumerate(input_data):\n        processed_input.append(f'{str(item)}')\n        max_value = max([int(str_val) for str_val in str(item) if str_val.isdigit()])\n        duplicates_count = sum((1 for char in str(item) if str(item).count(char) > 1))\n        result.append((max_value, duplicates_count))\n    return (result, ','.join(processed_input))", "input": "[123, 456, 789, 101112, 131415]", "output": "([(3, 0), (6, 0), (9, 0), (2, 4), (5, 3)], '123,456,789,101112,131415')", "imports": [], "original_snippet": "def f(input_data: list) -> tuple:\n    result = []\n    processed_input = []\n    for (idx, item) in enumerate(input_data):\n        processed_input.append(f'{str(item)}')\n        max_value = max([int(str_val) for str_val in str(item) if str_val.isdigit()])\n        duplicates_count = sum((1 for char in str(item) if str(item).count(char) > 1))\n        result.append((max_value, duplicates_count))\n    return (result, ','.join(processed_input))", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "import itertools\ndef f(words):\n    all_combinations = []\n    for length in range(1, len(words) + 1):\n        all_combinations.extend(itertools.combinations(words, length))\n    transformed_combinations = []\n    for comb in all_combinations:\n        product = 1\n        for word in comb:\n            product *= len(word)\n        transformed_combinations.append(product)\n    lookup_table = {}\n    for (i, (comb, transformation)) in enumerate(zip(all_combinations, transformed_combinations)):\n        initial_chars = ''.join([word[0] for word in comb])\n        lookup_table[f'combination_{i}'] = (initial_chars, transformation)\n    return lookup_table", "input": "['apple', 'banana', 'cat', 'dog']", "output": "{'combination_0': ('a', 5), 'combination_1': ('b', 6), 'combination_2': ('c', 3), 'combination_3': ('d', 3), 'combination_4': ('ab', 30), 'combination_5': ('ac', 15), 'combination_6': ('ad', 15), 'com...8': ('bd', 18), 'combination_9': ('cd', 9), 'combination_10': ('abc', 90), 'combination_11': ('abd', 90), 'combination_12': ('acd', 45), 'combination_13': ('bcd', 54), 'combination_14': ('abcd', 270)}", "imports": ["import itertools"], "original_snippet": "import itertools\ndef f(words):\n    all_combinations = []\n    for length in range(1, len(words) + 1):\n        all_combinations.extend(itertools.combinations(words, length))\n    transformed_combinations = []\n    for comb in all_combinations:\n        product = 1\n        for word in comb:\n            product *= len(word)\n        transformed_combinations.append(product)\n    lookup_table = {}\n    for (i, (comb, transformation)) in enumerate(zip(all_combinations, transformed_combinations)):\n        initial_chars = ''.join([word[0] for word in comb])\n        lookup_table[f'combination_{i}'] = (initial_chars, transformation)\n    return lookup_table", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(dictionary):\n    swapped_dict = {}\n    for (key, value) in dictionary.items():\n        swapped_dict[value] = key\n    return swapped_dict", "input": "{'google': '20', 'apple': '10', 'facebook': '30'}", "output": "{'20': 'google', '10': 'apple', '30': 'facebook'}", "imports": [], "original_snippet": "def f(dictionary):\n    swapped_dict = {}\n    for (key, value) in dictionary.items():\n        swapped_dict[value] = key\n    return swapped_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(original_string: str):\n    words = original_string.split()\n    longest_word = ''\n    shortest_word = words[0]\n    reversed_words = []\n    for word in words:\n        if len(word) > len(longest_word):\n            longest_word = word\n        if len(word) < len(shortest_word):\n            shortest_word = word\n        reversed_word = word[::-1]\n        reversed_words.append(reversed_word)\n    total_chars = len(''.join(reversed_words))\n    if total_chars % len(longest_word) == 0:\n        num_extra_chars = len(longest_word) - len(reversed_words[-1])\n        reversed_words[-1] += ' ' * num_extra_chars\n    result = ' '.join(reversed_words)\n    return result", "input": "\"The quick brown fox jumps over the lazy dog\"", "output": "'ehT kciuq nworb xof spmuj revo eht yzal god  '", "imports": [], "original_snippet": "def f(original_string: str):\n    words = original_string.split()\n    longest_word = ''\n    shortest_word = words[0]\n    reversed_words = []\n    for word in words:\n        if len(word) > len(longest_word):\n            longest_word = word\n        if len(word) < len(shortest_word):\n            shortest_word = word\n        reversed_word = word[::-1]\n        reversed_words.append(reversed_word)\n    total_chars = len(''.join(reversed_words))\n    if total_chars % len(longest_word) == 0:\n        num_extra_chars = len(longest_word) - len(reversed_words[-1])\n        reversed_words[-1] += ' ' * num_extra_chars\n    result = ' '.join(reversed_words)\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(string: str) -> list:\n    count = 0\n    special_characters = []\n    for i in range(len(string)):\n        if string[i] in ['@', '#', '$', '_']:\n            continue\n        if count == 0:\n            count += 1\n        elif string[i] == string[i - 1]:\n            count += 1\n        else:\n            special_characters.append(string[i - 1])\n            count = 1\n    special_characters.append(string[-1])\n    return [(i + 1, char) for (i, char) in enumerate(special_characters)]", "input": "'#python @_is a very #fun language$'", "output": "[(1, 'p'), (2, 'y'), (3, 't'), (4, 'h'), (5, 'o'), (6, 'n'), (7, '_'), (8, 'i'), (9, 's'), (10, ' '), (11, 'a'), (12, ' '), (13, 'v'), (14, 'e'), (15, 'r'), (16, 'y'), (17, '#'), (18, 'f'), (19, 'u'), (20, 'n'), (21, ' '), (22, 'l'), (23, 'a'), (24, 'n'), (25, 'g'), (26, 'u'), (27, 'a'), (28, 'g'), (29, '$')]", "imports": [], "original_snippet": "def f(string: str) -> list:\n    count = 0\n    special_characters = []\n    for i in range(len(string)):\n        if string[i] in ['@', '#', '$', '_']:\n            continue\n        if count == 0:\n            count += 1\n        elif string[i] == string[i - 1]:\n            count += 1\n        else:\n            special_characters.append(string[i - 1])\n            count = 1\n    special_characters.append(string[-1])\n    return [(i + 1, char) for (i, char) in enumerate(special_characters)]", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(input_list):\n    final = 0\n    for (idx, val) in enumerate(input_list):\n        if idx % 2 == 0:\n            if val > 5:\n                final += val ** 2\n            else:\n                final += val * 2\n    return final - 7", "input": "[4, 7, 2, 6, 9]", "output": "86", "imports": [], "original_snippet": "def f(input_list):\n    final = 0\n    for (idx, val) in enumerate(input_list):\n        if idx % 2 == 0:\n            if val > 5:\n                final += val ** 2\n            else:\n                final += val * 2\n    return final - 7", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers):\n    accumulated_values = []\n    uniques = []\n    unique_count = {}\n    unique_sum = 0\n    accumulated_total = 0\n    for (index, number) in enumerate(numbers):\n        if number not in unique_count:\n            uniques.append(number)\n            unique_count[number] = 1\n            unique_sum += number\n        accumulated_total += number * (index + 1)\n    return (accumulated_total, uniques, unique_sum)", "input": "[2, 4, 3, 1, 5]", "output": "(48, [2, 4, 3, 1, 5], 15)", "imports": [], "original_snippet": "def f(numbers):\n    accumulated_values = []\n    uniques = []\n    unique_count = {}\n    unique_sum = 0\n    accumulated_total = 0\n    for (index, number) in enumerate(numbers):\n        if number not in unique_count:\n            uniques.append(number)\n            unique_count[number] = 1\n            unique_sum += number\n        accumulated_total += number * (index + 1)\n    return (accumulated_total, uniques, unique_sum)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(input_list: list):\n    squared_list = []\n    for num in input_list:\n        squared_list.append(num * num)\n    return squared_list", "input": "[1, 2, 3, 4]", "output": "[1, 4, 9, 16]", "imports": [], "original_snippet": "def f(input_list: list):\n    squared_list = []\n    for num in input_list:\n        squared_list.append(num * num)\n    return squared_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list):\n    total_age = 0\n    counter = {}\n    for person in input_list:\n        if 'age' in person:\n            age = int(person['age'])\n        elif person.get('city') == 'New York':\n            age = next((x for x in sorted(counter) if x > 100), 101)\n            counter[age] = counter.get(age, 0) + 1\n        else:\n            age = 101\n        total_age += age\n    return total_age", "input": "[{\"name\": \"John\", \"city\": \"New York\"}, {\"name\": \"Alice\", \"age\": \"45\"}, {\"name\": \"Bob\"}, {\"name\": \"Charlie\", \"age\": \"25\"}, {\"name\": \"Doe\", \"city\": \"New York\"}]", "output": "373", "imports": [], "original_snippet": "def f(input_list):\n    total_age = 0\n    counter = {}\n    for person in input_list:\n        if 'age' in person:\n            age = int(person['age'])\n        elif person.get('city') == 'New York':\n            age = next((x for x in sorted(counter) if x > 100), 101)\n            counter[age] = counter.get(age, 0) + 1\n        else:\n            age = 101\n        total_age += age\n    return total_age", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input1: str, input2: str) -> dict:\n    result = {}\n    unique_characters = set(input2)\n    for char in unique_characters:\n        result[char] = input1.count(char)\n    return (result, len(unique_characters))", "input": "'aaabbbccdd', 'abcd'", "output": "({'d': 2, 'a': 3, 'b': 3, 'c': 2}, 4)", "imports": [], "original_snippet": "def f(input1: str, input2: str) -> dict:\n    result = {}\n    unique_characters = set(input2)\n    for char in unique_characters:\n        result[char] = input1.count(char)\n    return (result, len(unique_characters))", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(matrix: list) -> list:\n    result = []\n    for col in range(len(matrix[0])):\n        row = []\n        for row_index in range(len(matrix)):\n            row.append(matrix[row_index][col])\n        result.append(row)\n    return result", "input": "[[2, 4, 6, 8], [1, 3, 5, 7], [10, 20, 30, 40], [100, 200, 300, 400]]", "output": "[[2, 1, 10, 100], [4, 3, 20, 200], [6, 5, 30, 300], [8, 7, 40, 400]]", "imports": [], "original_snippet": "def f(matrix: list) -> list:\n    result = []\n    for col in range(len(matrix[0])):\n        row = []\n        for row_index in range(len(matrix)):\n            row.append(matrix[row_index][col])\n        result.append(row)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(output: int, multiplier: int):\n    state = {'total': 0}\n    num_items = output // multiplier\n    for i in range(num_items):\n        state[f'item_{i}'] = i * multiplier\n    return (state['item_0'], state['item_1'], state['item_2'], state['item_3'], state['item_4'], state['item_5'], state['item_6'], state['item_7'], state['item_8'], state['item_9'])", "input": "244306700, 2590435", "output": "(0, 2590435, 5180870, 7771305, 10361740, 12952175, 15542610, 18133045, 20723480, 23313915)", "imports": [], "original_snippet": "def f(output: int, multiplier: int):\n    state = {'total': 0}\n    num_items = output // multiplier\n    for i in range(num_items):\n        state[f'item_{i}'] = i * multiplier\n    return (state['item_0'], state['item_1'], state['item_2'], state['item_3'], state['item_4'], state['item_5'], state['item_6'], state['item_7'], state['item_8'], state['item_9'])", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(input_data: dict):\n    return {...}", "input": "[[\"parent\", \"John\"], [\"child\", \"Bob\", 10], [\"child\", \"Alice\", 15], [\"parent\", \"Emma\"], [\"child\", \"Connor\", 25], [\"child\", \"Mia\", 8]]", "output": "{Ellipsis}", "imports": [], "original_snippet": "def f(input_data: dict):\n    return {...}", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(baby_names: dict) -> dict:\n    new_dict = {}\n    for (name, length) in baby_names.items():\n        new_dict[name] = len(name)\n    sorted_dict = dict(sorted(new_dict.items(), key=lambda item: item[0]))\n    return sorted_dict", "input": "{'Eva': 3, 'Ethan': 5, 'Iseult': 6}", "output": "{'Ethan': 5, 'Eva': 3, 'Iseult': 6}", "imports": [], "original_snippet": "def f(baby_names: dict) -> dict:\n    new_dict = {}\n    for (name, length) in baby_names.items():\n        new_dict[name] = len(name)\n    sorted_dict = dict(sorted(new_dict.items(), key=lambda item: item[0]))\n    return sorted_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(numbers: list) -> tuple:\n    original_numbers = numbers[:]\n    processed_numbers = []\n    sum_processed = 0\n    for num in numbers:\n        if num % 2 == 0:\n            processed_numbers.append(num // 2)\n            sum_processed += num // 2\n        else:\n            processed_numbers.append(num * 3)\n            sum_processed += num * 3\n    return (original_numbers, processed_numbers, sum_processed)", "input": "[2, 4, 6, 8, 9]", "output": "([2, 4, 6, 8, 9], [1, 2, 3, 4, 27], 37)", "imports": [], "original_snippet": "def f(numbers: list) -> tuple:\n    original_numbers = numbers[:]\n    processed_numbers = []\n    sum_processed = 0\n    for num in numbers:\n        if num % 2 == 0:\n            processed_numbers.append(num // 2)\n            sum_processed += num // 2\n        else:\n            processed_numbers.append(num * 3)\n            sum_processed += num * 3\n    return (original_numbers, processed_numbers, sum_processed)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(input_str: str, n: int) -> dict:\n    substr_count = {}\n    for i in range(len(input_str)):\n        for j in range(i + 1, len(input_str) + 1):\n            if j - i <= n:\n                substring = input_str[i:j]\n                if substring in substr_count:\n                    substr_count[substring] += 1\n                else:\n                    substr_count[substring] = 1\n    return substr_count", "input": "\"Python Programming\", 3", "output": "{'P': 2, 'Py': 1, 'Pyt': 1, 'y': 1, 'yt': 1, 'yth': 1, 't': 1, 'th': 1, 'tho': 1, 'h': 1, 'ho': 1, 'hon': 1, 'o': 2, 'on': 1, 'on ': 1, 'n': 2, 'n ': 1, 'n P': 1, ' ': 1, ' P': 1, ' Pr': 1, 'Pr': 1, '...'r': 2, 'ro': 1, 'rog': 1, 'og': 1, 'ogr': 1, 'g': 2, 'gr': 1, 'gra': 1, 'ra': 1, 'ram': 1, 'a': 1, 'am': 1, 'amm': 1, 'm': 2, 'mm': 1, 'mmi': 1, 'mi': 1, 'min': 1, 'i': 1, 'in': 1, 'ing': 1, 'ng': 1}", "imports": [], "original_snippet": "def f(input_str: str, n: int) -> dict:\n    substr_count = {}\n    for i in range(len(input_str)):\n        for j in range(i + 1, len(input_str) + 1):\n            if j - i <= n:\n                substring = input_str[i:j]\n                if substring in substr_count:\n                    substr_count[substring] += 1\n                else:\n                    substr_count[substring] = 1\n    return substr_count", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(N: int) -> str:\n    odds = 0\n    evens = 0\n    for i in range(N + 1):\n        odds ^= 2 ** 3 ** i\n        evens ^= 2 ** 2 ** i\n    return bin(odds & evens)[2:]\n    output = '0'\n    place = 1\n    odds = 3\n    evens = 4\n    while min(odds, evens) < 2 ** (N + 1):\n        output += str(place % 2 * odds)\n        output += str((1 - place % 2) * evens)\n        place += 1\n        odds = max(odds, evens) * 2 * 2\n        evens += odds\n    if N == 0:\n        output = output[:-1]\n    return output", "input": "6", "output": "'10'", "imports": [], "original_snippet": "def f(N: int) -> str:\n    odds = 0\n    evens = 0\n    for i in range(N + 1):\n        odds ^= 2 ** 3 ** i\n        evens ^= 2 ** 2 ** i\n    return bin(odds & evens)[2:]\n    output = '0'\n    place = 1\n    odds = 3\n    evens = 4\n    while min(odds, evens) < 2 ** (N + 1):\n        output += str(place % 2 * odds)\n        output += str((1 - place % 2) * evens)\n        place += 1\n        odds = max(odds, evens) * 2 * 2\n        evens += odds\n    if N == 0:\n        output = output[:-1]\n    return output", "composite_functions": [], "_input_type": "int", "_output_type": "str"}
{"snippet": "def f(number: int):\n    divisors_sum = sum([i for i in range(1, number) if number % i == 0])\n    return divisors_sum", "input": "28", "output": "28", "imports": [], "original_snippet": "def f(number: int):\n    divisors_sum = sum([i for i in range(1, number) if number % i == 0])\n    return divisors_sum", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(A):\n    n = len(A)\n    A.sort()\n    sum_of_products = 0\n    for i in range(n):\n        sum_of_products += (A[n - 1] - A[i]) * A[i] ** (n - i - 1)\n    return sum_of_products", "input": "[3, 2, 1, 2]", "output": "8", "imports": [], "original_snippet": "def f(A):\n    n = len(A)\n    A.sort()\n    sum_of_products = 0\n    for i in range(n):\n        sum_of_products += (A[n - 1] - A[i]) * A[i] ** (n - i - 1)\n    return sum_of_products", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(list1: list, list2: list) -> str:\n    for i in range(len(list2)):\n        j = i - 1\n        key = list1[i]\n        while j >= 0 and (key < list1[j]) != list2[i]:\n            list1[j + 1] = list1[j]\n            j -= 1\n        list1[j + 1] = key\n    result = ''.join([chr(ord('A') + i) for i in range(len(list1))])\n    return result", "input": "[23, 85, 1776, 21], [-1, 1, -1, 1]", "output": "'ABCD'", "imports": [], "original_snippet": "def f(list1: list, list2: list) -> str:\n    for i in range(len(list2)):\n        j = i - 1\n        key = list1[i]\n        while j >= 0 and (key < list1[j]) != list2[i]:\n            list1[j + 1] = list1[j]\n            j -= 1\n        list1[j + 1] = key\n    result = ''.join([chr(ord('A') + i) for i in range(len(list1))])\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(input_string: str):\n    state = {'string_length': len(input_string), 'lowercase_list': [], 'uppercase_list': [], 'mixed_list': [], 'concatenated_string': ''}\n    for character in input_string:\n        if character.islower():\n            state['lowercase_list'].append(character)\n        elif character.isupper():\n            state['uppercase_list'].append(character)\n        else:\n            state['mixed_list'].append(character)\n    for character_list in [state['uppercase_list'], state['lowercase_list'], state['mixed_list']]:\n        state['concatenated_string'] = ''.join(character_list) + state['concatenated_string']\n    return state", "input": "'Hello World!'", "output": "{'string_length': 12, 'lowercase_list': ['e', 'l', 'l', 'o', 'o', 'r', 'l', 'd'], 'uppercase_list': ['H', 'W'], 'mixed_list': [' ', '!'], 'concatenated_string': ' !elloorldHW'}", "imports": [], "original_snippet": "def f(input_string: str):\n    state = {'string_length': len(input_string), 'lowercase_list': [], 'uppercase_list': [], 'mixed_list': [], 'concatenated_string': ''}\n    for character in input_string:\n        if character.islower():\n            state['lowercase_list'].append(character)\n        elif character.isupper():\n            state['uppercase_list'].append(character)\n        else:\n            state['mixed_list'].append(character)\n    for character_list in [state['uppercase_list'], state['lowercase_list'], state['mixed_list']]:\n        state['concatenated_string'] = ''.join(character_list) + state['concatenated_string']\n    return state", "composite_functions": [], "_input_type": "str", "_output_type": "dict"}
{"snippet": "def f(input_array: list):\n    transformed_array = []\n    for string in input_array:\n        transformed_string = ''\n        for (i, char) in enumerate(string):\n            if char.isalpha():\n                transformed_string += char\n        transformed_array.append(transformed_string)\n    return transformed_array", "input": "['hello world', 'Python 3.8', '123456', 'programming', 'This is a test.', '']", "output": "['helloworld', 'Python', '', 'programming', 'Thisisatest', '']", "imports": [], "original_snippet": "def f(input_array: list):\n    transformed_array = []\n    for string in input_array:\n        transformed_string = ''\n        for (i, char) in enumerate(string):\n            if char.isalpha():\n                transformed_string += char\n        transformed_array.append(transformed_string)\n    return transformed_array", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_str):\n    output_str = ''\n    count = 0\n    vowels = 'aeiouAEIOU'\n    for char in input_str:\n        if char.isalpha():\n            if char in vowels:\n                output_str += str(ord(char) + count)\n            else:\n                output_str += char\n            count += 1\n        elif count % 2 == 0:\n            output_str += char * 2\n        else:\n            output_str += char * 3\n    return output_str", "input": "'MakePythonAlgorithmsEasy'", "output": "'M98k104Pyth119n75lg124r120thms89118sy'", "imports": [], "original_snippet": "def f(input_str):\n    output_str = ''\n    count = 0\n    vowels = 'aeiouAEIOU'\n    for char in input_str:\n        if char.isalpha():\n            if char in vowels:\n                output_str += str(ord(char) + count)\n            else:\n                output_str += char\n            count += 1\n        elif count % 2 == 0:\n            output_str += char * 2\n        else:\n            output_str += char * 3\n    return output_str", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_number):\n    if input_number % 7 == 0:\n        return True\n    else:\n        return False", "input": "14", "output": "True", "imports": [], "original_snippet": "def f(input_number):\n    if input_number % 7 == 0:\n        return True\n    else:\n        return False", "composite_functions": [], "_input_type": "int", "_output_type": "bool"}
{"snippet": "def f(input_list):\n    result_list = []\n    for num in input_list:\n        if num % 2 == 0:\n            result_list.append(num * 2)\n        else:\n            result_list.append(num * 3)\n    return result_list", "input": "[1, 2, 3, 4, 5, 6]", "output": "[3, 4, 9, 8, 15, 12]", "imports": [], "original_snippet": "def f(input_list):\n    result_list = []\n    for num in input_list:\n        if num % 2 == 0:\n            result_list.append(num * 2)\n        else:\n            result_list.append(num * 3)\n    return result_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_string: str) -> str:\n    output_string = input_string.upper()\n    vowels_indices = []\n    consonants_indices = []\n    vowels = ['A', 'E', 'I', 'O', 'U']\n    for i in range(len(output_string)):\n        if output_string[i] in vowels:\n            vowels_indices.append(i)\n        else:\n            consonants_indices.append(i)\n    swapped_string = ''\n    for i in range(len(vowels_indices)):\n        if i < len(consonants_indices):\n            swapped_string += output_string[consonants_indices[i]]\n        else:\n            swapped_string += output_string[vowels_indices[i]]\n        if i < len(vowels_indices):\n            swapped_string += output_string[vowels_indices[i]]\n        else:\n            swapped_string += output_string[consonants_indices[i]]\n    return swapped_string", "input": "\"Hello World!\"", "output": "'HELOLO'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    output_string = input_string.upper()\n    vowels_indices = []\n    consonants_indices = []\n    vowels = ['A', 'E', 'I', 'O', 'U']\n    for i in range(len(output_string)):\n        if output_string[i] in vowels:\n            vowels_indices.append(i)\n        else:\n            consonants_indices.append(i)\n    swapped_string = ''\n    for i in range(len(vowels_indices)):\n        if i < len(consonants_indices):\n            swapped_string += output_string[consonants_indices[i]]\n        else:\n            swapped_string += output_string[vowels_indices[i]]\n        if i < len(vowels_indices):\n            swapped_string += output_string[vowels_indices[i]]\n        else:\n            swapped_string += output_string[consonants_indices[i]]\n    return swapped_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(arr, target):\n    left = 0\n    right = len(arr) - 1\n    while left <= right:\n        mid = (left + right) // 2\n        if arr[mid] == target:\n            return mid\n        elif arr[mid] < target:\n            left = mid + 1\n        else:\n            right = mid - 1\n    return -1", "input": "[10, 20, 30, 40, 50], 40", "output": "3", "imports": [], "original_snippet": "def f(arr, target):\n    left = 0\n    right = len(arr) - 1\n    while left <= right:\n        mid = (left + right) // 2\n        if arr[mid] == target:\n            return mid\n        elif arr[mid] < target:\n            left = mid + 1\n        else:\n            right = mid - 1\n    return -1", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(x1, x2, x3, x4):\n    return x1 and x2 and x3 and x4 or (x1 and x2) or (x3 and x4)", "input": "False, False, True, False", "output": "False", "imports": [], "original_snippet": "def f(x1, x2, x3, x4):\n    return x1 and x2 and x3 and x4 or (x1 and x2) or (x3 and x4)", "composite_functions": [], "_input_type": "tuple", "_output_type": "bool"}
{"snippet": "def f(input: str) -> str:\n    vowel_counts = {}\n    consonant_sets = {}\n    for char in input.lower():\n        if char in 'aeiou':\n            vowel_counts[char] = vowel_counts.get(char, 0) + 1\n        else:\n            set_of_chars = frozenset(filter(lambda x: x != char, input.lower()))\n            if set_of_chars not in consonant_sets:\n                consonant_sets[set_of_chars] = {char}\n            else:\n                consonant_sets[set_of_chars].add(char)\n    result = ''\n    for count in sorted(vowel_counts.values()):\n        for (char, ccount) in vowel_counts.items():\n            if ccount == count:\n                result += char\n                break\n    for set_of_chars in sorted(consonant_sets, key=lambda x: len(x)):\n        for char in consonant_sets[set_of_chars]:\n            result += char\n    return result", "input": "\"Python is fun to code\"", "output": "'iiiopythn sfcd'", "imports": [], "original_snippet": "def f(input: str) -> str:\n    vowel_counts = {}\n    consonant_sets = {}\n    for char in input.lower():\n        if char in 'aeiou':\n            vowel_counts[char] = vowel_counts.get(char, 0) + 1\n        else:\n            set_of_chars = frozenset(filter(lambda x: x != char, input.lower()))\n            if set_of_chars not in consonant_sets:\n                consonant_sets[set_of_chars] = {char}\n            else:\n                consonant_sets[set_of_chars].add(char)\n    result = ''\n    for count in sorted(vowel_counts.values()):\n        for (char, ccount) in vowel_counts.items():\n            if ccount == count:\n                result += char\n                break\n    for set_of_chars in sorted(consonant_sets, key=lambda x: len(x)):\n        for char in consonant_sets[set_of_chars]:\n            result += char\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(to_do_list: list):\n    return to_do_list", "input": "['Todo 1', 'Todo 2', 'Todo 3', 'Todo 4', 'Todo 5', 'Todo 6']", "output": "['Todo 1', 'Todo 2', 'Todo 3', 'Todo 4', 'Todo 5', 'Todo 6']", "imports": [], "original_snippet": "def f(to_do_list: list):\n    return to_do_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list):\n    if not isinstance(input_list, list) or not all([isinstance(i, int) and i >= 0 for i in input_list]) or len(input_list) == 0:\n        return 'Invalid input, input must be a list of non-negative integers.'\n    num_nodes = len(input_list)\n    neighbors = [set() for _ in range(num_nodes)]\n    degrees = [0] * num_nodes\n    for idx in range(num_nodes):\n        if idx > 0:\n            neighbors[idx].add(idx - 1)\n        if idx < num_nodes - 1:\n            neighbors[idx].add(idx + 1)\n        degrees[idx] = len(neighbors[idx])\n    results = {}\n    for (idx, val) in enumerate(input_list):\n        result = val\n        for adj_idx in neighbors[idx]:\n            if degrees[adj_idx] > 0:\n                result += input_list[adj_idx]\n        results[idx] = result\n    unique_output = sum(results.values())\n    return unique_output", "input": "['A', 'D', 'H', 'J']", "output": "'Invalid input, input must be a list of non-negative integers.'", "imports": [], "original_snippet": "def f(input_list):\n    if not isinstance(input_list, list) or not all([isinstance(i, int) and i >= 0 for i in input_list]) or len(input_list) == 0:\n        return 'Invalid input, input must be a list of non-negative integers.'\n    num_nodes = len(input_list)\n    neighbors = [set() for _ in range(num_nodes)]\n    degrees = [0] * num_nodes\n    for idx in range(num_nodes):\n        if idx > 0:\n            neighbors[idx].add(idx - 1)\n        if idx < num_nodes - 1:\n            neighbors[idx].add(idx + 1)\n        degrees[idx] = len(neighbors[idx])\n    results = {}\n    for (idx, val) in enumerate(input_list):\n        result = val\n        for adj_idx in neighbors[idx]:\n            if degrees[adj_idx] > 0:\n                result += input_list[adj_idx]\n        results[idx] = result\n    unique_output = sum(results.values())\n    return unique_output", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(numbers_1, numbers_2):\n    total_sum = sum(numbers_1) + sum(numbers_2)\n    unique_numbers = list(set().union(set(numbers_1), set(numbers_2)))\n    unique_sum = sum(unique_numbers)\n    return (total_sum, unique_numbers, unique_sum)", "input": "[10, 20, 30], [20, 40, 60]", "output": "(180, [20, 40, 10, 60, 30], 160)", "imports": [], "original_snippet": "def f(numbers_1, numbers_2):\n    total_sum = sum(numbers_1) + sum(numbers_2)\n    unique_numbers = list(set().union(set(numbers_1), set(numbers_2)))\n    unique_sum = sum(unique_numbers)\n    return (total_sum, unique_numbers, unique_sum)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "from collections import deque\ndef f(input_list):\n    str_words = deque([])\n    max_len = max(map(len, input_list))\n    states = [['' for _ in range(max_len)], ['' for _ in range(max_len + 1)]]\n    for word in input_list:\n        while str_words and len(word) < len(states[0][0]):\n            str_words.popleft()\n        if word in states[0]:\n            states[0].remove(word)\n            states[0].extend(states[1])\n            states[1][:] = [word]\n        else:\n            str_words.append(word)\n    while str_words:\n        str_words.popleft()\n    return (states[0], states[1])", "input": "[\"aim\", \"bill\", \"if\", \"appreciate\", \"explain\", \"love\"]", "output": "(['', '', '', '', '', '', '', '', '', ''], ['', '', '', '', '', '', '', '', '', '', ''])", "imports": ["from collections import deque"], "original_snippet": "from collections import deque\ndef f(input_list):\n    str_words = deque([])\n    max_len = max(map(len, input_list))\n    states = [['' for _ in range(max_len)], ['' for _ in range(max_len + 1)]]\n    for word in input_list:\n        while str_words and len(word) < len(states[0][0]):\n            str_words.popleft()\n        if word in states[0]:\n            states[0].remove(word)\n            states[0].extend(states[1])\n            states[1][:] = [word]\n        else:\n            str_words.append(word)\n    while str_words:\n        str_words.popleft()\n    return (states[0], states[1])", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(numbers: list) -> list:\n    pairs = []\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            pairs.append((i, j, numbers[i] + numbers[j]))\n    return pairs", "input": "[2, 3, 4, 5]", "output": "[(0, 1, 5), (0, 2, 6), (0, 3, 7), (1, 2, 7), (1, 3, 8), (2, 3, 9)]", "imports": [], "original_snippet": "def f(numbers: list) -> list:\n    pairs = []\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            pairs.append((i, j, numbers[i] + numbers[j]))\n    return pairs", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(numbers: list[int]):\n    prime_factors_cache = {}\n    for number in numbers:\n        if number > 0:\n            if number in prime_factors_cache:\n                factors = prime_factors_cache[number]\n            else:\n                factors = {}\n                factor = 2\n                while factor * factor <= number:\n                    while number % factor == 0:\n                        factors[factor] = factors.get(factor, 0) + 1\n                        number //= factor\n                    factor += 1\n                if number > 1:\n                    factors[number] = factors.get(number, 0) + 1\n                prime_factors_cache[number] = factors\n            (even, odd) = (0, 0)\n            total = 0\n            for count in factors.values():\n                total += 1\n                if count % 2 == 0:\n                    even += 1\n                else:\n                    odd += 1\n            if odd > 0:\n                if even > 0:\n                    total += 1\n                else:\n                    total += 2\n            elif even == 1:\n                total += 2\n            else:\n                total += 3\n            print(f'{total}: ', end='')\n        else:\n            print('0: ', end='')\n    print('')", "input": "[60, 120, -999, 829458, 785959, 909001]", "output": "None", "imports": [], "original_snippet": "def f(numbers: list[int]):\n    prime_factors_cache = {}\n    for number in numbers:\n        if number > 0:\n            if number in prime_factors_cache:\n                factors = prime_factors_cache[number]\n            else:\n                factors = {}\n                factor = 2\n                while factor * factor <= number:\n                    while number % factor == 0:\n                        factors[factor] = factors.get(factor, 0) + 1\n                        number //= factor\n                    factor += 1\n                if number > 1:\n                    factors[number] = factors.get(number, 0) + 1\n                prime_factors_cache[number] = factors\n            (even, odd) = (0, 0)\n            total = 0\n            for count in factors.values():\n                total += 1\n                if count % 2 == 0:\n                    even += 1\n                else:\n                    odd += 1\n            if odd > 0:\n                if even > 0:\n                    total += 1\n                else:\n                    total += 2\n            elif even == 1:\n                total += 2\n            else:\n                total += 3\n            print(f'{total}: ', end='')\n        else:\n            print('0: ', end='')\n    print('')", "composite_functions": [], "_input_type": "list", "_output_type": "NoneType"}
{"snippet": "def f(input_list: list[int]) -> list[int]:\n    if not input_list or len(input_list) == 1:\n        return input_list\n    output_list = [1] * len(input_list)\n    for i in range(len(input_list)):\n        for j in range(i):\n            if input_list[j] < input_list[i] and output_list[i] <= output_list[j]:\n                output_list[i] = output_list[j] + 1\n    max_len = max(output_list)\n    lis = []\n    lis_idx = 0\n    for i in range(len(input_list) - 1, -1, -1):\n        if output_list[i] == max_len:\n            lis.append(input_list[i])\n            max_len -= 1\n        if max_len == 0:\n            break\n    lis.reverse()\n    return lis", "input": "[3, 10, 2, 1, 20]", "output": "[3, 10, 20]", "imports": [], "original_snippet": "def f(input_list: list[int]) -> list[int]:\n    if not input_list or len(input_list) == 1:\n        return input_list\n    output_list = [1] * len(input_list)\n    for i in range(len(input_list)):\n        for j in range(i):\n            if input_list[j] < input_list[i] and output_list[i] <= output_list[j]:\n                output_list[i] = output_list[j] + 1\n    max_len = max(output_list)\n    lis = []\n    lis_idx = 0\n    for i in range(len(input_list) - 1, -1, -1):\n        if output_list[i] == max_len:\n            lis.append(input_list[i])\n            max_len -= 1\n        if max_len == 0:\n            break\n    lis.reverse()\n    return lis", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_string: str) -> list:\n    unicode_values = [ord(char) for char in input_string]\n    transformations = {}\n    for (i, value) in enumerate(unicode_values):\n        if value % 2 == 0 and value % 3 != 0:\n            transformations[i] = 'e'\n        elif value % 2 == 1 and value % 3 != 0:\n            transformations[i] = 'o'\n        elif value % 2 == 0 and value % 3 == 0 or (value % 2 == 1 and value % 3 == 0):\n            transformations[i] = 'Another complex transformation'\n        elif value % 3 == 2 and any([num in str(value) for num in ['1', '2', '3', '4', '5']]):\n            transformations[i] = 'Another complex transformation for special case'\n        elif all([num in str(value) for num in ['1', '2', '3', '4', '5']]):\n            transformations[i] = 'Sum from set 1 to 5 is 15 and here you need to track that'\n        elif value > 100:\n            transformations[i] = 'A value greater than 100 is detected'\n        else:\n            transformations[i] = 'Unidentified transformation'\n    return [length for length in transformations.values()]", "input": "'This is a test for transformation'", "output": "['Another complex transformation', 'e', 'Another complex transformation', 'o', 'e', 'Another complex transformation', 'o', 'e', 'o', 'e', 'e', 'o', 'o', 'e', 'e', 'Another complex transformation', 'An... 'e', 'o', 'Another complex transformation', 'Another complex transformation', 'Another complex transformation', 'o', 'o', 'e', 'Another complex transformation', 'Another complex transformation', 'e']", "imports": [], "original_snippet": "def f(input_string: str) -> list:\n    unicode_values = [ord(char) for char in input_string]\n    transformations = {}\n    for (i, value) in enumerate(unicode_values):\n        if value % 2 == 0 and value % 3 != 0:\n            transformations[i] = 'e'\n        elif value % 2 == 1 and value % 3 != 0:\n            transformations[i] = 'o'\n        elif value % 2 == 0 and value % 3 == 0 or (value % 2 == 1 and value % 3 == 0):\n            transformations[i] = 'Another complex transformation'\n        elif value % 3 == 2 and any([num in str(value) for num in ['1', '2', '3', '4', '5']]):\n            transformations[i] = 'Another complex transformation for special case'\n        elif all([num in str(value) for num in ['1', '2', '3', '4', '5']]):\n            transformations[i] = 'Sum from set 1 to 5 is 15 and here you need to track that'\n        elif value > 100:\n            transformations[i] = 'A value greater than 100 is detected'\n        else:\n            transformations[i] = 'Unidentified transformation'\n    return [length for length in transformations.values()]", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_string):\n    periods = 0\n    for char in input_string:\n        if char == '.':\n            periods += 1\n    return periods", "input": "'Hello..world..!'", "output": "4", "imports": [], "original_snippet": "def f(input_string):\n    periods = 0\n    for char in input_string:\n        if char == '.':\n            periods += 1\n    return periods", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_list: list) -> list:\n    result = [num for num in input_list if num % 2 == 0]\n    result.sort()\n    return result", "input": "[1,2,3,5,6,7,83,10]", "output": "[2, 6, 10]", "imports": [], "original_snippet": "def f(input_list: list) -> list:\n    result = [num for num in input_list if num % 2 == 0]\n    result.sort()\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(text: str) -> str:\n    encrypted_text = ''\n    Caesar_shift = len(text)\n    for char in text:\n        if char.isalpha():\n            if char.isupper():\n                encrypted_text += chr((ord(char) - ord('A') + Caesar_shift) % 26 + ord('A'))\n            else:\n                encrypted_text += chr((ord(char) - ord('a') + Caesar_shift) % 26 + ord('a'))\n        else:\n            encrypted_text += char\n    return encrypted_text", "input": "'Hello World!'", "output": "'Tqxxa Iadxp!'", "imports": [], "original_snippet": "def f(text: str) -> str:\n    encrypted_text = ''\n    Caesar_shift = len(text)\n    for char in text:\n        if char.isalpha():\n            if char.isupper():\n                encrypted_text += chr((ord(char) - ord('A') + Caesar_shift) % 26 + ord('A'))\n            else:\n                encrypted_text += chr((ord(char) - ord('a') + Caesar_shift) % 26 + ord('a'))\n        else:\n            encrypted_text += char\n    return encrypted_text", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(numbers: list[int]) -> int:\n    max_sum = numbers[0]\n    current_sum = numbers[0]\n    for i in range(1, len(numbers)):\n        current_sum = max(current_sum + numbers[i], numbers[i])\n        max_sum = max(max_sum, current_sum)\n    return max_sum", "input": "[-2, 1, -3, 4, -1, 2, 1, -5, 4]", "output": "6", "imports": [], "original_snippet": "def f(numbers: list[int]) -> int:\n    max_sum = numbers[0]\n    current_sum = numbers[0]\n    for i in range(1, len(numbers)):\n        current_sum = max(current_sum + numbers[i], numbers[i])\n        max_sum = max(max_sum, current_sum)\n    return max_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_string):\n    letter_counts = {}\n    for char in input_string:\n        letter_counts[char] = letter_counts.get(char, 0) + 1\n    highest_freq = 0\n    lowest_freq = float('inf')\n    for (char, freq) in letter_counts.items():\n        if freq > highest_freq:\n            highest_freq = freq\n            highest_char = char\n        if freq < lowest_freq:\n            lowest_freq = freq\n            lowest_char = char\n    result_string = highest_char + lowest_char\n    return result_string", "input": "'Hello, World!'", "output": "'lH'", "imports": [], "original_snippet": "def f(input_string):\n    letter_counts = {}\n    for char in input_string:\n        letter_counts[char] = letter_counts.get(char, 0) + 1\n    highest_freq = 0\n    lowest_freq = float('inf')\n    for (char, freq) in letter_counts.items():\n        if freq > highest_freq:\n            highest_freq = freq\n            highest_char = char\n        if freq < lowest_freq:\n            lowest_freq = freq\n            lowest_char = char\n    result_string = highest_char + lowest_char\n    return result_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_str: str) -> str:\n    reversed_str = input_str[::-1]\n    uppercase_chars = [c.upper() for c in reversed_str if c.isalpha()]\n    even_positions = [c for c in reversed_str if sorted(reversed_str).index(c) % 2 == 0]\n    common_chars = list(set(uppercase_chars) & set(even_positions))\n    return ''.join(common_chars)", "input": "\"Hello, World!\"", "output": "'W'", "imports": [], "original_snippet": "def f(input_str: str) -> str:\n    reversed_str = input_str[::-1]\n    uppercase_chars = [c.upper() for c in reversed_str if c.isalpha()]\n    even_positions = [c for c in reversed_str if sorted(reversed_str).index(c) % 2 == 0]\n    common_chars = list(set(uppercase_chars) & set(even_positions))\n    return ''.join(common_chars)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_string: str, input_length: int) -> int:\n    string_length = len(input_string)\n    state = {'prefix_sum': 0, 'suffix_sum': 0, 'sequence_repeat_count': {}}\n    operations = {'prefix_sum_operations': 0, 'suffix_sum_operations': 0, 'sequence_repeat_operations': 0}\n    for index in range(string_length):\n        char = input_string[index]\n        if char not in state['sequence_repeat_count']:\n            state['sequence_repeat_count'][char] = 1\n        else:\n            state['sequence_repeat_count'][char] += 1\n        if index < input_length:\n            local_prefix_sum = index * state['sequence_repeat_count'][char]\n            state['prefix_sum'] += local_prefix_sum\n            operations['prefix_sum_operations'] += local_prefix_sum\n        else:\n            local_suffix_sum = (string_length - index) * state['sequence_repeat_count'][char]\n            state['suffix_sum'] += local_suffix_sum\n            operations['suffix_sum_operations'] += local_suffix_sum\n    result = state['prefix_sum'] + state['suffix_sum']\n    return result", "input": "'abcabcabcabcabcabcabcabcabc', 3", "output": "1275", "imports": [], "original_snippet": "def f(input_string: str, input_length: int) -> int:\n    string_length = len(input_string)\n    state = {'prefix_sum': 0, 'suffix_sum': 0, 'sequence_repeat_count': {}}\n    operations = {'prefix_sum_operations': 0, 'suffix_sum_operations': 0, 'sequence_repeat_operations': 0}\n    for index in range(string_length):\n        char = input_string[index]\n        if char not in state['sequence_repeat_count']:\n            state['sequence_repeat_count'][char] = 1\n        else:\n            state['sequence_repeat_count'][char] += 1\n        if index < input_length:\n            local_prefix_sum = index * state['sequence_repeat_count'][char]\n            state['prefix_sum'] += local_prefix_sum\n            operations['prefix_sum_operations'] += local_prefix_sum\n        else:\n            local_suffix_sum = (string_length - index) * state['sequence_repeat_count'][char]\n            state['suffix_sum'] += local_suffix_sum\n            operations['suffix_sum_operations'] += local_suffix_sum\n    result = state['prefix_sum'] + state['suffix_sum']\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(input_data: dict) -> dict:\n    result = {}\n    for (key, value) in input_data.items():\n        if key not in result:\n            result[key] = value\n        else:\n            raise ValueError('Duplicate key found')\n    for (key, value) in result.items():\n        if isinstance(value, dict):\n            result[key] = {inner_key: inner_value for (inner_key, inner_value) in sorted(value.items(), key=lambda item: item[1])}\n            if len(result[key]) != len(value):\n                raise ValueError('Duplicate values in nested dictionary')\n    return result", "input": "{\n    'A': {'a': 1},\n    'B': {'b': 2},\n    'C': {'a': 3, 'b': 1},\n    'D': {'b': 2},\n}", "output": "{'A': {'a': 1}, 'B': {'b': 2}, 'C': {'b': 1, 'a': 3}, 'D': {'b': 2}}", "imports": [], "original_snippet": "def f(input_data: dict) -> dict:\n    result = {}\n    for (key, value) in input_data.items():\n        if key not in result:\n            result[key] = value\n        else:\n            raise ValueError('Duplicate key found')\n    for (key, value) in result.items():\n        if isinstance(value, dict):\n            result[key] = {inner_key: inner_value for (inner_key, inner_value) in sorted(value.items(), key=lambda item: item[1])}\n            if len(result[key]) != len(value):\n                raise ValueError('Duplicate values in nested dictionary')\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(numbers: list[int]) -> float:\n    even_count = 0\n    odd_count = 0\n    for num in numbers:\n        if num % 2 == 0:\n            even_count += 1\n        elif num % 3 != 0:\n            odd_count += 1\n    return even_count ** 2 / odd_count ** 3", "input": "[1, 2, 3, 4, 5]", "output": "0.5", "imports": [], "original_snippet": "def f(numbers: list[int]) -> float:\n    even_count = 0\n    odd_count = 0\n    for num in numbers:\n        if num % 2 == 0:\n            even_count += 1\n        elif num % 3 != 0:\n            odd_count += 1\n    return even_count ** 2 / odd_count ** 3", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(listInts: list[int]) -> list[int]:\n    for i in range(len(listInts)):\n        for j in range(0, len(listInts) - i - 1):\n            if listInts[j] > listInts[j + 1]:\n                (listInts[j], listInts[j + 1]) = (listInts[j + 1], listInts[j])\n    return listInts", "input": "[10, 3, -1, 35, 0]", "output": "[-1, 0, 3, 10, 35]", "imports": [], "original_snippet": "def f(listInts: list[int]) -> list[int]:\n    for i in range(len(listInts)):\n        for j in range(0, len(listInts) - i - 1):\n            if listInts[j] > listInts[j + 1]:\n                (listInts[j], listInts[j + 1]) = (listInts[j + 1], listInts[j])\n    return listInts", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "import heapq\nimport collections\nimport re\ndef f(input_str):\n    input_str = input_str.lower()\n    uniques = list(set(input_str))\n    unique_heap = []\n    for unq in uniques:\n        heapq.heappush(unique_heap, (-input_str.count(unq), -ord(unq), -len(unq), unq))\n    stack = []\n    substrings = []\n    is_palindrome = True\n    current_substring = ''\n    for c in input_str[::-1]:\n        if c != ' ':\n            current_substring += c\n            is_palindrome = is_palindrome and c == input_str[len(input_str) - len(current_substring) - 1]\n        else:\n            substrings.append(current_substring[::-1])\n            current_substring = ''\n    while current_substring:\n        substrings.append(current_substring[::-1])\n        current_substring = ''\n    stored_palindromes = []\n    for s in substrings:\n        stored_palindromes.append(s[::-1])\n    result = ''\n    result = ''.join(heapq.heappop(unique_heap)[3])\n    while stack:\n        result += stack[-1]\n        stack.pop()\n    char_freq = collections.Counter(input_str)\n    return (result, uniques, char_freq, stored_palindromes)", "input": "'race car'", "output": "('r', ['c', 'a', ' ', 'r', 'e'], Counter({'r': 2, 'a': 2, 'c': 2, 'e': 1, ' ': 1}), ['rac', 'ecar'])", "imports": ["import heapq", "import collections", "import re"], "original_snippet": "import heapq\nimport collections\nimport re\ndef f(input_str):\n    input_str = input_str.lower()\n    uniques = list(set(input_str))\n    unique_heap = []\n    for unq in uniques:\n        heapq.heappush(unique_heap, (-input_str.count(unq), -ord(unq), -len(unq), unq))\n    stack = []\n    substrings = []\n    is_palindrome = True\n    current_substring = ''\n    for c in input_str[::-1]:\n        if c != ' ':\n            current_substring += c\n            is_palindrome = is_palindrome and c == input_str[len(input_str) - len(current_substring) - 1]\n        else:\n            substrings.append(current_substring[::-1])\n            current_substring = ''\n    while current_substring:\n        substrings.append(current_substring[::-1])\n        current_substring = ''\n    stored_palindromes = []\n    for s in substrings:\n        stored_palindromes.append(s[::-1])\n    result = ''\n    result = ''.join(heapq.heappop(unique_heap)[3])\n    while stack:\n        result += stack[-1]\n        stack.pop()\n    char_freq = collections.Counter(input_str)\n    return (result, uniques, char_freq, stored_palindromes)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_str):\n    input_list = input_str.split(',')\n    output_list = []\n    unique_word_count = {}\n    unique_word_index = 0\n    for word_group in input_list:\n        words = word_group.split('=')\n        input_word = words[0].strip('\" ')\n        search_word = words[1].strip('\" ')\n        if input_word not in unique_word_count:\n            unique_word_count[input_word] = 0\n            unique_word_index += 1\n        unique_word_count[input_word] += 1\n        for i in range(1, unique_word_index + 1):\n            output_list.append((input_word, unique_word_count[input_word] + search_word.find(input_word) + i))\n    return output_list", "input": "\"'Apple'=Apple, 'Banana'=banana, 'Cherry'=CHERRY, 'Apple'=apples\"", "output": "[(\"'Apple'\", 1), (\"'Banana'\", 1), (\"'Banana'\", 2), (\"'Cherry'\", 1), (\"'Cherry'\", 2), (\"'Cherry'\", 3), (\"'Apple'\", 2), (\"'Apple'\", 3), (\"'Apple'\", 4)]", "imports": [], "original_snippet": "def f(input_str):\n    input_list = input_str.split(',')\n    output_list = []\n    unique_word_count = {}\n    unique_word_index = 0\n    for word_group in input_list:\n        words = word_group.split('=')\n        input_word = words[0].strip('\" ')\n        search_word = words[1].strip('\" ')\n        if input_word not in unique_word_count:\n            unique_word_count[input_word] = 0\n            unique_word_index += 1\n        unique_word_count[input_word] += 1\n        for i in range(1, unique_word_index + 1):\n            output_list.append((input_word, unique_word_count[input_word] + search_word.find(input_word) + i))\n    return output_list", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "def f(input_list):\n    result_dict = {}\n    sum_result = 0\n    for (idx, num) in enumerate(input_list):\n        result_dict[idx] = num ** 2\n        sum_result += num ** 2\n    return (result_dict, sum_result)", "input": "[2, 4, 6, 8, 10]", "output": "({0: 4, 1: 16, 2: 36, 3: 64, 4: 100}, 220)", "imports": [], "original_snippet": "def f(input_list):\n    result_dict = {}\n    sum_result = 0\n    for (idx, num) in enumerate(input_list):\n        result_dict[idx] = num ** 2\n        sum_result += num ** 2\n    return (result_dict, sum_result)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(input):\n    tracker = {'sum': 0, 'count': 0}\n    for val in input:\n        tracker['count'] = len(input)\n        tracker['sum'] = tracker['sum'] + val\n    output = {key: tracker[key] for key in ['count', 'sum']}\n    return output", "input": "[1, 2, 3, 4, 5]", "output": "{'count': 5, 'sum': 15}", "imports": [], "original_snippet": "def f(input):\n    tracker = {'sum': 0, 'count': 0}\n    for val in input:\n        tracker['count'] = len(input)\n        tracker['sum'] = tracker['sum'] + val\n    output = {key: tracker[key] for key in ['count', 'sum']}\n    return output", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(input_number: int) -> dict:\n    if input_number <= 0:\n        return 'Please enter a positive number'\n    digits = [int(x) for x in str(input_number)]\n    digit_groups = {'1-digit': 0, '2-digits': 0, '3-digits': 0, '4-digits': 0}\n    for digit in digits:\n        count = len(str(digit))\n        if count in digit_groups:\n            digit_groups[count] += 1\n    return digit_groups", "input": "12801432", "output": "{'1-digit': 0, '2-digits': 0, '3-digits': 0, '4-digits': 0}", "imports": [], "original_snippet": "def f(input_number: int) -> dict:\n    if input_number <= 0:\n        return 'Please enter a positive number'\n    digits = [int(x) for x in str(input_number)]\n    digit_groups = {'1-digit': 0, '2-digits': 0, '3-digits': 0, '4-digits': 0}\n    for digit in digits:\n        count = len(str(digit))\n        if count in digit_groups:\n            digit_groups[count] += 1\n    return digit_groups", "composite_functions": [], "_input_type": "int", "_output_type": "dict"}
{"snippet": "def f(strings):\n    result = []\n    previous_words = set()\n    for string in strings:\n        current_words = set(string.split())\n        result.append(' '.join(current_words - previous_words))\n        previous_words = current_words\n    return ' '.join(result)", "input": "(\n    'A fish out of water.',\n    'The quick brown fox jumps over the lazy dog.',\n    'The quick brown fox'\n)", "output": "'fish water. out of A The the jumps brown quick fox lazy over dog. '", "imports": [], "original_snippet": "def f(strings):\n    result = []\n    previous_words = set()\n    for string in strings:\n        current_words = set(string.split())\n        result.append(' '.join(current_words - previous_words))\n        previous_words = current_words\n    return ' '.join(result)", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(dictionary):\n    total = 0\n    for (key, value) in dictionary.items():\n        if isinstance(value, dict):\n            for (nested_key, nested_value) in value.items():\n                if isinstance(nested_value, list):\n                    for item in nested_value:\n                        if isinstance(item, dict):\n                            if 'value' in item and 'condition' in item:\n                                if item['condition'] == 'even':\n                                    total += item['value'] * 2\n                                else:\n                                    total += item['value'] ** 2\n    return total", "input": "{\"outer\": {\"inner\": [{\"value\": 5, \"condition\": \"even\"}, {\"value\": 7, \"condition\": \"odd\"}], \"condition\": \"even\"}}", "output": "59", "imports": [], "original_snippet": "def f(dictionary):\n    total = 0\n    for (key, value) in dictionary.items():\n        if isinstance(value, dict):\n            for (nested_key, nested_value) in value.items():\n                if isinstance(nested_value, list):\n                    for item in nested_value:\n                        if isinstance(item, dict):\n                            if 'value' in item and 'condition' in item:\n                                if item['condition'] == 'even':\n                                    total += item['value'] * 2\n                                else:\n                                    total += item['value'] ** 2\n    return total", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(input_string, character):\n    state = character * len(input_string)\n    character_count = 0\n    for character in input_string:\n        if character == character:\n            character_count += 1\n    difference = len(input_string) - character_count\n    state = character * difference\n    return (input_string + state[::-1])[::-1]", "input": "'Hello World!', 'd'", "output": "'!dlroW olleH'", "imports": [], "original_snippet": "def f(input_string, character):\n    state = character * len(input_string)\n    character_count = 0\n    for character in input_string:\n        if character == character:\n            character_count += 1\n    difference = len(input_string) - character_count\n    state = character * difference\n    return (input_string + state[::-1])[::-1]", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(input: list) -> list:\n    result: dict = {}\n    for data in input:\n        (key, value) = data\n        temp_key = ''.join([char for char in key if char.isalpha() or char.isdigit()])\n        if len(temp_key) < 1 or len(temp_key) > 3:\n            raise ValueError('Constraints fail for key')\n        operations = {'A': 1, 'B': -1, 'C': 2, 'D': -2, 'E': 3, 'F': -3, 'G': 4, 'H': -4, 'E': -10}\n        if temp_key not in operations:\n            raise ValueError('Valid operations for key are missing')\n        value = operations[temp_key] * len(value)\n        result[key] = value\n    sortedValues = sorted([(value, key) for (key, value) in result.items()])\n    sortedData = [(tupleSecondary, value) for (value, tupleSecondary) in sortedValues]\n    return sortedData", "input": "[('<=E', \"Good\"), ('!=A', \"Morning\"), ('>H', \"John\"), ('<C', \"Smith\"), ('#F', \"Hello\")]", "output": "[('<=E', -40), ('>H', -16), ('#F', -15), ('!=A', 7), ('<C', 10)]", "imports": [], "original_snippet": "def f(input: list) -> list:\n    result: dict = {}\n    for data in input:\n        (key, value) = data\n        temp_key = ''.join([char for char in key if char.isalpha() or char.isdigit()])\n        if len(temp_key) < 1 or len(temp_key) > 3:\n            raise ValueError('Constraints fail for key')\n        operations = {'A': 1, 'B': -1, 'C': 2, 'D': -2, 'E': 3, 'F': -3, 'G': 4, 'H': -4, 'E': -10}\n        if temp_key not in operations:\n            raise ValueError('Valid operations for key are missing')\n        value = operations[temp_key] * len(value)\n        result[key] = value\n    sortedValues = sorted([(value, key) for (key, value) in result.items()])\n    sortedData = [(tupleSecondary, value) for (value, tupleSecondary) in sortedValues]\n    return sortedData", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(num_1, info_1):\n    num_1 = list(map(int, list(str(num_1))))\n    if len(num_1) == 1:\n        return 0\n    height_1 = 1\n    index = 1\n    for (key, value) in info_1.items():\n        for i in range(len(num_1)):\n            if num_1[i] == value.get(key):\n                height_1 = max(height_1, len(value) + index)\n                index += 1\n            else:\n                index = 1\n    return height_1", "input": "13333,{'!':{'q':0,'5':2},'@':{'1':3,'8':3},'z':{'3':4}}", "output": "1", "imports": [], "original_snippet": "def f(num_1, info_1):\n    num_1 = list(map(int, list(str(num_1))))\n    if len(num_1) == 1:\n        return 0\n    height_1 = 1\n    index = 1\n    for (key, value) in info_1.items():\n        for i in range(len(num_1)):\n            if num_1[i] == value.get(key):\n                height_1 = max(height_1, len(value) + index)\n                index += 1\n            else:\n                index = 1\n    return height_1", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(sequence: list) -> list:\n    result = []\n    for item in sequence:\n        if item % 3 == 0:\n            result.append(~item & 255)\n        elif item % 2 == 0:\n            result.append(-item)\n        else:\n            result.append(item * item)\n    return result", "input": "[9, 8, 7, 6, 5, 4, 3, 2, 1]", "output": "[246, -8, 49, 249, 25, -4, 252, -2, 1]", "imports": [], "original_snippet": "def f(sequence: list) -> list:\n    result = []\n    for item in sequence:\n        if item % 3 == 0:\n            result.append(~item & 255)\n        elif item % 2 == 0:\n            result.append(-item)\n        else:\n            result.append(item * item)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list):\n    unique_list = []\n    for item in input_list:\n        if item not in unique_list:\n            unique_list.append(item)\n    return unique_list", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "imports": [], "original_snippet": "def f(input_list):\n    unique_list = []\n    for item in input_list:\n        if item not in unique_list:\n            unique_list.append(item)\n    return unique_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_string: str):\n    result = {'upper_vowel_set': set(), 'lower_consonant_set': set()}\n    for char in input_string:\n        if char.isupper():\n            if char.lower() in 'aeiou':\n                result['upper_vowel_set'].add(char)\n        elif char.islower():\n            if char not in 'aeiou':\n                result['lower_consonant_set'].add(char)\n    upper_vowel_string = ''.join(sorted(result['upper_vowel_set'], reverse=True))\n    lower_consonant_string = ''.join(sorted(result['lower_consonant_set']))\n    final_string = upper_vowel_string.replace('O', '0', 1) + lower_consonant_string.replace('O', '0', 2)\n    return final_string", "input": "'ZZzz'", "output": "'z'", "imports": [], "original_snippet": "def f(input_string: str):\n    result = {'upper_vowel_set': set(), 'lower_consonant_set': set()}\n    for char in input_string:\n        if char.isupper():\n            if char.lower() in 'aeiou':\n                result['upper_vowel_set'].add(char)\n        elif char.islower():\n            if char not in 'aeiou':\n                result['lower_consonant_set'].add(char)\n    upper_vowel_string = ''.join(sorted(result['upper_vowel_set'], reverse=True))\n    lower_consonant_string = ''.join(sorted(result['lower_consonant_set']))\n    final_string = upper_vowel_string.replace('O', '0', 1) + lower_consonant_string.replace('O', '0', 2)\n    return final_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_string: str, range_start: int, range_end: int) -> dict:\n    result = {}\n    for i in range(range_start, range_end + 1):\n        sum_of_digits = sum((int(digit) for digit in input_string[i:] if digit.isdigit()))\n        result[i] = sum_of_digits\n    return result", "input": "'123456789', 0, 5", "output": "{0: 45, 1: 44, 2: 42, 3: 39, 4: 35, 5: 30}", "imports": [], "original_snippet": "def f(input_string: str, range_start: int, range_end: int) -> dict:\n    result = {}\n    for i in range(range_start, range_end + 1):\n        sum_of_digits = sum((int(digit) for digit in input_string[i:] if digit.isdigit()))\n        result[i] = sum_of_digits\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(input_string: str):\n    transformed_string = ''\n    current_sequence = ''\n    prev_char = ''\n    for char in input_string:\n        if char.isupper():\n            if char != prev_char:\n                new_char = chr(ord('A') + (ord(char) - ord('A') + 1) % 26)\n                transformed_string += new_char\n                current_sequence = ''\n            else:\n                current_sequence += char\n                transformed_string += '_' * len(current_sequence)\n        elif char == ' ':\n            transformed_string += ' '\n        else:\n            current_sequence += char\n            transformed_string += '_' * len(current_sequence)\n        prev_char = char\n    return transformed_string", "input": "'This is a Test to Create a Python Code snippet that Requir'", "output": "'U______ _________ ______ U______ _________ D_______________ ______ Q_______________ D______ _________________________________________________ __________________________________________________ S_______________'", "imports": [], "original_snippet": "def f(input_string: str):\n    transformed_string = ''\n    current_sequence = ''\n    prev_char = ''\n    for char in input_string:\n        if char.isupper():\n            if char != prev_char:\n                new_char = chr(ord('A') + (ord(char) - ord('A') + 1) % 26)\n                transformed_string += new_char\n                current_sequence = ''\n            else:\n                current_sequence += char\n                transformed_string += '_' * len(current_sequence)\n        elif char == ' ':\n            transformed_string += ' '\n        else:\n            current_sequence += char\n            transformed_string += '_' * len(current_sequence)\n        prev_char = char\n    return transformed_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import queue\ndef f(input_list):\n    q = queue.Queue()\n    for item in input_list:\n        q.put(item)\n    max_value = q.queue[0]\n    while not q.empty():\n        current = q.get()\n        if current > max_value:\n            max_value = current\n    while not q.empty():\n        value = q.get()\n        if value != max_value:\n            q.put(value)\n    total_sum = 0\n    for value in q.queue:\n        total_sum += value\n    result = total_sum ** 2\n    return result", "input": "[10, 3, 50, 15, 25]", "output": "0", "imports": ["import queue"], "original_snippet": "import queue\ndef f(input_list):\n    q = queue.Queue()\n    for item in input_list:\n        q.put(item)\n    max_value = q.queue[0]\n    while not q.empty():\n        current = q.get()\n        if current > max_value:\n            max_value = current\n    while not q.empty():\n        value = q.get()\n        if value != max_value:\n            q.put(value)\n    total_sum = 0\n    for value in q.queue:\n        total_sum += value\n    result = total_sum ** 2\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(words, input_var) -> list:\n    if not words or not input_var:\n        return []\n    unprocessed_words = words[:]\n    processed_words_lengths = []\n    result = ['', '', '']\n    iterator = 0\n    while unprocessed_words:\n        word = unprocessed_words.pop(0)\n        processed_words_lengths.extend(word[:input_var])\n        words_list = [stack for stack in result if stack[-input_var:] == '']\n        if words_list:\n            words_list[0] += word\n            iterator += 1\n        else:\n            result[iterator] += word\n    for (i, stack) in enumerate(result):\n        remaining_characters = stack[:input_var]\n        result[i] = stack[input_var:] + ' '.join(processed_words_lengths[:input_var])\n        processed_words_lengths = processed_words_lengths[input_var:] + list(remaining_characters)\n    return (result, processed_words_lengths)", "input": "[\"abc\", \"def\", \"ghi\", \"jkl\", \"mno\", \"pqr\", \"stu\", \"vwx\", \"yz\"],5", "output": "(['a b c d e', 'f g h i j', 'k l m n o'], ['p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'])", "imports": [], "original_snippet": "def f(words, input_var) -> list:\n    if not words or not input_var:\n        return []\n    unprocessed_words = words[:]\n    processed_words_lengths = []\n    result = ['', '', '']\n    iterator = 0\n    while unprocessed_words:\n        word = unprocessed_words.pop(0)\n        processed_words_lengths.extend(word[:input_var])\n        words_list = [stack for stack in result if stack[-input_var:] == '']\n        if words_list:\n            words_list[0] += word\n            iterator += 1\n        else:\n            result[iterator] += word\n    for (i, stack) in enumerate(result):\n        remaining_characters = stack[:input_var]\n        result[i] = stack[input_var:] + ' '.join(processed_words_lengths[:input_var])\n        processed_words_lengths = processed_words_lengths[input_var:] + list(remaining_characters)\n    return (result, processed_words_lengths)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(items):\n    max_sums = {}\n    for (name, value) in items:\n        if name not in max_sums or max_sums[name] < value:\n            max_sums[name] = value\n    value_sums = {}\n    for (name, value) in items:\n        if value is not False:\n            if name not in value_sums:\n                value_sums[name] = 0\n            value_sums[name] += value\n    result = []\n    for (name, value) in items:\n        if value is False:\n            if value_sums.get(name, 0) > value_sums.get(name, 0):\n                value = True\n        if value:\n            result.append((name, value))\n    return result", "input": "[('name0', 10),('name0', 5), ('name1', 15), ('name1', 3), ('name2', 20), ('name2', 10), ('name3', False), ('name4', False)]", "output": "[('name0', 10), ('name0', 5), ('name1', 15), ('name1', 3), ('name2', 20), ('name2', 10)]", "imports": [], "original_snippet": "def f(items):\n    max_sums = {}\n    for (name, value) in items:\n        if name not in max_sums or max_sums[name] < value:\n            max_sums[name] = value\n    value_sums = {}\n    for (name, value) in items:\n        if value is not False:\n            if name not in value_sums:\n                value_sums[name] = 0\n            value_sums[name] += value\n    result = []\n    for (name, value) in items:\n        if value is False:\n            if value_sums.get(name, 0) > value_sums.get(name, 0):\n                value = True\n        if value:\n            result.append((name, value))\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from collections import defaultdict\ndef f(input_list):\n    current_sum = 0\n    even_count = 0\n    variable_state = defaultdict(dict)\n    for (idx, value) in enumerate(input_list):\n        if value % 2 == 0:\n            variable_state[idx].update({'value': value, 'tripled': value * 3})\n            current_sum += variable_state[idx]['tripled']\n            even_count += 1\n        elif value % 3 == 0:\n            variable_state[idx].update({'value': value, 'added': value + 5})\n            current_sum += variable_state[idx]['added']\n        elif value % 5 == 0:\n            variable_state[idx].update({'value': value, 'subtracted': value - 5})\n            current_sum += variable_state[idx]['subtracted']\n        else:\n            variable_state[idx]['value'] = value\n    output_list = []\n    for (idx, value) in variable_state.items():\n        if 'tripled' in value:\n            output_list.append(value['tripled'])\n        elif 'added' in value:\n            output_list.append(value['added'])\n        elif 'subtracted' in value:\n            output_list.append(value['subtracted'])\n        else:\n            output_list.append(value['value'])\n    return output_list + [even_count]", "input": "[6, 9, 10, 15, 20, 21]", "output": "[18, 14, 30, 20, 60, 26, 3]", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(input_list):\n    current_sum = 0\n    even_count = 0\n    variable_state = defaultdict(dict)\n    for (idx, value) in enumerate(input_list):\n        if value % 2 == 0:\n            variable_state[idx].update({'value': value, 'tripled': value * 3})\n            current_sum += variable_state[idx]['tripled']\n            even_count += 1\n        elif value % 3 == 0:\n            variable_state[idx].update({'value': value, 'added': value + 5})\n            current_sum += variable_state[idx]['added']\n        elif value % 5 == 0:\n            variable_state[idx].update({'value': value, 'subtracted': value - 5})\n            current_sum += variable_state[idx]['subtracted']\n        else:\n            variable_state[idx]['value'] = value\n    output_list = []\n    for (idx, value) in variable_state.items():\n        if 'tripled' in value:\n            output_list.append(value['tripled'])\n        elif 'added' in value:\n            output_list.append(value['added'])\n        elif 'subtracted' in value:\n            output_list.append(value['subtracted'])\n        else:\n            output_list.append(value['value'])\n    return output_list + [even_count]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(num: int) -> int:\n    result = 0\n    while num > 0:\n        last_digit = num % 10\n        if last_digit == 2 or last_digit == 3 or last_digit == 5 or (last_digit == 7):\n            result += 1\n        num = num // 10\n    return result", "input": "num = 1234", "output": "2", "imports": [], "original_snippet": "def f(num: int) -> int:\n    result = 0\n    while num > 0:\n        last_digit = num % 10\n        if last_digit == 2 or last_digit == 3 or last_digit == 5 or (last_digit == 7):\n            result += 1\n        num = num // 10\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(sandwich_name):\n    sandwich_ingredients = sandwich_name.split(',')\n    sandwich_price = 0\n    for ingredient in sandwich_ingredients:\n        if ingredient in ['lettuce', 'tomatoes', 'cheese']:\n            price = 5\n            bread_price = 2\n        else:\n            price = random.randint(1, 10)\n            bread_price = 2\n        sandwich_price += (price + bread_price) / 2\n    return round(sandwich_price)", "input": "'cheese,lettuce,tomatoes'", "output": "10", "imports": [], "original_snippet": "def f(sandwich_name):\n    sandwich_ingredients = sandwich_name.split(',')\n    sandwich_price = 0\n    for ingredient in sandwich_ingredients:\n        if ingredient in ['lettuce', 'tomatoes', 'cheese']:\n            price = 5\n            bread_price = 2\n        else:\n            price = random.randint(1, 10)\n            bread_price = 2\n        sandwich_price += (price + bread_price) / 2\n    return round(sandwich_price)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_list):\n    total_sum = 0\n    for i in range(len(input_list)):\n        total_sum += abs(input_list[i]) ** 2\n    return total_sum", "input": "[2, -3, 4, -5]", "output": "54", "imports": [], "original_snippet": "def f(input_list):\n    total_sum = 0\n    for i in range(len(input_list)):\n        total_sum += abs(input_list[i]) ** 2\n    return total_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(input_string: str) -> int:\n    char_indices = {}\n    start = 0\n    max_length = 0\n    for (index, char) in enumerate(input_string):\n        if char in char_indices and char_indices[char] >= start:\n            start = char_indices[char] + 1\n        else:\n            max_length = max(max_length, index - start + 1)\n        char_indices[char] = index\n    return max_length", "input": "\"pwwkew\"", "output": "3", "imports": [], "original_snippet": "def f(input_string: str) -> int:\n    char_indices = {}\n    start = 0\n    max_length = 0\n    for (index, char) in enumerate(input_string):\n        if char in char_indices and char_indices[char] >= start:\n            start = char_indices[char] + 1\n        else:\n            max_length = max(max_length, index - start + 1)\n        char_indices[char] = index\n    return max_length", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_list: list) -> list:\n    if not input_list:\n        raise ValueError('Input list cannot be empty')\n    binary_digits = []\n    for i in range(len(input_list)):\n        if i == 0:\n            binary_digits.append(int(input_list[i] >= 0))\n        else:\n            similar_next_neighbour = abs(input_list[i] - input_list[i - 1]) <= 1e-09 and abs(input_list[i - 1] - input_list[i - 2]) <= 1e-09\n            dissimilar_next_neighbour = not similar_next_neighbour\n            if similar_next_neighbour:\n                binary_digits.append(1)\n            if dissimilar_next_neighbour:\n                binary_digits.append(0)\n    return binary_digits", "input": "[1.23, 1.22, 1.221, 1.232, 1.231]", "output": "[1, 0, 0, 0, 0]", "imports": [], "original_snippet": "def f(input_list: list) -> list:\n    if not input_list:\n        raise ValueError('Input list cannot be empty')\n    binary_digits = []\n    for i in range(len(input_list)):\n        if i == 0:\n            binary_digits.append(int(input_list[i] >= 0))\n        else:\n            similar_next_neighbour = abs(input_list[i] - input_list[i - 1]) <= 1e-09 and abs(input_list[i - 1] - input_list[i - 2]) <= 1e-09\n            dissimilar_next_neighbour = not similar_next_neighbour\n            if similar_next_neighbour:\n                binary_digits.append(1)\n            if dissimilar_next_neighbour:\n                binary_digits.append(0)\n    return binary_digits", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(nums: list) -> dict:\n    counts = {}\n    return counts", "input": "[1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 1, 2, 3, 4, 5]", "output": "{}", "imports": [], "original_snippet": "def f(nums: list) -> dict:\n    counts = {}\n    return counts", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(input_string):\n    letter_counts = {}\n    output_result = ''\n    for char in input_string:\n        if char in letter_counts:\n            letter_counts[char] += 1\n        else:\n            letter_counts[char] = 1\n    for (char, count) in letter_counts.items():\n        output_result += char\n        output_result += str(count)\n    return output_result", "input": "\"saaaaaaaaaajh\"", "output": "'s1a10j1h1'", "imports": [], "original_snippet": "def f(input_string):\n    letter_counts = {}\n    output_result = ''\n    for char in input_string:\n        if char in letter_counts:\n            letter_counts[char] += 1\n        else:\n            letter_counts[char] = 1\n    for (char, count) in letter_counts.items():\n        output_result += char\n        output_result += str(count)\n    return output_result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_list: list) -> dict:\n    answer_dict = {}\n    for string in input_list:\n        reversed_string = string[::-1]\n        for char in reversed_string:\n            if char in answer_dict:\n                answer_dict[char] += 1\n            else:\n                answer_dict[char] = 1\n    return answer_dict", "input": "['hello', 'world', 'python', 'programming']", "output": "{'o': 4, 'l': 3, 'e': 1, 'h': 2, 'd': 1, 'r': 3, 'w': 1, 'n': 2, 't': 1, 'y': 1, 'p': 2, 'g': 2, 'i': 1, 'm': 2, 'a': 1}", "imports": [], "original_snippet": "def f(input_list: list) -> dict:\n    answer_dict = {}\n    for string in input_list:\n        reversed_string = string[::-1]\n        for char in reversed_string:\n            if char in answer_dict:\n                answer_dict[char] += 1\n            else:\n                answer_dict[char] = 1\n    return answer_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(input_str):\n    words = input_str.split()\n    lower_case_words = [word.lower() for word in words]\n    distinct_words = set(lower_case_words)\n    return len(distinct_words)", "input": "\"Hello world\"", "output": "2", "imports": [], "original_snippet": "def f(input_str):\n    words = input_str.split()\n    lower_case_words = [word.lower() for word in words]\n    distinct_words = set(lower_case_words)\n    return len(distinct_words)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(input_string: str) -> str:\n    input_string = input_string.lower()\n    input_string += input_string.upper()\n    appended_chars = []\n    for i in range(26):\n        char = chr(97 + i)\n        char_up = char.upper()\n        if char not in input_string and char_up not in input_string:\n            input_string += char\n            appended_chars.append(char)\n    return input_string", "input": "'abcdefgnijklmnopqrstuvwxyzABCDEF!'", "output": "'abcdefgnijklmnopqrstuvwxyzabcdef!ABCDEFGNIJKLMNOPQRSTUVWXYZABCDEF!h'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    input_string = input_string.lower()\n    input_string += input_string.upper()\n    appended_chars = []\n    for i in range(26):\n        char = chr(97 + i)\n        char_up = char.upper()\n        if char not in input_string and char_up not in input_string:\n            input_string += char\n            appended_chars.append(char)\n    return input_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(input_str: str) -> str:\n    input = list(input_str)\n    stack = []\n    map = {}\n    for i in range(len(input)):\n        stack.append(input[i])\n    for i in range(len(stack)):\n        char = stack.pop()\n        map[char] = ord(char)\n    for i in range(len(input)):\n        if input[i] in map:\n            input[i] = str(map[input[i]])\n    return ''.join(input)", "input": "'Test String!'", "output": "'84101115116328311611410511010333'", "imports": [], "original_snippet": "def f(input_str: str) -> str:\n    input = list(input_str)\n    stack = []\n    map = {}\n    for i in range(len(input)):\n        stack.append(input[i])\n    for i in range(len(stack)):\n        char = stack.pop()\n        map[char] = ord(char)\n    for i in range(len(input)):\n        if input[i] in map:\n            input[i] = str(map[input[i]])\n    return ''.join(input)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(transactions):\n    total_value = 0\n    highest_value = float('-inf')\n    lowest_value = float('inf')\n    for transaction in transactions:\n        if transaction.get('type', 'Deposit') == 'Deposit':\n            total_value += transaction['amount']\n            if transaction['amount'] > highest_value:\n                highest_value = transaction['amount']\n        else:\n            total_value -= transaction['amount']\n            if transaction['amount'] < lowest_value:\n                lowest_value = transaction['amount']\n    return (total_value, highest_value, lowest_value)", "input": "[\n    {'type': 'Deposit', 'amount': 100},\n    {'type': 'Withdrawal', 'amount': 50},\n    {'amount': 200}, # Assuming this is a deposit\n    {'type': 'Deposit', 'amount': 75},\n    {'type': 'Withdrawal', 'amount': 125}, # Close to the minimum value\n    {'type': 'Deposit', 'amount': 100} # Highest value\n]", "output": "(300, 200, 50)", "imports": [], "original_snippet": "def f(transactions):\n    total_value = 0\n    highest_value = float('-inf')\n    lowest_value = float('inf')\n    for transaction in transactions:\n        if transaction.get('type', 'Deposit') == 'Deposit':\n            total_value += transaction['amount']\n            if transaction['amount'] > highest_value:\n                highest_value = transaction['amount']\n        else:\n            total_value -= transaction['amount']\n            if transaction['amount'] < lowest_value:\n                lowest_value = transaction['amount']\n    return (total_value, highest_value, lowest_value)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(n1, n2):\n    sum = n1 + n2\n    product = n1 * n2\n    if sum == 10:\n        delta = abs(product)\n    else:\n        delta = abs(product - sum)\n    return n1 - delta", "input": "4, 3", "output": "-1", "imports": [], "original_snippet": "def f(n1, n2):\n    sum = n1 + n2\n    product = n1 * n2\n    if sum == 10:\n        delta = abs(product)\n    else:\n        delta = abs(product - sum)\n    return n1 - delta", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(n: int, memo={}):\n    if n in memo:\n        return memo[n]\n    if n == 0:\n        return 0\n    memo[n] = sum((f(i, memo) for i in range(n))) * 3 + n\n    return memo[n]", "input": "10", "output": "349525", "imports": [], "original_snippet": "def f(n: int, memo={}):\n    if n in memo:\n        return memo[n]\n    if n == 0:\n        return 0\n    memo[n] = sum((f(i, memo) for i in range(n))) * 3 + n\n    return memo[n]", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
