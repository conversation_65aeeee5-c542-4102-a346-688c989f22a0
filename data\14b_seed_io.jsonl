{"snippet": "def f(a):\n    return a", "input": "\"Hello world\"", "output": "\"Hello world\"", "imports": [], "original_snippet": "def f(a):\n    return a", "composite_functions": []}
{"snippet": "def f(root: list):\n    parents = {0: None}\n    for (i, node) in enumerate(root):\n        if node:\n            if i % 2 == 0:\n                parents[node] = root[(i - 1) // 2] if (i - 1) // 2 in parents else None\n            else:\n                parents[node] = root[(i - 1) // 2] if (i - 1) // 2 in parents else None\n    children = {node: [] for node in parents}\n    for node in parents:\n        if parents[node]:\n            children[parents[node]].append(node)\n    return [parents, children]", "input": "[1, 2, 3, 4, 5, 6, 7]", "output": "[{0: None, 1: None, 2: 1, 3: 1, 4: 2, 5: 2, 6: 3, 7: 3}, {0: [], 1: [2, 3], 2: [4, 5], 3: [6, 7], 4: [], 5: [], 6: [], 7: []}]", "imports": [], "original_snippet": "def f(root: list):\n    parents = {0: None}\n    for (i, node) in enumerate(root):\n        if node:\n            if i % 2 == 0:\n                parents[node] = root[(i - 1) // 2] if (i - 1) // 2 in parents else None\n            else:\n                parents[node] = root[(i - 1) // 2] if (i - 1) // 2 in parents else None\n    children = {node: [] for node in parents}\n    for node in parents:\n        if parents[node]:\n            children[parents[node]].append(node)\n    return [parents, children]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(a: str):\n    ascii_values = [ord(char) for char in a]\n    sorted_values = sorted(ascii_values)\n    encoded_string = ''.join([chr(value) for value in sorted_values])\n    return encoded_string", "input": "\"Hello world\"", "output": "' Hdellloorw'", "imports": [], "original_snippet": "def f(a: str):\n    ascii_values = [ord(char) for char in a]\n    sorted_values = sorted(ascii_values)\n    encoded_string = ''.join([chr(value) for value in sorted_values])\n    return encoded_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(data: dict):\n    data = {k.lower(): v for (k, v) in data.items()}\n    data = {k: sorted(v) for (k, v) in data.items()}\n    flat_list = []\n    for lst in data.values():\n        flat_list.extend(lst)\n    return sorted(flat_list)", "input": "{'Fruits': ['apple', 'banana', 'cherry'], 'Vegetables': ['carrot', 'lemon', 'lettuce'], 'Grains': ['rice', 'wheat', 'barley']}", "output": "['apple', 'banana', 'barley', 'carrot', 'cherry', 'lemon', 'lettuce', 'rice', 'wheat']", "imports": [], "original_snippet": "def f(data: dict):\n    data = {k.lower(): v for (k, v) in data.items()}\n    data = {k: sorted(v) for (k, v) in data.items()}\n    flat_list = []\n    for lst in data.values():\n        flat_list.extend(lst)\n    return sorted(flat_list)", "composite_functions": [], "_input_type": "dict", "_output_type": "list"}
{"snippet": "def f(n):\n    primes = [2]\n    for i in range(3, n + 1, 2):\n        for p in primes:\n            if i % p == 0:\n                break\n        else:\n            primes.append(i)\n    return primes", "input": "53", "output": "[2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53]", "imports": [], "original_snippet": "def f(n):\n    primes = [2]\n    for i in range(3, n + 1, 2):\n        for p in primes:\n            if i % p == 0:\n                break\n        else:\n            primes.append(i)\n    return primes", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(lst: list):\n    sorted_lst = sorted(lst)\n    odd_indexed_elements = sorted_lst[1::2]\n    summed_elements = [odd_indexed_elements[i] + odd_indexed_elements[i + 1] for i in range(0, len(odd_indexed_elements) - 1, 2)]\n    return sum(summed_elements)", "input": "[15, 8, 20, 3, 12, 7, 18, 2, 13, 1]", "output": "36", "imports": [], "original_snippet": "def f(lst: list):\n    sorted_lst = sorted(lst)\n    odd_indexed_elements = sorted_lst[1::2]\n    summed_elements = [odd_indexed_elements[i] + odd_indexed_elements[i + 1] for i in range(0, len(odd_indexed_elements) - 1, 2)]\n    return sum(summed_elements)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(s: str):\n    alphabet = [chr(i) for i in range(ord('a'), ord('z') + 1)]\n    nums = [str(i) for i in range(10)]\n    specials = ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '-']\n    lists = [list(s.lower()), [i for i in s if i in alphabet], [i for i in s if i in nums], [i for i in s if any([i in chars for chars in specials])]]\n    results = [(len(x), sum((ord(i) for i in x)) % 26) for x in lists][1:]\n    return ''.join([chr(x + 97) for x in [y % 26 for y in [sum(x) for x in results]]])", "input": "\"This is an example\"", "output": "'faa'", "imports": [], "original_snippet": "def f(s: str):\n    alphabet = [chr(i) for i in range(ord('a'), ord('z') + 1)]\n    nums = [str(i) for i in range(10)]\n    specials = ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '-']\n    lists = [list(s.lower()), [i for i in s if i in alphabet], [i for i in s if i in nums], [i for i in s if any([i in chars for chars in specials])]]\n    results = [(len(x), sum((ord(i) for i in x)) % 26) for x in lists][1:]\n    return ''.join([chr(x + 97) for x in [y % 26 for y in [sum(x) for x in results]]])", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(lst):\n    lst_sorted = sorted(lst)\n    lst_new = [lst_sorted[0]]\n    for i in range(1, len(lst_sorted)):\n        lst_new.append(lst_sorted[i - 1] + lst_sorted[i])\n    return lst_new", "input": "[1, 3, 5, 7]", "output": "[1, 4, 8, 12]", "imports": [], "original_snippet": "def f(lst):\n    lst_sorted = sorted(lst)\n    lst_new = [lst_sorted[0]]\n    for i in range(1, len(lst_sorted)):\n        lst_new.append(lst_sorted[i - 1] + lst_sorted[i])\n    return lst_new", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(s: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    s_lower = s.lower()\n    s_filtered = ''.join([i for i in s_lower if i.isalnum()])\n    s_converted = ''.join([str(alphabet.index(i)) for i in s_filtered])\n    s_encoded = ''.join([str(int(c) + 7) if int(c) + 7 < 10 else str(int(c) + 7 - 10) for c in s_converted])\n    return s_encoded[::-1].strip('0')", "input": "\"Bell\"", "output": "'888818'", "imports": [], "original_snippet": "def f(s: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    s_lower = s.lower()\n    s_filtered = ''.join([i for i in s_lower if i.isalnum()])\n    s_converted = ''.join([str(alphabet.index(i)) for i in s_filtered])\n    s_encoded = ''.join([str(int(c) + 7) if int(c) + 7 < 10 else str(int(c) + 7 - 10) for c in s_converted])\n    return s_encoded[::-1].strip('0')", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(lst: list, info: dict):\n    merged_info = sorted(list(info.items()), key=lambda x: x[0])\n    merged_lst = sorted(lst) + [x[1] for x in merged_info]\n    return sum(merged_lst)", "input": "[15, 8, 20], {'age': 20, 'height': 177}", "output": "240", "imports": [], "original_snippet": "def f(lst: list, info: dict):\n    merged_info = sorted(list(info.items()), key=lambda x: x[0])\n    merged_lst = sorted(lst) + [x[1] for x in merged_info]\n    return sum(merged_lst)", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(limit: tuple):\n    (next_num, current_num) = (0, 1)\n    fib_numbers = []\n    while next_num <= limit[0]:\n        fib_numbers.append(next_num)\n        (next_num, current_num) = (current_num, next_num + current_num)\n    even_fib_numbers = [num for num in fib_numbers if num % 2 == 0]\n    return sum(even_fib_numbers)", "input": "(20,)", "output": "10", "imports": [], "original_snippet": "def f(limit: tuple):\n    (next_num, current_num) = (0, 1)\n    fib_numbers = []\n    while next_num <= limit[0]:\n        fib_numbers.append(next_num)\n        (next_num, current_num) = (current_num, next_num + current_num)\n    even_fib_numbers = [num for num in fib_numbers if num % 2 == 0]\n    return sum(even_fib_numbers)", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(data: dict):\n    weighted_sum = lambda values: sum(((i + 1) * value for (i, value) in enumerate(values)))\n    weighted_sum_dict = {k: weighted_sum(v) for (k, v) in data.items()}\n    return weighted_sum_dict", "input": "{'A': [1, 2, 3], 'B': [4, 5, 6], 'C': [7, 8, 9]}", "output": "{'A': 14, 'B': 32, 'C': 50}", "imports": [], "original_snippet": "def f(data: dict):\n    weighted_sum = lambda values: sum(((i + 1) * value for (i, value) in enumerate(values)))\n    weighted_sum_dict = {k: weighted_sum(v) for (k, v) in data.items()}\n    return weighted_sum_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(numbers: list, target_sum: int):\n    transformed_numbers = [n % 10 for n in numbers]\n    (start, end) = (0, 1)\n    current_sum = transformed_numbers[start]\n    sequences = []\n    while start < len(transformed_numbers) and end <= len(transformed_numbers):\n        if current_sum == target_sum:\n            sequences.append(transformed_numbers[start:end])\n            start += 1\n            if start < len(transformed_numbers):\n                current_sum += transformed_numbers[start]\n            end = start + 1\n        elif current_sum < target_sum:\n            if end < len(transformed_numbers):\n                current_sum += transformed_numbers[end]\n                end += 1\n            else:\n                start += 1\n                if start < len(transformed_numbers):\n                    current_sum = transformed_numbers[start]\n                end = start + 1\n        else:\n            current_sum -= transformed_numbers[start]\n            start += 1\n    return sequences", "input": "[15, 14, 25, 46, 21, 23, 19], 12", "output": "[[5, 6, 1], [], []]", "imports": [], "original_snippet": "def f(numbers: list, target_sum: int):\n    transformed_numbers = [n % 10 for n in numbers]\n    (start, end) = (0, 1)\n    current_sum = transformed_numbers[start]\n    sequences = []\n    while start < len(transformed_numbers) and end <= len(transformed_numbers):\n        if current_sum == target_sum:\n            sequences.append(transformed_numbers[start:end])\n            start += 1\n            if start < len(transformed_numbers):\n                current_sum += transformed_numbers[start]\n            end = start + 1\n        elif current_sum < target_sum:\n            if end < len(transformed_numbers):\n                current_sum += transformed_numbers[end]\n                end += 1\n            else:\n                start += 1\n                if start < len(transformed_numbers):\n                    current_sum = transformed_numbers[start]\n                end = start + 1\n        else:\n            current_sum -= transformed_numbers[start]\n            start += 1\n    return sequences", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "from typing import List\ndef f(grid: List[List[int]]):\n    row_means = [sum(row) / len(row) for row in grid]\n    row_ranges = [max(row) - min(row) for row in grid]\n    product_result = 1\n    for (mean, rng) in zip(row_means, row_ranges):\n        product_result *= mean * rng\n    normalized_result = product_result % 1\n    return normalized_result", "input": "[[1, 2, 3], [4, 5, 6], [7, 8, 9]]", "output": "0.0", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(grid: List[List[int]]):\n    row_means = [sum(row) / len(row) for row in grid]\n    row_ranges = [max(row) - min(row) for row in grid]\n    product_result = 1\n    for (mean, rng) in zip(row_means, row_ranges):\n        product_result *= mean * rng\n    normalized_result = product_result % 1\n    return normalized_result", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(people: dict):\n    age_groups = {'Teens': range(13, 20), 'Adults': range(20, 60), 'Seniors': range(60, 120)}\n    age_to_color = {age_group: set() for age_group in age_groups}\n    for (name, data) in people.items():\n        for (age, favorite_color) in data.items():\n            for (age_group, age_range) in age_groups.items():\n                if age in age_range:\n                    age_to_color[age_group].add(favorite_color)\n                else:\n                    continue\n    return age_to_color", "input": "{'John': {18: 'blue', 20: 'red'}, 'Jill': {15: 'blue', 25: 'yellow'}, 'Alex': {55: 'green', 60: 'orange'}}", "output": "{'Teens': {'blue'}, 'Adults': {'red', 'yellow', 'green'}, 'Seniors': {'orange'}}", "imports": [], "original_snippet": "def f(people: dict):\n    age_groups = {'Teens': range(13, 20), 'Adults': range(20, 60), 'Seniors': range(60, 120)}\n    age_to_color = {age_group: set() for age_group in age_groups}\n    for (name, data) in people.items():\n        for (age, favorite_color) in data.items():\n            for (age_group, age_range) in age_groups.items():\n                if age in age_range:\n                    age_to_color[age_group].add(favorite_color)\n                else:\n                    continue\n    return age_to_color", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "import math\ndef f(lst: list):\n    lst_reversed = lst[::-1]\n    transformed_lst = [x ** 2 if n % 2 == 0 else x ** (1 / 3) for (n, x) in enumerate(lst_reversed)]\n    return sum(transformed_lst)", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9]", "output": "171.6644426946952", "imports": ["import math"], "original_snippet": "import math\ndef f(lst: list):\n    lst_reversed = lst[::-1]\n    transformed_lst = [x ** 2 if n % 2 == 0 else x ** (1 / 3) for (n, x) in enumerate(lst_reversed)]\n    return sum(transformed_lst)", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(n):\n    matching_values = []\n    rest_sum = 0\n    loop_count = 0\n    for i in range(n):\n        square = i ** 2\n        if square % 2 == 0:\n            matching_values.append(square)\n            rest_sum += i\n        loop_count += 1\n    return sum(matching_values) - loop_count * rest_sum", "input": "50", "output": "-10400", "imports": [], "original_snippet": "def f(n):\n    matching_values = []\n    rest_sum = 0\n    loop_count = 0\n    for i in range(n):\n        square = i ** 2\n        if square % 2 == 0:\n            matching_values.append(square)\n            rest_sum += i\n        loop_count += 1\n    return sum(matching_values) - loop_count * rest_sum", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "import math\ndef f(lst: list):\n    flat_list = [item for sublist in lst for item in sublist]\n    transformed_list = [math.sqrt(item) for item in flat_list]\n    filtered_list = [num for num in transformed_list if num % 2 != 0]\n    return sum(filtered_list)", "input": "[[3, 8], [49, 4], [2, 100]]", "output": "12.974691494688162", "imports": ["import math"], "original_snippet": "import math\ndef f(lst: list):\n    flat_list = [item for sublist in lst for item in sublist]\n    transformed_list = [math.sqrt(item) for item in flat_list]\n    filtered_list = [num for num in transformed_list if num % 2 != 0]\n    return sum(filtered_list)", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(n: int):\n    binary = bin(n)[2:]\n    reversed_binary = binary[::-1]\n    chunks = [reversed_binary[i:i + 3] for i in range(0, len(reversed_binary), 3)]\n    octal_string = ''.join([str(int(chunk, 2)) for chunk in chunks])\n    return octal_string", "input": "15", "output": "'71'", "imports": [], "original_snippet": "def f(n: int):\n    binary = bin(n)[2:]\n    reversed_binary = binary[::-1]\n    chunks = [reversed_binary[i:i + 3] for i in range(0, len(reversed_binary), 3)]\n    octal_string = ''.join([str(int(chunk, 2)) for chunk in chunks])\n    return octal_string", "composite_functions": [], "_input_type": "int", "_output_type": "str"}
{"snippet": "def f(nested_dict: dict) -> dict:\n    transformed_dict = {}\n    for key in nested_dict:\n        if len(nested_dict[key]) == 1:\n            transformed_dict[key] = [bool(nested_dict[key][0])]\n        else:\n            transformed_dict[key] = [bool(nested_dict[key].get(i)) for i in range(1, len(nested_dict[key]) + 1)]\n    return transformed_dict", "input": "{'John': {'a': {'Jill': True}, 'b': {'Jill': False}}, 'Jill': {'a': {'John': True}, 'b': {'John': False}}}", "output": "{'John': [False, False], 'Jill': [False, False]}", "imports": [], "original_snippet": "def f(nested_dict: dict) -> dict:\n    transformed_dict = {}\n    for key in nested_dict:\n        if len(nested_dict[key]) == 1:\n            transformed_dict[key] = [bool(nested_dict[key][0])]\n        else:\n            transformed_dict[key] = [bool(nested_dict[key].get(i)) for i in range(1, len(nested_dict[key]) + 1)]\n    return transformed_dict", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "from collections import defaultdict\ndef f(data: dict):\n    transformed_data = defaultdict(list)\n    for (key, value) in data.items():\n        for i in range(3):\n            transformed_key = key + str(i)\n            transformed_value = value + i\n            transformed_data[transformed_key].append(transformed_value)\n    return transformed_data.items()", "input": "{'a': 1, 'b': 2}", "output": "dict_items([('a0', [1]), ('a1', [2]), ('a2', [3]), ('b0', [2]), ('b1', [3]), ('b2', [4])])", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(data: dict):\n    transformed_data = defaultdict(list)\n    for (key, value) in data.items():\n        for i in range(3):\n            transformed_key = key + str(i)\n            transformed_value = value + i\n            transformed_data[transformed_key].append(transformed_value)\n    return transformed_data.items()", "composite_functions": [], "_input_type": "dict", "_output_type": "str"}
{"snippet": "def f(strings: list):\n    vowels = 'aeiou'\n    transformed_strings = [[ch for ch in string if ch not in vowels] for string in strings]\n    combined_list = sorted([char for sublist in transformed_strings for char in sublist])\n    ascii_sum = sum((ord(ch) for ch in combined_list)) % 26\n    return ''.join([chr(x + 97) for x in [ascii_sum]])", "input": "[\"hello\", \"world\", \"python\"]", "output": "'y'", "imports": [], "original_snippet": "def f(strings: list):\n    vowels = 'aeiou'\n    transformed_strings = [[ch for ch in string if ch not in vowels] for string in strings]\n    combined_list = sorted([char for sublist in transformed_strings for char in sublist])\n    ascii_sum = sum((ord(ch) for ch in combined_list)) % 26\n    return ''.join([chr(x + 97) for x in [ascii_sum]])", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(numbers: list):\n    if not all((isinstance(n, int) and n > 0 for n in numbers)):\n        raise ValueError('Input must be a list of positive integers')\n    numbers.sort()\n    max_value = numbers[-1]\n    min_value = numbers[0]\n    total_product = 1\n    pairs_with_highest_difference = []\n    for n in numbers[1:-1]:\n        total_product *= n\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            current_difference = abs(total_product - numbers[i] * numbers[j])\n            if current_difference == 0:\n                pairs_with_highest_difference.append((numbers[i], numbers[j]))\n    return pairs_with_highest_difference", "input": "[3, 1, 5, 9, 2, 7]", "output": "[]", "imports": [], "original_snippet": "def f(numbers: list):\n    if not all((isinstance(n, int) and n > 0 for n in numbers)):\n        raise ValueError('Input must be a list of positive integers')\n    numbers.sort()\n    max_value = numbers[-1]\n    min_value = numbers[0]\n    total_product = 1\n    pairs_with_highest_difference = []\n    for n in numbers[1:-1]:\n        total_product *= n\n    for i in range(len(numbers)):\n        for j in range(i + 1, len(numbers)):\n            current_difference = abs(total_product - numbers[i] * numbers[j])\n            if current_difference == 0:\n                pairs_with_highest_difference.append((numbers[i], numbers[j]))\n    return pairs_with_highest_difference", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(lst: list):\n    flattened_list = [item for sublist in lst for item in sublist]\n    membership = any((item in ['b', 'h'] for item in flattened_list))\n    c = 0 if membership else 1\n    counts = [0, 6, 3, 6, 9, 3, c, 5, 2, 1, 2, 4, 2, 5, 2, 7, 0, 7, 3, 6, 1, 8, 2, 6, 7, 4, 7, 0, 9, 6, 5, 2, 0, 0, 6, 0, 4, 1, 4, 7, 0, 2, 1, 4, 7, 0, 6, 8, 7, 3, 4, 4]\n    result = c in counts\n    return result", "input": "[[\"a\", \"b\", \"c\"], [\"d\", \"b\"], [\"e\", \"f\"], [\"h\", \"i\"]]", "output": "True", "imports": [], "original_snippet": "def f(lst: list):\n    flattened_list = [item for sublist in lst for item in sublist]\n    membership = any((item in ['b', 'h'] for item in flattened_list))\n    c = 0 if membership else 1\n    counts = [0, 6, 3, 6, 9, 3, c, 5, 2, 1, 2, 4, 2, 5, 2, 7, 0, 7, 3, 6, 1, 8, 2, 6, 7, 4, 7, 0, 9, 6, 5, 2, 0, 0, 6, 0, 4, 1, 4, 7, 0, 2, 1, 4, 7, 0, 6, 8, 7, 3, 4, 4]\n    result = c in counts\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "bool"}
{"snippet": "def f(words: list):\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    output_words = []\n    for word in words:\n        if any((vowel in word for vowel in vowels)) and len(word) % 4 == 0:\n            output_words.append(word)\n    return output_words", "input": "['hello', 'world', 'python', 'computer', 'neural', 'networks']", "output": "['computer', 'networks']", "imports": [], "original_snippet": "def f(words: list):\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    output_words = []\n    for word in words:\n        if any((vowel in word for vowel in vowels)) and len(word) % 4 == 0:\n            output_words.append(word)\n    return output_words", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(dict1: dict, dict2: dict):\n    combined = {k: (dict1[k][::-1], dict2[k][::-1]) if all((i.isdigit() and int(i) % 2 == 0 for i in k)) else (dict1[k], dict2[k]) for k in dict1.keys() & dict2.keys()}\n    combined = {k: combined[k][0] + combined[k][1] for k in combined.keys()}\n    return dict(sorted(combined.items(), key=lambda x: x[0], reverse=True))", "input": "{'24': [1, 2], '16': [3, 4], '18': [5, 6]}, {'24': [7, 8], '16': [9, 10], '00': [11, 12]}", "output": "{'24': [2, 1, 8, 7], '16': [3, 4, 9, 10]}", "imports": [], "original_snippet": "def f(dict1: dict, dict2: dict):\n    combined = {k: (dict1[k][::-1], dict2[k][::-1]) if all((i.isdigit() and int(i) % 2 == 0 for i in k)) else (dict1[k], dict2[k]) for k in dict1.keys() & dict2.keys()}\n    combined = {k: combined[k][0] + combined[k][1] for k in combined.keys()}\n    return dict(sorted(combined.items(), key=lambda x: x[0], reverse=True))", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(lst: list):\n    even_squares = [i ** 2 for i in lst if i % 2 == 0]\n    odd_sums = [sum((i for i in lst[:j + 1])) for (j, i) in enumerate(lst) if i % 2 != 0 and lst[j] < max(lst)]\n    if even_squares and odd_sums:\n        return sorted(even_squares) + odd_sums\n    return lst", "input": "[4, 1, 10, 2, 8, 3, 7, 5, 6]", "output": "[4, 16, 36, 64, 100, 5, 28, 35, 40]", "imports": [], "original_snippet": "def f(lst: list):\n    even_squares = [i ** 2 for i in lst if i % 2 == 0]\n    odd_sums = [sum((i for i in lst[:j + 1])) for (j, i) in enumerate(lst) if i % 2 != 0 and lst[j] < max(lst)]\n    if even_squares and odd_sums:\n        return sorted(even_squares) + odd_sums\n    return lst", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(sequence: list):\n    all_nums = [num for num in sequence if num > 0]\n    prime_nums = [num for num in all_nums if all((num % i != 0 for i in range(2, num)))]\n    negative_ints = [num for num in sequence if num < 0]\n    odd_primes = [prime for prime in prime_nums if prime % 2 != 0]\n    negative_squared = [neg ** 2 for neg in negative_ints]\n    uniq_prime_digits = sorted(set((str(prime) for prime in odd_primes)))\n    squared_digits = sorted(set((str(squared) for squared in negative_squared)))\n    result = uniq_prime_digits + squared_digits\n    return ''.join(result)", "input": "[3, 15, -2, -5, 11, 37, 53, -8, 9, -7, 5, -9]", "output": "'11337553254496481'", "imports": [], "original_snippet": "def f(sequence: list):\n    all_nums = [num for num in sequence if num > 0]\n    prime_nums = [num for num in all_nums if all((num % i != 0 for i in range(2, num)))]\n    negative_ints = [num for num in sequence if num < 0]\n    odd_primes = [prime for prime in prime_nums if prime % 2 != 0]\n    negative_squared = [neg ** 2 for neg in negative_ints]\n    uniq_prime_digits = sorted(set((str(prime) for prime in odd_primes)))\n    squared_digits = sorted(set((str(squared) for squared in negative_squared)))\n    result = uniq_prime_digits + squared_digits\n    return ''.join(result)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(data):\n    return data * 5", "input": "'Test'", "output": "'TestTestTestTestTest'", "imports": [], "original_snippet": "def f(data):\n    return data * 5", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(s: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    s_filtered = ''.join([i for i in s.lower() if i in alphabet])\n    transform_result = [f'{s_filtered.count(char)}{alphabet.index(char) + 1}' for char in alphabet if s_filtered.count(char) != 0]\n    return ''.join(transform_result)", "input": "\"This is a test input.\"", "output": "'11151839114116319420121'", "imports": [], "original_snippet": "def f(s: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    s_filtered = ''.join([i for i in s.lower() if i in alphabet])\n    transform_result = [f'{s_filtered.count(char)}{alphabet.index(char) + 1}' for char in alphabet if s_filtered.count(char) != 0]\n    return ''.join(transform_result)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(s: str):\n    reversed_s = s[::-1]\n    key = lambda c: chr((ord(c) + 5) % 256)\n    encoded_s = ''.join([key(c) for c in reversed_s])\n    return encoded_s", "input": "\"Hello world\"", "output": "'iqwt|%tqqjM'", "imports": [], "original_snippet": "def f(s: str):\n    reversed_s = s[::-1]\n    key = lambda c: chr((ord(c) + 5) % 256)\n    encoded_s = ''.join([key(c) for c in reversed_s])\n    return encoded_s", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(lst: list):\n    import math\n    transformed_lst = [x ** 2 if i % 2 == 0 else math.sqrt(x) for (i, x) in enumerate(lst)]\n    set_1 = set((x for (i, x) in enumerate(lst) if i % 2 == 0 and i > 0 and (lst[i] != 1) and math.log2(x).is_integer()))\n    sorted_lst = sorted(transformed_lst)\n    median = sorted_lst[len(sorted_lst) // 2]\n    sum_left_of_median = sum((value for value in sorted_lst if value < median))\n    set_2 = set((x for x in lst if x > median))\n    intersection = set_1.intersection(set_2)\n    return len(intersection)", "input": "[64, 3, 256, 5, 1, 21, 64, 4, 16, 8]", "output": "3", "imports": ["import math"], "original_snippet": "def f(lst: list):\n    import math\n    transformed_lst = [x ** 2 if i % 2 == 0 else math.sqrt(x) for (i, x) in enumerate(lst)]\n    set_1 = set((x for (i, x) in enumerate(lst) if i % 2 == 0 and i > 0 and (lst[i] != 1) and math.log2(x).is_integer()))\n    sorted_lst = sorted(transformed_lst)\n    median = sorted_lst[len(sorted_lst) // 2]\n    sum_left_of_median = sum((value for value in sorted_lst if value < median))\n    set_2 = set((x for x in lst if x > median))\n    intersection = set_1.intersection(set_2)\n    return len(intersection)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(lst: list):\n    lst_sorted = sorted(lst)\n    sum_third = sum((lst_sorted[i] for i in range(0, len(lst_sorted), 3) if i < len(lst_sorted)))\n    sum_fourth = sum((lst_sorted[i] for i in range(1, len(lst_sorted), 4) if i < len(lst_sorted)))\n    sum_fifth = sum((lst_sorted[i] for i in range(2, len(lst_sorted), 5) if i < len(lst_sorted)))\n    return (sum_third, sum_fourth, sum_fifth)", "input": "[7, 3, 8, 1, 5]", "output": "(8, 3, 5)", "imports": [], "original_snippet": "def f(lst: list):\n    lst_sorted = sorted(lst)\n    sum_third = sum((lst_sorted[i] for i in range(0, len(lst_sorted), 3) if i < len(lst_sorted)))\n    sum_fourth = sum((lst_sorted[i] for i in range(1, len(lst_sorted), 4) if i < len(lst_sorted)))\n    sum_fifth = sum((lst_sorted[i] for i in range(2, len(lst_sorted), 5) if i < len(lst_sorted)))\n    return (sum_third, sum_fourth, sum_fifth)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(lst: list):\n    evens = [i for i in lst if i % 2 == 0]\n    odds = [i for i in lst if i % 2 != 0]\n    transformed_evens = [i for i in zip(evens, sorted(evens))]\n    transformed_odds = [i for i in zip(odds, sorted(odds, key=lambda x: -x))]\n    result = [''.join([str(x) for x in transformed_evens]), ''.join([str(x) for x in transformed_odds])]\n    return ','.join(result).replace(',', '')", "input": "[4, 9, 2, 8, 5, 10]", "output": "'(4 2)(2 4)(8 8)(10 10)(9 9)(5 5)'", "imports": [], "original_snippet": "def f(lst: list):\n    evens = [i for i in lst if i % 2 == 0]\n    odds = [i for i in lst if i % 2 != 0]\n    transformed_evens = [i for i in zip(evens, sorted(evens))]\n    transformed_odds = [i for i in zip(odds, sorted(odds, key=lambda x: -x))]\n    result = [''.join([str(x) for x in transformed_evens]), ''.join([str(x) for x in transformed_odds])]\n    return ','.join(result).replace(',', '')", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "from typing import Dict\ndef f(input_str: str) -> str:\n    input_str = input_str[::-1]\n    reversed_chars = [str(ord(char) - 97) for char in input_str]\n    reversed_chars = [ch.zfill(2) for ch in reversed_chars]\n    reversed_chars = [ch.replace('9', 'a') for ch in reversed_chars]\n    reversed_txt = ''.join(reversed_chars) + f'#{reversed(input_str)}'\n    return reversed_txt", "input": "'aBc'", "output": "'02-3100#<reversed object at 0x7f774810e2f0>'", "imports": ["from typing import Dict"], "original_snippet": "from typing import Dict\ndef f(input_str: str) -> str:\n    input_str = input_str[::-1]\n    reversed_chars = [str(ord(char) - 97) for char in input_str]\n    reversed_chars = [ch.zfill(2) for ch in reversed_chars]\n    reversed_chars = [ch.replace('9', 'a') for ch in reversed_chars]\n    reversed_txt = ''.join(reversed_chars) + f'#{reversed(input_str)}'\n    return reversed_txt", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(data: dict):\n    sums = {}\n    for (key, d) in data.items():\n        sums[key] = sum((values.get('value', 0) for values in d.values() if values and 'index' in values))\n    results = ((key, val % 7) for (key, val) in sums.items())\n    return tuple(results)", "input": "{'test_1': {1: {'index': 1, 'value': 3}, 2: {'index': 2}}, \n 'test_2': {1: {'index': 1, 'value': 5}, 2: {'index': 2, 'value': 2}}}", "output": "(('test_1', 3), ('test_2', 0))", "imports": [], "original_snippet": "def f(data: dict):\n    sums = {}\n    for (key, d) in data.items():\n        sums[key] = sum((values.get('value', 0) for values in d.values() if values and 'index' in values))\n    results = ((key, val % 7) for (key, val) in sums.items())\n    return tuple(results)", "composite_functions": [], "_input_type": "dict", "_output_type": "tuple"}
{"snippet": "def f(numbers: str):\n    numbers_reversed = numbers[::-1]\n    (hours, minutes) = map(int, numbers_reversed.split(':'))\n    result = (hours + minutes) % 12\n    return result", "input": "'09:15'", "output": "9", "imports": [], "original_snippet": "def f(numbers: str):\n    numbers_reversed = numbers[::-1]\n    (hours, minutes) = map(int, numbers_reversed.split(':'))\n    result = (hours + minutes) % 12\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(numbers: list) -> int:\n    if not all((isinstance(n, int) for n in numbers)):\n        raise ValueError('Input must be a list of integers')\n    numbers.sort()\n    even_product = 1\n    odd_sum = 0\n    for (i, n) in enumerate(numbers):\n        if i % 2 == 0:\n            even_product *= n\n        else:\n            odd_sum += n\n    return (even_product + odd_sum) % 5", "input": "[1, 4, 7, 10, 13, 16, 19, 22, 25, 28]", "output": "0", "imports": [], "original_snippet": "def f(numbers: list) -> int:\n    if not all((isinstance(n, int) for n in numbers)):\n        raise ValueError('Input must be a list of integers')\n    numbers.sort()\n    even_product = 1\n    odd_sum = 0\n    for (i, n) in enumerate(numbers):\n        if i % 2 == 0:\n            even_product *= n\n        else:\n            odd_sum += n\n    return (even_product + odd_sum) % 5", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(sentence: str):\n    words = sentence.split()\n    modified_word_list = [word[::-1] if index % 2 == 0 else word for (index, word) in enumerate(words)]\n    transformed_string = ''.join(modified_word_list)\n    sorted_sentence = ' '.join(sorted(transformed_string.split(' '), key=lambda x: len(x), reverse=True))\n    ascii_letters = [ord(char) if char.isalpha() else char for char in sorted_sentence]\n    last_letter = chr(sum(ascii_letters[-5:]) % 26 + 97)\n    return last_letter", "input": "\"He's been gone for a while\"", "output": "'r'", "imports": [], "original_snippet": "def f(sentence: str):\n    words = sentence.split()\n    modified_word_list = [word[::-1] if index % 2 == 0 else word for (index, word) in enumerate(words)]\n    transformed_string = ''.join(modified_word_list)\n    sorted_sentence = ' '.join(sorted(transformed_string.split(' '), key=lambda x: len(x), reverse=True))\n    ascii_letters = [ord(char) if char.isalpha() else char for char in sorted_sentence]\n    last_letter = chr(sum(ascii_letters[-5:]) % 26 + 97)\n    return last_letter", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import math\ndef f(vals: tuple, add: int):\n    num = vals[0] ** vals[1]\n    total_dots = math.floor(math.log(num)) + math.floor(math.log(num + add))\n    return total_dots", "input": "(100, 2), 119", "output": "18", "imports": ["import math"], "original_snippet": "import math\ndef f(vals: tuple, add: int):\n    num = vals[0] ** vals[1]\n    total_dots = math.floor(math.log(num)) + math.floor(math.log(num + add))\n    return total_dots", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(single_unique_element):\n    lst = [single_unique_element]\n    lst.sort()\n    for i in lst:\n        if lst[0] < i < lst[1]:\n            return i", "input": "'rightmost_giant'", "output": "None", "imports": [], "original_snippet": "def f(single_unique_element):\n    lst = [single_unique_element]\n    lst.sort()\n    for i in lst:\n        if lst[0] < i < lst[1]:\n            return i", "composite_functions": [], "_input_type": "str", "_output_type": "NoneType"}
{"snippet": "def f(numbers: list):\n    primes = [num for num in numbers if num > 1 and all((num % i != 0 for i in range(2, int(num ** 0.5) + 1)))]\n    result = [num + 3 if num % 5 == 0 else num for num in numbers if num % 2 == 0]\n    return result", "input": "[2, 3, 5, 7, 8, 10, 12, 15]", "output": "[2, 8, 13, 12]", "imports": [], "original_snippet": "def f(numbers: list):\n    primes = [num for num in numbers if num > 1 and all((num % i != 0 for i in range(2, int(num ** 0.5) + 1)))]\n    result = [num + 3 if num % 5 == 0 else num for num in numbers if num % 2 == 0]\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(strings: list):\n    max_count = float('inf')\n    result = ''\n    for s in strings:\n        current_count = 0\n        for char in s:\n            if char in result:\n                current_count += 1\n        if current_count < max_count:\n            (result, max_count) = (s, current_count)\n    return result", "input": "['abcd', 'bacd', 'acbd', 'abcd', 'acbd']", "output": "'abcd'", "imports": [], "original_snippet": "def f(strings: list):\n    max_count = float('inf')\n    result = ''\n    for s in strings:\n        current_count = 0\n        for char in s:\n            if char in result:\n                current_count += 1\n        if current_count < max_count:\n            (result, max_count) = (s, current_count)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(data_points: list):\n    processed_data = []\n    for point in data_points:\n        if 'id' in point and 'value' in point and ('text' in point):\n            ascii_sum = sum((ord(char) for char in str(point['text'])))\n            point['summed_ascii'] = ascii_sum\n            processed_data.append(point)\n    sorted_data = sorted(processed_data, key=lambda x: x['summed_ascii'])\n    concatenated_sum = ''.join((str(point['summed_ascii']) for point in sorted_data))\n    character_count = {char: concatenated_sum.count(char) for char in set(concatenated_sum)}\n    sorted_characters = sorted(character_count.keys(), key=lambda x: character_count[x], reverse=True)\n    highest_frequency_char = sorted_characters[0]\n    return highest_frequency_char", "input": "[{'id': 1, 'value': 20, 'text': 'hello'}, {'id': 2, 'value': 30, 'text': 'world'}, {'id': 3, 'value': 40, 'text': 'python'}]", "output": "'5'", "imports": [], "original_snippet": "def f(data_points: list):\n    processed_data = []\n    for point in data_points:\n        if 'id' in point and 'value' in point and ('text' in point):\n            ascii_sum = sum((ord(char) for char in str(point['text'])))\n            point['summed_ascii'] = ascii_sum\n            processed_data.append(point)\n    sorted_data = sorted(processed_data, key=lambda x: x['summed_ascii'])\n    concatenated_sum = ''.join((str(point['summed_ascii']) for point in sorted_data))\n    character_count = {char: concatenated_sum.count(char) for char in set(concatenated_sum)}\n    sorted_characters = sorted(character_count.keys(), key=lambda x: character_count[x], reverse=True)\n    highest_frequency_char = sorted_characters[0]\n    return highest_frequency_char", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "import re\ndef f(text: str):\n    pattern = re.compile('(\\\\d*0)(.)')\n    matches = pattern.findall(text)\n    processed = [f'{s[-1]}' for (s, _) in matches]\n    result = ''.join(processed)\n    return result", "input": "'l0b0h0f0c0j'", "output": "'00000'", "imports": ["import re"], "original_snippet": "import re\ndef f(text: str):\n    pattern = re.compile('(\\\\d*0)(.)')\n    matches = pattern.findall(text)\n    processed = [f'{s[-1]}' for (s, _) in matches]\n    result = ''.join(processed)\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(data: list):\n    sorted_data = sorted(data)\n    result = []\n    for i in range(len(sorted_data)):\n        if i % 3 == 0:\n            result.append((sorted_data[i][0] * 3, sorted_data[i][1]))\n        elif i % 2 == 0:\n            result.append((sorted_data[i][0] * 2, sorted_data[i][1]))\n        else:\n            result.append((sorted_data[i][0], sorted_data[i][1]))\n    return result", "input": "[(1, 'a'), (3, 'b'), (2, 'c'), (4, 'd')]", "output": "[(3, 'a'), (2, 'c'), (6, 'b'), (12, 'd')]", "imports": [], "original_snippet": "def f(data: list):\n    sorted_data = sorted(data)\n    result = []\n    for i in range(len(sorted_data)):\n        if i % 3 == 0:\n            result.append((sorted_data[i][0] * 3, sorted_data[i][1]))\n        elif i % 2 == 0:\n            result.append((sorted_data[i][0] * 2, sorted_data[i][1]))\n        else:\n            result.append((sorted_data[i][0], sorted_data[i][1]))\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(l):\n    max_sum = -float('inf')\n    max_list = []\n    for sublist in l:\n        if isinstance(sublist, int):\n            local_sum = sublist\n        elif isinstance(sublist, list):\n            try:\n                local_sum = sum(sublist)\n            except TypeError:\n                local_sum = -float('inf')\n        else:\n            local_sum = -float('inf')\n        if local_sum > max_sum:\n            max_sum = local_sum\n            max_list = sublist\n    return max_list", "input": "[[[1, 2, 3], 4, 5], [[6, 7], 8, 9], [10]]", "output": "[10]", "imports": [], "original_snippet": "def f(l):\n    max_sum = -float('inf')\n    max_list = []\n    for sublist in l:\n        if isinstance(sublist, int):\n            local_sum = sublist\n        elif isinstance(sublist, list):\n            try:\n                local_sum = sum(sublist)\n            except TypeError:\n                local_sum = -float('inf')\n        else:\n            local_sum = -float('inf')\n        if local_sum > max_sum:\n            max_sum = local_sum\n            max_list = sublist\n    return max_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(lst: list):\n    processed_list = [x % ((i + 1) * 2) for (i, x) in enumerate(lst)]\n    reversed_lst = processed_list[::-1]\n    appended_lst = [x + 10 for x in reversed_lst]\n    final_lst = sorted(appended_lst, reverse=True)\n    count_odd_elements = sum((1 for x in final_lst if x % 2 != 0))\n    return (final_lst, count_odd_elements)", "input": "[3, 12, 5, 16, 7, 18, 9]", "output": "([19, 17, 16, 15, 11, 10, 10], 4)", "imports": [], "original_snippet": "def f(lst: list):\n    processed_list = [x % ((i + 1) * 2) for (i, x) in enumerate(lst)]\n    reversed_lst = processed_list[::-1]\n    appended_lst = [x + 10 for x in reversed_lst]\n    final_lst = sorted(appended_lst, reverse=True)\n    count_odd_elements = sum((1 for x in final_lst if x % 2 != 0))\n    return (final_lst, count_odd_elements)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(input_string: str) -> int:\n    counter = set()\n    for char in input_string:\n        if char.isupper():\n            counter.add(char)\n    return len(counter)", "input": "'AbcDef'", "output": "2", "imports": [], "original_snippet": "def f(input_string: str) -> int:\n    counter = set()\n    for char in input_string:\n        if char.isupper():\n            counter.add(char)\n    return len(counter)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(n: int):\n    binary = bin(n)[2:]\n    reversed_binary = binary[::-1]\n    chunks = [reversed_binary[i:i + 3] for i in range(0, len(reversed_binary), 3)]\n    octal_numbers = [str(int(chunk, 2)) for chunk in chunks]\n    return ''.join(octal_numbers)", "input": "15", "output": "'71'", "imports": [], "original_snippet": "def f(n: int):\n    binary = bin(n)[2:]\n    reversed_binary = binary[::-1]\n    chunks = [reversed_binary[i:i + 3] for i in range(0, len(reversed_binary), 3)]\n    octal_numbers = [str(int(chunk, 2)) for chunk in chunks]\n    return ''.join(octal_numbers)", "composite_functions": [], "_input_type": "int", "_output_type": "str"}
{"snippet": "def f(nums: list):\n    import math\n    doubled_squares = [num * num * 2 for num in nums]\n    sqrt_differences = [abs(math.sqrt(num1) - math.sqrt(num2)) for (num1, num2) in zip(nums[1:], nums[:-1])]\n    num_pairs = list(zip(doubled_squares, sqrt_differences))\n    unique_num_pairs = dict()\n    for (key, value) in num_pairs:\n        if key not in unique_num_pairs or value > unique_num_pairs[key]:\n            unique_num_pairs[key] = value\n    sorted_num_pairs = sorted(unique_num_pairs.items())\n    result_str = ','.join([f'({key},{value})' for (key, value) in sorted_num_pairs])\n    return result_str", "input": "[2, 3, 5, 7, 11, 13]", "output": "'(8,0.31783724519578205),(18,0.5040171699309126),(50,0.4096833335648009),(98,0.6708734792908091),(242,0.2889264851085893)'", "imports": ["import math"], "original_snippet": "def f(nums: list):\n    import math\n    doubled_squares = [num * num * 2 for num in nums]\n    sqrt_differences = [abs(math.sqrt(num1) - math.sqrt(num2)) for (num1, num2) in zip(nums[1:], nums[:-1])]\n    num_pairs = list(zip(doubled_squares, sqrt_differences))\n    unique_num_pairs = dict()\n    for (key, value) in num_pairs:\n        if key not in unique_num_pairs or value > unique_num_pairs[key]:\n            unique_num_pairs[key] = value\n    sorted_num_pairs = sorted(unique_num_pairs.items())\n    result_str = ','.join([f'({key},{value})' for (key, value) in sorted_num_pairs])\n    return result_str", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "from typing import Tuple, List\ndef f(data: List[dict]) -> Tuple[List[dict], int]:\n    THRESHOLD = 500\n    processed_data = []\n    total_sum = 0\n    for item in data:\n        values = item['value']\n        values_sum = sum(values)\n        if values_sum % 2 != 0:\n            if sum(values) > THRESHOLD:\n                evenified = [v // 2 for v in values]\n                processed_data.append({'value': evenified})\n            total_sum += values_sum - len(values)\n        else:\n            processed_data.append(item)\n    return (processed_data, total_sum)", "input": "[{'value': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}, {'value': [11, 12, 13, 14, 15]}]", "output": "([], 105)", "imports": ["from typing import Tuple, List"], "original_snippet": "from typing import Tuple, List\ndef f(data: List[dict]) -> Tuple[List[dict], int]:\n    THRESHOLD = 500\n    processed_data = []\n    total_sum = 0\n    for item in data:\n        values = item['value']\n        values_sum = sum(values)\n        if values_sum % 2 != 0:\n            if sum(values) > THRESHOLD:\n                evenified = [v // 2 for v in values]\n                processed_data.append({'value': evenified})\n            total_sum += values_sum - len(values)\n        else:\n            processed_data.append(item)\n    return (processed_data, total_sum)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(s: str):\n    url = 'https://plus.google.com/u/0/'\n    href = url + '+' + s\n    result = [str.index(ch) for str in (href,) for ch in s]\n    return result", "input": "'menetekel'", "output": "[22, 18, 31, 18, 1, 18, 35, 18, 9]", "imports": [], "original_snippet": "def f(s: str):\n    url = 'https://plus.google.com/u/0/'\n    href = url + '+' + s\n    result = [str.index(ch) for str in (href,) for ch in s]\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "import copy\nimport numpy as np\ndef f(data: list):\n    transformed_data = []\n    for item in data:\n        sorted_items = sorted(item.items(), key=lambda x: x[1])\n        after_multiplication = [val * 2 for (key, val) in sorted_items if val % 2 == 0]\n        after_sort = sorted(after_multiplication)\n        transformed_data.append(after_sort)\n    flat_list = [item for sublist in transformed_data for item in sublist]\n    result = tuple(flat_list)\n    return result", "input": "[\n {\"a\": 3.8, \"b\": 5.2, \"d\": 7},\n {\"a\": 6.1, \"b\": 4.5, \"d\": 9}\n]", "output": "()", "imports": ["import copy", "import numpy as np"], "original_snippet": "import copy\nimport numpy as np\ndef f(data: list):\n    transformed_data = []\n    for item in data:\n        sorted_items = sorted(item.items(), key=lambda x: x[1])\n        after_multiplication = [val * 2 for (key, val) in sorted_items if val % 2 == 0]\n        after_sort = sorted(after_multiplication)\n        transformed_data.append(after_sort)\n    flat_list = [item for sublist in transformed_data for item in sublist]\n    result = tuple(flat_list)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(limit: tuple):\n    (next_num, current_num) = (0, 1)\n    fib_numbers = []\n    while next_num <= limit[0]:\n        fib_numbers.append(next_num)\n        (next_num, current_num) = (current_num, next_num + current_num)\n    even_fib_numbers = [num for num in fib_numbers if num % 2 == 0]\n    return sum(even_fib_numbers)", "input": "(4,)", "output": "2", "imports": [], "original_snippet": "def f(limit: tuple):\n    (next_num, current_num) = (0, 1)\n    fib_numbers = []\n    while next_num <= limit[0]:\n        fib_numbers.append(next_num)\n        (next_num, current_num) = (current_num, next_num + current_num)\n    even_fib_numbers = [num for num in fib_numbers if num % 2 == 0]\n    return sum(even_fib_numbers)", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(s: str):\n    vowel_dict = {'a': 1, 'e': 2, 'i': 3, 'o': 4, 'u': 5}\n    output = []\n    for char in s:\n        if char in vowel_dict:\n            key = vowel_dict[char]\n            output.append(f'vx{key}')\n    return tuple(output)", "input": "'aeiou'", "output": "('vx1', 'vx2', 'vx3', 'vx4', 'vx5')", "imports": [], "original_snippet": "def f(s: str):\n    vowel_dict = {'a': 1, 'e': 2, 'i': 3, 'o': 4, 'u': 5}\n    output = []\n    for char in s:\n        if char in vowel_dict:\n            key = vowel_dict[char]\n            output.append(f'vx{key}')\n    return tuple(output)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(text: str, integer: int):\n    rules = {1: lambda x: x.upper(), 2: lambda x: x.lower(), 3: lambda x: x[::-1], 4: lambda x: list(filter(lambda y: y not in x, x))}\n    transformed_text = rules.get(integer, lambda x: x)(text)\n    return transformed_text", "input": "'Hello World!', 2", "output": "'hello world!'", "imports": [], "original_snippet": "def f(text: str, integer: int):\n    rules = {1: lambda x: x.upper(), 2: lambda x: x.lower(), 3: lambda x: x[::-1], 4: lambda x: list(filter(lambda y: y not in x, x))}\n    transformed_text = rules.get(integer, lambda x: x)(text)\n    return transformed_text", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(elements: list):\n    frequency_dict = {}\n    for element in elements:\n        frequency_dict[element] = frequency_dict.get(element, 0) + 1\n    sorted_tuples = sorted(frequency_dict.items(), key=lambda item: item[1], reverse=True)\n    for i in range(1, len(sorted_tuples)):\n        if sorted_tuples[i][1] != sorted_tuples[i - 1][1]:\n            return i\n    return len(sorted_tuples)", "input": "[1, 2, 3, 1, 4, 2, 5, 2, 6, 1, 1, 2, 1, 2]", "output": "2", "imports": [], "original_snippet": "def f(elements: list):\n    frequency_dict = {}\n    for element in elements:\n        frequency_dict[element] = frequency_dict.get(element, 0) + 1\n    sorted_tuples = sorted(frequency_dict.items(), key=lambda item: item[1], reverse=True)\n    for i in range(1, len(sorted_tuples)):\n        if sorted_tuples[i][1] != sorted_tuples[i - 1][1]:\n            return i\n    return len(sorted_tuples)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(sequence: list):\n    mapped_sequence = map(lambda x: x % 5, sequence)\n    filtered_sequence = filter(lambda x: x > 1, mapped_sequence)\n    summed_sequence = sum(filtered_sequence)\n    return summed_sequence % 7 + 1", "input": "[8, 13, 24, 35, 46]", "output": "4", "imports": [], "original_snippet": "def f(sequence: list):\n    mapped_sequence = map(lambda x: x % 5, sequence)\n    filtered_sequence = filter(lambda x: x > 1, mapped_sequence)\n    summed_sequence = sum(filtered_sequence)\n    return summed_sequence % 7 + 1", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(lst: list):\n    squares = [i ** 2 for i in lst if i % 2 == 0]\n    sums = [sum((j for j in range(i + 1) if j % 2 != 0)) for i in lst]\n    products = [squares[i] * sums[i] for i in range(min(len(squares), len(sums)))]\n    return sum(products)", "input": "[2, 4, 6, 8]", "output": "1416", "imports": [], "original_snippet": "def f(lst: list):\n    squares = [i ** 2 for i in lst if i % 2 == 0]\n    sums = [sum((j for j in range(i + 1) if j % 2 != 0)) for i in lst]\n    products = [squares[i] * sums[i] for i in range(min(len(squares), len(sums)))]\n    return sum(products)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(custom_sequence: dict):\n    sum_n = 0\n    transform_1 = lambda n: n + custom_sequence['total']\n    key_map = {k: transform_1(v) for (k, v) in custom_sequence['transform_2'].items()}\n    transform_3 = lambda i: sum([v * 2 for (k, v) in key_map.items() if len(k) in custom_sequence['extra_modulo']])\n    for (k, val) in key_map.items():\n        sum_n += transform_3(val)\n    result = sum_n % 10\n    return result", "input": "{'total': 3,\n 'transform_2': {'k1': 4, 'k2': 5},\n 'extra_modulo': [2, 3]}", "output": "0", "imports": [], "original_snippet": "def f(custom_sequence: dict):\n    sum_n = 0\n    transform_1 = lambda n: n + custom_sequence['total']\n    key_map = {k: transform_1(v) for (k, v) in custom_sequence['transform_2'].items()}\n    transform_3 = lambda i: sum([v * 2 for (k, v) in key_map.items() if len(k) in custom_sequence['extra_modulo']])\n    for (k, val) in key_map.items():\n        sum_n += transform_3(val)\n    result = sum_n % 10\n    return result", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(arr: list, N: int):\n    max_sum = sum(arr[:N])\n    current_sum = max_sum\n    for i in range(N, len(arr)):\n        current_sum += arr[i] - arr[i - N]\n        max_sum = max(max_sum, current_sum)\n    return max_sum", "input": "[-2, 1, -3, 4, -1, 2, 1, -5, 4], 4", "output": "6", "imports": [], "original_snippet": "def f(arr: list, N: int):\n    max_sum = sum(arr[:N])\n    current_sum = max_sum\n    for i in range(N, len(arr)):\n        current_sum += arr[i] - arr[i - N]\n        max_sum = max(max_sum, current_sum)\n    return max_sum", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(numbers: list):\n    step1 = [x ** 2 if x % 2 == 0 else 0 for x in numbers]\n    bin_lengths = [len(bin(num)[2:]) for num in step1 if num != 0]\n    avg_bin_length = sum(bin_lengths) / len(bin_lengths)\n    rounded_avg = int(avg_bin_length)\n    step5 = [num - rounded_avg for num in numbers]\n    max_value = max(step5)\n    return max_value", "input": "[2, 4, 6, 8, 10]", "output": "5", "imports": [], "original_snippet": "def f(numbers: list):\n    step1 = [x ** 2 if x % 2 == 0 else 0 for x in numbers]\n    bin_lengths = [len(bin(num)[2:]) for num in step1 if num != 0]\n    avg_bin_length = sum(bin_lengths) / len(bin_lengths)\n    rounded_avg = int(avg_bin_length)\n    step5 = [num - rounded_avg for num in numbers]\n    max_value = max(step5)\n    return max_value", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from heapq import heapify, heappop\ndef f(input_tuple):\n    text_heap = [(priority * -1, text) for (priority, text) in input_tuple]\n    heapify(text_heap)\n    res = []\n    current_count = 1\n    while text_heap:\n        (_, text) = heappop(text_heap)\n        res.extend([text] * current_count)\n        current_count += 1\n    return res", "input": "((9, 'f1'), (4, 'f2'), (7, 'f3'), (5, 'f4'))", "output": "['f1', 'f3', 'f3', 'f4', 'f4', 'f4', 'f2', 'f2', 'f2', 'f2']", "imports": ["from heapq import heapify, heappop"], "original_snippet": "from heapq import heapify, heappop\ndef f(input_tuple):\n    text_heap = [(priority * -1, text) for (priority, text) in input_tuple]\n    heapify(text_heap)\n    res = []\n    current_count = 1\n    while text_heap:\n        (_, text) = heappop(text_heap)\n        res.extend([text] * current_count)\n        current_count += 1\n    return res", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(data: list):\n    subarrays = [[]]\n    for i in range(len(data)):\n        for j in range(i + 1, len(data)):\n            if data[i] + data[j] == 0:\n                subarrays.append(data[i:j])\n    unique_subarrays = set((tuple(subarray) for subarray in subarrays))\n    sum_of_squares = sum((len(subarray) ** 2 for subarray in unique_subarrays))\n    return sum_of_squares", "input": "[1, 2, 3, -1, -2, -3]", "output": "27", "imports": [], "original_snippet": "def f(data: list):\n    subarrays = [[]]\n    for i in range(len(data)):\n        for j in range(i + 1, len(data)):\n            if data[i] + data[j] == 0:\n                subarrays.append(data[i:j])\n    unique_subarrays = set((tuple(subarray) for subarray in subarrays))\n    sum_of_squares = sum((len(subarray) ** 2 for subarray in unique_subarrays))\n    return sum_of_squares", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(s: str, char_filter: str='ABCDEF'):\n    invalids_removed = ''.join([c for c in s if c not in char_filter])\n    mapped_chars = [chr((ord(c) - 97 ^ i) % 26 + 97) if 'a' <= c <= 'z' else c for (i, c) in enumerate(invalids_removed, start=1)]\n    final_string = ''.join(mapped_chars)\n    return final_string", "input": "\"Python is fun!\"", "output": "'Paqdll ab oya!'", "imports": [], "original_snippet": "def f(s: str, char_filter: str='ABCDEF'):\n    invalids_removed = ''.join([c for c in s if c not in char_filter])\n    mapped_chars = [chr((ord(c) - 97 ^ i) % 26 + 97) if 'a' <= c <= 'z' else c for (i, c) in enumerate(invalids_removed, start=1)]\n    final_string = ''.join(mapped_chars)\n    return final_string", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from typing import Dict, Tuple\ndef f(meetings: Dict[str, Tuple[Dict[int, str], Tuple[int, int]]]):\n    transformed_meetings = {}\n    for (name, (data, duration)) in meetings.items():\n        (start, end) = (duration[0], duration[1] + duration[0])\n        hours = {i: set() for i in range(start, end)}\n        for (time, favorite_color) in data.items():\n            hour = time // 100\n            if start <= hour < end:\n                hours[hour].add(favorite_color)\n            else:\n                continue\n        transformed_meetings[name] = hours\n    return transformed_meetings", "input": "{'John': ({1300: 'blue', 0000: 'red', 1400: 'yellow'}, (1300, 200)), 'Emily': ({900: 'green', 1500: 'orange'}, (1450, 45))}", "output": "{'John': {1300: set(), 1301: set(), 1302: set(), 1303: set(), 1304: set(), 1305: set(), 1306: set(), 1307: set(), 1308: set(), 1309: set(), 1310: set(), 1311: set(), 1312: set(), 1313: set(), 1314: se...t(), 1480: set(), 1481: set(), 1482: set(), 1483: set(), 1484: set(), 1485: set(), 1486: set(), 1487: set(), 1488: set(), 1489: set(), 1490: set(), 1491: set(), 1492: set(), 1493: set(), 1494: set()}}", "imports": ["from typing import Dict, Tuple"], "original_snippet": "from typing import Dict, Tuple\ndef f(meetings: Dict[str, Tuple[Dict[int, str], Tuple[int, int]]]):\n    transformed_meetings = {}\n    for (name, (data, duration)) in meetings.items():\n        (start, end) = (duration[0], duration[1] + duration[0])\n        hours = {i: set() for i in range(start, end)}\n        for (time, favorite_color) in data.items():\n            hour = time // 100\n            if start <= hour < end:\n                hours[hour].add(favorite_color)\n            else:\n                continue\n        transformed_meetings[name] = hours\n    return transformed_meetings", "composite_functions": [], "_input_type": "dict", "_output_type": "str"}
{"snippet": "def f(nested_list):\n    result = []\n    for i in range(len(nested_list)):\n        if isinstance(nested_list[i], list):\n            result.append(f(nested_list[i]))\n        else:\n            current_value = nested_list[i]\n            if i >= 2:\n                previous_value = nested_list[i - 2]\n                prev_prev_value = nested_list[i - 1]\n                transformed_value = current_value * previous_value * prev_prev_value\n            elif i == 1:\n                previous_value = nested_list[i - 1]\n                transformed_value = current_value * previous_value\n            else:\n                transformed_value = current_value\n            result.append(transformed_value)\n    return result", "input": "[1, [2, [3, 4], 5], 6]", "output": "[1, [2, [3, 12], [3, 4, 3, 4, 3, 4, 3, 4, 3, 4, 3, 4, 3, 4, 3, 4, 3, 4, 3, 4]], [2, [3, 4], 5, 2, [3, 4], 5, 2, [3, 4], 5, 2, [3, 4], 5, 2, [3, 4], 5, 2, [3, 4], 5]]", "imports": [], "original_snippet": "def f(nested_list):\n    result = []\n    for i in range(len(nested_list)):\n        if isinstance(nested_list[i], list):\n            result.append(f(nested_list[i]))\n        else:\n            current_value = nested_list[i]\n            if i >= 2:\n                previous_value = nested_list[i - 2]\n                prev_prev_value = nested_list[i - 1]\n                transformed_value = current_value * previous_value * prev_prev_value\n            elif i == 1:\n                previous_value = nested_list[i - 1]\n                transformed_value = current_value * previous_value\n            else:\n                transformed_value = current_value\n            result.append(transformed_value)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(num: int, input_str: str):\n    arrays = []\n    code_point_values = [ord(char) + ord(char) % num for char in input_str]\n    curr_array = []\n    for (idx, code_point) in enumerate(code_point_values):\n        if idx % num == 0 and curr_array:\n            arrays.append(curr_array)\n            curr_array = []\n        if code_point % num > num / 2:\n            curr_array.append(code_point)\n    if curr_array:\n        arrays.append(curr_array)\n    reversed_arrays = [arr[::-1] for arr in arrays]\n    chunked: list = [[char * 3 for char in arr] for arr in reversed_arrays]\n    transformed_array = list(filter(None, chunked))\n    return transformed_array", "input": "3, 'abcdefg'", "output": "[[294], [303], [312]]", "imports": [], "original_snippet": "def f(num: int, input_str: str):\n    arrays = []\n    code_point_values = [ord(char) + ord(char) % num for char in input_str]\n    curr_array = []\n    for (idx, code_point) in enumerate(code_point_values):\n        if idx % num == 0 and curr_array:\n            arrays.append(curr_array)\n            curr_array = []\n        if code_point % num > num / 2:\n            curr_array.append(code_point)\n    if curr_array:\n        arrays.append(curr_array)\n    reversed_arrays = [arr[::-1] for arr in arrays]\n    chunked: list = [[char * 3 for char in arr] for arr in reversed_arrays]\n    transformed_array = list(filter(None, chunked))\n    return transformed_array", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "import math\nfrom typing import List\ndef f(numbers: List[complex]):\n    real_numbers = [num for num in numbers if num.real == num.imag]\n    imag_numbers = [num for num in numbers if num.real == -num.imag]\n    real_counts = {num: numbers.count(num) for num in real_numbers}\n    imag_magnitude = sum([abs(num) for num in imag_numbers if num.real != 0])\n    result = sum([real_counts.get(num, 0) ** 2 for num in real_numbers]) + imag_magnitude\n    return result", "input": "[3+3j, 4+4j, 5+5j, 4-4j, 4+4j, 5+5j]", "output": "22.65685424949238", "imports": ["import math", "from typing import List"], "original_snippet": "import math\nfrom typing import List\ndef f(numbers: List[complex]):\n    real_numbers = [num for num in numbers if num.real == num.imag]\n    imag_numbers = [num for num in numbers if num.real == -num.imag]\n    real_counts = {num: numbers.count(num) for num in real_numbers}\n    imag_magnitude = sum([abs(num) for num in imag_numbers if num.real != 0])\n    result = sum([real_counts.get(num, 0) ** 2 for num in real_numbers]) + imag_magnitude\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "from typing import List, Tuple, Dict\ndef f(transactions: List[Tuple[str, int]]):\n    spending_segments = {}\n    for transaction in transactions:\n        (name, amount) = transaction\n        if name not in spending_segments:\n            spending_segments[name] = {'low': 0, 'med': 0, 'high': 0}\n        if amount < 50:\n            spending_segments[name]['low'] += 1\n        elif amount < 100:\n            spending_segments[name]['med'] += 1\n        else:\n            spending_segments[name]['high'] += 1\n    ordered_segments = {name: sorted([key for key in segment.keys() if segment[key] == max(segment.values())], reverse=True) for (name, segment) in spending_segments.items()}\n    return {name: tuple(ordered_segments[name]) for name in sorted(ordered_segments.keys())}", "input": "[(\"Alice\", 75), (\"Bob\", 50), (\"Alice\", 120), (\"Alice\", 20), (\"Bob\", 30)]", "output": "{'Alice': ('med', 'low', 'high'), 'Bob': ('med', 'low')}", "imports": ["from typing import List, Tuple, Dict"], "original_snippet": "from typing import List, Tuple, Dict\ndef f(transactions: List[Tuple[str, int]]):\n    spending_segments = {}\n    for transaction in transactions:\n        (name, amount) = transaction\n        if name not in spending_segments:\n            spending_segments[name] = {'low': 0, 'med': 0, 'high': 0}\n        if amount < 50:\n            spending_segments[name]['low'] += 1\n        elif amount < 100:\n            spending_segments[name]['med'] += 1\n        else:\n            spending_segments[name]['high'] += 1\n    ordered_segments = {name: sorted([key for key in segment.keys() if segment[key] == max(segment.values())], reverse=True) for (name, segment) in spending_segments.items()}\n    return {name: tuple(ordered_segments[name]) for name in sorted(ordered_segments.keys())}", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(numbers: list):\n    filtered_numbers = [num for num in numbers if num % 3 == 0 or num % 5 == 0]\n    transformed_numbers = [filtered_numbers[i] ^ filtered_numbers[i + 1] for i in range(0, len(filtered_numbers), 2)]\n    result = sum(transformed_numbers) % 10\n    return result", "input": "[3, 5, 7, 9, 11, 15]", "output": "2", "imports": [], "original_snippet": "def f(numbers: list):\n    filtered_numbers = [num for num in numbers if num % 3 == 0 or num % 5 == 0]\n    transformed_numbers = [filtered_numbers[i] ^ filtered_numbers[i + 1] for i in range(0, len(filtered_numbers), 2)]\n    result = sum(transformed_numbers) % 10\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers: list):\n    evens = [x for x in numbers if x % 2 == 0]\n    sum_of_evens = sum(evens) if evens else 0\n    max_even = max(evens) if evens else 0\n    result = len(numbers) * max_even - sum_of_evens\n    return result", "input": "[4, 6, 2, 8, 10]", "output": "20", "imports": [], "original_snippet": "def f(numbers: list):\n    evens = [x for x in numbers if x % 2 == 0]\n    sum_of_evens = sum(evens) if evens else 0\n    max_even = max(evens) if evens else 0\n    result = len(numbers) * max_even - sum_of_evens\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import Dict, List\ndef f(d: Dict[int, List[int]]) -> List[int]:\n    r_dict: Dict[int, List[int]] = dict()\n    for key in d:\n        r_dict[key] = [x for x in d[key] if x > sum(d[key]) / len(d[key])]\n        r_dict[key] = sorted(r_dict[key], reverse=True)[0] * 10 if r_dict[key] else sum(d[key])\n    result_list: List[int] = sorted([r_dict[k] for k in r_dict])\n    result: int = sum(result_list)\n    final_result: int = result % 1000\n    return list(str(final_result))[::-1]", "input": "{23: [50, 100, 75], 42: [70, 80, 90], 53: [10, 20, 12], 60: [77, 88, 5]}", "output": "['0', '8', '9']", "imports": ["from typing import Dict, List"], "original_snippet": "from typing import Dict, List\ndef f(d: Dict[int, List[int]]) -> List[int]:\n    r_dict: Dict[int, List[int]] = dict()\n    for key in d:\n        r_dict[key] = [x for x in d[key] if x > sum(d[key]) / len(d[key])]\n        r_dict[key] = sorted(r_dict[key], reverse=True)[0] * 10 if r_dict[key] else sum(d[key])\n    result_list: List[int] = sorted([r_dict[k] for k in r_dict])\n    result: int = sum(result_list)\n    final_result: int = result % 1000\n    return list(str(final_result))[::-1]", "composite_functions": [], "_input_type": "dict", "_output_type": "list"}
{"snippet": "def f(n, m, k, l):\n    result = pow(n // m, k)\n    final_result = result * (k + l)\n    return final_result", "input": "16, 4, 2, 7", "output": "144", "imports": [], "original_snippet": "def f(n, m, k, l):\n    result = pow(n // m, k)\n    final_result = result * (k + l)\n    return final_result", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(strings: list) -> str:\n    transformed_strings = [[str(ord(ch)) for ch in string] for string in strings]\n    combined_list = [char for sublist in transformed_strings for char in sublist]\n    combined_string = ''.join(combined_list)\n    ascii_sum = sum([int(ch) for ch in combined_list])\n    return f'{combined_string}{ascii_sum}'", "input": "[\"hello\", \"world\", \"python\"]", "output": "'1041011081081111191111141081001121211161041111101758'", "imports": [], "original_snippet": "def f(strings: list) -> str:\n    transformed_strings = [[str(ord(ch)) for ch in string] for string in strings]\n    combined_list = [char for sublist in transformed_strings for char in sublist]\n    combined_string = ''.join(combined_list)\n    ascii_sum = sum([int(ch) for ch in combined_list])\n    return f'{combined_string}{ascii_sum}'", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(ids_and_values: list):\n    flat_values = [value for id_value_dict in ids_and_values for value in id_value_dict['values']]\n    unique_products = {value: value ** 2 for value in set(flat_values) if flat_values.count(value) > 1}\n    sorted_products = sorted(unique_products.items(), key=lambda item: item[1], reverse=True)\n    predefined_list = list(range(101))\n    return predefined_list.index(sum((sorted_products[0][1] for _ in sorted_products)))", "input": "[{'id': 1, 'values': [1, 2]}, {'id': 2, 'values': [3, 4]}]", "output": "0", "imports": [], "original_snippet": "def f(ids_and_values: list):\n    flat_values = [value for id_value_dict in ids_and_values for value in id_value_dict['values']]\n    unique_products = {value: value ** 2 for value in set(flat_values) if flat_values.count(value) > 1}\n    sorted_products = sorted(unique_products.items(), key=lambda item: item[1], reverse=True)\n    predefined_list = list(range(101))\n    return predefined_list.index(sum((sorted_products[0][1] for _ in sorted_products)))", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(words: list) -> str:\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    filtered_words = [word for word in words if any((vowel in word for vowel in vowels)) and len(word) % 4 == 0]\n    transformed_words = [word.upper() for word in filtered_words]\n    combined_output = ''.join(transformed_words[::-1])\n    return combined_output", "input": "['hello', 'world', 'python', 'computer', 'neural', 'networks']", "output": "'NETWORKSCOMPUTER'", "imports": [], "original_snippet": "def f(words: list) -> str:\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    filtered_words = [word for word in words if any((vowel in word for vowel in vowels)) and len(word) % 4 == 0]\n    transformed_words = [word.upper() for word in filtered_words]\n    combined_output = ''.join(transformed_words[::-1])\n    return combined_output", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(sequence: list, mathematics_practical_standards: list):\n    all_nums = [num for num in sequence if num > 0]\n    prime_nums = [num for num in all_nums if all((num % i != 0 for i in range(2, num)))]\n    odd_primes = [prime for prime in prime_nums if prime % 2 != 0]\n    uniq_prime_digits = sorted(set((str(prime) for prime in odd_primes)))\n    squared_prime_digits = sorted(set((str(prime ** 2) for prime in prime_nums)))\n    result = uniq_prime_digits + squared_prime_digits\n    math_standards_within_required = [standard for standard in result if standard in mathematics_practical_standards]\n    return math_standards_within_required", "input": "[3, 15, 17, 53, 97], [\"Geometry_And_Spatial_Sense\"]", "output": "[]", "imports": [], "original_snippet": "def f(sequence: list, mathematics_practical_standards: list):\n    all_nums = [num for num in sequence if num > 0]\n    prime_nums = [num for num in all_nums if all((num % i != 0 for i in range(2, num)))]\n    odd_primes = [prime for prime in prime_nums if prime % 2 != 0]\n    uniq_prime_digits = sorted(set((str(prime) for prime in odd_primes)))\n    squared_prime_digits = sorted(set((str(prime ** 2) for prime in prime_nums)))\n    result = uniq_prime_digits + squared_prime_digits\n    math_standards_within_required = [standard for standard in result if standard in mathematics_practical_standards]\n    return math_standards_within_required", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(numbers: list):\n    transformed_list = [x for x in numbers]\n    for i in range(len(numbers)):\n        transformed_list[i] **= 2\n        if i + 1 != len(numbers):\n            transformed_list[i] *= numbers[i + 1]\n        transformed_list[i] /= i + 1\n    return transformed_list", "input": "[4, 3, 2, 1]", "output": "[48.0, 9.0, 1.3333333333333333, 0.25]", "imports": [], "original_snippet": "def f(numbers: list):\n    transformed_list = [x for x in numbers]\n    for i in range(len(numbers)):\n        transformed_list[i] **= 2\n        if i + 1 != len(numbers):\n            transformed_list[i] *= numbers[i + 1]\n        transformed_list[i] /= i + 1\n    return transformed_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "import math\ndef f(numbers: list) -> dict:\n    mean = sum(numbers) / len(numbers)\n    variance = sum(((n - mean) ** 2 for n in numbers)) / len(numbers)\n    std = math.sqrt(variance)\n    return {'mean': mean, 'std': std}", "input": "[1, 2, 3, 4, 5]", "output": "{'mean': 3.0, 'std': 1.4142135623730951}", "imports": ["import math"], "original_snippet": "import math\ndef f(numbers: list) -> dict:\n    mean = sum(numbers) / len(numbers)\n    variance = sum(((n - mean) ** 2 for n in numbers)) / len(numbers)\n    std = math.sqrt(variance)\n    return {'mean': mean, 'std': std}", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "from typing import List, Tuple\ndef f(l: List[List[int]]) -> Tuple[List[int], int]:\n    max_sum = -float('inf')\n    target_length = -1\n    for sublist in l:\n        if isinstance(sublist, List):\n            sub_sum = sum(sublist)\n            if sub_sum > max_sum:\n                max_sum = sub_sum\n                target_length = len(sublist)\n    result_sublist = [sublist for sublist in l if len(sublist) == target_length and sum(sublist) == max_sum]\n    return (result_sublist, target_length)", "input": "[[1, 2, 3], [4, 5, 6], [7, 8, 9], [10, 11, 12, 13], [14, 15, 16]]", "output": "([[10, 11, 12, 13]], 4)", "imports": ["from typing import List, Tuple"], "original_snippet": "from typing import List, Tuple\ndef f(l: List[List[int]]) -> Tuple[List[int], int]:\n    max_sum = -float('inf')\n    target_length = -1\n    for sublist in l:\n        if isinstance(sublist, List):\n            sub_sum = sum(sublist)\n            if sub_sum > max_sum:\n                max_sum = sub_sum\n                target_length = len(sublist)\n    result_sublist = [sublist for sublist in l if len(sublist) == target_length and sum(sublist) == max_sum]\n    return (result_sublist, target_length)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(numbers: list):\n    pairs = [(numbers[i], numbers[i + 1]) for i in range(0, len(numbers), 2)]\n    even_sums = [x + y for (x, y) in pairs if (x + y) % 2 == 0]\n    squared_smaller = [min(x, y) ** 2 for (x, y) in pairs if min(x, y) >= 0]\n    max_product = max([x * y for (x, y) in pairs])\n    return even_sums + squared_smaller + [max_product]", "input": "[14, 30, 6, 7, -5, 16, 9, 11]", "output": "[44, 20, 196, 36, 81, 420]", "imports": [], "original_snippet": "def f(numbers: list):\n    pairs = [(numbers[i], numbers[i + 1]) for i in range(0, len(numbers), 2)]\n    even_sums = [x + y for (x, y) in pairs if (x + y) % 2 == 0]\n    squared_smaller = [min(x, y) ** 2 for (x, y) in pairs if min(x, y) >= 0]\n    max_product = max([x * y for (x, y) in pairs])\n    return even_sums + squared_smaller + [max_product]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "import math\ndef f(arr: list):\n    evens = [x for x in arr if x % 2 == 0]\n    odds = [x for x in arr if x % 2 != 0]\n    even_product = 1\n    for num in evens:\n        even_product *= num\n    odd_product = 1\n    for num in odds:\n        odd_product *= num\n    result = math.log2(even_product) + math.log2(odd_product)\n    return result", "input": "[4, 2, 6, 8, 10]", "output": "11.906890595608518", "imports": ["import math"], "original_snippet": "import math\ndef f(arr: list):\n    evens = [x for x in arr if x % 2 == 0]\n    odds = [x for x in arr if x % 2 != 0]\n    even_product = 1\n    for num in evens:\n        even_product *= num\n    odd_product = 1\n    for num in odds:\n        odd_product *= num\n    result = math.log2(even_product) + math.log2(odd_product)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "from typing import List, Callable, Dict\ndef f(strings: List[str], transformation_func: Callable[[str], str], rules: Dict[str, int]) -> (List[str], int, int):\n    transformed_strings = [transformation_func(string) for string in strings]\n    transformed_numbers = [ord(char) for char in ''.join(transformed_strings)]\n    transformed_concatenated = int(''.join((str(num) for num in transformed_numbers)))\n    modded_numbers = [num % 26 for num in transformed_numbers]\n    modded_concatenated = int(''.join((str(num) for num in modded_numbers)))\n    final_sorted = sorted(((key, val % 7) for (key, val) in rules.items()))\n    final_sum = sum((transformed_concatenated, modded_concatenated))\n    sorted_strings = sorted([string for string in strings])\n    return (sorted_strings, final_sorted, final_sum)", "input": "['test_1', 'test_2', 'test_3'], lambda x: x[::-1], {'a': 13, 'b': 17, 'c': 27}", "output": "(['test_1', 'test_2', 'test_3'], [('a', 6), ('b', 3), ('c', 6)], 499511611510343363063473392723277507367827213428)", "imports": ["from typing import List, Callable, Dict"], "original_snippet": "from typing import List, Callable, Dict\ndef f(strings: List[str], transformation_func: Callable[[str], str], rules: Dict[str, int]) -> (List[str], int, int):\n    transformed_strings = [transformation_func(string) for string in strings]\n    transformed_numbers = [ord(char) for char in ''.join(transformed_strings)]\n    transformed_concatenated = int(''.join((str(num) for num in transformed_numbers)))\n    modded_numbers = [num % 26 for num in transformed_numbers]\n    modded_concatenated = int(''.join((str(num) for num in modded_numbers)))\n    final_sorted = sorted(((key, val % 7) for (key, val) in rules.items()))\n    final_sum = sum((transformed_concatenated, modded_concatenated))\n    sorted_strings = sorted([string for string in strings])\n    return (sorted_strings, final_sorted, final_sum)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "from typing import List, Tuple\ndef f(string: str) -> Tuple[List[int], List[str]]:\n    distinct = set(string)\n    total_distinct = len(distinct)\n    integer = total_distinct // 3\n    with_one = str(integer) + '1'\n    return ([integer], [with_one])", "input": "'abcdedrtyuio'", "output": "([3], ['31'])", "imports": ["from typing import List, Tuple"], "original_snippet": "from typing import List, Tuple\ndef f(string: str) -> Tuple[List[int], List[str]]:\n    distinct = set(string)\n    total_distinct = len(distinct)\n    integer = total_distinct // 3\n    with_one = str(integer) + '1'\n    return ([integer], [with_one])", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "from typing import List, Tuple\nimport itertools\ndef f(x: List[int]) -> Tuple[List[int], List[int]]:\n    sums = [sum(x[:i]) for i in range(1, len(x) + 1)]\n    diffs = [abs(i - j) for (i, j) in itertools.combinations(sums, 2)]\n    unique_differences = list(set(diffs))\n    lesser_values = [i for i in unique_differences if i < 50]\n    greater_values = [i for i in unique_differences if i > 100]\n    return (lesser_values, greater_values)", "input": "[1, 2, 3, 4, 5]", "output": "([2, 3, 4, 5, 7, 9, 12, 14], [])", "imports": ["from typing import List, Tuple", "import itertools"], "original_snippet": "from typing import List, Tuple\nimport itertools\ndef f(x: List[int]) -> Tuple[List[int], List[int]]:\n    sums = [sum(x[:i]) for i in range(1, len(x) + 1)]\n    diffs = [abs(i - j) for (i, j) in itertools.combinations(sums, 2)]\n    unique_differences = list(set(diffs))\n    lesser_values = [i for i in unique_differences if i < 50]\n    greater_values = [i for i in unique_differences if i > 100]\n    return (lesser_values, greater_values)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "import heapq\ndef f(numbers: list):\n    filtered_nums = [num for num in numbers if 1 <= num <= 10]\n    heap = [heapq.nsmallest(1, filtered_nums)[0]]\n    while len(filtered_nums) > 1:\n        min_nums = heapq.nsmallest(2, filtered_nums)\n        filtered_nums.remove(min_nums[0])\n        filtered_nums.remove(min_nums[1])\n        heap.append(min_nums[-1])\n    grouped_nums = []\n    while len(heap) > 1:\n        group = [heap[0]]\n        heap.remove(heap[0])\n        while heap[0] in group:\n            group.append(heapq.heappop(heap))\n        grouped_nums.extend(group)\n    return sum(grouped_nums)", "input": "[3, 11, 20, 8, 6, 10, 9, 15, 7, 1, 12, 5, 17]", "output": "18", "imports": ["import heapq"], "original_snippet": "import heapq\ndef f(numbers: list):\n    filtered_nums = [num for num in numbers if 1 <= num <= 10]\n    heap = [heapq.nsmallest(1, filtered_nums)[0]]\n    while len(filtered_nums) > 1:\n        min_nums = heapq.nsmallest(2, filtered_nums)\n        filtered_nums.remove(min_nums[0])\n        filtered_nums.remove(min_nums[1])\n        heap.append(min_nums[-1])\n    grouped_nums = []\n    while len(heap) > 1:\n        group = [heap[0]]\n        heap.remove(heap[0])\n        while heap[0] in group:\n            group.append(heapq.heappop(heap))\n        grouped_nums.extend(group)\n    return sum(grouped_nums)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(words: list):\n    vowels = 'aeiouAEIOU'\n    transformed_words = []\n    for word in words:\n        transformed = ''\n        for char in word:\n            if char in vowels:\n                transformed += 'v'\n            else:\n                transformed += char\n        transformed_words.append(transformed)\n    combined = ''.join(transformed_words)\n    return len(combined)", "input": "[\"hello\", \"world\"]", "output": "10", "imports": [], "original_snippet": "def f(words: list):\n    vowels = 'aeiouAEIOU'\n    transformed_words = []\n    for word in words:\n        transformed = ''\n        for char in word:\n            if char in vowels:\n                transformed += 'v'\n            else:\n                transformed += char\n        transformed_words.append(transformed)\n    combined = ''.join(transformed_words)\n    return len(combined)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(digits: str):\n    length = len(digits)\n    for i in range(length):\n        unit = digits[:i + 1]\n        multiplication = length // (i + 1)\n        if unit * multiplication == digits:\n            return i + 1\n    return -1", "input": "'900900900900900900900900900900900900900'", "output": "3", "imports": [], "original_snippet": "def f(digits: str):\n    length = len(digits)\n    for i in range(length):\n        unit = digits[:i + 1]\n        multiplication = length // (i + 1)\n        if unit * multiplication == digits:\n            return i + 1\n    return -1", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(sequence: list):\n    permutations = [(sequence[i] + sequence[j]) % 10 + 1 for i in range(len(sequence)) for j in range(i + 1, len(sequence))]\n    unique_values = set(permutations)\n    count_values = {value: permutations.count(value) for value in unique_values}\n    sorted_count_values = sorted(count_values.items(), key=lambda item: item[1], reverse=True)\n    interesting_values = [value for (value, _) in sorted_count_values if value < 5]\n    accumulative_sums = [sum(interesting_values[:i + 1]) for i in range(min(len(interesting_values), 3))]\n    return max(accumulative_sums)", "input": "[1, 2, 3, 1, 4, 2, 5, 2, 6, 1, 1, 2, 1, 2]", "output": "8", "imports": [], "original_snippet": "def f(sequence: list):\n    permutations = [(sequence[i] + sequence[j]) % 10 + 1 for i in range(len(sequence)) for j in range(i + 1, len(sequence))]\n    unique_values = set(permutations)\n    count_values = {value: permutations.count(value) for value in unique_values}\n    sorted_count_values = sorted(count_values.items(), key=lambda item: item[1], reverse=True)\n    interesting_values = [value for (value, _) in sorted_count_values if value < 5]\n    accumulative_sums = [sum(interesting_values[:i + 1]) for i in range(min(len(interesting_values), 3))]\n    return max(accumulative_sums)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(text: str) -> str:\n    unique_characters = ''.join(sorted(set(text)))\n    evens = [char for (i, char) in enumerate(unique_characters) if i % 2 == 0]\n    odds = [char for (i, char) in enumerate(unique_characters) if i % 2 != 0]\n    transformed_evens = [hex(ord(char)) for char in evens]\n    transformed_odds = [oct(ord(char)) for char in odds]\n    gathered_chars = ''.join((char for chars in zip(transformed_evens, transformed_odds) for char in chars))\n    final_output = ''.join((char + str(len(char)) for char in gathered_chars))\n    return final_output", "input": "'coding'", "output": "'01x1613101o111414101x1617101o111511101x161e101o1115171'", "imports": [], "original_snippet": "def f(text: str) -> str:\n    unique_characters = ''.join(sorted(set(text)))\n    evens = [char for (i, char) in enumerate(unique_characters) if i % 2 == 0]\n    odds = [char for (i, char) in enumerate(unique_characters) if i % 2 != 0]\n    transformed_evens = [hex(ord(char)) for char in evens]\n    transformed_odds = [oct(ord(char)) for char in odds]\n    gathered_chars = ''.join((char for chars in zip(transformed_evens, transformed_odds) for char in chars))\n    final_output = ''.join((char + str(len(char)) for char in gathered_chars))\n    return final_output", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(numbers: list):\n    ix = 0\n    filtered = [num for num in numbers if num > -5]\n    count = len(filtered)\n    results = [filtered[ix], filtered[ix + 1]]\n    while ix < count - 3:\n        if filtered[ix + 1] % 2 == 1 and filtered[ix + 2] % 2 == 1:\n            results.append(filtered[ix + 3])\n        ix += 1\n    return sum(results)", "input": "[3, 7, -4, -2, 5, 9, 11, 14, -8]", "output": "35", "imports": [], "original_snippet": "def f(numbers: list):\n    ix = 0\n    filtered = [num for num in numbers if num > -5]\n    count = len(filtered)\n    results = [filtered[ix], filtered[ix + 1]]\n    while ix < count - 3:\n        if filtered[ix + 1] % 2 == 1 and filtered[ix + 2] % 2 == 1:\n            results.append(filtered[ix + 3])\n        ix += 1\n    return sum(results)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers: list):\n    number_sorted = sorted(numbers)\n    odd_count = len([n for n in number_sorted if n % 2 != 0])\n    even_count = len(number_sorted) - odd_count\n    if even_count > odd_count:\n        difference = number_sorted[even_count - 1] - number_sorted[odd_count] if odd_count else number_sorted[even_count - 1]\n    else:\n        average = (number_sorted[even_count] + number_sorted[odd_count - 1]) / 2 if odd_count else number_sorted[even_count]\n        difference = number_sorted[even_count] - average\n    summed = sum(number_sorted[:even_count or odd_count])\n    result = difference * summed\n    if result > 1000:\n        result = result / 2\n    return result", "input": "[10, 3, 5, 2, 8, 5, 4]", "output": "0", "imports": [], "original_snippet": "def f(numbers: list):\n    number_sorted = sorted(numbers)\n    odd_count = len([n for n in number_sorted if n % 2 != 0])\n    even_count = len(number_sorted) - odd_count\n    if even_count > odd_count:\n        difference = number_sorted[even_count - 1] - number_sorted[odd_count] if odd_count else number_sorted[even_count - 1]\n    else:\n        average = (number_sorted[even_count] + number_sorted[odd_count - 1]) / 2 if odd_count else number_sorted[even_count]\n        difference = number_sorted[even_count] - average\n    summed = sum(number_sorted[:even_count or odd_count])\n    result = difference * summed\n    if result > 1000:\n        result = result / 2\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(l: int, m: int, n: int):\n    formula_value = (m * l ** n / n + m / l ** n) ** (1 / m) - 1\n    return int(formula_value)", "input": "8, 10, 12", "output": "10", "imports": [], "original_snippet": "def f(l: int, m: int, n: int):\n    formula_value = (m * l ** n / n + m / l ** n) ** (1 / m) - 1\n    return int(formula_value)", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(lst: list):\n    pos = [x for x in lst if x > 0]\n    neg = [x for x in lst if x < 0]\n    pos_shifted = [x + 1 for x in pos]\n    neg_shifted = [x - 1 for x in neg]\n    pos_xor = pos_shifted[0] if pos_shifted else 0\n    for num in pos_shifted[1:]:\n        pos_xor ^= num\n    neg_bin_sum = sum([bin(x)[2:].count('1') for x in neg_shifted])\n    result = (pos_xor, neg_bin_sum, len(pos), len(neg))\n    return result", "input": "[-5, -3, -2, 0, 1, 2, 3]", "output": "(5, 5, 3, 3)", "imports": [], "original_snippet": "def f(lst: list):\n    pos = [x for x in lst if x > 0]\n    neg = [x for x in lst if x < 0]\n    pos_shifted = [x + 1 for x in pos]\n    neg_shifted = [x - 1 for x in neg]\n    pos_xor = pos_shifted[0] if pos_shifted else 0\n    for num in pos_shifted[1:]:\n        pos_xor ^= num\n    neg_bin_sum = sum([bin(x)[2:].count('1') for x in neg_shifted])\n    result = (pos_xor, neg_bin_sum, len(pos), len(neg))\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "from typing import List, Tuple\ndef f(string: str) -> Tuple[List[int]]:\n    spaces = ''.join((' ' for _ in range(400)))\n    print(spaces)\n    return [len(spaces)]", "input": "'I like programming'", "output": "[400]", "imports": ["from typing import List, Tuple"], "original_snippet": "from typing import List, Tuple\ndef f(string: str) -> Tuple[List[int]]:\n    spaces = ''.join((' ' for _ in range(400)))\n    print(spaces)\n    return [len(spaces)]", "composite_functions": [], "_input_type": "str", "_output_type": "list"}
{"snippet": "from typing import List, Dict\ndef f(integer_list: List[int], transformations: Dict[str, dict]):\n    transformed_list = [x + transformations['initial']['increment'] for x in integer_list]\n    sum_value = sum(transformed_list) + transformations['sum']['multiplier'] * transformations['initial']['increment']\n    n = transformations['modulus']['N']\n    while sum_value >= n:\n        sum_value -= n\n    if sum_value % 2 == 0:\n        final_result = [x * transformations['multiply']['factor'] for x in transformed_list]\n    else:\n        indices = [i for i in range(len(integer_list)) if integer_list[i] % len(transformed_list) == 0]\n        filtered_list = [transformed_list[i] for i in indices]\n        final_result = [x - transformations['subtract']['amount'] for x in filtered_list]\n    return final_result", "input": "[1, 2, 3, 4, 5], {\n    \"initial\": {\"increment\": 10},\n    \"sum\": {\"multiplier\": 2},\n    \"modulus\": {\"N\": 40},\n    \"multiply\": {\"factor\": 5},\n    \"subtract\": {\"amount\": 7}\n}", "output": "[8]", "imports": ["from typing import List, Dict"], "original_snippet": "from typing import List, Dict\ndef f(integer_list: List[int], transformations: Dict[str, dict]):\n    transformed_list = [x + transformations['initial']['increment'] for x in integer_list]\n    sum_value = sum(transformed_list) + transformations['sum']['multiplier'] * transformations['initial']['increment']\n    n = transformations['modulus']['N']\n    while sum_value >= n:\n        sum_value -= n\n    if sum_value % 2 == 0:\n        final_result = [x * transformations['multiply']['factor'] for x in transformed_list]\n    else:\n        indices = [i for i in range(len(integer_list)) if integer_list[i] % len(transformed_list) == 0]\n        filtered_list = [transformed_list[i] for i in indices]\n        final_result = [x - transformations['subtract']['amount'] for x in filtered_list]\n    return final_result", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "from typing import List\ndef f(numbers: List[int]) -> int:\n    cube_list = [x ** 3 for x in numbers]\n    summed_digital_list = [sum((int(digit) for digit in str(cube))) for cube in cube_list]\n    sorted_summed_digital_list = sorted(summed_digital_list, reverse=True)\n    return sorted_summed_digital_list[0]", "input": "[15, 22, 25, 32, 41]", "output": "26", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(numbers: List[int]) -> int:\n    cube_list = [x ** 3 for x in numbers]\n    summed_digital_list = [sum((int(digit) for digit in str(cube))) for cube in cube_list]\n    sorted_summed_digital_list = sorted(summed_digital_list, reverse=True)\n    return sorted_summed_digital_list[0]", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(s: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz0123456789'\n    encoded_s = ''.join([str(alphabet.index(c)) + '01'[i % 2] for (i, c) in enumerate(s)])\n    num_s = int(encoded_s)\n    rotated_num_s = num_s % 36 ** len(str(num_s))\n    return str(rotated_num_s)[::-1]", "input": "'abc'", "output": "'0211'", "imports": [], "original_snippet": "def f(s: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz0123456789'\n    encoded_s = ''.join([str(alphabet.index(c)) + '01'[i % 2] for (i, c) in enumerate(s)])\n    num_s = int(encoded_s)\n    rotated_num_s = num_s % 36 ** len(str(num_s))\n    return str(rotated_num_s)[::-1]", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(a, b):\n    return max(a, b)", "input": "10, 4", "output": "10", "imports": [], "original_snippet": "def f(a, b):\n    return max(a, b)", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(s: str):\n    ascii_values = [ord(char) for char in s if ord(char) % 2 == 0]\n    sorted_values = [value if value % 2 == 0 else -value for value in ascii_values]\n    string = ''.join([chr(value) for value in sorted_values])\n    ascii_numbers = [ord(char) for char in string]\n    result = sum(ascii_numbers) * sum(ascii_numbers) if sum(ascii_numbers) % 2 == 0 else pow(2, sum(ascii_numbers))\n    return result", "input": "\"Hello world\"", "output": "412164", "imports": [], "original_snippet": "def f(s: str):\n    ascii_values = [ord(char) for char in s if ord(char) % 2 == 0]\n    sorted_values = [value if value % 2 == 0 else -value for value in ascii_values]\n    string = ''.join([chr(value) for value in sorted_values])\n    ascii_numbers = [ord(char) for char in string]\n    result = sum(ascii_numbers) * sum(ascii_numbers) if sum(ascii_numbers) % 2 == 0 else pow(2, sum(ascii_numbers))\n    return result", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(records: dict):\n    updated_records = {}\n    for (key, value) in records.items():\n        nested_value = value.copy()\n        nested_value['flag'] = key % nested_value['year'] + nested_value['month']\n        nested_value['month'] = (nested_value['month'] + nested_value['year']) % 12\n        updated_records[key] = nested_value\n    return updated_records", "input": "{1: {'flag': 0, 'year': 2, 'month': 1}, 2: {'flag': 1, 'year': 3, 'month': 2}}", "output": "{1: {'flag': 2, 'year': 2, 'month': 3}, 2: {'flag': 4, 'year': 3, 'month': 5}}", "imports": [], "original_snippet": "def f(records: dict):\n    updated_records = {}\n    for (key, value) in records.items():\n        nested_value = value.copy()\n        nested_value['flag'] = key % nested_value['year'] + nested_value['month']\n        nested_value['month'] = (nested_value['month'] + nested_value['year']) % 12\n        updated_records[key] = nested_value\n    return updated_records", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(words: list) -> list:\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    output_words = []\n    for word in words:\n        if any((vowel in word for vowel in vowels)):\n            vowel_count = sum((1 for vowel in vowels if vowel in word))\n            if vowel_count % 2 == 0:\n                output_words.append(word.upper())\n            else:\n                output_words.append(word[::-1])\n    output_words.sort(key=len)\n    return output_words", "input": "['hello', 'world', 'python', 'computer', 'neural', 'networks']", "output": "['HELLO', 'dlrow', 'nohtyp', 'laruen', 'retupmoc', 'NETWORKS']", "imports": [], "original_snippet": "def f(words: list) -> list:\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    output_words = []\n    for word in words:\n        if any((vowel in word for vowel in vowels)):\n            vowel_count = sum((1 for vowel in vowels if vowel in word))\n            if vowel_count % 2 == 0:\n                output_words.append(word.upper())\n            else:\n                output_words.append(word[::-1])\n    output_words.sort(key=len)\n    return output_words", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(data_list: list):\n    transformed_data = []\n    for data in data_list:\n        if isinstance(data, dict):\n            keys = sorted(data.keys())\n            transformed_data.append({key: data[key] for key in keys[::-2]})\n        else:\n            transformed_data.append(data[::-1])\n    return transformed_data", "input": "[{'car': 'Honda', 'bike': 'Yamaha', 'truck': 'FORD'}, (12, 3, 45, 67)]", "output": "[{'truck': 'FORD', 'bike': 'Yamaha'}, (67, 45, 3, 12)]", "imports": [], "original_snippet": "def f(data_list: list):\n    transformed_data = []\n    for data in data_list:\n        if isinstance(data, dict):\n            keys = sorted(data.keys())\n            transformed_data.append({key: data[key] for key in keys[::-2]})\n        else:\n            transformed_data.append(data[::-1])\n    return transformed_data", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(string: str):\n    unique_chars = set(string)\n    sub_strings = [string[i:i + 2] for i in range(len(string) - 1)]\n    filtered_strings = list(filter(lambda x: ''.join(sorted(x)) in ['01', '23', '45', '67', '89'], sub_strings))\n    modified_strings = [''.join([chr(abs(ord(char) - 7)) for char in subs]) for subs in filtered_strings]\n    aggregate_result = ''.join(modified_strings[::2])\n    reversion = aggregate_result[::-1]\n    return reversion", "input": "'8912345670'", "output": "'.-21'", "imports": [], "original_snippet": "def f(string: str):\n    unique_chars = set(string)\n    sub_strings = [string[i:i + 2] for i in range(len(string) - 1)]\n    filtered_strings = list(filter(lambda x: ''.join(sorted(x)) in ['01', '23', '45', '67', '89'], sub_strings))\n    modified_strings = [''.join([chr(abs(ord(char) - 7)) for char in subs]) for subs in filtered_strings]\n    aggregate_result = ''.join(modified_strings[::2])\n    reversion = aggregate_result[::-1]\n    return reversion", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(lst: list, n: int):\n    unique_sorted_lst = list(set(lst))\n    unique_sorted_lst.sort()\n    num_items_less_than_or_equal_dict = {item: len([x for x in unique_sorted_lst if x <= item]) for item in unique_sorted_lst}\n    sum_of_squares = sum([num_items_less_than_or_equal_dict[item] ** 2 if item <= n else 0 for item in lst])\n    return sum_of_squares", "input": "[3, 6, 2, 4, 2, 3, 6, 8, 9, 9, 2, 3], 5", "output": "24", "imports": [], "original_snippet": "def f(lst: list, n: int):\n    unique_sorted_lst = list(set(lst))\n    unique_sorted_lst.sort()\n    num_items_less_than_or_equal_dict = {item: len([x for x in unique_sorted_lst if x <= item]) for item in unique_sorted_lst}\n    sum_of_squares = sum([num_items_less_than_or_equal_dict[item] ** 2 if item <= n else 0 for item in lst])\n    return sum_of_squares", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(initial_string: str, numbers: list):\n    encoded = initial_string.upper()\n    reversed_string = encoded[::-1]\n    shifted = ''.join((chr((ord(c) + numbers[i % len(numbers)]) % 26 + 65) for (i, c) in enumerate(reversed_string)))\n    divided_pairs = [(shifted[i:i + 2], int(numbers[i % len(numbers)])) for i in range(0, len(shifted), 2)]\n    final = [c * (n % 10) for (c, n) in divided_pairs]\n    return ''.join(final)", "input": "'a', [1, 2, 3]", "output": "'O'", "imports": [], "original_snippet": "def f(initial_string: str, numbers: list):\n    encoded = initial_string.upper()\n    reversed_string = encoded[::-1]\n    shifted = ''.join((chr((ord(c) + numbers[i % len(numbers)]) % 26 + 65) for (i, c) in enumerate(reversed_string)))\n    divided_pairs = [(shifted[i:i + 2], int(numbers[i % len(numbers)])) for i in range(0, len(shifted), 2)]\n    final = [c * (n % 10) for (c, n) in divided_pairs]\n    return ''.join(final)", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "from typing import List, Tuple\ndef f(arr: List[int]):\n    results = []\n    stack = []\n    stack_operations = [lambda x: x + 4, lambda x: x - 2, lambda x: x * 7, lambda x: x / 3]\n    for i in range(len(arr) - 1):\n        current_element = arr[i]\n        next_element = arr[i + 1]\n        stack.append(current_element % 3)\n        stack.append(next_element % 2)\n        stack_operations_index = (i + 1) % 4\n        transformed_element = stack_operations[stack_operations_index](current_element)\n        results.append(transformed_element)\n    sorted_results = sorted([x % 5 for x in results])\n    constructor_counter = len(results) + 5\n    for y in range(len(results)):\n        total_upspinal_multiplier = results[y] * (y % 3) + constructor_counter\n        sorted_results[y] = -2 * y % results[y] + total_upspinal_multiplier\n    return tuple(sorted_results)", "input": "[4, 7, 1, 5, 9, 6]", "output": "(10, 106, 11.0, 13, 23)", "imports": ["from typing import List, Tuple"], "original_snippet": "from typing import List, Tuple\ndef f(arr: List[int]):\n    results = []\n    stack = []\n    stack_operations = [lambda x: x + 4, lambda x: x - 2, lambda x: x * 7, lambda x: x / 3]\n    for i in range(len(arr) - 1):\n        current_element = arr[i]\n        next_element = arr[i + 1]\n        stack.append(current_element % 3)\n        stack.append(next_element % 2)\n        stack_operations_index = (i + 1) % 4\n        transformed_element = stack_operations[stack_operations_index](current_element)\n        results.append(transformed_element)\n    sorted_results = sorted([x % 5 for x in results])\n    constructor_counter = len(results) + 5\n    for y in range(len(results)):\n        total_upspinal_multiplier = results[y] * (y % 3) + constructor_counter\n        sorted_results[y] = -2 * y % results[y] + total_upspinal_multiplier\n    return tuple(sorted_results)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(numbers: list):\n    sorted_numbers = sorted(numbers)\n    modified_numbers = []\n    for i in range(len(sorted_numbers)):\n        if i % 2 == 0:\n            if sorted_numbers[i] > 0:\n                modified_numbers.append(sorted_numbers[i] * 2)\n            else:\n                modified_numbers.append(sorted_numbers[i] // 3)\n        else:\n            modified_numbers.append(sorted_numbers[i] // 4)\n    return sum(modified_numbers)", "input": "[3, 2, 1, 7, 5, -4, 0]", "output": "21", "imports": [], "original_snippet": "def f(numbers: list):\n    sorted_numbers = sorted(numbers)\n    modified_numbers = []\n    for i in range(len(sorted_numbers)):\n        if i % 2 == 0:\n            if sorted_numbers[i] > 0:\n                modified_numbers.append(sorted_numbers[i] * 2)\n            else:\n                modified_numbers.append(sorted_numbers[i] // 3)\n        else:\n            modified_numbers.append(sorted_numbers[i] // 4)\n    return sum(modified_numbers)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(words: list) -> str:\n    even_words = [word for word in words if len(word) % 2 == 0 and word.isalpha()]\n    vowels = 'aeiouAEIOU'\n    result = [''.join((char for char in word if char not in vowels)) for word in even_words]\n    return '-'.join(result)", "input": "['Hello', 'World', 'Python', 'Programming', 'Is', 'Fun']", "output": "'Pythn-s'", "imports": [], "original_snippet": "def f(words: list) -> str:\n    even_words = [word for word in words if len(word) % 2 == 0 and word.isalpha()]\n    vowels = 'aeiouAEIOU'\n    result = [''.join((char for char in word if char not in vowels)) for word in even_words]\n    return '-'.join(result)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(data: dict):\n    transformed_data = {key[::-1]: ''.join((str(int(char) * 2) if char.isdigit() else char.upper() for char in value)) for (key, value) in data.items()}\n    matching_pairs = 0\n    for (k1, v1) in transformed_data.items():\n        if v1[::-1] in transformed_data.values():\n            matching_pairs += 1\n    return matching_pairs", "input": "{'Python': 'abc', 'code': 'xyz'}", "output": "0", "imports": [], "original_snippet": "def f(data: dict):\n    transformed_data = {key[::-1]: ''.join((str(int(char) * 2) if char.isdigit() else char.upper() for char in value)) for (key, value) in data.items()}\n    matching_pairs = 0\n    for (k1, v1) in transformed_data.items():\n        if v1[::-1] in transformed_data.values():\n            matching_pairs += 1\n    return matching_pairs", "composite_functions": [], "_input_type": "dict", "_output_type": "int"}
{"snippet": "def f(expression: str):\n    closing_precedence = {'}': '{', ']': '[', ')': '(', '>': '<'}\n    stack = []\n    for char in expression:\n        if char in closing_precedence.values():\n            stack.append(char)\n        elif char in closing_precedence.keys():\n            if not stack or closing_precedence[char] != stack.pop():\n                return 'Invalid sequence with closing element precedence check.'\n    return 'Balanced and properly nested for given hierarchy.'", "input": "\"<{[()]}>\"", "output": "'Balanced and properly nested for given hierarchy.'", "imports": [], "original_snippet": "def f(expression: str):\n    closing_precedence = {'}': '{', ']': '[', ')': '(', '>': '<'}\n    stack = []\n    for char in expression:\n        if char in closing_precedence.values():\n            stack.append(char)\n        elif char in closing_precedence.keys():\n            if not stack or closing_precedence[char] != stack.pop():\n                return 'Invalid sequence with closing element precedence check.'\n    return 'Balanced and properly nested for given hierarchy.'", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(numbers: list):\n    unique_numbers = list(set(numbers))\n    unique_numbers.sort()\n    chunk_sums = []\n    for i in range(0, len(unique_numbers), 3):\n        chunk = unique_numbers[i:i + 3]\n        if len(chunk) == 1:\n            chunk_sums.append(chunk[0] ** 2)\n        else:\n            chunk_sums.append(sum((x ** 2 for x in chunk)))\n    return {'sums': sorted(chunk_sums)}", "input": "[4, 1, 3, 2, 2, 1, 6, 6, 6, 7, 8]", "output": "{'sums': [14, 64, 101]}", "imports": [], "original_snippet": "def f(numbers: list):\n    unique_numbers = list(set(numbers))\n    unique_numbers.sort()\n    chunk_sums = []\n    for i in range(0, len(unique_numbers), 3):\n        chunk = unique_numbers[i:i + 3]\n        if len(chunk) == 1:\n            chunk_sums.append(chunk[0] ** 2)\n        else:\n            chunk_sums.append(sum((x ** 2 for x in chunk)))\n    return {'sums': sorted(chunk_sums)}", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(name_tpl: tuple):\n    (name, digit) = name_tpl\n    ascii_values = [ord(c) if c.islower() else ord(c) + 26 for c in name]\n    transformed_str = ''.join([str(d) + c for (c, d) in zip(name, ascii_values)] + [str(digit)])\n    return len([c for c in transformed_str if c.isdigit()])", "input": "(\"Menetekel\", 42)", "output": "29", "imports": [], "original_snippet": "def f(name_tpl: tuple):\n    (name, digit) = name_tpl\n    ascii_values = [ord(c) if c.islower() else ord(c) + 26 for c in name]\n    transformed_str = ''.join([str(d) + c for (c, d) in zip(name, ascii_values)] + [str(digit)])\n    return len([c for c in transformed_str if c.isdigit()])", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(grid: list):\n    result = []\n    rows = len(grid)\n    cols = len(grid[0])\n    for diag in range(rows + cols - 1):\n        for i in range(max(0, diag - cols + 1), min(diag + 1, rows)):\n            j = diag - i\n            if j < cols:\n                row = i\n                col = j\n                quadrant = 4\n                if row < rows // 2 and col < cols // 2:\n                    quadrant = 1\n                elif row < rows // 2 and col >= cols // 2:\n                    quadrant = 2\n                elif row >= rows // 2 and col < cols // 2:\n                    quadrant = 3\n                num = grid[row][col]\n                if quadrant == 1:\n                    num *= 2\n                elif quadrant == 2:\n                    num += 10\n                elif quadrant == 3:\n                    num -= 5\n                result.append(num)\n    return result", "input": "[[1, 2, 3], \n [4, 5, 6], \n [7, 8, 9]]", "output": "[2, 12, -1, 13, 5, 2, 6, 8, 9]", "imports": [], "original_snippet": "def f(grid: list):\n    result = []\n    rows = len(grid)\n    cols = len(grid[0])\n    for diag in range(rows + cols - 1):\n        for i in range(max(0, diag - cols + 1), min(diag + 1, rows)):\n            j = diag - i\n            if j < cols:\n                row = i\n                col = j\n                quadrant = 4\n                if row < rows // 2 and col < cols // 2:\n                    quadrant = 1\n                elif row < rows // 2 and col >= cols // 2:\n                    quadrant = 2\n                elif row >= rows // 2 and col < cols // 2:\n                    quadrant = 3\n                num = grid[row][col]\n                if quadrant == 1:\n                    num *= 2\n                elif quadrant == 2:\n                    num += 10\n                elif quadrant == 3:\n                    num -= 5\n                result.append(num)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(decimal_number: int):\n    binary_number = bin(decimal_number)[2:]\n    lengths = [len(bin(i)[2:]) for i in range(1, decimal_number + 1)]\n    return sum(lengths[:len(binary_number)]) * 2", "input": "25", "output": "22", "imports": [], "original_snippet": "def f(decimal_number: int):\n    binary_number = bin(decimal_number)[2:]\n    lengths = [len(bin(i)[2:]) for i in range(1, decimal_number + 1)]\n    return sum(lengths[:len(binary_number)]) * 2", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(s: str):\n    alphabet = list('abcdefghijklmnopqrstuvwxyz')\n    words_transform = ['-'.join(sorted(word.lower())) for word in s.lower().split() if all((c in alphabet for c in word.lower()))]\n    reversed_words_transform = [''.join(reversed(w)) for w in words_transform]\n    first_reversed_index = [i for (i, letter) in enumerate(reversed(s.lower())) if letter in reversed_words_transform][0]\n    return first_reversed_index", "input": "\"Assign every letter from alphabet a new value based on its oddness: 0 if odd, 1 if even\"", "output": "39", "imports": [], "original_snippet": "def f(s: str):\n    alphabet = list('abcdefghijklmnopqrstuvwxyz')\n    words_transform = ['-'.join(sorted(word.lower())) for word in s.lower().split() if all((c in alphabet for c in word.lower()))]\n    reversed_words_transform = [''.join(reversed(w)) for w in words_transform]\n    first_reversed_index = [i for (i, letter) in enumerate(reversed(s.lower())) if letter in reversed_words_transform][0]\n    return first_reversed_index", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(numbers: list):\n    filtered_numbers = [x for x in numbers if x % 2 == 0]\n    smallest_even = min(filtered_numbers)\n    sum_odd_positions = sum(numbers[1::2])\n    return smallest_even * sum_odd_positions if sum_odd_positions % 5 == 0 else 0", "input": "[1, 2, 3, 4, 5, 6, 7, 8]", "output": "40", "imports": [], "original_snippet": "def f(numbers: list):\n    filtered_numbers = [x for x in numbers if x % 2 == 0]\n    smallest_even = min(filtered_numbers)\n    sum_odd_positions = sum(numbers[1::2])\n    return smallest_even * sum_odd_positions if sum_odd_positions % 5 == 0 else 0", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers: list):\n    odd_numbers = sum((num % 2 != 0 for num in numbers))\n    transformed_numbers = [num ** 2 for num in numbers]\n    even_numbers = sum((num % 2 == 0 for num in transformed_numbers))\n    return abs(odd_numbers - even_numbers)", "input": "[10, 7, 3, 4, 8, 6, 5, 11, 2]", "output": "1", "imports": [], "original_snippet": "def f(numbers: list):\n    odd_numbers = sum((num % 2 != 0 for num in numbers))\n    transformed_numbers = [num ** 2 for num in numbers]\n    even_numbers = sum((num % 2 == 0 for num in transformed_numbers))\n    return abs(odd_numbers - even_numbers)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "import math\ndef f(numbers: list[int]) -> int:\n    squares = [x ** 2 for x in numbers]\n    even_squares = [num for num in squares if num % 2 == 0]\n    n = sum(even_squares) // 2\n    final_result = math.floor(math.sqrt(n)) + math.floor(math.log(n, 3.141592653589793))\n    return final_result", "input": "[2, 3, 5, 6, 7, 9]", "output": "6", "imports": ["import math"], "original_snippet": "import math\ndef f(numbers: list[int]) -> int:\n    squares = [x ** 2 for x in numbers]\n    even_squares = [num for num in squares if num % 2 == 0]\n    n = sum(even_squares) // 2\n    final_result = math.floor(math.sqrt(n)) + math.floor(math.log(n, 3.141592653589793))\n    return final_result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(data: list):\n    transformed_data = [num ** 2 if num % 2 == 0 else num for num in data]\n    even_count = sum((1 for num in transformed_data if num % 2 == 0))\n    idx = 0\n    for _ in range(even_count):\n        transformed_data.append(transformed_data.pop(idx))\n        idx += 1\n    return sum(transformed_data)", "input": "[1, 2, 3, 4, 5, 6, 7, 8]", "output": "136", "imports": [], "original_snippet": "def f(data: list):\n    transformed_data = [num ** 2 if num % 2 == 0 else num for num in data]\n    even_count = sum((1 for num in transformed_data if num % 2 == 0))\n    idx = 0\n    for _ in range(even_count):\n        transformed_data.append(transformed_data.pop(idx))\n        idx += 1\n    return sum(transformed_data)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers: list):\n    doubled_numbers = [num * 2 for num in numbers]\n    sorted_doubled_numbers = sorted(doubled_numbers)\n    max_sum = sum(sorted_doubled_numbers[-3:])\n    return {'result': max_sum}", "input": "[1, 5, 10, 20, 25]", "output": "{'result': 110}", "imports": [], "original_snippet": "def f(numbers: list):\n    doubled_numbers = [num * 2 for num in numbers]\n    sorted_doubled_numbers = sorted(doubled_numbers)\n    max_sum = sum(sorted_doubled_numbers[-3:])\n    return {'result': max_sum}", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(data: list, input_list: list):\n    ascii_values = set((ord(char) % 10 for char in ''.join(data)))\n    transformed_data = [ord(char) for char in ''.join(data) if ord(char) % 10 not in ascii_values]\n    sorted_data = sorted(transformed_data, key=lambda x: (x % 5, x % 7))\n    concatenated_data = ''.join((chr(num) for num in sorted_data))\n    filtered_data = [num for num in sorted_data if num % 5 > 0 and abs(num % 7 - 3) < 3]\n    output = [concatenated_data + '_' + str(num) for num in input_list if concatenated_data.startswith(str(num % 2))]\n    return output", "input": "['a', 'b', 'c', 'd', 'e'], [1, 2, 3, 4, 5, 6, 7, 8, 9]", "output": "[]", "imports": [], "original_snippet": "def f(data: list, input_list: list):\n    ascii_values = set((ord(char) % 10 for char in ''.join(data)))\n    transformed_data = [ord(char) for char in ''.join(data) if ord(char) % 10 not in ascii_values]\n    sorted_data = sorted(transformed_data, key=lambda x: (x % 5, x % 7))\n    concatenated_data = ''.join((chr(num) for num in sorted_data))\n    filtered_data = [num for num in sorted_data if num % 5 > 0 and abs(num % 7 - 3) < 3]\n    output = [concatenated_data + '_' + str(num) for num in input_list if concatenated_data.startswith(str(num % 2))]\n    return output", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "from collections import deque\ndef f(input_list):\n    transformed_deque = deque()\n    for item in input_list:\n        if isinstance(item, (list, set, tuple)):\n            for sub_item in f(item):\n                transformed_deque.append(sub_item)\n        else:\n            transformed_deque.append(item ** 2)\n    sorted_deque = sorted(transformed_deque, key=lambda x: x % 5)\n    return list(sorted_deque)", "input": "[[1, 2, 3], [4, 5, 6]]", "output": "[25, 1, 16, 36, 4, 9]", "imports": ["from collections import deque"], "original_snippet": "from collections import deque\ndef f(input_list):\n    transformed_deque = deque()\n    for item in input_list:\n        if isinstance(item, (list, set, tuple)):\n            for sub_item in f(item):\n                transformed_deque.append(sub_item)\n        else:\n            transformed_deque.append(item ** 2)\n    sorted_deque = sorted(transformed_deque, key=lambda x: x % 5)\n    return list(sorted_deque)", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import List\ndef f(numbers: List[int]) -> int:\n    if not all((isinstance(num, int) for num in numbers)):\n        raise ValueError('Input must be a list of integers')\n    remainder_max = float('inf')\n    min_remainder_counter = 0\n    results = []\n    for i in range(min(len(numbers), 3)):\n        num = numbers[-i - 1] / 5\n        quotient = num // 1\n        remainder = num % 1\n        remainders = int(remainder * 10 % 10)\n        if remainders < remainder_max:\n            remainder_max = remainders\n            min_remainder_counter = 1\n        elif remainders == remainder_max:\n            min_remainder_counter += 1\n        results = [quotient - remainders]\n    return min(results) * min_remainder_counter", "input": "[25, 7, 87, 16, 21, 99, 24, 25, 46]", "output": "-3.0", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(numbers: List[int]) -> int:\n    if not all((isinstance(num, int) for num in numbers)):\n        raise ValueError('Input must be a list of integers')\n    remainder_max = float('inf')\n    min_remainder_counter = 0\n    results = []\n    for i in range(min(len(numbers), 3)):\n        num = numbers[-i - 1] / 5\n        quotient = num // 1\n        remainder = num % 1\n        remainders = int(remainder * 10 % 10)\n        if remainders < remainder_max:\n            remainder_max = remainders\n            min_remainder_counter = 1\n        elif remainders == remainder_max:\n            min_remainder_counter += 1\n        results = [quotient - remainders]\n    return min(results) * min_remainder_counter", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(numbers: list):\n    transformed = [n % 10 for n in numbers]\n    ranges = [transformed[i:i + 5] for i in range(0, len(transformed), 5)]\n    result = []\n    for (idx, rng) in enumerate(ranges):\n        filtered_rng = [x for x in rng if x > 5]\n        if filtered_rng:\n            filtered_rng.sort()\n            result.append(max(filtered_rng) * 2 * (idx + 1))\n        else:\n            result.append(sum(rng) * (idx + 1))\n    return result", "input": "[15, 14, 25, 46, 21, 23, 19]", "output": "[12, 36]", "imports": [], "original_snippet": "def f(numbers: list):\n    transformed = [n % 10 for n in numbers]\n    ranges = [transformed[i:i + 5] for i in range(0, len(transformed), 5)]\n    result = []\n    for (idx, rng) in enumerate(ranges):\n        filtered_rng = [x for x in rng if x > 5]\n        if filtered_rng:\n            filtered_rng.sort()\n            result.append(max(filtered_rng) * 2 * (idx + 1))\n        else:\n            result.append(sum(rng) * (idx + 1))\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(num: int):\n    intermediate = str(num * 2 - 5)[::-1]\n    result = int(intermediate * 2) % num\n    return int(str(result)[::-1])", "input": "6", "output": "5", "imports": [], "original_snippet": "def f(num: int):\n    intermediate = str(num * 2 - 5)[::-1]\n    result = int(intermediate * 2) % num\n    return int(str(result)[::-1])", "composite_functions": [], "_input_type": "int", "_output_type": "int"}
{"snippet": "def f(text: str) -> str:\n    chars_to_modify = set('PYTHONCODE')\n    modified_text = [char if char in chars_to_modify else chr(ord(char) + 10) for char in text]\n    unique_chars = ''.join(set(modified_text))\n    reversed_unique_chars = unique_chars[::-1]\n    gathered_chars = ''.join((str(ord(char)) for (i, char) in enumerate(reversed_unique_chars) if i % 2 == 0))\n    transformed_gathered_chars = [hex(int(char)) for char in gathered_chars if '0x' + char not in ['0x52', '0x10', '0x11']]\n    output = ''.join((char + str(len(char)) for char in transformed_gathered_chars))\n    return output", "input": "'HELLO'", "output": "'0x730x930x630x93'", "imports": [], "original_snippet": "def f(text: str) -> str:\n    chars_to_modify = set('PYTHONCODE')\n    modified_text = [char if char in chars_to_modify else chr(ord(char) + 10) for char in text]\n    unique_chars = ''.join(set(modified_text))\n    reversed_unique_chars = unique_chars[::-1]\n    gathered_chars = ''.join((str(ord(char)) for (i, char) in enumerate(reversed_unique_chars) if i % 2 == 0))\n    transformed_gathered_chars = [hex(int(char)) for char in gathered_chars if '0x' + char not in ['0x52', '0x10', '0x11']]\n    output = ''.join((char + str(len(char)) for char in transformed_gathered_chars))\n    return output", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "import math\ndef f(lst):\n    s1 = [int(math.sqrt(x)) for x in lst if x % 2 == 1]\n    s2 = [x ** 2 for x in lst if x % 2 == 0]\n    for i in s2:\n        for (n, x) in enumerate(lst):\n            if x == int(math.sqrt(i)):\n                lst.insert(0, i)\n                lst.pop(n)\n                break\n    return s1 + lst", "input": "[1, 3, 9, 4, 2]", "output": "[1, 1, 3, 4, 16, 1, 3, 2]", "imports": ["import math"], "original_snippet": "import math\ndef f(lst):\n    s1 = [int(math.sqrt(x)) for x in lst if x % 2 == 1]\n    s2 = [x ** 2 for x in lst if x % 2 == 0]\n    for i in s2:\n        for (n, x) in enumerate(lst):\n            if x == int(math.sqrt(i)):\n                lst.insert(0, i)\n                lst.pop(n)\n                break\n    return s1 + lst", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from collections.abc import Iterable\ndef f(data: list, even_indices: bool=True) -> int:\n    total_sum = 0\n    for item in data:\n        if even_indices and isinstance(item, int) and (item % 2 == 0) and (data.index(item) % 2 == 0):\n            total_sum += item\n        elif isinstance(item, list):\n            total_sum += f(item)\n    if even_indices:\n        return total_sum", "input": "[1, [2, 3, 4], [5, [6, 7, 8], 9], 10]", "output": "20", "imports": ["from collections.abc import Iterable"], "original_snippet": "from collections.abc import Iterable\ndef f(data: list, even_indices: bool=True) -> int:\n    total_sum = 0\n    for item in data:\n        if even_indices and isinstance(item, int) and (item % 2 == 0) and (data.index(item) % 2 == 0):\n            total_sum += item\n        elif isinstance(item, list):\n            total_sum += f(item)\n    if even_indices:\n        return total_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from math import sqrt, ceil\nfrom typing import List, Tuple\ndef f(numbers: List[int]) -> Tuple[List[int], int]:\n    sorted_numbers = sorted(numbers)\n    even_numbers = [x for x in sorted_numbers if x % 2 == 0]\n    square_roots = [ceil(sqrt(x)) for x in even_numbers]\n    max_sqrt = max(square_roots)\n    sum_of_squares = [x ** 2 for x in sorted_numbers if x != max_sqrt]\n    average_square = sum(sum_of_squares) // len(sum_of_squares)\n    product_even_numbers = [x for x in even_numbers if x * average_square < max_sqrt ** 3]\n    final_list = [x for x in even_numbers if x >= max_sqrt]\n    return (product_even_numbers, len(final_list))", "input": "[20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75]", "output": "([], 6)", "imports": ["from math import sqrt, ceil", "from typing import List, Tuple"], "original_snippet": "from math import sqrt, ceil\nfrom typing import List, Tuple\ndef f(numbers: List[int]) -> Tuple[List[int], int]:\n    sorted_numbers = sorted(numbers)\n    even_numbers = [x for x in sorted_numbers if x % 2 == 0]\n    square_roots = [ceil(sqrt(x)) for x in even_numbers]\n    max_sqrt = max(square_roots)\n    sum_of_squares = [x ** 2 for x in sorted_numbers if x != max_sqrt]\n    average_square = sum(sum_of_squares) // len(sum_of_squares)\n    product_even_numbers = [x for x in even_numbers if x * average_square < max_sqrt ** 3]\n    final_list = [x for x in even_numbers if x >= max_sqrt]\n    return (product_even_numbers, len(final_list))", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "from typing import List\ndef f(numbers: List[int]) -> int:\n    multiplied_numbers = [x * 2 for x in numbers]\n    subtracted_numbers = [x - 1 for x in multiplied_numbers]\n    filtered_numbers = [x for x in subtracted_numbers if x % 3 == 0]\n    reversed_numbers = filtered_numbers[::-1]\n    squared_numbers = [x ** 2 for x in reversed_numbers]\n    total_sum = sum(squared_numbers)\n    result = 2 * total_sum // 100\n    return result", "input": "[1, 2, 3, 4, 5]", "output": "1", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(numbers: List[int]) -> int:\n    multiplied_numbers = [x * 2 for x in numbers]\n    subtracted_numbers = [x - 1 for x in multiplied_numbers]\n    filtered_numbers = [x for x in subtracted_numbers if x % 3 == 0]\n    reversed_numbers = filtered_numbers[::-1]\n    squared_numbers = [x ** 2 for x in reversed_numbers]\n    total_sum = sum(squared_numbers)\n    result = 2 * total_sum // 100\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(s: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    alphabet_index_map = {alphabet[i]: i for i in range(len(alphabet))}\n    encoded_s = [str(alphabet_index_map[c] + 1) for c in s]\n    encoded_s.reverse()\n    encoded_s[-1] = str(int(encoded_s[-1]) % 25)\n    for i in range(1, len(encoded_s)):\n        encoded_s[-i] = str(int(encoded_s[-i]) % int(encoded_s[-i + 1]) + 1)\n    return ''.join(encoded_s)", "input": "'abc'", "output": "'312'", "imports": [], "original_snippet": "def f(s: str):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    alphabet_index_map = {alphabet[i]: i for i in range(len(alphabet))}\n    encoded_s = [str(alphabet_index_map[c] + 1) for c in s]\n    encoded_s.reverse()\n    encoded_s[-1] = str(int(encoded_s[-1]) % 25)\n    for i in range(1, len(encoded_s)):\n        encoded_s[-i] = str(int(encoded_s[-i]) % int(encoded_s[-i + 1]) + 1)\n    return ''.join(encoded_s)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(s: str):\n    if not isinstance(s, str):\n        return ''\n    transformed_chars = []\n    for (idx, char) in enumerate(s):\n        transformed_chars.append(ord(char) + idx)\n    min_val = min(transformed_chars)\n    transformed_chars = [chr(val - min_val) for val in transformed_chars]\n    return ''.join(transformed_chars)", "input": "'example'", "output": "'\\x02\\x16\\x00\\r\\x11\\x0e\\x08'", "imports": [], "original_snippet": "def f(s: str):\n    if not isinstance(s, str):\n        return ''\n    transformed_chars = []\n    for (idx, char) in enumerate(s):\n        transformed_chars.append(ord(char) + idx)\n    min_val = min(transformed_chars)\n    transformed_chars = [chr(val - min_val) for val in transformed_chars]\n    return ''.join(transformed_chars)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(a: int, b: int) -> int:\n    if a % 2 == 0 and b % 2 != 0:\n        return a + b\n    else:\n        return a - b", "input": "10, 7", "output": "17", "imports": [], "original_snippet": "def f(a: int, b: int) -> int:\n    if a % 2 == 0 and b % 2 != 0:\n        return a + b\n    else:\n        return a - b", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(tree: dict, node_string: str):\n    node_list = node_string.split(',')\n    current_indices = [0] * len(node_list)\n    reverse_edge = []\n    for node in node_list:\n        edges = tree[node]\n        if current_indices[0] < len(edges):\n            current_indices = [i + 1 if j == 0 else i for (j, i) in enumerate(current_indices)]\n            reverse_edge.append(edges)\n    output_list = list(zip(*reverse_edge[::-1]))\n    return output_list", "input": "{'node1': ['node2', 'node3'], 'node2': ['node4', 'node5', 'node6'],\n'node3': ['node7'], 'node4': ['node8'], 'node5': ['node9', 'node10'],\n'node6': ['node11'], 'node7': ['node12', 'node13', 'node14'],\n'node8': [], 'node9': [], 'node10': [], 'node11': [], 'node12': [],\n'node13': [], 'node14': []}, 'node1,node2,node3'", "output": "[('node4', 'node2'), ('node5', 'node3')]", "imports": [], "original_snippet": "def f(tree: dict, node_string: str):\n    node_list = node_string.split(',')\n    current_indices = [0] * len(node_list)\n    reverse_edge = []\n    for node in node_list:\n        edges = tree[node]\n        if current_indices[0] < len(edges):\n            current_indices = [i + 1 if j == 0 else i for (j, i) in enumerate(current_indices)]\n            reverse_edge.append(edges)\n    output_list = list(zip(*reverse_edge[::-1]))\n    return output_list", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(nums: list) -> int:\n    filtered = sorted([num for num in nums if num % 2 != 0 and int(num ** 0.5) ** 2 == num])\n    freq_dict = {num: filtered.count(num) for num in set(filtered)}\n    transformed_dict = {key: value * key * key * key for (key, value) in freq_dict.items()}\n    sum_trans = sum(transformed_dict.values())\n    return sum_trans * len(freq_dict)", "input": "[1, 4, 6, 9, 10, 15, 25, 30, 49, 50, 100]", "output": "536016", "imports": [], "original_snippet": "def f(nums: list) -> int:\n    filtered = sorted([num for num in nums if num % 2 != 0 and int(num ** 0.5) ** 2 == num])\n    freq_dict = {num: filtered.count(num) for num in set(filtered)}\n    transformed_dict = {key: value * key * key * key for (key, value) in freq_dict.items()}\n    sum_trans = sum(transformed_dict.values())\n    return sum_trans * len(freq_dict)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(a: int, b: int, c: dict):\n    result = []\n    for sublist in c:\n        all_boolean_chars = set([x in list(range(1, a + b + 1)) for x in sublist])\n        for signs in [1, -1]:\n            local_x = signs * sum(list(range(1, a + b + 1))[:a])\n            local_y = signs * sum(list(range(1, a + b + 1))[a - 1:a + b])\n            if all_boolean_chars == {True}:\n                condition_match = a * local_x + b * local_y == sum(sublist)\n                result.append([local_x, local_y])\n    return result", "input": "2, 3, [(7, 5, -2)]", "output": "[]", "imports": [], "original_snippet": "def f(a: int, b: int, c: dict):\n    result = []\n    for sublist in c:\n        all_boolean_chars = set([x in list(range(1, a + b + 1)) for x in sublist])\n        for signs in [1, -1]:\n            local_x = signs * sum(list(range(1, a + b + 1))[:a])\n            local_y = signs * sum(list(range(1, a + b + 1))[a - 1:a + b])\n            if all_boolean_chars == {True}:\n                condition_match = a * local_x + b * local_y == sum(sublist)\n                result.append([local_x, local_y])\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(filepath: str, version_count: int):\n    file_status = 'OPERATING'\n    non_empty_versions = []\n    for i in range(1, version_count + 1):\n        version = 1 if file_status == 'OPERATING' and i == 1 else i\n        previous_version = i - 1 if i > 1 else False\n        current_version = i\n        next_version = i + 1\n        file_status = 'DOWN' if not previous_version and version == 1 else 'OPERATING'\n        version += 2 if file_status == 'OPERATING' else 1\n        non_empty_versions.append((file_status, version))\n        if version % 2 != 0:\n            version -= 1\n            file_status = 'OPERATING'\n        if file_status == 'DOWN' and (not next_version):\n            version -= 1\n        if version == version_count:\n            break\n    non_empty_files_count = len([status for (status, version) in non_empty_versions if status == 'OPERATING'])\n    empty_files_count = len(non_empty_versions) - non_empty_files_count\n    return (non_empty_files_count, empty_files_count)", "input": "\"example.txt\", 5", "output": "(4, 1)", "imports": [], "original_snippet": "def f(filepath: str, version_count: int):\n    file_status = 'OPERATING'\n    non_empty_versions = []\n    for i in range(1, version_count + 1):\n        version = 1 if file_status == 'OPERATING' and i == 1 else i\n        previous_version = i - 1 if i > 1 else False\n        current_version = i\n        next_version = i + 1\n        file_status = 'DOWN' if not previous_version and version == 1 else 'OPERATING'\n        version += 2 if file_status == 'OPERATING' else 1\n        non_empty_versions.append((file_status, version))\n        if version % 2 != 0:\n            version -= 1\n            file_status = 'OPERATING'\n        if file_status == 'DOWN' and (not next_version):\n            version -= 1\n        if version == version_count:\n            break\n    non_empty_files_count = len([status for (status, version) in non_empty_versions if status == 'OPERATING'])\n    empty_files_count = len(non_empty_versions) - non_empty_files_count\n    return (non_empty_files_count, empty_files_count)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(lst: list):\n    mirrored_list = lst[::-1]\n    doubled_list = [num ** 2 - 2 * num + 1 for num in mirrored_list if num > 0]\n    sorted_list = sorted(doubled_list)\n    return sorted_list", "input": "[5, -3, 6, 2, -2, 0, 1, 3, -4, 7, 8]", "output": "[0, 1, 4, 16, 25, 36, 49]", "imports": [], "original_snippet": "def f(lst: list):\n    mirrored_list = lst[::-1]\n    doubled_list = [num ** 2 - 2 * num + 1 for num in mirrored_list if num > 0]\n    sorted_list = sorted(doubled_list)\n    return sorted_list", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(message: str):\n    from collections import Counter\n    char_count = Counter(message)\n    letter_freqs = [(key, value) for (key, value) in char_count.items()]\n    letter_freqs.sort(key=lambda x: x[0])\n    num_reversed = letter_freqs[-2][0][::-1]\n    return num_reversed", "input": "'aC1O2B a2O1B1C2'", "output": "'O'", "imports": ["from collections import Counter"], "original_snippet": "def f(message: str):\n    from collections import Counter\n    char_count = Counter(message)\n    letter_freqs = [(key, value) for (key, value) in char_count.items()]\n    letter_freqs.sort(key=lambda x: x[0])\n    num_reversed = letter_freqs[-2][0][::-1]\n    return num_reversed", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "from typing import List\ndef f(numbers: List[int]) -> str:\n    numbers.sort()\n    even_indices = [i for i in range(len(numbers)) if i % 2 == 0]\n    filtered_numbers = [numbers[i] for i in even_indices if numbers[i] % 3 == 1]\n    doubled_numbers = [n * 2 for n in filtered_numbers]\n    prime_indices = [i for i in range(len(numbers)) if all((i % d != 0 for d in range(2, i)))]\n    largest_prime_index = max(prime_indices, default=-1)\n    result = ''.join([str(doubled_numbers[i] if i < len(doubled_numbers) else numbers[i]) for i in range(len(numbers))])\n    return result", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]", "output": "'2142638506789101112131415161718192021222324252627282930'", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(numbers: List[int]) -> str:\n    numbers.sort()\n    even_indices = [i for i in range(len(numbers)) if i % 2 == 0]\n    filtered_numbers = [numbers[i] for i in even_indices if numbers[i] % 3 == 1]\n    doubled_numbers = [n * 2 for n in filtered_numbers]\n    prime_indices = [i for i in range(len(numbers)) if all((i % d != 0 for d in range(2, i)))]\n    largest_prime_index = max(prime_indices, default=-1)\n    result = ''.join([str(doubled_numbers[i] if i < len(doubled_numbers) else numbers[i]) for i in range(len(numbers))])\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(numbers: list):\n    odd_sum_list = []\n    left_shift_string_list = []\n    final_string_list = []\n    odd_positions_sum = [sum(numbers[:i + 1]) for i in range(len(numbers)) if (i + len(numbers)) % 2]\n    for i in odd_positions_sum:\n        if i % 7 == 0:\n            odd_sum_list.append(i)\n        else:\n            break\n    string_version_numbers = [str(number) for number in numbers]\n    for i in range(len(odd_sum_list)):\n        left_shifted_number = string_version_numbers[i:] + string_version_numbers[:i]\n        left_shift_string = ''.join(left_shifted_number)\n        left_shift_string_list.append(left_shift_string)\n    final_string_list = [string for string in left_shift_string_list if '3' in string]\n    odd_sum_string = ''.join((str(number) + '_' for number in odd_sum_list))\n    return f'{odd_sum_string}{final_string_list}'", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9]", "output": "'[]'", "imports": [], "original_snippet": "def f(numbers: list):\n    odd_sum_list = []\n    left_shift_string_list = []\n    final_string_list = []\n    odd_positions_sum = [sum(numbers[:i + 1]) for i in range(len(numbers)) if (i + len(numbers)) % 2]\n    for i in odd_positions_sum:\n        if i % 7 == 0:\n            odd_sum_list.append(i)\n        else:\n            break\n    string_version_numbers = [str(number) for number in numbers]\n    for i in range(len(odd_sum_list)):\n        left_shifted_number = string_version_numbers[i:] + string_version_numbers[:i]\n        left_shift_string = ''.join(left_shifted_number)\n        left_shift_string_list.append(left_shift_string)\n    final_string_list = [string for string in left_shift_string_list if '3' in string]\n    odd_sum_string = ''.join((str(number) + '_' for number in odd_sum_list))\n    return f'{odd_sum_string}{final_string_list}'", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "from typing import List\nimport math\ndef f(numbers: List[int]) -> None:\n    filtered_numbers = [num for num in numbers if num % 3 != 0 and num % 5 != 0 and (num % 2 == 0)]\n    transformed_numbers = [filtered_numbers[i] // 2 for i in range(0, len(filtered_numbers), 2)]\n    squares_sum = sum((math.log(num) if num < 10 else num ** 2 for num in transformed_numbers))\n    return squares_sum", "input": "[2, 9, 8, 14, 20, 22, 33, 34, 36]", "output": "290.9459101490553", "imports": ["from typing import List", "import math"], "original_snippet": "from typing import List\nimport math\ndef f(numbers: List[int]) -> None:\n    filtered_numbers = [num for num in numbers if num % 3 != 0 and num % 5 != 0 and (num % 2 == 0)]\n    transformed_numbers = [filtered_numbers[i] // 2 for i in range(0, len(filtered_numbers), 2)]\n    squares_sum = sum((math.log(num) if num < 10 else num ** 2 for num in transformed_numbers))\n    return squares_sum", "composite_functions": [], "_input_type": "list", "_output_type": "float"}
{"snippet": "def f(lst: list, lookup_table: dict):\n    lst = sorted(lst)\n    c = filter(lambda x: x not in lookup_table.keys(), lst)\n    c = map(lambda x: x + 1, c)\n    c = sorted(c)\n    transformed_list = [lookup_table[key] for key in c]\n    return transformed_list", "input": "[2, 5, 1, 3, 4, 0], {1: 'a', 2: 'b', 3: 'c', 4: 'd', 5: 'e', 37: 'f'}", "output": "['a']", "imports": [], "original_snippet": "def f(lst: list, lookup_table: dict):\n    lst = sorted(lst)\n    c = filter(lambda x: x not in lookup_table.keys(), lst)\n    c = map(lambda x: x + 1, c)\n    c = sorted(c)\n    transformed_list = [lookup_table[key] for key in c]\n    return transformed_list", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(string_pairs: list, func=lambda l, r: l.upper() if len(r) > len(l) else r):\n    transformed_string_pairs = [(func(string_pair[0].split(' ')[0], string_pair[1].split(' ')[0]), func(string_pair[0].split(' ')[1], string_pair[1].split(' ')[1])) for string_pair in string_pairs]\n    combined_output = [f'{pair[0]} {pair[1]}' for pair in transformed_string_pairs]\n    sorted_pairs = sorted(combined_output, key=lambda x: len(x.split(' ')[0]), reverse=True)\n    return '.'.join(sorted_pairs)", "input": "[('She is cool', 'He is hot'), ('She is smart', 'He is stupid')]", "output": "'He is.He is'", "imports": [], "original_snippet": "def f(string_pairs: list, func=lambda l, r: l.upper() if len(r) > len(l) else r):\n    transformed_string_pairs = [(func(string_pair[0].split(' ')[0], string_pair[1].split(' ')[0]), func(string_pair[0].split(' ')[1], string_pair[1].split(' ')[1])) for string_pair in string_pairs]\n    combined_output = [f'{pair[0]} {pair[1]}' for pair in transformed_string_pairs]\n    sorted_pairs = sorted(combined_output, key=lambda x: len(x.split(' ')[0]), reverse=True)\n    return '.'.join(sorted_pairs)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "import heapq\ndef f(numbers: list):\n    heapified = list(map(lambda x: x * -1, numbers))\n    heapq.heapify(heapified)\n    processed = [-heapq.heappop(heapified) for _ in range(5)]\n    return processed", "input": "[-90, -80, -70, -60, -50, -40, -30, -20, -10]", "output": "[-10, -20, -30, -40, -50]", "imports": ["import heapq"], "original_snippet": "import heapq\ndef f(numbers: list):\n    heapified = list(map(lambda x: x * -1, numbers))\n    heapq.heapify(heapified)\n    processed = [-heapq.heappop(heapified) for _ in range(5)]\n    return processed", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import List\nimport math\ndef f(input_str: str, upper_limit: int, scaling_factor: float, threshold: int) -> List[str]:\n    char_code_points = [ord(char) for char in input_str]\n    scaled_values = [code_point * scaling_factor for code_point in char_code_points]\n    transformed_values = [math.ceil(value / threshold) for value in scaled_values]\n    stringified_transformed_values = [str(value) for value in transformed_values if value < upper_limit]\n    return stringified_transformed_values", "input": "\"test\", 100, 5.5, 20", "output": "['32', '28', '32', '32']", "imports": ["from typing import List", "import math"], "original_snippet": "from typing import List\nimport math\ndef f(input_str: str, upper_limit: int, scaling_factor: float, threshold: int) -> List[str]:\n    char_code_points = [ord(char) for char in input_str]\n    scaled_values = [code_point * scaling_factor for code_point in char_code_points]\n    transformed_values = [math.ceil(value / threshold) for value in scaled_values]\n    stringified_transformed_values = [str(value) for value in transformed_values if value < upper_limit]\n    return stringified_transformed_values", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(s: str):\n    numbers = list(map(int, s.split()))\n    sorted_numbers = sorted(numbers, reverse=True)\n    squared_sum = sum((num ** 2 for num in sorted_numbers[:3]))\n    frequency_map = {num: numbers.count(num) for num in numbers}\n    cumulative_product = 1\n    for (i, num) in enumerate(sorted_numbers[:4]):\n        accumulated_product = num\n        for j in range(i):\n            accumulated_product *= sorted_numbers[j]\n        cumulative_product *= accumulated_product * frequency_map[num]\n    return cumulative_product", "input": "'3 5 7 11 15 20 60 23 40 76'", "output": "265189264588800000", "imports": [], "original_snippet": "def f(s: str):\n    numbers = list(map(int, s.split()))\n    sorted_numbers = sorted(numbers, reverse=True)\n    squared_sum = sum((num ** 2 for num in sorted_numbers[:3]))\n    frequency_map = {num: numbers.count(num) for num in numbers}\n    cumulative_product = 1\n    for (i, num) in enumerate(sorted_numbers[:4]):\n        accumulated_product = num\n        for j in range(i):\n            accumulated_product *= sorted_numbers[j]\n        cumulative_product *= accumulated_product * frequency_map[num]\n    return cumulative_product", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(input_data: List[List[int]]) -> int:\n    total = 0\n    for row in input_data:\n        row_sum = 0\n        for value in row:\n            row_sum += value\n        row_sum *= input_data.index(row) + 1\n        if row_sum % 2 == 0:\n            row_sum //= 2\n        total += row_sum\n    return total", "input": "[[12, 33, 32], [4, 2, 1], [7, 13, 15]]", "output": "189", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(input_data: List[List[int]]) -> int:\n    total = 0\n    for row in input_data:\n        row_sum = 0\n        for value in row:\n            row_sum += value\n        row_sum *= input_data.index(row) + 1\n        if row_sum % 2 == 0:\n            row_sum //= 2\n        total += row_sum\n    return total", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from math import prod\ndef f(numbers_list: list) -> int:\n    differences_product = 1\n    for sublist in numbers_list:\n        min_val = min(sublist)\n        max_val = max(sublist)\n        differences_product *= max_val - min_val\n    return differences_product", "input": "[[3, 5, 7], [8, 10, 12], [15, 18, 21]]", "output": "96", "imports": ["from math import prod"], "original_snippet": "from math import prod\ndef f(numbers_list: list) -> int:\n    differences_product = 1\n    for sublist in numbers_list:\n        min_val = min(sublist)\n        max_val = max(sublist)\n        differences_product *= max_val - min_val\n    return differences_product", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(strings: list):\n    char_freqs = {}\n    for string in strings:\n        for char in string:\n            char_freqs[char] = char_freqs.get(char, 0) + 1\n    return char_freqs", "input": "[\"hello\", \"world\", \"test\"]", "output": "{'h': 1, 'e': 2, 'l': 3, 'o': 2, 'w': 1, 'r': 1, 'd': 1, 't': 2, 's': 1}", "imports": [], "original_snippet": "def f(strings: list):\n    char_freqs = {}\n    for string in strings:\n        for char in string:\n            char_freqs[char] = char_freqs.get(char, 0) + 1\n    return char_freqs", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(str1: str, count: int) -> str:\n    sorted_string = sorted(str1)\n    origin_str = list(sorted_string)\n    new_res = ''\n    res = ''\n    cmp_n = ''\n    pos = 0\n    ctr = 0\n    while pos < len(sorted_string):\n        if sorted_string[pos] == '||':\n            new_res += sorted_string[pos]\n        elif origin_str[pos] == cmp_n:\n            ctr += 1\n            new_res += '&'\n        elif sorted_string[pos] == '..':\n            new_res += '.'\n        elif pos + 1 < len(sorted_string):\n            if sorted_string[pos + 1] == '..':\n                new_res += '.'\n                pos += 1\n            elif sorted_string[pos + 1] == '|':\n                new_res += '.'\n                pos += 1\n            elif sorted_string[pos + 1] == '..':\n                new_res += '.'\n                pos += 1\n            else:\n                new_res += '||'\n                pos += 1\n        if ctr == count:\n            ctr = -1\n        if sorted_string[pos] == '..':\n            new_res += '.'\n        elif sorted_string[pos] == '|':\n            new_res += 'x'\n        else:\n            res += new_res[::-1]\n            new_res = ''\n            cmp_n = sorted_string[pos]\n        pos += 1\n    return res", "input": "' << [[|]|]|', 1", "output": "'||||||||'", "imports": [], "original_snippet": "def f(str1: str, count: int) -> str:\n    sorted_string = sorted(str1)\n    origin_str = list(sorted_string)\n    new_res = ''\n    res = ''\n    cmp_n = ''\n    pos = 0\n    ctr = 0\n    while pos < len(sorted_string):\n        if sorted_string[pos] == '||':\n            new_res += sorted_string[pos]\n        elif origin_str[pos] == cmp_n:\n            ctr += 1\n            new_res += '&'\n        elif sorted_string[pos] == '..':\n            new_res += '.'\n        elif pos + 1 < len(sorted_string):\n            if sorted_string[pos + 1] == '..':\n                new_res += '.'\n                pos += 1\n            elif sorted_string[pos + 1] == '|':\n                new_res += '.'\n                pos += 1\n            elif sorted_string[pos + 1] == '..':\n                new_res += '.'\n                pos += 1\n            else:\n                new_res += '||'\n                pos += 1\n        if ctr == count:\n            ctr = -1\n        if sorted_string[pos] == '..':\n            new_res += '.'\n        elif sorted_string[pos] == '|':\n            new_res += 'x'\n        else:\n            res += new_res[::-1]\n            new_res = ''\n            cmp_n = sorted_string[pos]\n        pos += 1\n    return res", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(dictionaries: list[dict[int, int]]):\n    from collections import defaultdict\n    result = defaultdict(list)\n    for d in dictionaries:\n        transformed_keys = [d[k] * 3 + 7 for k in sorted(d.keys(), reverse=True)]\n        transformed_values = [(3 * v + 2, 3 * v + 1) for v in sorted(d.values(), reverse=True)]\n        new_dictionary = {k: transformed_values[i] for (i, k) in enumerate(transformed_keys)}\n        result.update(new_dictionary)\n    return dict(result)", "input": "[{'a': 1, 'b': 2, 'c': 3}, {'d': 4, 'e': 5, 'f': 6}]", "output": "{16: (11, 10), 13: (8, 7), 10: (5, 4), 25: (20, 19), 22: (17, 16), 19: (14, 13)}", "imports": ["from collections import defaultdict"], "original_snippet": "def f(dictionaries: list[dict[int, int]]):\n    from collections import defaultdict\n    result = defaultdict(list)\n    for d in dictionaries:\n        transformed_keys = [d[k] * 3 + 7 for k in sorted(d.keys(), reverse=True)]\n        transformed_values = [(3 * v + 2, 3 * v + 1) for v in sorted(d.values(), reverse=True)]\n        new_dictionary = {k: transformed_values[i] for (i, k) in enumerate(transformed_keys)}\n        result.update(new_dictionary)\n    return dict(result)", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "from typing import List, Dict\nimport math\ndef f(geometric_shapes: List[Dict[str, float]], central_circle_radius: float):\n    transformed_shapes = []\n    for shape in geometric_shapes:\n        shape_type = shape.get('type').lower()\n        if shape_type == 'circle':\n            new_radius = shape['radius'] + central_circle_radius\n        elif shape_type == 'rectangle':\n            new_side_a = shape['side_a'] + central_circle_radius\n        elif shape_type == 'triangle':\n            new_side_a = shape['side_a'] + 2 * central_circle_radius\n        else:\n            new_radius = shape['radius'] + central_circle_radius\n        transformed_shapes.append({shape_type: new_radius})\n    area_of_shapes = [math.pi * shape['circle']['radius'] ** 2 for shape in transformed_shapes if shape.get('type') == 'circle']\n    perimeter_of_shapes = [2 * shape['circle']['radius'] * math.pi for shape in transformed_shapes if shape.get('type') == 'circle']\n    transformed_shapes_area = len(area_of_shapes) * sum(area_of_shapes)\n    transformed_shapes_perimeter = sum(perimeter_of_shapes)\n    result = (transformed_shapes_area, transformed_shapes_perimeter)\n    return result", "input": "[{'type': 'circle', 'radius': 4.3}, {'type': 'rectangle', 'side_a': 5.1, 'side_b': 6.7}], 2.0", "output": "(0, 0)", "imports": ["from typing import List, Dict", "import math"], "original_snippet": "from typing import List, Dict\nimport math\ndef f(geometric_shapes: List[Dict[str, float]], central_circle_radius: float):\n    transformed_shapes = []\n    for shape in geometric_shapes:\n        shape_type = shape.get('type').lower()\n        if shape_type == 'circle':\n            new_radius = shape['radius'] + central_circle_radius\n        elif shape_type == 'rectangle':\n            new_side_a = shape['side_a'] + central_circle_radius\n        elif shape_type == 'triangle':\n            new_side_a = shape['side_a'] + 2 * central_circle_radius\n        else:\n            new_radius = shape['radius'] + central_circle_radius\n        transformed_shapes.append({shape_type: new_radius})\n    area_of_shapes = [math.pi * shape['circle']['radius'] ** 2 for shape in transformed_shapes if shape.get('type') == 'circle']\n    perimeter_of_shapes = [2 * shape['circle']['radius'] * math.pi for shape in transformed_shapes if shape.get('type') == 'circle']\n    transformed_shapes_area = len(area_of_shapes) * sum(area_of_shapes)\n    transformed_shapes_perimeter = sum(perimeter_of_shapes)\n    result = (transformed_shapes_area, transformed_shapes_perimeter)\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "import numpy as np\ndef f(elements: list):\n    binary_elements = [bin(num)[2:] for num in elements]\n    odd_binary_digits = ''.join([digit for element in binary_elements for digit in element.split('1')[1::2]])\n    reversed_string = odd_binary_digits[::-1]\n    encoded_string = ''.join([chr(ord(c) + 5) for c in reversed_string])\n    return encoded_string", "input": "[36, 77, 27, 16, 48, 85]", "output": "'55555555555'", "imports": ["import numpy as np"], "original_snippet": "import numpy as np\ndef f(elements: list):\n    binary_elements = [bin(num)[2:] for num in elements]\n    odd_binary_digits = ''.join([digit for element in binary_elements for digit in element.split('1')[1::2]])\n    reversed_string = odd_binary_digits[::-1]\n    encoded_string = ''.join([chr(ord(c) + 5) for c in reversed_string])\n    return encoded_string", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(people: list, turn_index: int):\n    selected_people = []\n    for (i, person) in enumerate(people):\n        if i == turn_index:\n            if person.get('preference'):\n                selected_people.append([person['name'], person['age']])\n        elif person.get('food') and person.get('hobby'):\n            if 'vegetarian' in person['food'] and 'gaming' in person['hobby']:\n                selected_people.append([person['name'], person['age']])\n    return selected_people", "input": "[\n    {'name': 'John', 'age': 28, 'food': ['pasta', 'vegetarian']},\n    {'name': 'Alice', 'age': 32, 'hobby': ['hiking', 'gardening']},\n    {'name': 'David', 'age': 42, 'hobby': ['gaming', 'photography']},\n    {'name': 'Lucy', 'age': 31, 'preference': True}\n], 3", "output": "[['Lucy', 31]]", "imports": [], "original_snippet": "def f(people: list, turn_index: int):\n    selected_people = []\n    for (i, person) in enumerate(people):\n        if i == turn_index:\n            if person.get('preference'):\n                selected_people.append([person['name'], person['age']])\n        elif person.get('food') and person.get('hobby'):\n            if 'vegetarian' in person['food'] and 'gaming' in person['hobby']:\n                selected_people.append([person['name'], person['age']])\n    return selected_people", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "from collections import deque\ndef f(graph: dict):\n    visited_edges = set()\n    max_distance_nodes = {}\n    queue = deque()\n    for node in graph:\n        if node not in max_distance_nodes:\n            queue.append((node, 0))\n            max_distance_nodes[node] = 0\n            visited_edges.add(node)\n        else:\n            continue\n        while queue:\n            (current_node, current_distance) = queue.popleft()\n            if current_node in graph:\n                for edge in graph[current_node]:\n                    if edge not in visited_edges:\n                        visited_edges.add(edge)\n                        max_distance_nodes[edge] = current_distance + 1\n                        queue.append((edge, current_distance + 1))\n    for node in graph:\n        if node in max_distance_nodes:\n            max_distance_nodes[node] = max(max_distance_nodes[node], max_distance_nodes.get(graph[node][0], 0))\n    return max_distance_nodes", "input": "{0: [1, 2], 1: [3, 4], 2: [5, 6], 3: [7, 8], 4: [9, 10], 5: [11, 12], 6: [13, 14], 7: [15, 16], 9: [17, 18], 12: [19, 20], 15: [21, 22]}", "output": "{0: 1, 1: 2, 2: 2, 3: 3, 4: 3, 5: 3, 6: 3, 7: 4, 8: 3, 9: 4, 10: 3, 11: 3, 12: 4, 13: 3, 14: 3, 15: 5, 16: 4, 17: 4, 18: 4, 19: 4, 20: 4, 21: 5, 22: 5}", "imports": ["from collections import deque"], "original_snippet": "from collections import deque\ndef f(graph: dict):\n    visited_edges = set()\n    max_distance_nodes = {}\n    queue = deque()\n    for node in graph:\n        if node not in max_distance_nodes:\n            queue.append((node, 0))\n            max_distance_nodes[node] = 0\n            visited_edges.add(node)\n        else:\n            continue\n        while queue:\n            (current_node, current_distance) = queue.popleft()\n            if current_node in graph:\n                for edge in graph[current_node]:\n                    if edge not in visited_edges:\n                        visited_edges.add(edge)\n                        max_distance_nodes[edge] = current_distance + 1\n                        queue.append((edge, current_distance + 1))\n    for node in graph:\n        if node in max_distance_nodes:\n            max_distance_nodes[node] = max(max_distance_nodes[node], max_distance_nodes.get(graph[node][0], 0))\n    return max_distance_nodes", "composite_functions": [], "_input_type": "dict", "_output_type": "dict"}
{"snippet": "def f(data: list):\n    sorted_data = sorted(data, key=lambda x: (x[1] % 2, x[0] % 2))\n    transformed_data = [(x[0] + 10, x[1] + 2) if x[1] > 5 else (0, x[0]) for x in sorted_data]\n    return transformed_data", "input": "[(1, 6), (3, 3), (2, 10), (4, 1), (5, 8)]", "output": "[(12, 12), (11, 8), (15, 10), (0, 4), (0, 3)]", "imports": [], "original_snippet": "def f(data: list):\n    sorted_data = sorted(data, key=lambda x: (x[1] % 2, x[0] % 2))\n    transformed_data = [(x[0] + 10, x[1] + 2) if x[1] > 5 else (0, x[0]) for x in sorted_data]\n    return transformed_data", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(data: list):\n    sorted_data = sorted(data)\n    merged_lists = []\n    initial_values = sorted_data[::2]\n    initial_values_dict = {i: val for (i, val) in enumerate(initial_values)}\n    while initial_values_dict:\n        (current_list_index, current_middle_value) = max(initial_values_dict.items(), key=lambda x: x[1])\n        other_half = [val for val in sorted_data if initial_values_dict[current_list_index] < val < current_middle_value and val != current_middle_value]\n        merged_lists.append(other_half)\n        del initial_values_dict[current_list_index]\n    return sum((other_len * (other_len - 1) // 2 for other_len in (len(other) for other in merged_lists)))", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "0", "imports": [], "original_snippet": "def f(data: list):\n    sorted_data = sorted(data)\n    merged_lists = []\n    initial_values = sorted_data[::2]\n    initial_values_dict = {i: val for (i, val) in enumerate(initial_values)}\n    while initial_values_dict:\n        (current_list_index, current_middle_value) = max(initial_values_dict.items(), key=lambda x: x[1])\n        other_half = [val for val in sorted_data if initial_values_dict[current_list_index] < val < current_middle_value and val != current_middle_value]\n        merged_lists.append(other_half)\n        del initial_values_dict[current_list_index]\n    return sum((other_len * (other_len - 1) // 2 for other_len in (len(other) for other in merged_lists)))", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(s1: str, s2: str) -> bool:\n    s1_sorted = sorted(s1)\n    s2_sorted = sorted(s2)\n    common_chars = set(s1_sorted).intersection(set(s2_sorted))\n    num_common_chars = len(common_chars)\n    s1_divisible_by_3 = num_common_chars % 3 == 0\n    s2_divisible_by_3 = num_common_chars % 3 == 0\n    return s1_divisible_by_3 and s2_divisible_by_3", "input": "'apple', 'banana'", "output": "False", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(s1: str, s2: str) -> bool:\n    s1_sorted = sorted(s1)\n    s2_sorted = sorted(s2)\n    common_chars = set(s1_sorted).intersection(set(s2_sorted))\n    num_common_chars = len(common_chars)\n    s1_divisible_by_3 = num_common_chars % 3 == 0\n    s2_divisible_by_3 = num_common_chars % 3 == 0\n    return s1_divisible_by_3 and s2_divisible_by_3", "composite_functions": [], "_input_type": "tuple", "_output_type": "bool"}
{"snippet": "def f(l: list):\n    even_sum = 0\n    for num in l:\n        if num % 2 == 0:\n            even_sum += num\n    odd_sum = sum((2 * num if num % 2 != 0 else 0 for num in l))\n    return [even_sum, odd_sum]", "input": "[4,1,10,9,10,9,10,9,10,9,10,9,100,99]", "output": "[154, 290]", "imports": [], "original_snippet": "def f(l: list):\n    even_sum = 0\n    for num in l:\n        if num % 2 == 0:\n            even_sum += num\n    odd_sum = sum((2 * num if num % 2 != 0 else 0 for num in l))\n    return [even_sum, odd_sum]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(data_dict: dict, sequence: list):\n    transformed_sequence = [str(chr(ord(c) ^ 5)) if ord(c) % 10 > 4 else c for c in sequence]\n    data_dict['transformed'] = transformed_sequence\n    data_dict['threshold'] = 4 if len(sequence) % 2 == 0 else 5\n    combined = ''.join(data_dict['transformed'])\n    unique = sorted(set(combined), key=combined.index)\n    group_bits = [chr(ord(c) % 255) for c in combined]\n    grouped = {}\n    for c in unique:\n        bits = [cpt for (cpt, elm) in enumerate(group_bits) if elm == c]\n        counts = {'odd': len([i for i in bits if i % 2 == 1]), 'even': len([i for i in bits if i % 2 == 0])}\n        grouped[c] = counts\n    thresholded = [v for (k, v) in grouped.items() if data_dict['threshold'] % ord(k) == 0]\n    counts = {'odd': sum([d['odd'] for d in thresholded]), 'even': sum([d['even'] for d in thresholded])}\n    result = {'transformed_sequence': data_dict['transformed'], 'threshold': data_dict['threshold'], 'counts': counts}\n    return result", "input": "{'transformed': ['test', 'stack', 'overflow', 'python25', 'numbers', 'occurrences', 'grouped', 'threshold', 'outputs', 'odd', 'odd', 'even', 'even'], 'threshold': 4}, \n['c', 'b', 'a', 'd']", "output": "{'transformed_sequence': ['f', 'g', 'd', 'd'], 'threshold': 4, 'counts': {'odd': 0, 'even': 0}}", "imports": [], "original_snippet": "def f(data_dict: dict, sequence: list):\n    transformed_sequence = [str(chr(ord(c) ^ 5)) if ord(c) % 10 > 4 else c for c in sequence]\n    data_dict['transformed'] = transformed_sequence\n    data_dict['threshold'] = 4 if len(sequence) % 2 == 0 else 5\n    combined = ''.join(data_dict['transformed'])\n    unique = sorted(set(combined), key=combined.index)\n    group_bits = [chr(ord(c) % 255) for c in combined]\n    grouped = {}\n    for c in unique:\n        bits = [cpt for (cpt, elm) in enumerate(group_bits) if elm == c]\n        counts = {'odd': len([i for i in bits if i % 2 == 1]), 'even': len([i for i in bits if i % 2 == 0])}\n        grouped[c] = counts\n    thresholded = [v for (k, v) in grouped.items() if data_dict['threshold'] % ord(k) == 0]\n    counts = {'odd': sum([d['odd'] for d in thresholded]), 'even': sum([d['even'] for d in thresholded])}\n    result = {'transformed_sequence': data_dict['transformed'], 'threshold': data_dict['threshold'], 'counts': counts}\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "dict"}
{"snippet": "def f(s: str) -> str:\n    balanced = 0\n    processed_chars = []\n    for char in s:\n        if char == '(':\n            balanced += 1\n        elif char == ')':\n            if balanced > 0:\n                balanced -= 1\n                processed_chars.append(char)\n        else:\n            processed_chars.append(char)\n    reversed_cleartext = ''.join(processed_chars[::-1])\n    transformed_ciphertext = ''.join([chr(ord(c) % 50 + 97) for c in reversed_cleartext])\n    return transformed_ciphertext", "input": "'((abc)de)f'", "output": "'c\\x8aba\\x8a\\x92\\x91\\x90'", "imports": [], "original_snippet": "def f(s: str) -> str:\n    balanced = 0\n    processed_chars = []\n    for char in s:\n        if char == '(':\n            balanced += 1\n        elif char == ')':\n            if balanced > 0:\n                balanced -= 1\n                processed_chars.append(char)\n        else:\n            processed_chars.append(char)\n    reversed_cleartext = ''.join(processed_chars[::-1])\n    transformed_ciphertext = ''.join([chr(ord(c) % 50 + 97) for c in reversed_cleartext])\n    return transformed_ciphertext", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(string_list: list) -> str:\n    reversed_chars = []\n    for string in string_list:\n        for char in string:\n            reversed_chars.append(char)\n    reversed_chars.sort()\n    reversed_chars.reverse()\n    return ''.join(reversed_chars)", "input": "['apple', 'fruit', 'banana']", "output": "'utrppnnlifebaaaa'", "imports": [], "original_snippet": "def f(string_list: list) -> str:\n    reversed_chars = []\n    for string in string_list:\n        for char in string:\n            reversed_chars.append(char)\n    reversed_chars.sort()\n    reversed_chars.reverse()\n    return ''.join(reversed_chars)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(text: str, rotation: int) -> str:\n    rotated_text = ''\n    for letter in text:\n        ascii_num = ord(letter)\n        if 65 <= ascii_num <= 90:\n            rotated_letter = chr((ascii_num - 65 + rotation) % 26 + 65)\n        elif 97 <= ascii_num <= 122:\n            rotated_letter = chr((ascii_num - 97 + rotation) % 26 + 97)\n        else:\n            rotated_letter = letter\n        rotated_text += rotated_letter\n    return rotated_text", "input": "'abcdefg', 2", "output": "'cdefghi'", "imports": [], "original_snippet": "def f(text: str, rotation: int) -> str:\n    rotated_text = ''\n    for letter in text:\n        ascii_num = ord(letter)\n        if 65 <= ascii_num <= 90:\n            rotated_letter = chr((ascii_num - 65 + rotation) % 26 + 65)\n        elif 97 <= ascii_num <= 122:\n            rotated_letter = chr((ascii_num - 97 + rotation) % 26 + 97)\n        else:\n            rotated_letter = letter\n        rotated_text += rotated_letter\n    return rotated_text", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "from typing import List\ndef f(lst: List[int]) -> int:\n    lst_sorted = sorted(lst)\n    odd = [num for num in lst_sorted if num % 2 == 1]\n    even = [num for num in lst_sorted if num % 2 == 0]\n    product = even[0] * odd[1] if len(odd) > 1 else even[0]\n    index = next((i for (i, num) in enumerate(lst_sorted) if num % 2 == 0), None)\n    if index is not None:\n        lst_sorted.insert(index, product)\n    return lst_sorted", "input": "[8, 5, 1, 2]", "output": "[1, 10, 2, 5, 8]", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(lst: List[int]) -> int:\n    lst_sorted = sorted(lst)\n    odd = [num for num in lst_sorted if num % 2 == 1]\n    even = [num for num in lst_sorted if num % 2 == 0]\n    product = even[0] * odd[1] if len(odd) > 1 else even[0]\n    index = next((i for (i, num) in enumerate(lst_sorted) if num % 2 == 0), None)\n    if index is not None:\n        lst_sorted.insert(index, product)\n    return lst_sorted", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_list, iteration):\n    all_between = [0]\n    between = [0]\n    iterations_close = []\n    for i in range(iteration):\n        if i < len(between):\n            unique_close = all_between[between[i] - 1]\n        else:\n            unique_close = 0\n        all_between.append(unique_close)\n        between.append(unique_close)\n        iterations_close.append(unique_close)\n    return iterations_close", "input": "[0, 1, 2, 3], 4", "output": "[0, 0, 0, 0]", "imports": [], "original_snippet": "def f(input_list, iteration):\n    all_between = [0]\n    between = [0]\n    iterations_close = []\n    for i in range(iteration):\n        if i < len(between):\n            unique_close = all_between[between[i] - 1]\n        else:\n            unique_close = 0\n        all_between.append(unique_close)\n        between.append(unique_close)\n        iterations_close.append(unique_close)\n    return iterations_close", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(lst: list):\n    filtered_lst = [x for x in lst if 4 < x < 30]\n    zip_lst = zip(filtered_lst, filtered_lst[1:])\n    grouped_elements = [next((g for g in list(zip_lst) if abs(g[0] - g[1]) <= 5), None)]\n    transformed_elements = [x[0] * 2 if x[0] % 2 == 0 else x[0] - 1 for x in grouped_elements]\n    return sum(transformed_elements)", "input": "[10, 32, 13, 20, 26, 15, 22, 34, 29]", "output": "20", "imports": [], "original_snippet": "def f(lst: list):\n    filtered_lst = [x for x in lst if 4 < x < 30]\n    zip_lst = zip(filtered_lst, filtered_lst[1:])\n    grouped_elements = [next((g for g in list(zip_lst) if abs(g[0] - g[1]) <= 5), None)]\n    transformed_elements = [x[0] * 2 if x[0] % 2 == 0 else x[0] - 1 for x in grouped_elements]\n    return sum(transformed_elements)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(lst: list):\n    transformed_lst = []\n    for (idx, num) in enumerate(sorted(lst)):\n        if idx % 2 == 0:\n            transformed_lst.append(num * 2)\n        else:\n            transformed_lst.append(num // 2)\n    return transformed_lst", "input": "[4, 2, 6, 9]", "output": "[4, 2, 12, 4]", "imports": [], "original_snippet": "def f(lst: list):\n    transformed_lst = []\n    for (idx, num) in enumerate(sorted(lst)):\n        if idx % 2 == 0:\n            transformed_lst.append(num * 2)\n        else:\n            transformed_lst.append(num // 2)\n    return transformed_lst", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import List\ndef f(numbers: List[int]) -> List[int]:\n    even_indices = [i for (i, n) in enumerate(numbers) if n % 2 == 0]\n    sorted_nums = sorted(numbers)\n    sublists = [sorted_nums[i:] for i in even_indices]\n    sums_mod_100 = [sum(s) % 100 for s in sublists]\n    transformed_output = [sums_mod_100[i] * 5 + 10 for i in range(len(sums_mod_100))]\n    return transformed_output", "input": "[12, 15, 18, 21, 24, 27, 30, 32, 35]", "output": "[80, 445, 250, 495, 345]", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(numbers: List[int]) -> List[int]:\n    even_indices = [i for (i, n) in enumerate(numbers) if n % 2 == 0]\n    sorted_nums = sorted(numbers)\n    sublists = [sorted_nums[i:] for i in even_indices]\n    sums_mod_100 = [sum(s) % 100 for s in sublists]\n    transformed_output = [sums_mod_100[i] * 5 + 10 for i in range(len(sums_mod_100))]\n    return transformed_output", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(k: int, tup: tuple) -> float:\n    (a, b) = tup\n    sum_up = a + b\n    if abs(a) <= abs(b):\n        sorted_list = sorted(tup)\n        median_index = len(sorted_list) // 2\n        median = sorted_list[median_index]\n    else:\n        sorted_list = sorted(tup, reverse=True)\n        count = {}\n        for num in sorted_list:\n            count[num] = count.get(num, 0) + 1\n        mode = max(count, key=count.get)\n    return sum_up / (k ** 2 + 2)", "input": "10, (42, -56)", "output": "-0.13725490196078433", "imports": [], "original_snippet": "def f(k: int, tup: tuple) -> float:\n    (a, b) = tup\n    sum_up = a + b\n    if abs(a) <= abs(b):\n        sorted_list = sorted(tup)\n        median_index = len(sorted_list) // 2\n        median = sorted_list[median_index]\n    else:\n        sorted_list = sorted(tup, reverse=True)\n        count = {}\n        for num in sorted_list:\n            count[num] = count.get(num, 0) + 1\n        mode = max(count, key=count.get)\n    return sum_up / (k ** 2 + 2)", "composite_functions": [], "_input_type": "tuple", "_output_type": "float"}
{"snippet": "def f(data: list):\n    word_count = {}\n    for (num, word) in data:\n        if word not in word_count:\n            word_count[word] = 1\n        else:\n            word_count[word] += 1\n    valid_words = [word for word in word_count if word_count[word] % 2 == 0]\n    valid_words.sort(key=lambda w: word_count[w])\n    return valid_words", "input": "[(2, 'apple'), (3, 'banana'), (4, 'apple'), (5, 'cherry'), (6, 'banana'), (7, 'apple')]", "output": "['banana']", "imports": [], "original_snippet": "def f(data: list):\n    word_count = {}\n    for (num, word) in data:\n        if word not in word_count:\n            word_count[word] = 1\n        else:\n            word_count[word] += 1\n    valid_words = [word for word in word_count if word_count[word] % 2 == 0]\n    valid_words.sort(key=lambda w: word_count[w])\n    return valid_words", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(numbers: list[int]) -> int:\n    weighted_remainders = []\n    prime_numbers = [2, 3, 5, 7, 11]\n    for number in numbers:\n        for prime in prime_numbers:\n            remainder = number % prime\n            weighted_remainder = prime * remainder\n            weighted_remainders.append(weighted_remainder)\n    total_weighted_remainder = sum(weighted_remainders)\n    average_weighted_remainder = total_weighted_remainder % sum(prime_numbers)\n    return average_weighted_remainder", "input": "[12, 15, 17, 19]", "output": "26", "imports": [], "original_snippet": "def f(numbers: list[int]) -> int:\n    weighted_remainders = []\n    prime_numbers = [2, 3, 5, 7, 11]\n    for number in numbers:\n        for prime in prime_numbers:\n            remainder = number % prime\n            weighted_remainder = prime * remainder\n            weighted_remainders.append(weighted_remainder)\n    total_weighted_remainder = sum(weighted_remainders)\n    average_weighted_remainder = total_weighted_remainder % sum(prime_numbers)\n    return average_weighted_remainder", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(data: list):\n    squared_data = []\n    seen = set()\n    for sublist in data:\n        transformed_sublist = []\n        for num in sublist:\n            squared = num * num\n            if squared not in seen:\n                seen.add(squared)\n                transformed_sublist.append(squared)\n        squared_data.append(sorted(transformed_sublist))\n    return squared_data", "input": "[[1, 2, 2], [3, 4, 4], [1, 5]]", "output": "[[1, 4], [9, 16], [25]]", "imports": [], "original_snippet": "def f(data: list):\n    squared_data = []\n    seen = set()\n    for sublist in data:\n        transformed_sublist = []\n        for num in sublist:\n            squared = num * num\n            if squared not in seen:\n                seen.add(squared)\n                transformed_sublist.append(squared)\n        squared_data.append(sorted(transformed_sublist))\n    return squared_data", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(numbers: list, target_sum: int):\n    start = 0\n    current_sum = 0\n    shortest_sequence = None\n    for end in range(len(numbers)):\n        current_sum += numbers[end]\n        while current_sum >= target_sum and start <= end:\n            if shortest_sequence is None or end - start < len(shortest_sequence) - 1:\n                shortest_sequence = numbers[start:end + 1]\n            current_sum -= numbers[start]\n            start += 1\n    return shortest_sequence", "input": "[2, 3, 1, 1, 2, 4, 3], 7", "output": "[4, 3]", "imports": [], "original_snippet": "def f(numbers: list, target_sum: int):\n    start = 0\n    current_sum = 0\n    shortest_sequence = None\n    for end in range(len(numbers)):\n        current_sum += numbers[end]\n        while current_sum >= target_sum and start <= end:\n            if shortest_sequence is None or end - start < len(shortest_sequence) - 1:\n                shortest_sequence = numbers[start:end + 1]\n            current_sum -= numbers[start]\n            start += 1\n    return shortest_sequence", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(word_container: dict):\n    vowels = 'aeiou'\n    words = word_container.get('words', [])\n    vowel_counts = [sum((1 for letter in word if letter in vowels.lower() and letter in word.lower())) for word in words]\n    largest_vowel_count = max(vowel_counts)\n    words_with_largest_vowel_count = [word for (word, count) in zip(words, vowel_counts) if count == largest_vowel_count]\n    return words_with_largest_vowel_count", "input": "{'words': ['apple', 'banana', 'cherry', 'date', 'elderberry', 'fig', 'grape', 'honeydew', 'I', 'KIWI', 'lemon', 'mango', 'nectarine', 'orange']}", "output": "['nectarine']", "imports": [], "original_snippet": "def f(word_container: dict):\n    vowels = 'aeiou'\n    words = word_container.get('words', [])\n    vowel_counts = [sum((1 for letter in word if letter in vowels.lower() and letter in word.lower())) for word in words]\n    largest_vowel_count = max(vowel_counts)\n    words_with_largest_vowel_count = [word for (word, count) in zip(words, vowel_counts) if count == largest_vowel_count]\n    return words_with_largest_vowel_count", "composite_functions": [], "_input_type": "dict", "_output_type": "list"}
{"snippet": "def f(day: int, month: int, year: int):\n    is_leap_year = year % 4 == 0 and year % 100 != 0 or year % 400 == 0\n    months = {'january': 31, 'february': 28 + int(is_leap_year), 'march': 31, 'april': 30, 'may': 31, 'june': 30, 'july': 31, 'august': 31, 'september': 30, 'october': 31, 'november': 30, 'december': 31}\n    if day < 1 or day > 31:\n        day = 12\n    months.pop('january')\n    month_str = list(months.keys())[month - 2]\n    days_in_month = months[month_str]\n    mod = day % 2\n    return month_str + str(days_in_month) + str(mod)", "input": "6, 1, 1988", "output": "'december310'", "imports": [], "original_snippet": "def f(day: int, month: int, year: int):\n    is_leap_year = year % 4 == 0 and year % 100 != 0 or year % 400 == 0\n    months = {'january': 31, 'february': 28 + int(is_leap_year), 'march': 31, 'april': 30, 'may': 31, 'june': 30, 'july': 31, 'august': 31, 'september': 30, 'october': 31, 'november': 30, 'december': 31}\n    if day < 1 or day > 31:\n        day = 12\n    months.pop('january')\n    month_str = list(months.keys())[month - 2]\n    days_in_month = months[month_str]\n    mod = day % 2\n    return month_str + str(days_in_month) + str(mod)", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "def f(dictionaries: list[dict]) -> str:\n    sorted_dicts = sorted(dictionaries, key=lambda x: x['value'])\n    result = ''\n    for (idx, entry) in enumerate(sorted_dicts):\n        key = entry['key']\n        new_key = ''.join(sorted(key))\n        value = entry['value'] + idx\n        mod_result = value % 3\n        result += new_key + str(mod_result)\n    return result", "input": "[{'key': 'hello', 'value': 10}, {'key': 'world', 'value': 5}]", "output": "'dlorw2ehllo2'", "imports": [], "original_snippet": "def f(dictionaries: list[dict]) -> str:\n    sorted_dicts = sorted(dictionaries, key=lambda x: x['value'])\n    result = ''\n    for (idx, entry) in enumerate(sorted_dicts):\n        key = entry['key']\n        new_key = ''.join(sorted(key))\n        value = entry['value'] + idx\n        mod_result = value % 3\n        result += new_key + str(mod_result)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(name: str):\n    reversed_s = name[::-1]\n    ascii_sum = sum([ord(c) for c in reversed_s])\n    shifted = ''.join([chr((ord(c) + ascii_sum) % 26 + 65) for c in reversed_s])\n    final_encoded = shifted[::-1]\n    return len(final_encoded)", "input": "'UUNET'", "output": "5", "imports": [], "original_snippet": "def f(name: str):\n    reversed_s = name[::-1]\n    ascii_sum = sum([ord(c) for c in reversed_s])\n    shifted = ''.join([chr((ord(c) + ascii_sum) % 26 + 65) for c in reversed_s])\n    final_encoded = shifted[::-1]\n    return len(final_encoded)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(text: str) -> str:\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    character_points = {c: i + 1 for (i, c) in enumerate(alphabet)}\n    deep_parse = [str(character_points[c]) for c in text if c in character_points]\n    reduced = [str(int(d) % 3) for d in deep_parse]\n    sorted_reduced = ''.join(sorted(reduced))\n    reversed_and_returned = sorted_reduced[::-1]\n    return reversed_and_returned", "input": "'devacademy'", "output": "'2211111110'", "imports": [], "original_snippet": "def f(text: str) -> str:\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    character_points = {c: i + 1 for (i, c) in enumerate(alphabet)}\n    deep_parse = [str(character_points[c]) for c in text if c in character_points]\n    reduced = [str(int(d) % 3) for d in deep_parse]\n    sorted_reduced = ''.join(sorted(reduced))\n    reversed_and_returned = sorted_reduced[::-1]\n    return reversed_and_returned", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(to_add: list, to_multiply: list):\n    transformed_to_add = [num + 10 for num in to_add]\n    transformed_to_multiply = [num ** 2 for num in to_multiply]\n    final_sum = sum(transformed_to_add) + sum(transformed_to_multiply)\n    return final_sum", "input": "[1,3,5], [2,4,6]", "output": "95", "imports": [], "original_snippet": "def f(to_add: list, to_multiply: list):\n    transformed_to_add = [num + 10 for num in to_add]\n    transformed_to_multiply = [num ** 2 for num in to_multiply]\n    final_sum = sum(transformed_to_add) + sum(transformed_to_multiply)\n    return final_sum", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(nums: List[int]) -> List[int]:\n    nums.sort()\n    mod_3 = {num: [] for num in range(1, max(nums) // 3 + 1)}\n    mod_5 = {num: [] for num in range(1, max(nums) // 5 + 1)}\n    for num in nums:\n        if num % 3 == 0:\n            modified_num = int(num ** 2) + 3\n            for (key, value) in mod_3.items():\n                if modified_num % key == 0:\n                    mod_3[key].append((modified_num, key))\n                    break\n        elif num % 5 == 0:\n            modified_num = int(num ** 0.5)\n            for (key, value) in mod_5.items():\n                if modified_num % key == 0:\n                    mod_5[key].append((modified_num, key))\n                    break\n    frequency = []\n    for d in (mod_3, mod_5):\n        freq = {key: len(set(value)) for (key, value) in d.items()}\n        frequency.append(freq)\n    return frequency", "input": "[1, 6, 2, 9, 3, 11, 4, 18, 5, 20, 24, 25, 15, 12, 30, 36, 33, 40, 45, 60]", "output": "[{1: 12, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0, 11: 0, 12: 0, 13: 0, 14: 0, 15: 0, 16: 0, 17: 0, 18: 0, 19: 0, 20: 0}, {1: 4, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0, 11: 0, 12: 0}]", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(nums: List[int]) -> List[int]:\n    nums.sort()\n    mod_3 = {num: [] for num in range(1, max(nums) // 3 + 1)}\n    mod_5 = {num: [] for num in range(1, max(nums) // 5 + 1)}\n    for num in nums:\n        if num % 3 == 0:\n            modified_num = int(num ** 2) + 3\n            for (key, value) in mod_3.items():\n                if modified_num % key == 0:\n                    mod_3[key].append((modified_num, key))\n                    break\n        elif num % 5 == 0:\n            modified_num = int(num ** 0.5)\n            for (key, value) in mod_5.items():\n                if modified_num % key == 0:\n                    mod_5[key].append((modified_num, key))\n                    break\n    frequency = []\n    for d in (mod_3, mod_5):\n        freq = {key: len(set(value)) for (key, value) in d.items()}\n        frequency.append(freq)\n    return frequency", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(n: int, a: int, s: int) -> tuple:\n    sequence = []\n    for i in range(n):\n        if a % 2 == 0:\n            sequence.insert(0, a)\n        else:\n            sequence.append(a)\n        a += 1\n    product = 1\n    for number in sequence:\n        product *= number\n    return (product, s)", "input": "3, 2, 15", "output": "(24, 15)", "imports": [], "original_snippet": "def f(n: int, a: int, s: int) -> tuple:\n    sequence = []\n    for i in range(n):\n        if a % 2 == 0:\n            sequence.insert(0, a)\n        else:\n            sequence.append(a)\n        a += 1\n    product = 1\n    for number in sequence:\n        product *= number\n    return (product, s)", "composite_functions": [], "_input_type": "tuple", "_output_type": "tuple"}
{"snippet": "def f(nested_num_list: list) -> int:\n    binary_list = [[[bin(x)[2:].zfill(8) for x in sublst] for sublst in lst] for lst in nested_num_list]\n    transformed_list = [[list(map(lambda x: x[3:] + x[:3], sublist)) for sublist in lst] for lst in binary_list]\n    joined_strs = [''.join(item) for sublist in transformed_list for item in sublist]\n    bit_reversed_strings = [s[4:][::-1] + s[:4] for s in joined_strs]\n    final_integers = list(map(lambda x: int(x, 2), bit_reversed_strings))\n    result_str = bin(sum(final_integers))[2:].zfill(16)\n    return result_str", "input": "[[[4, 5], [6, 7]], [[8, 9], [10, 11]]]", "output": "'0101110000001110'", "imports": [], "original_snippet": "def f(nested_num_list: list) -> int:\n    binary_list = [[[bin(x)[2:].zfill(8) for x in sublst] for sublst in lst] for lst in nested_num_list]\n    transformed_list = [[list(map(lambda x: x[3:] + x[:3], sublist)) for sublist in lst] for lst in binary_list]\n    joined_strs = [''.join(item) for sublist in transformed_list for item in sublist]\n    bit_reversed_strings = [s[4:][::-1] + s[:4] for s in joined_strs]\n    final_integers = list(map(lambda x: int(x, 2), bit_reversed_strings))\n    result_str = bin(sum(final_integers))[2:].zfill(16)\n    return result_str", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(lst: list):\n    sorted_lst = sorted(lst)\n    transformed_lst = []\n    for (i, num) in enumerate(sorted_lst):\n        if i % 3 == 0:\n            transformed_lst.append(num ** 2 - num + 1)\n        elif i % 3 == 1:\n            transformed_lst.append(num * (num + 1) // 2)\n        else:\n            transformed_lst.append(num * 2)\n    result_lst = [x for x in transformed_lst if x % 3 == 0]\n    s = sum(result_lst)\n    return s", "input": "[4, 6, 1, 5, 9, 2, 7]", "output": "45", "imports": [], "original_snippet": "def f(lst: list):\n    sorted_lst = sorted(lst)\n    transformed_lst = []\n    for (i, num) in enumerate(sorted_lst):\n        if i % 3 == 0:\n            transformed_lst.append(num ** 2 - num + 1)\n        elif i % 3 == 1:\n            transformed_lst.append(num * (num + 1) // 2)\n        else:\n            transformed_lst.append(num * 2)\n    result_lst = [x for x in transformed_lst if x % 3 == 0]\n    s = sum(result_lst)\n    return s", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(sequence: list):\n    from collections import Counter\n    even_odd_sequences = [element for element in sequence if (element % 2 == 0) != bool(sequence.index(element) % 2)]\n    all_nums = [num for num in sequence if num % 3 != 0 and num % 5 != 0]\n    prime_nums = [num for num in all_nums if all((num % i != 0 for i in range(2, num)))]\n    unique_prime_digits = sorted(set((str(prime) for prime in prime_nums)))\n    squared_prime_digits = sorted(set((str(prime ** 2) for prime in prime_nums)))\n    result = unique_prime_digits + squared_prime_digits\n    prime_count = Counter(result)\n    result = [c for (c, freq) in prime_count.items() if freq == 2]\n    return 'Yes' if any((c.isupper() for c in result)) else 'No'", "input": "[34, 15, 17, 53, 97]", "output": "'No'", "imports": ["from collections import Counter"], "original_snippet": "def f(sequence: list):\n    from collections import Counter\n    even_odd_sequences = [element for element in sequence if (element % 2 == 0) != bool(sequence.index(element) % 2)]\n    all_nums = [num for num in sequence if num % 3 != 0 and num % 5 != 0]\n    prime_nums = [num for num in all_nums if all((num % i != 0 for i in range(2, num)))]\n    unique_prime_digits = sorted(set((str(prime) for prime in prime_nums)))\n    squared_prime_digits = sorted(set((str(prime ** 2) for prime in prime_nums)))\n    result = unique_prime_digits + squared_prime_digits\n    prime_count = Counter(result)\n    result = [c for (c, freq) in prime_count.items() if freq == 2]\n    return 'Yes' if any((c.isupper() for c in result)) else 'No'", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(numbers: list):\n    import math\n    sorted_numbers = sorted(numbers)\n    transformed_values = []\n    for i in range(len(sorted_numbers)):\n        if i % 2 == 0:\n            result = math.sqrt(sorted_numbers[i])\n        else:\n            result = math.log(sorted_numbers[i], 2)\n        transformed_values.append(result)\n    max_result = max(transformed_values)\n    min_result = min(transformed_values)\n    return (max_result, min_result)", "input": "[4, 8, 9, 16, 25, 36]", "output": "(5.169925001442312, 2.0)", "imports": ["import math"], "original_snippet": "def f(numbers: list):\n    import math\n    sorted_numbers = sorted(numbers)\n    transformed_values = []\n    for i in range(len(sorted_numbers)):\n        if i % 2 == 0:\n            result = math.sqrt(sorted_numbers[i])\n        else:\n            result = math.log(sorted_numbers[i], 2)\n        transformed_values.append(result)\n    max_result = max(transformed_values)\n    min_result = min(transformed_values)\n    return (max_result, min_result)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "from math import sqrt, ceil\ndef f(sequence: str):\n    square_roots = [int(x) for x in sequence if x.isdigit()]\n    square_roots = [i for i in square_roots if sqrt(i).is_integer()]\n    a_list = []\n    for (i, char) in enumerate(sequence):\n        if char.islower():\n            a_list.append(chr(i + ord('a')))\n    return (a_list, len(square_roots))", "input": "'abcdefghijklmnopqrstuvwxyz12345'", "output": "(['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'], 2)", "imports": ["from math import sqrt, ceil"], "original_snippet": "from math import sqrt, ceil\ndef f(sequence: str):\n    square_roots = [int(x) for x in sequence if x.isdigit()]\n    square_roots = [i for i in square_roots if sqrt(i).is_integer()]\n    a_list = []\n    for (i, char) in enumerate(sequence):\n        if char.islower():\n            a_list.append(chr(i + ord('a')))\n    return (a_list, len(square_roots))", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(numbers: list):\n    sorted_numbers = sorted(numbers)\n    transformed_numbers = [abs(sorted_numbers[i] - sorted_numbers[i - 1]) ** 2 for i in range(2, len(sorted_numbers))]\n    filtered_numbers = [num for num in transformed_numbers if num % 5 == 0]\n    return sum(filtered_numbers)", "input": "[5, 10, 15, 20, 25]", "output": "75", "imports": [], "original_snippet": "def f(numbers: list):\n    sorted_numbers = sorted(numbers)\n    transformed_numbers = [abs(sorted_numbers[i] - sorted_numbers[i - 1]) ** 2 for i in range(2, len(sorted_numbers))]\n    filtered_numbers = [num for num in transformed_numbers if num % 5 == 0]\n    return sum(filtered_numbers)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(sequence: list, pivot_index: int) -> int:\n    pivot = sequence[pivot_index]\n    left_sum = sum(sequence[:pivot_index])\n    right_sum = sum(sequence[pivot_index + 1:])\n    adjusted = [n - pivot for n in sequence if n != pivot]\n    product = 1\n    for adj in adjusted:\n        product *= adj\n    count = 0\n    while product > 0:\n        product //= 10\n        count += 1\n    balance_factor = count % 2\n    result = (pivot + left_sum + balance_factor) % 9\n    return result", "input": "[42, -56, 48, -99], 1", "output": "4", "imports": [], "original_snippet": "def f(sequence: list, pivot_index: int) -> int:\n    pivot = sequence[pivot_index]\n    left_sum = sum(sequence[:pivot_index])\n    right_sum = sum(sequence[pivot_index + 1:])\n    adjusted = [n - pivot for n in sequence if n != pivot]\n    product = 1\n    for adj in adjusted:\n        product *= adj\n    count = 0\n    while product > 0:\n        product //= 10\n        count += 1\n    balance_factor = count % 2\n    result = (pivot + left_sum + balance_factor) % 9\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "from math import factorial\ndef f(numbers: list):\n    final_sum = 0\n    for i in range(0, len(numbers), 2):\n        cumulative_sum = sum(numbers[i:i + 2])\n        final_sum += factorial(cumulative_sum)\n    return final_sum", "input": "[1, 2, 3, 4, 5, 6]", "output": "39921846", "imports": ["from math import factorial"], "original_snippet": "from math import factorial\ndef f(numbers: list):\n    final_sum = 0\n    for i in range(0, len(numbers), 2):\n        cumulative_sum = sum(numbers[i:i + 2])\n        final_sum += factorial(cumulative_sum)\n    return final_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(dictionaries: list[dict]) -> list:\n    sorted_dicts = sorted(dictionaries, key=lambda x: (x['age'], x['name']), reverse=True)\n    result = [entry['name'] for entry in sorted_dicts]\n    return result", "input": "[{'name': 'John', 'age': 25}, {'name': 'Jane', 'age': 23}, {'name': 'Jim', 'age': 25}]", "output": "['John', 'Jim', 'Jane']", "imports": [], "original_snippet": "def f(dictionaries: list[dict]) -> list:\n    sorted_dicts = sorted(dictionaries, key=lambda x: (x['age'], x['name']), reverse=True)\n    result = [entry['name'] for entry in sorted_dicts]\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_string: str) -> str:\n    input_length = len(input_string)\n    max_sentence_length = 8\n    sentence = [input_string]\n    sentence_length = input_length\n    for i in range(max_sentence_length - input_length):\n        next_word_length = i + 2\n        if next_word_length >= max_sentence_length:\n            next_word = '...'\n        else:\n            possible_chars = [chr(ord(input_string[i % input_length]) + (next_word_length - input_length + i)) for i in range(next_word_length)]\n            next_word = ''.join(possible_chars)\n        sentence.append(next_word)\n        sentence_length += next_word_length\n    return ' '.join(sentence)", "input": "'foo'", "output": "'foo eo fpq gqrj hrsku istlvw'", "imports": [], "original_snippet": "def f(input_string: str) -> str:\n    input_length = len(input_string)\n    max_sentence_length = 8\n    sentence = [input_string]\n    sentence_length = input_length\n    for i in range(max_sentence_length - input_length):\n        next_word_length = i + 2\n        if next_word_length >= max_sentence_length:\n            next_word = '...'\n        else:\n            possible_chars = [chr(ord(input_string[i % input_length]) + (next_word_length - input_length + i)) for i in range(next_word_length)]\n            next_word = ''.join(possible_chars)\n        sentence.append(next_word)\n        sentence_length += next_word_length\n    return ' '.join(sentence)", "composite_functions": [], "_input_type": "str", "_output_type": "str"}
{"snippet": "def f(sequence: list):\n    filtered_list = [num for num in sequence if num % 2 == 0 and num > 0]\n    processed_list = [num ** 2 * 2 for num in filtered_list if num % 3 == 0 and num <= 20]\n    sorted_list = sorted(processed_list, reverse=True)\n    mod_values = [str(num % idx) for (idx, num) in enumerate(sorted_list, 1)]\n    result = '(((' + ','.join(mod_values) + '))'\n    return result", "input": "[10, 4, 2, 8, 6, 20, 12, 14, 16, 18, 24, 26, 38, 52, 68, 100, 7, 3, 77]", "output": "'(((0,0,0))'", "imports": [], "original_snippet": "def f(sequence: list):\n    filtered_list = [num for num in sequence if num % 2 == 0 and num > 0]\n    processed_list = [num ** 2 * 2 for num in filtered_list if num % 3 == 0 and num <= 20]\n    sorted_list = sorted(processed_list, reverse=True)\n    mod_values = [str(num % idx) for (idx, num) in enumerate(sorted_list, 1)]\n    result = '(((' + ','.join(mod_values) + '))'\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(elements, x):\n    count = 0\n    for i in range(len(elements)):\n        for j in range(i + 1, len(elements)):\n            for k in range(j + 1, len(elements)):\n                possible_sets = [elements[i], elements[j], elements[k]]\n                if max(possible_sets) % 2 != 0 and sum(possible_sets) > x:\n                    count += 1\n    return count", "input": "[3, 5, 7, 9], 6", "output": "4", "imports": [], "original_snippet": "def f(elements, x):\n    count = 0\n    for i in range(len(elements)):\n        for j in range(i + 1, len(elements)):\n            for k in range(j + 1, len(elements)):\n                possible_sets = [elements[i], elements[j], elements[k]]\n                if max(possible_sets) % 2 != 0 and sum(possible_sets) > x:\n                    count += 1\n    return count", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "from collections import Counter\ndef f(strings: list):\n    string_counts = [Counter(string) for string in strings]\n    common_diffs = []\n    for i in range(len(strings) - 1):\n        for j in range(i + 1, len(strings)):\n            diff = {key: string_counts[i][key] - string_counts[j][key] for key in string_counts[i] if key not in string_counts[j]}\n            diff.update({key: string_counts[j][key] for key in string_counts[j] if key not in string_counts[i]})\n            common_diffs.append(diff)\n    diff_counts = Counter({key: 0 for diff in common_diffs for key in diff})\n    for diff in common_diffs:\n        diff_counts.update(diff)\n    return max(diff_counts, key=diff_counts.get)", "input": "[\"hello\", \"world\", \"anotherworld\", \"different\"]", "output": "'f'", "imports": ["from collections import Counter"], "original_snippet": "from collections import Counter\ndef f(strings: list):\n    string_counts = [Counter(string) for string in strings]\n    common_diffs = []\n    for i in range(len(strings) - 1):\n        for j in range(i + 1, len(strings)):\n            diff = {key: string_counts[i][key] - string_counts[j][key] for key in string_counts[i] if key not in string_counts[j]}\n            diff.update({key: string_counts[j][key] for key in string_counts[j] if key not in string_counts[i]})\n            common_diffs.append(diff)\n    diff_counts = Counter({key: 0 for diff in common_diffs for key in diff})\n    for diff in common_diffs:\n        diff_counts.update(diff)\n    return max(diff_counts, key=diff_counts.get)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(char_list: list, token_list: list):\n    char_dict = {}\n    for (char, index) in zip(char_list, token_list):\n        key = ''.join((char.lower() for char in char_dict.keys()))\n        for token in token_list:\n            test_token = ''.join((c for c in token.lower() if key.count(c) < 2))\n            if test_token not in char_dict:\n                break\n        if test_token:\n            char_dict[test_token] = index\n    remaining_tokens = [token for token in char_dict.values() if char_list.count(''.join(token.lower()).strip()) <= 2]\n    remaining_tokens.sort()\n    remaining_tokens_count = len(remaining_tokens)\n    return remaining_tokens_count", "input": "'abcde', ['a', 'b', 'c', 'd', 'e']", "output": "5", "imports": [], "original_snippet": "def f(char_list: list, token_list: list):\n    char_dict = {}\n    for (char, index) in zip(char_list, token_list):\n        key = ''.join((char.lower() for char in char_dict.keys()))\n        for token in token_list:\n            test_token = ''.join((c for c in token.lower() if key.count(c) < 2))\n            if test_token not in char_dict:\n                break\n        if test_token:\n            char_dict[test_token] = index\n    remaining_tokens = [token for token in char_dict.values() if char_list.count(''.join(token.lower()).strip()) <= 2]\n    remaining_tokens.sort()\n    remaining_tokens_count = len(remaining_tokens)\n    return remaining_tokens_count", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "def f(data_list: list):\n    from collections import deque\n    stack = [s for s in data_list if isinstance(s, str)]\n    queue = [q for q in data_list if isinstance(q, int)]\n    final_list = []\n    for s in stack:\n        queue_splice = deque(queue)\n        index_chars = [index for (index, char) in enumerate(s) if char.isdigit()]\n        extracted_numbers = [q for (index, q) in enumerate(queue_splice, 0) if index in index_chars]\n        extracted_numbers_r = [sum(([1, 2, 3] * 3)[:q % 3]) if q & 1 else 0 for q in extracted_numbers]\n        final_list.extend(list(s) + extracted_numbers_r)\n    return len(final_list)", "input": "['321', 'f4g', 2, 5, (1, 2), {'lo': 3}]", "output": "9", "imports": ["from collections import deque"], "original_snippet": "def f(data_list: list):\n    from collections import deque\n    stack = [s for s in data_list if isinstance(s, str)]\n    queue = [q for q in data_list if isinstance(q, int)]\n    final_list = []\n    for s in stack:\n        queue_splice = deque(queue)\n        index_chars = [index for (index, char) in enumerate(s) if char.isdigit()]\n        extracted_numbers = [q for (index, q) in enumerate(queue_splice, 0) if index in index_chars]\n        extracted_numbers_r = [sum(([1, 2, 3] * 3)[:q % 3]) if q & 1 else 0 for q in extracted_numbers]\n        final_list.extend(list(s) + extracted_numbers_r)\n    return len(final_list)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(lst: List[int]) -> str:\n    n = len(lst)\n    lst.sort()\n    avg = sum(lst) // n\n    min_elem = lst[0]\n    max_elem = lst[n - 1]\n    difference = max_elem - min_elem\n    stretch = 2\n    impossible = []\n    for k in range(n):\n        for l in range(n):\n            for m in range(n):\n                for p in range(n):\n                    t = lst[k] * lst[l] + lst[m] * lst[p]\n                    if t % difference == 0:\n                        impossible.insert(int(t / difference), lst[l])\n                        break\n                if t in impossible:\n                    impossible.remove(t)\n    model = [str(i) for i in range(n)]\n    x = 5321\n    for index in range(n):\n        if index < avg:\n            model.insert(index, str(n - index))\n            x += lst[index] * lst[index]\n        else:\n            model.insert(index, str(avg - index % 2))\n            x -= lst[index]\n    return str(x)[2] + ''.join(model)", "input": "[3, 8, 4, 2, 9, 1, 5, 6]", "output": "'28765434301234567'", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(lst: List[int]) -> str:\n    n = len(lst)\n    lst.sort()\n    avg = sum(lst) // n\n    min_elem = lst[0]\n    max_elem = lst[n - 1]\n    difference = max_elem - min_elem\n    stretch = 2\n    impossible = []\n    for k in range(n):\n        for l in range(n):\n            for m in range(n):\n                for p in range(n):\n                    t = lst[k] * lst[l] + lst[m] * lst[p]\n                    if t % difference == 0:\n                        impossible.insert(int(t / difference), lst[l])\n                        break\n                if t in impossible:\n                    impossible.remove(t)\n    model = [str(i) for i in range(n)]\n    x = 5321\n    for index in range(n):\n        if index < avg:\n            model.insert(index, str(n - index))\n            x += lst[index] * lst[index]\n        else:\n            model.insert(index, str(avg - index % 2))\n            x -= lst[index]\n    return str(x)[2] + ''.join(model)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(numbers: list[int]) -> int:\n    numbers = [x ** 3 for x in numbers]\n    numbers.sort()\n    lennum = len(numbers)\n    half_len = lennum // 2\n    maxi = 0\n    for i in range(half_len):\n        depth = half_len - i\n        current_sum = sum(numbers[i:i + depth])\n        maxi = max(maxi, current_sum)\n    return maxi * (maxi + 1) // 2 - maxi + 10", "input": "[2, 1, 3, 4, 10, 7]", "output": "640", "imports": [], "original_snippet": "def f(numbers: list[int]) -> int:\n    numbers = [x ** 3 for x in numbers]\n    numbers.sort()\n    lennum = len(numbers)\n    half_len = lennum // 2\n    maxi = 0\n    for i in range(half_len):\n        depth = half_len - i\n        current_sum = sum(numbers[i:i + depth])\n        maxi = max(maxi, current_sum)\n    return maxi * (maxi + 1) // 2 - maxi + 10", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(s: str) -> tuple:\n    s_rev = s[::-1]\n    count = sum((c not in 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789' for c in s_rev))\n    result = ''.join((chr(((ord(c) ^ 85) + count) % 256) for c in s_rev))\n    return (result, count)", "input": "\"abc\"", "output": "('674', 0)", "imports": [], "original_snippet": "def f(s: str) -> tuple:\n    s_rev = s[::-1]\n    count = sum((c not in 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789' for c in s_rev))\n    result = ''.join((chr(((ord(c) ^ 85) + count) % 256) for c in s_rev))\n    return (result, count)", "composite_functions": [], "_input_type": "str", "_output_type": "tuple"}
{"snippet": "def f(matrix: list):\n    max_abs_val = float('-inf')\n    max_abs_val_indices = None\n    for (i, row) in enumerate(matrix):\n        for (j, num) in enumerate(row):\n            if max_abs_val < abs(num):\n                max_abs_val = abs(num)\n                max_abs_val_indices = (i, j)\n    (max_row, max_col) = max_abs_val_indices\n    matrix[max_row][max_col] -= abs(max_abs_val)\n    min_element = float('inf')\n    for row in matrix:\n        min_in_row = min(row)\n        if min_element > min_in_row:\n            min_element = min_in_row\n    return min_element", "input": "[[3, 8], [9, 2], [4, 1]]", "output": "0", "imports": [], "original_snippet": "def f(matrix: list):\n    max_abs_val = float('-inf')\n    max_abs_val_indices = None\n    for (i, row) in enumerate(matrix):\n        for (j, num) in enumerate(row):\n            if max_abs_val < abs(num):\n                max_abs_val = abs(num)\n                max_abs_val_indices = (i, j)\n    (max_row, max_col) = max_abs_val_indices\n    matrix[max_row][max_col] -= abs(max_abs_val)\n    min_element = float('inf')\n    for row in matrix:\n        min_in_row = min(row)\n        if min_element > min_in_row:\n            min_element = min_in_row\n    return min_element", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(target: int) -> str:\n    prefix_sums = [0] * (target + 1)\n    for i in range(1, target + 1):\n        prefix_sums[i] = prefix_sums[i - 1] + i\n    subarray_counts = {0: 1}\n    num = 0\n    res = '[]'\n    current_sum = 0\n    for i in range(target):\n        num += 1\n        if prefix_sums[target] - prefix_sums[i] >= target:\n            res = str(list(range(1, num + 1)))\n            break\n        for k in subarray_counts.keys():\n            new_sum = k + num\n            if new_sum >= target:\n                subarray_counts[target] = subarray_counts.get(target, 0) + subarray_counts[k]\n        current_sum = 0\n        subarray_counts = {}\n        for j in range(num):\n            current_sum += num - j\n            if current_sum >= target:\n                res = str(list(range(1, num + 1)))\n        if res != '[]':\n            break\n    return res", "input": "4", "output": "'[1]'", "imports": [], "original_snippet": "def f(target: int) -> str:\n    prefix_sums = [0] * (target + 1)\n    for i in range(1, target + 1):\n        prefix_sums[i] = prefix_sums[i - 1] + i\n    subarray_counts = {0: 1}\n    num = 0\n    res = '[]'\n    current_sum = 0\n    for i in range(target):\n        num += 1\n        if prefix_sums[target] - prefix_sums[i] >= target:\n            res = str(list(range(1, num + 1)))\n            break\n        for k in subarray_counts.keys():\n            new_sum = k + num\n            if new_sum >= target:\n                subarray_counts[target] = subarray_counts.get(target, 0) + subarray_counts[k]\n        current_sum = 0\n        subarray_counts = {}\n        for j in range(num):\n            current_sum += num - j\n            if current_sum >= target:\n                res = str(list(range(1, num + 1)))\n        if res != '[]':\n            break\n    return res", "composite_functions": [], "_input_type": "int", "_output_type": "str"}
{"snippet": "def f(places: list):\n    categorized_cities_sum = {}\n    for place in places:\n        (city, state) = place.split(':')\n        initial_letter = city[0].lower()\n        ascii_sum = sum((ord(char) * (idx + 1) for (idx, char) in enumerate(city)))\n        categorized_cities_sum[initial_letter] = categorized_cities_sum.get(initial_letter, 0) + ascii_sum\n    result = [value for (_, value) in categorized_cities_sum.items()]\n    result.sort(reverse=True)\n    return result", "input": "['New York:NY', 'San Francisco:CA', 'Los Angeles:CA', 'Austin:TX', 'Boston:MA', 'Miami:FL']", "output": "[9165, 6532, 3530, 2312, 2293, 1539]", "imports": [], "original_snippet": "def f(places: list):\n    categorized_cities_sum = {}\n    for place in places:\n        (city, state) = place.split(':')\n        initial_letter = city[0].lower()\n        ascii_sum = sum((ord(char) * (idx + 1) for (idx, char) in enumerate(city)))\n        categorized_cities_sum[initial_letter] = categorized_cities_sum.get(initial_letter, 0) + ascii_sum\n    result = [value for (_, value) in categorized_cities_sum.items()]\n    result.sort(reverse=True)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(numbers: list):\n    cumulative_sums = []\n    current_cumulative_sum = 0\n    odd_numbers_seen = []\n    for num in numbers:\n        if num % 2 != 0:\n            odd_numbers_seen.append(num)\n        current_cumulative_sum += sum(odd_numbers_seen)\n        cumulative_sums.append(current_cumulative_sum)\n    return min(cumulative_sums)", "input": "[7, 3, 4, 20, 5, -1, 4]", "output": "7", "imports": [], "original_snippet": "def f(numbers: list):\n    cumulative_sums = []\n    current_cumulative_sum = 0\n    odd_numbers_seen = []\n    for num in numbers:\n        if num % 2 != 0:\n            odd_numbers_seen.append(num)\n        current_cumulative_sum += sum(odd_numbers_seen)\n        cumulative_sums.append(current_cumulative_sum)\n    return min(cumulative_sums)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(numbers: List[int]) -> List[int]:\n    numbers.sort()\n    transformed_numbers = [num ** 3 + 2 * num if num % 3 == 0 else num for num in numbers]\n    reversed_numbers = transformed_numbers[::-1]\n    final_numbers = [num - 10 for num in reversed_numbers] if len(reversed_numbers) > 2 else [num for num in reversed_numbers]\n    return final_numbers", "input": "[1, 2, 3, 4, 5]", "output": "[-5, -6, 23, -8, -9]", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(numbers: List[int]) -> List[int]:\n    numbers.sort()\n    transformed_numbers = [num ** 3 + 2 * num if num % 3 == 0 else num for num in numbers]\n    reversed_numbers = transformed_numbers[::-1]\n    final_numbers = [num - 10 for num in reversed_numbers] if len(reversed_numbers) > 2 else [num for num in reversed_numbers]\n    return final_numbers", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import List\ndef f(strings_list: List[str]):\n    cleaned_list = [s.strip() for s in strings_list]\n    combined_string = ''.join(cleaned_list)\n    transformed_utf8_values = [ord(c) % 26 for c in combined_string]\n    transformed_list = [t * (i + 1) for (i, t) in enumerate(transformed_utf8_values)]\n    return [v % 5 for v in transformed_list]", "input": "[' Hello ', ' 1 2 3 ', ' W o r l d ']", "output": "[0, 1, 2, 1, 0, 3, 2, 2, 4, 0, 4, 2, 1, 4, 0, 1, 3, 3, 3]", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(strings_list: List[str]):\n    cleaned_list = [s.strip() for s in strings_list]\n    combined_string = ''.join(cleaned_list)\n    transformed_utf8_values = [ord(c) % 26 for c in combined_string]\n    transformed_list = [t * (i + 1) for (i, t) in enumerate(transformed_utf8_values)]\n    return [v % 5 for v in transformed_list]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(input_str: str, multiplier: int):\n    ascii_values = [ord(char) for char in input_str]\n    transformed_values = [value * multiplier + value % multiplier for value in ascii_values]\n    chunk_size = 3 if len(input_str) % 2 == 0 else 4\n    chunks = [transformed_values[i:i + chunk_size] for i in range(0, len(transformed_values), chunk_size)]\n    sorted_chunks = sorted(chunks, key=lambda x: x[-1])\n    sorted_values = [value for chunk in sorted_chunks for value in chunk]\n    delimiters = ['-' if i % 2 == 0 else '|' for i in range(len(sorted_values) - 1)]\n    result_str = ''.join([str(value) + delimiter for (value, delimiter) in zip(sorted_values, delimiters)])\n    return result_str", "input": "\"example\", 3", "output": "'337-324|305-305|360-292|'", "imports": [], "original_snippet": "def f(input_str: str, multiplier: int):\n    ascii_values = [ord(char) for char in input_str]\n    transformed_values = [value * multiplier + value % multiplier for value in ascii_values]\n    chunk_size = 3 if len(input_str) % 2 == 0 else 4\n    chunks = [transformed_values[i:i + chunk_size] for i in range(0, len(transformed_values), chunk_size)]\n    sorted_chunks = sorted(chunks, key=lambda x: x[-1])\n    sorted_values = [value for chunk in sorted_chunks for value in chunk]\n    delimiters = ['-' if i % 2 == 0 else '|' for i in range(len(sorted_values) - 1)]\n    result_str = ''.join([str(value) + delimiter for (value, delimiter) in zip(sorted_values, delimiters)])\n    return result_str", "composite_functions": [], "_input_type": "tuple", "_output_type": "str"}
{"snippet": "import numpy as np\ndef f(numbers: list):\n    positive_numbers = [num for num in numbers if num >= 0]\n    if not positive_numbers:\n        return 0\n    numbers_multiplied = [num * i for (i, num) in enumerate(positive_numbers)]\n    sorted_multiplied = sorted(numbers_multiplied, reverse=True)\n    sum_numbers = np.sum(sorted_multiplied)\n    return int(sum_numbers * len(numbers))", "input": "[1, 2, -3, 4, 5, -6, 7, 8, -9]", "output": "837", "imports": ["import numpy as np"], "original_snippet": "import numpy as np\ndef f(numbers: list):\n    positive_numbers = [num for num in numbers if num >= 0]\n    if not positive_numbers:\n        return 0\n    numbers_multiplied = [num * i for (i, num) in enumerate(positive_numbers)]\n    sorted_multiplied = sorted(numbers_multiplied, reverse=True)\n    sum_numbers = np.sum(sorted_multiplied)\n    return int(sum_numbers * len(numbers))", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(string: str) -> int:\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    string = string.lower()\n    num_list = [alphabet.index(char) + 1 for char in string if char in alphabet]\n    filtered_list = num_list[::3]\n    squared = [num ** 2 for num in filtered_list]\n    squared.sort()\n    return len(squared)", "input": "'Hello Python!'", "output": "4", "imports": [], "original_snippet": "def f(string: str) -> int:\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    string = string.lower()\n    num_list = [alphabet.index(char) + 1 for char in string if char in alphabet]\n    filtered_list = num_list[::3]\n    squared = [num ** 2 for num in filtered_list]\n    squared.sort()\n    return len(squared)", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(user_input: str):\n    transformed_string = ''.join(['*' if char.isalpha() else char for char in user_input])\n    set_of_string = set(transformed_string)\n    char_count = {char: transformed_string.count(char) for char in set_of_string}\n    return sum((value % 2 == 0 for value in char_count.values()))", "input": "\"hello world\"", "output": "1", "imports": [], "original_snippet": "def f(user_input: str):\n    transformed_string = ''.join(['*' if char.isalpha() else char for char in user_input])\n    set_of_string = set(transformed_string)\n    char_count = {char: transformed_string.count(char) for char in set_of_string}\n    return sum((value % 2 == 0 for value in char_count.values()))", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "from typing import Dict\ndef f(input_str: str) -> Dict[int, list[str]]:\n    char_list = list(input_str)\n    substr_list = [char_list[i:i + 2] for i in range(0, len(char_list), 2) if i + 2 <= len(char_list)]\n    substr_list.sort()\n    substr_set = set([''.join(substr) for substr in substr_list])\n    results_dict = {i: sorted(substr_set) for i in range(len(substr_list) // 2 + 1)}\n    return results_dict", "input": "'abcdedrtyuio'", "output": "{0: ['ab', 'cd', 'ed', 'io', 'rt', 'yu'], 1: ['ab', 'cd', 'ed', 'io', 'rt', 'yu'], 2: ['ab', 'cd', 'ed', 'io', 'rt', 'yu'], 3: ['ab', 'cd', 'ed', 'io', 'rt', 'yu']}", "imports": ["from typing import Dict"], "original_snippet": "from typing import Dict\ndef f(input_str: str) -> Dict[int, list[str]]:\n    char_list = list(input_str)\n    substr_list = [char_list[i:i + 2] for i in range(0, len(char_list), 2) if i + 2 <= len(char_list)]\n    substr_list.sort()\n    substr_set = set([''.join(substr) for substr in substr_list])\n    results_dict = {i: sorted(substr_set) for i in range(len(substr_list) // 2 + 1)}\n    return results_dict", "composite_functions": [], "_input_type": "str", "_output_type": "dict"}
{"snippet": "def f(s: str):\n    restricted_chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'\n    trimmed_s = ''.join([c for c in s if c not in restricted_chars])\n    transformed_values = [((ord(c) - 97) * (i + 1) ^ 2 ** i) % 256 for (i, c) in enumerate(trimmed_s)]\n    sorted_values = sorted(transformed_values)\n    final_result = sum(sorted_values)\n    return final_result", "input": "'The quick brown fox jumps over the lazy dog'", "output": "4703", "imports": [], "original_snippet": "def f(s: str):\n    restricted_chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'\n    trimmed_s = ''.join([c for c in s if c not in restricted_chars])\n    transformed_values = [((ord(c) - 97) * (i + 1) ^ 2 ** i) % 256 for (i, c) in enumerate(trimmed_s)]\n    sorted_values = sorted(transformed_values)\n    final_result = sum(sorted_values)\n    return final_result", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(word: str):\n    if not word:\n        return 0\n    is_lowercase = word == word.lower()\n    if is_lowercase:\n        transformation = lambda c: ord(c) + 1\n    else:\n        transformation = lambda c: ord(c) - 1\n    hashed_value = 0\n    for (i, ch) in enumerate(word):\n        new_value = transformation(ch)\n        hashed_value += new_value * i + i * (i + 1) / 2\n    return hashed_value", "input": "'ab'", "output": "100.0", "imports": [], "original_snippet": "def f(word: str):\n    if not word:\n        return 0\n    is_lowercase = word == word.lower()\n    if is_lowercase:\n        transformation = lambda c: ord(c) + 1\n    else:\n        transformation = lambda c: ord(c) - 1\n    hashed_value = 0\n    for (i, ch) in enumerate(word):\n        new_value = transformation(ch)\n        hashed_value += new_value * i + i * (i + 1) / 2\n    return hashed_value", "composite_functions": [], "_input_type": "str", "_output_type": "float"}
{"snippet": "def f(strings: list):\n    from collections import defaultdict\n    frequencies = defaultdict(int)\n    for string in strings:\n        digit_count = sum((c.isdigit() for c in string))\n        frequencies[digit_count] += 1\n    sorted_frequencies = sorted(frequencies.items(), key=lambda x: (x[1], x[0]), reverse=True)\n    reordered_strings = []\n    max_occurance = None\n    for (num, count) in sorted_frequencies:\n        if max_occurance is None or count >= max_occurance:\n            reordered_strings.extend((s for s in strings if sum((c.isdigit() for c in s)) == num))\n            max_occurance = count\n        else:\n            break\n    ordered_list = reordered_strings[::-1]\n    output_string = ''.join(ordered_list)\n    return output_string", "input": "['1apple', '2banana', 'abc3', '34cake', 'no?PID']", "output": "'abc32banana1apple'", "imports": ["from collections import defaultdict"], "original_snippet": "def f(strings: list):\n    from collections import defaultdict\n    frequencies = defaultdict(int)\n    for string in strings:\n        digit_count = sum((c.isdigit() for c in string))\n        frequencies[digit_count] += 1\n    sorted_frequencies = sorted(frequencies.items(), key=lambda x: (x[1], x[0]), reverse=True)\n    reordered_strings = []\n    max_occurance = None\n    for (num, count) in sorted_frequencies:\n        if max_occurance is None or count >= max_occurance:\n            reordered_strings.extend((s for s in strings if sum((c.isdigit() for c in s)) == num))\n            max_occurance = count\n        else:\n            break\n    ordered_list = reordered_strings[::-1]\n    output_string = ''.join(ordered_list)\n    return output_string", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(lst: list):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    lst_filtered = [i for i in lst if len(i) == 3]\n    transformed = []\n    for (i, word) in enumerate(lst_filtered):\n        transformed.append(str(ord(word[1]) - ord(word[0])) + str(ord(word[2]) - ord(word[1])))\n    binary_lst = [int(i, 16) for i in transformed]\n    final_lst = [str(bin(i)[2:][::-1]) for i in binary_lst]\n    return ''.join(final_lst)", "input": "[\"aaa\", \"abc\", \"abd\", \"abe\", \"abb\", \"aac\"]", "output": "'01000101001110010000101'", "imports": [], "original_snippet": "def f(lst: list):\n    alphabet = 'abcdefghijklmnopqrstuvwxyz'\n    lst_filtered = [i for i in lst if len(i) == 3]\n    transformed = []\n    for (i, word) in enumerate(lst_filtered):\n        transformed.append(str(ord(word[1]) - ord(word[0])) + str(ord(word[2]) - ord(word[1])))\n    binary_lst = [int(i, 16) for i in transformed]\n    final_lst = [str(bin(i)[2:][::-1]) for i in binary_lst]\n    return ''.join(final_lst)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "from typing import List, Tuple\ndef f(input_list: List[Tuple[str, int, int]]):\n    aged_mature = []\n    mid_age = []\n    young_adults = []\n    for (name, age, full_name_length) in input_list:\n        if age > 30:\n            aged_mature.append(full_name_length ** 2)\n        else:\n            mid_age.append(full_name_length + 10 if full_name_length < age else full_name_length)\n    for (name, age, full_name_length) in input_list:\n        if age <= 20:\n            young_adults.append(full_name_length * age if age < 10 else full_name_length - age)\n    groups = {'aged_mature': sum(aged_mature) if aged_mature else None, 'mid_age': tuple(mid_age), 'young_adults': tuple(sorted(young_adults, reverse=True))}\n    return groups", "input": "[('John', 28, 5), ('Geta', 20, 4), ('Albin', 27, 5), ('Embe', 21, 4), ('Will', 17, 4)]", "output": "{'aged_mature': None, 'mid_age': (15, 14, 15, 14, 14), 'young_adults': (-13, -16)}", "imports": ["from typing import List, Tuple"], "original_snippet": "from typing import List, Tuple\ndef f(input_list: List[Tuple[str, int, int]]):\n    aged_mature = []\n    mid_age = []\n    young_adults = []\n    for (name, age, full_name_length) in input_list:\n        if age > 30:\n            aged_mature.append(full_name_length ** 2)\n        else:\n            mid_age.append(full_name_length + 10 if full_name_length < age else full_name_length)\n    for (name, age, full_name_length) in input_list:\n        if age <= 20:\n            young_adults.append(full_name_length * age if age < 10 else full_name_length - age)\n    groups = {'aged_mature': sum(aged_mature) if aged_mature else None, 'mid_age': tuple(mid_age), 'young_adults': tuple(sorted(young_adults, reverse=True))}\n    return groups", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(arr: list):\n    flattened = [item for sublist in arr for item in sublist]\n    (unique, freqs) = ([], [])\n    for num in flattened:\n        if num not in unique:\n            unique.append(num)\n            freqs.append(flattened.count(num))\n    freq_map = {key: val for (key, val) in zip(unique, freqs)}\n    transformed_freqs = []\n    for (key, val) in freq_map.items():\n        if key % 2 == 0:\n            transformed_freqs.append(val * 2)\n        else:\n            transformed_freqs.append(val + 1)\n    return transformed_freqs", "input": "[[1, 2, 3], [3, 4, 5], [5, 6, 7]]", "output": "[2, 2, 3, 2, 3, 2, 2]", "imports": [], "original_snippet": "def f(arr: list):\n    flattened = [item for sublist in arr for item in sublist]\n    (unique, freqs) = ([], [])\n    for num in flattened:\n        if num not in unique:\n            unique.append(num)\n            freqs.append(flattened.count(num))\n    freq_map = {key: val for (key, val) in zip(unique, freqs)}\n    transformed_freqs = []\n    for (key, val) in freq_map.items():\n        if key % 2 == 0:\n            transformed_freqs.append(val * 2)\n        else:\n            transformed_freqs.append(val + 1)\n    return transformed_freqs", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(s: str) -> int:\n    max_length = 0\n    current_length = 0\n    current_char = ''\n    for char in s:\n        if char == current_char:\n            current_length += 1\n        else:\n            current_char = char\n            current_length = 1\n        max_length = max(max_length, current_length)\n    return max_length", "input": "'abccc'", "output": "3", "imports": [], "original_snippet": "def f(s: str) -> int:\n    max_length = 0\n    current_length = 0\n    current_char = ''\n    for char in s:\n        if char == current_char:\n            current_length += 1\n        else:\n            current_char = char\n            current_length = 1\n        max_length = max(max_length, current_length)\n    return max_length", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(matrix: list):\n    transposed_matrix = list(map(list, zip(*matrix)))\n    sums_and_products = [{'even_sum': sum((num for num in sublist if num % 2 == 0)), 'odd_product': 1} for sublist in transposed_matrix]\n    min_value = float('inf')\n    max_value = float('-inf')\n    for data in sums_and_products:\n        if data['even_sum'] not in (0, None):\n            min_value = min(min_value, data['even_sum'])\n            max_value = max(max_value, data['even_sum'])\n        if 'odd_product' in data and data['odd_product'] not in (0, 1):\n            min_value = min(min_value, data['odd_product'])\n            max_value = max(max_value, data['odd_product'])\n    return (min_value, max_value)", "input": "[[1, 2, 3], [4, 5, 6], [7, 8, 9], [10, 11, 12]]", "output": "(10, 18)", "imports": [], "original_snippet": "def f(matrix: list):\n    transposed_matrix = list(map(list, zip(*matrix)))\n    sums_and_products = [{'even_sum': sum((num for num in sublist if num % 2 == 0)), 'odd_product': 1} for sublist in transposed_matrix]\n    min_value = float('inf')\n    max_value = float('-inf')\n    for data in sums_and_products:\n        if data['even_sum'] not in (0, None):\n            min_value = min(min_value, data['even_sum'])\n            max_value = max(max_value, data['even_sum'])\n        if 'odd_product' in data and data['odd_product'] not in (0, 1):\n            min_value = min(min_value, data['odd_product'])\n            max_value = max(max_value, data['odd_product'])\n    return (min_value, max_value)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "from collections import Counter\ndef f(strings: list):\n    char_freqs = Counter((char for string in strings for char in string))\n    return {char: freq for (char, freq) in char_freqs.items() if freq > 1}", "input": "[\"hello\", \"world\", \"test\"]", "output": "{'e': 2, 'l': 3, 'o': 2, 't': 2}", "imports": ["from collections import Counter"], "original_snippet": "from collections import Counter\ndef f(strings: list):\n    char_freqs = Counter((char for string in strings for char in string))\n    return {char: freq for (char, freq) in char_freqs.items() if freq > 1}", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(input_list: list, threshold: int=5, filter_multiple: int=3):\n    filtered_list = [n for n in input_list if n % filter_multiple != 0]\n    sorted_list = sorted(filtered_list, reverse=True)\n    grouped_list = [(sorted_list[i], sorted_list[i + 1]) for i in range(0, len(sorted_list) - 1, 2)]\n    merged_list = [m for pairs in grouped_list for m in pairs]\n    result = sum(merged_list) % threshold\n    return result", "input": "[10, 15, 20, 25, 30, 35, 40, 45, 50, 55]", "output": "0", "imports": [], "original_snippet": "def f(input_list: list, threshold: int=5, filter_multiple: int=3):\n    filtered_list = [n for n in input_list if n % filter_multiple != 0]\n    sorted_list = sorted(filtered_list, reverse=True)\n    grouped_list = [(sorted_list[i], sorted_list[i + 1]) for i in range(0, len(sorted_list) - 1, 2)]\n    merged_list = [m for pairs in grouped_list for m in pairs]\n    result = sum(merged_list) % threshold\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(matrix, indices, char):\n    filtered_matrix = [matrix[i] for i in indices]\n    rows_with_char_count = [sum((1 for cell in row if cell == char)) for row in filtered_matrix]\n    max_char_count = max(rows_with_char_count)\n    result = indices[rows_with_char_count.index(max_char_count)]\n    return result", "input": "[[1, 2, 3], [1, 2, 3], [1, 2, 3]], [0, 1, 2], 1", "output": "0", "imports": [], "original_snippet": "def f(matrix, indices, char):\n    filtered_matrix = [matrix[i] for i in indices]\n    rows_with_char_count = [sum((1 for cell in row if cell == char)) for row in filtered_matrix]\n    max_char_count = max(rows_with_char_count)\n    result = indices[rows_with_char_count.index(max_char_count)]\n    return result", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "from collections import defaultdict\ndef f(states_data):\n    flattened_data = [item for sub_dict in states_data for item in sub_dict.items()]\n    sorted_flattened_data = sorted(flattened_data, key=lambda x: x[0])\n    transformed_outputs = []\n    untransformed_outputs = []\n    power_of_two = 1\n    for (state, number) in sorted_flattened_data:\n        if number not in transformed_outputs:\n            if number % 2 == 0:\n                transformed_number = number * power_of_two\n                transformed_outputs.append(transformed_number)\n            else:\n                untransformed_outputs.append(number)\n        if len(transformed_outputs) % 2 == 0:\n            power_of_two *= 2\n    processed_outputs = defaultdict(list)\n    for (index, value) in enumerate(transformed_outputs):\n        if index % 2 == 0:\n            processed_outputs['Even'].append(value + power_of_two)\n        else:\n            processed_outputs['Odd'].append(value)\n        power_of_two *= 2\n    for value in untransformed_outputs:\n        processed_outputs['Even'].append(value)\n    sorted_keys = sorted(processed_outputs.keys())\n    return [processed_outputs[key] for key in sorted_keys]", "input": "[{'CA': 10}, {'NY': 20}, {'TX': 30}, {'FL': 40}]", "output": "[[14, 56], [40, 60]]", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(states_data):\n    flattened_data = [item for sub_dict in states_data for item in sub_dict.items()]\n    sorted_flattened_data = sorted(flattened_data, key=lambda x: x[0])\n    transformed_outputs = []\n    untransformed_outputs = []\n    power_of_two = 1\n    for (state, number) in sorted_flattened_data:\n        if number not in transformed_outputs:\n            if number % 2 == 0:\n                transformed_number = number * power_of_two\n                transformed_outputs.append(transformed_number)\n            else:\n                untransformed_outputs.append(number)\n        if len(transformed_outputs) % 2 == 0:\n            power_of_two *= 2\n    processed_outputs = defaultdict(list)\n    for (index, value) in enumerate(transformed_outputs):\n        if index % 2 == 0:\n            processed_outputs['Even'].append(value + power_of_two)\n        else:\n            processed_outputs['Odd'].append(value)\n        power_of_two *= 2\n    for value in untransformed_outputs:\n        processed_outputs['Even'].append(value)\n    sorted_keys = sorted(processed_outputs.keys())\n    return [processed_outputs[key] for key in sorted_keys]", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import List, Dict, Tuple\ndef f(words: list[str]) -> Dict[str, Tuple[int, int]]:\n    word_lengths = {}\n    unique_vowel_count = {}\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    for word in words:\n        letters = sorted([char.lower() for char in word if char.isalpha()])\n        length = len(letters)\n        unique_vowel_count[word] = sum((1 for char in letters if char in vowels))\n        word_lengths[word] = (length, unique_vowel_count[word])\n    return {word: word_lengths[word] for word in sorted(word_lengths)}", "input": "['apple', 'bear', 'antelope']", "output": "{'antelope': (8, 4), 'apple': (5, 2), 'bear': (4, 2)}", "imports": ["from typing import List, Dict, Tuple"], "original_snippet": "from typing import List, Dict, Tuple\ndef f(words: list[str]) -> Dict[str, Tuple[int, int]]:\n    word_lengths = {}\n    unique_vowel_count = {}\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    for word in words:\n        letters = sorted([char.lower() for char in word if char.isalpha()])\n        length = len(letters)\n        unique_vowel_count[word] = sum((1 for char in letters if char in vowels))\n        word_lengths[word] = (length, unique_vowel_count[word])\n    return {word: word_lengths[word] for word in sorted(word_lengths)}", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(numbers: list) -> int:\n    sorted_numbers = sorted(numbers)\n    filtered_numbers = [num for (i, num) in enumerate(sorted_numbers) if i % 2 == 0]\n    transformed_numbers = [num * (num % 5 + 1) for num in filtered_numbers]\n    total_sum = sum(transformed_numbers)\n    result = total_sum // 10 % 10\n    return result", "input": "[3, 2, 1, 7, 5, -4, 0]", "output": "2", "imports": [], "original_snippet": "def f(numbers: list) -> int:\n    sorted_numbers = sorted(numbers)\n    filtered_numbers = [num for (i, num) in enumerate(sorted_numbers) if i % 2 == 0]\n    transformed_numbers = [num * (num % 5 + 1) for num in filtered_numbers]\n    total_sum = sum(transformed_numbers)\n    result = total_sum // 10 % 10\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(input_list: List[int]):\n    result = []\n    for (i, num) in enumerate(input_list):\n        if num % 2 == 0:\n            result.append(num ** 2 - i)\n        elif num % 3 == 0:\n            result.append(i * num + 1)\n        elif num % 5 == 0:\n            result.append(num ** 3 // (i + 1))\n        else:\n            result.append(num - i)\n    return sum(result)", "input": "[2, 3, 5, 7, 9]", "output": "90", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(input_list: List[int]):\n    result = []\n    for (i, num) in enumerate(input_list):\n        if num % 2 == 0:\n            result.append(num ** 2 - i)\n        elif num % 3 == 0:\n            result.append(i * num + 1)\n        elif num % 5 == 0:\n            result.append(num ** 3 // (i + 1))\n        else:\n            result.append(num - i)\n    return sum(result)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from collections import defaultdict\ndef f(data: list):\n    filtered_data = [(item['name'], item['value']) for item in data if item['name'].isalpha() and item['value'] > 0]\n    sorted_data = sorted(filtered_data, key=lambda x: x[1], reverse=True)\n    filtered_by_range = [(name, value) for (name, value) in sorted_data if value < 1000 and value % 2 != 0]\n    category_dict = defaultdict(int)\n    for (name, value) in filtered_by_range:\n        category_dict[name] += value\n    final_list = [(key, value) for (key, value) in category_dict.items()]\n    squared_values = [value ** 2 for (_, value) in final_list if value % 5 != 0]\n    result = max(squared_values) * len(squared_values) if squared_values else len(squared_values)\n    return result", "input": "[{'name': 'Alice', 'value': 500}, {'name': 'Bob', 'value': 250}, {'name': 'Charlie', 'value': 750}, {'name': 'Daisy', 'value': 1000}, {'name': 'Euler', 'value': 300}]", "output": "0", "imports": ["from collections import defaultdict"], "original_snippet": "from collections import defaultdict\ndef f(data: list):\n    filtered_data = [(item['name'], item['value']) for item in data if item['name'].isalpha() and item['value'] > 0]\n    sorted_data = sorted(filtered_data, key=lambda x: x[1], reverse=True)\n    filtered_by_range = [(name, value) for (name, value) in sorted_data if value < 1000 and value % 2 != 0]\n    category_dict = defaultdict(int)\n    for (name, value) in filtered_by_range:\n        category_dict[name] += value\n    final_list = [(key, value) for (key, value) in category_dict.items()]\n    squared_values = [value ** 2 for (_, value) in final_list if value % 5 != 0]\n    result = max(squared_values) * len(squared_values) if squared_values else len(squared_values)\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(lists: List[List[int]]):\n    min_len = min((len(sublist) for sublist in lists))\n    min_sublists = [sublist for sublist in lists if len(sublist) == min_len]\n    min_sublists.sort(key=sum)\n    results = []\n    for sublist in min_sublists:\n        diff = sublist[0] - sublist[1]\n        results.append(diff)\n    return sum(results)", "input": "[[1, 2], [3, 4], [5, 3], [1, 5]]", "output": "-4", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(lists: List[List[int]]):\n    min_len = min((len(sublist) for sublist in lists))\n    min_sublists = [sublist for sublist in lists if len(sublist) == min_len]\n    min_sublists.sort(key=sum)\n    results = []\n    for sublist in min_sublists:\n        diff = sublist[0] - sublist[1]\n        results.append(diff)\n    return sum(results)", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "import math\nfrom functools import reduce\ndef f(data: list):\n    transformed_data = []\n    step1 = [int(math.sin(x)) if x % 2 == 0 else x for x in data]\n    step2 = [int(x / 2) if x > 1 else x for x in step1]\n    step3 = sorted(step2)\n    step4 = [x ** 2 if x % 3 == 0 else x for x in step3]\n    step5 = [abs(x) for x in step4]\n    step6 = reduce(lambda x, y: x + y, step5)\n    return step6", "input": "[1, 3, 5, 7, 9, 11, 13, 15, 17]", "output": "73", "imports": ["import math", "from functools import reduce"], "original_snippet": "import math\nfrom functools import reduce\ndef f(data: list):\n    transformed_data = []\n    step1 = [int(math.sin(x)) if x % 2 == 0 else x for x in data]\n    step2 = [int(x / 2) if x > 1 else x for x in step1]\n    step3 = sorted(step2)\n    step4 = [x ** 2 if x % 3 == 0 else x for x in step3]\n    step5 = [abs(x) for x in step4]\n    step6 = reduce(lambda x, y: x + y, step5)\n    return step6", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(nested_num_list: list) -> int:\n    flat_list = [num for sublist in nested_num_list for nums in sublist for num in nums]\n    hex_strings = [format(num, '04x') for num in flat_list]\n    transformed_hex_strings = [s[2:] + s[:2] for s in hex_strings]\n    modified_integers = [int(s, 16) for s in transformed_hex_strings]\n    result = sum(modified_integers) % 100\n    return result", "input": "[[[4, 5], [6, 7]], [[8, 9], [10, 11]]]", "output": "60", "imports": [], "original_snippet": "def f(nested_num_list: list) -> int:\n    flat_list = [num for sublist in nested_num_list for nums in sublist for num in nums]\n    hex_strings = [format(num, '04x') for num in flat_list]\n    transformed_hex_strings = [s[2:] + s[:2] for s in hex_strings]\n    modified_integers = [int(s, 16) for s in transformed_hex_strings]\n    result = sum(modified_integers) % 100\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers: list[int]):\n    modified_numbers = [(num + 2) ** 2 for num in numbers if num % 2 == 0]\n    aggregated_result = sum(modified_numbers) * len(modified_numbers)\n    return aggregated_result", "input": "[2, 3, 4, 5, 6]", "output": "348", "imports": [], "original_snippet": "def f(numbers: list[int]):\n    modified_numbers = [(num + 2) ** 2 for num in numbers if num % 2 == 0]\n    aggregated_result = sum(modified_numbers) * len(modified_numbers)\n    return aggregated_result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(numbers: list):\n    transformed_data = {num: num ** 2 + num for num in numbers}\n    filtered_pairs = [(key, value) for (key, value) in transformed_data.items() if key % 2 == 0 and value > 10]\n    final_output = [(k, v) for (k, v) in filtered_pairs if k % 3 == 0 or v % 5 == 0]\n    return final_output", "input": "[4, 6, 9, 12, 15, 18]", "output": "[(4, 20), (6, 42), (12, 156), (18, 342)]", "imports": [], "original_snippet": "def f(numbers: list):\n    transformed_data = {num: num ** 2 + num for num in numbers}\n    filtered_pairs = [(key, value) for (key, value) in transformed_data.items() if key % 2 == 0 and value > 10]\n    final_output = [(k, v) for (k, v) in filtered_pairs if k % 3 == 0 or v % 5 == 0]\n    return final_output", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "def f(numbers: list) -> str:\n    sum_even_numbers = 0\n    sum_squared_numbers = 0\n    first_odd_number_found = False\n    for num in numbers:\n        if num % 2 == 0:\n            sum_even_numbers += num\n        elif not first_odd_number_found:\n            first_odd_number_found = True\n        else:\n            sum_squared_numbers += num ** 2\n    result_tuple = (sum_even_numbers, sum_squared_numbers)\n    result_str = ''.join([str(ord(c) % 80) for c in str(result_tuple)])\n    return result_str", "input": "[1, 2, 3, 4, 5]", "output": "'40544432515241'", "imports": [], "original_snippet": "def f(numbers: list) -> str:\n    sum_even_numbers = 0\n    sum_squared_numbers = 0\n    first_odd_number_found = False\n    for num in numbers:\n        if num % 2 == 0:\n            sum_even_numbers += num\n        elif not first_odd_number_found:\n            first_odd_number_found = True\n        else:\n            sum_squared_numbers += num ** 2\n    result_tuple = (sum_even_numbers, sum_squared_numbers)\n    result_str = ''.join([str(ord(c) % 80) for c in str(result_tuple)])\n    return result_str", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(words: list[str]) -> dict:\n    word_dict = {}\n    for word in words:\n        for w in word.split():\n            if w in word_dict:\n                word_dict[w] += 1\n            else:\n                word_dict[w] = 1\n    sorted_word_dict = dict(sorted(word_dict.items(), key=lambda item: len(item[0]), reverse=True))\n    return sorted_word_dict", "input": "['apple', 'banana', 'apple', 'pear', 'banana']", "output": "{'banana': 2, 'apple': 2, 'pear': 1}", "imports": [], "original_snippet": "def f(words: list[str]) -> dict:\n    word_dict = {}\n    for word in words:\n        for w in word.split():\n            if w in word_dict:\n                word_dict[w] += 1\n            else:\n                word_dict[w] = 1\n    sorted_word_dict = dict(sorted(word_dict.items(), key=lambda item: len(item[0]), reverse=True))\n    return sorted_word_dict", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(s: str):\n    ascii_values = [ord(char) for char in s]\n    selected_numbers = [num for (idx, num) in enumerate(ascii_values, start=1) if idx % 2 == 0 and num % 13 == 0]\n    selected_str = ''.join(map(str, selected_numbers))\n    reversed_str = selected_str[::-1]\n    partitioned_strs = [reversed_str[idx:idx + 8] for idx in range(0, len(reversed_str)) if len(reversed_str) >= 8]\n    final_list = [int(num_str[::-1]) for num_str in partitioned_strs]\n    conditional_sum = sum((final_element for final_element in final_list if final_element % 2 == 0 and final_element % 5 < 3))\n    return conditional_sum", "input": "'Ba9AqF24'", "output": "0", "imports": [], "original_snippet": "def f(s: str):\n    ascii_values = [ord(char) for char in s]\n    selected_numbers = [num for (idx, num) in enumerate(ascii_values, start=1) if idx % 2 == 0 and num % 13 == 0]\n    selected_str = ''.join(map(str, selected_numbers))\n    reversed_str = selected_str[::-1]\n    partitioned_strs = [reversed_str[idx:idx + 8] for idx in range(0, len(reversed_str)) if len(reversed_str) >= 8]\n    final_list = [int(num_str[::-1]) for num_str in partitioned_strs]\n    conditional_sum = sum((final_element for final_element in final_list if final_element % 2 == 0 and final_element % 5 < 3))\n    return conditional_sum", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "from typing import List, Tuple\ndef f(data: List[Tuple[str, int, str]]) -> Tuple[int, int, int, int]:\n    eligible_names = []\n    total_landline_half = 0\n    total_mobile_half = 0\n    ineligible_numbers_binary = []\n    for (name, age, phone_num) in data:\n        if age > 25:\n            eligible_names.append(name)\n        else:\n            half = int(phone_num) // 2\n            ineligible_numbers_binary.append(half)\n            if str(phone_num)[0] == '0':\n                total_landline_half += half\n            else:\n                total_mobile_half += half\n    return (len(eligible_names), total_landline_half, total_mobile_half, sum(ineligible_numbers_binary))", "input": "[('John', 30, '0123456789'), ('Katie', 21, '12345678901'), ('Maria', 28, '0987654321'), ('George', 35, '0321654987'), ('Alex', 18, '14785236901')]", "output": "(3, 0, 13565457900, 13565457900)", "imports": ["from typing import List, Tuple"], "original_snippet": "from typing import List, Tuple\ndef f(data: List[Tuple[str, int, str]]) -> Tuple[int, int, int, int]:\n    eligible_names = []\n    total_landline_half = 0\n    total_mobile_half = 0\n    ineligible_numbers_binary = []\n    for (name, age, phone_num) in data:\n        if age > 25:\n            eligible_names.append(name)\n        else:\n            half = int(phone_num) // 2\n            ineligible_numbers_binary.append(half)\n            if str(phone_num)[0] == '0':\n                total_landline_half += half\n            else:\n                total_mobile_half += half\n    return (len(eligible_names), total_landline_half, total_mobile_half, sum(ineligible_numbers_binary))", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(names: list, positions: list) -> list:\n    reversed_names = names[::-1]\n    transformed_names = [name * position for (name, position) in zip(reversed_names, positions)]\n    sorted_names = sorted(transformed_names, reverse=True)\n    return sorted_names", "input": "['John', 'Michael', 'Sam'], [3, 2, 1]", "output": "['SamSamSam', 'MichaelMichael', 'John']", "imports": [], "original_snippet": "def f(names: list, positions: list) -> list:\n    reversed_names = names[::-1]\n    transformed_names = [name * position for (name, position) in zip(reversed_names, positions)]\n    sorted_names = sorted(transformed_names, reverse=True)\n    return sorted_names", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(numbers: list) -> int:\n    even_count = sum((1 for num in numbers if num % 2 == 0))\n    odd_count = len(numbers) - even_count\n    interleaved = []\n    even_index = 0\n    odd_index = 0\n    for _ in range(min(even_count, odd_count)):\n        interleaved.append(numbers[even_index])\n        even_index += 1\n        interleaved.append(numbers[odd_index])\n        odd_index += 1\n    if even_count > odd_count:\n        interleaved.extend(numbers[even_index:even_index + even_count - odd_count])\n    else:\n        interleaved.extend(numbers[odd_index:odd_index + odd_count - even_count])\n    interleaved_sum = sum(interleaved)\n    return interleaved_sum", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9]", "output": "25", "imports": [], "original_snippet": "def f(numbers: list) -> int:\n    even_count = sum((1 for num in numbers if num % 2 == 0))\n    odd_count = len(numbers) - even_count\n    interleaved = []\n    even_index = 0\n    odd_index = 0\n    for _ in range(min(even_count, odd_count)):\n        interleaved.append(numbers[even_index])\n        even_index += 1\n        interleaved.append(numbers[odd_index])\n        odd_index += 1\n    if even_count > odd_count:\n        interleaved.extend(numbers[even_index:even_index + even_count - odd_count])\n    else:\n        interleaved.extend(numbers[odd_index:odd_index + odd_count - even_count])\n    interleaved_sum = sum(interleaved)\n    return interleaved_sum", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(l):\n    grouped_data = dict()\n    for sublist in l:\n        if isinstance(sublist, int):\n            (group, sub_group) = ('Single', 'Integer')\n        elif isinstance(sublist, list):\n            (group, sub_group) = ('List', 'List')\n        else:\n            (group, sub_group) = ('Other', 'TypeError')\n        if group in grouped_data:\n            if sub_group in grouped_data[group]:\n                grouped_data[group][sub_group].append(sublist)\n            else:\n                grouped_data[group][sub_group] = [sublist]\n        else:\n            grouped_data[group] = {sub_group: [sublist]}\n    final_result = len(grouped_data['Other']['TypeError']) if 'Other' in grouped_data and 'TypeError' in grouped_data['Other'] else 0\n    return final_result", "input": "[1, [2, 3], [4, [5, 6]], \"str\", [\"list\"], {\"key\": \"value\"}, 7, 8]", "output": "2", "imports": [], "original_snippet": "def f(l):\n    grouped_data = dict()\n    for sublist in l:\n        if isinstance(sublist, int):\n            (group, sub_group) = ('Single', 'Integer')\n        elif isinstance(sublist, list):\n            (group, sub_group) = ('List', 'List')\n        else:\n            (group, sub_group) = ('Other', 'TypeError')\n        if group in grouped_data:\n            if sub_group in grouped_data[group]:\n                grouped_data[group][sub_group].append(sublist)\n            else:\n                grouped_data[group][sub_group] = [sublist]\n        else:\n            grouped_data[group] = {sub_group: [sublist]}\n    final_result = len(grouped_data['Other']['TypeError']) if 'Other' in grouped_data and 'TypeError' in grouped_data['Other'] else 0\n    return final_result", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "def f(texts: list) -> str:\n    text_dict = {word: text.count(word) for text in texts for word in text.split()}\n    sorted_texts = sorted(text_dict.items(), key=lambda x: x[1], reverse=True)\n    sorted_texts = [x for x in sorted_texts if 'a' in x[0] or 'e' in x[0] or 'i' in x[0] or ('o' in x[0]) or ('u' in x[0])]\n    result = ''.join([text[0][i] if text[0].index(text[0][i]) % 2 == 0 else '@' for text in sorted_texts for i in range(len(text[0]))])\n    return result.upper()", "input": "['hello world', 'apple pie', 'banana split']", "output": "'H@LLOW@R@DA@@@EP@EB@N@N@S@L@T'", "imports": [], "original_snippet": "def f(texts: list) -> str:\n    text_dict = {word: text.count(word) for text in texts for word in text.split()}\n    sorted_texts = sorted(text_dict.items(), key=lambda x: x[1], reverse=True)\n    sorted_texts = [x for x in sorted_texts if 'a' in x[0] or 'e' in x[0] or 'i' in x[0] or ('o' in x[0]) or ('u' in x[0])]\n    result = ''.join([text[0][i] if text[0].index(text[0][i]) % 2 == 0 else '@' for text in sorted_texts for i in range(len(text[0]))])\n    return result.upper()", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "def f(s: str):\n    if len(s) == 0:\n        return 0\n    elif s[0] == '1':\n        return f(s[1:]) * 2 + 3\n    elif s[0] == '0':\n        return f(s[1:]) ** 2 - 1\n    else:\n        return f(s[1:]) + 2 * int(s[0])", "input": "'111002'", "output": "1813", "imports": [], "original_snippet": "def f(s: str):\n    if len(s) == 0:\n        return 0\n    elif s[0] == '1':\n        return f(s[1:]) * 2 + 3\n    elif s[0] == '0':\n        return f(s[1:]) ** 2 - 1\n    else:\n        return f(s[1:]) + 2 * int(s[0])", "composite_functions": [], "_input_type": "str", "_output_type": "int"}
{"snippet": "def f(sequence: list):\n    sorted_sequence = sorted(sequence)\n    even_numbers = [num for num in sorted_sequence if num % 2 == 0]\n    for i in range(len(even_numbers)):\n        even_numbers[i] = even_numbers[i] ** 2\n    odd_numbers = [num for num in sorted_sequence if num % 2 == 1]\n    for i in range(1, len(odd_numbers), 2):\n        odd_numbers[i] = odd_numbers[i] - odd_numbers[i - 1]\n    merged_sequence = even_numbers + odd_numbers\n    combined_sequence = sum(merged_sequence)\n    return combined_sequence", "input": "[3, 2, 1, 7, 5, -4, 0]", "output": "30", "imports": [], "original_snippet": "def f(sequence: list):\n    sorted_sequence = sorted(sequence)\n    even_numbers = [num for num in sorted_sequence if num % 2 == 0]\n    for i in range(len(even_numbers)):\n        even_numbers[i] = even_numbers[i] ** 2\n    odd_numbers = [num for num in sorted_sequence if num % 2 == 1]\n    for i in range(1, len(odd_numbers), 2):\n        odd_numbers[i] = odd_numbers[i] - odd_numbers[i - 1]\n    merged_sequence = even_numbers + odd_numbers\n    combined_sequence = sum(merged_sequence)\n    return combined_sequence", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(strings: List[str]) -> str:\n    res_string = ''\n    for s in strings:\n        count = 0\n        for char in s:\n            if char in 'aeiou':\n                count += 1\n        res_string += str(count)\n    threshold = 5\n    transformed_string = ''\n    for i in res_string:\n        if int(i) < threshold:\n            transformed_string += chr((int(i) + threshold) % 26 + 65)\n        else:\n            transformed_string += chr((int(i) - threshold) % 26 + 97)\n    return (f'{sum((int(i) for i in res_string))}'.zfill(2), f'{len(strings)}'.zfill(3), transformed_string)", "input": "['Apple', 'Orange', 'Banana', 'Strawberry', 'Kiwi', 'Watermelon']", "output": "('14', '006', 'GHIHHJ')", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(strings: List[str]) -> str:\n    res_string = ''\n    for s in strings:\n        count = 0\n        for char in s:\n            if char in 'aeiou':\n                count += 1\n        res_string += str(count)\n    threshold = 5\n    transformed_string = ''\n    for i in res_string:\n        if int(i) < threshold:\n            transformed_string += chr((int(i) + threshold) % 26 + 65)\n        else:\n            transformed_string += chr((int(i) - threshold) % 26 + 97)\n    return (f'{sum((int(i) for i in res_string))}'.zfill(2), f'{len(strings)}'.zfill(3), transformed_string)", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(numbers: list, input_value: int) -> list:\n    sorted_numbers = sorted(numbers, key=lambda x: x % input_value)\n    group_numbers = [sorted_numbers[i:i + input_value] for i in range(0, len(sorted_numbers), input_value)]\n    transformed_numbers = [[n ** 2 for n in group] for group in group_numbers]\n    final_result = sum([sum(group) for group in transformed_numbers])\n    return [final_result]", "input": "[5, 8, 9, 14, 3], 3", "output": "[375]", "imports": [], "original_snippet": "def f(numbers: list, input_value: int) -> list:\n    sorted_numbers = sorted(numbers, key=lambda x: x % input_value)\n    group_numbers = [sorted_numbers[i:i + input_value] for i in range(0, len(sorted_numbers), input_value)]\n    transformed_numbers = [[n ** 2 for n in group] for group in group_numbers]\n    final_result = sum([sum(group) for group in transformed_numbers])\n    return [final_result]", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
{"snippet": "def f(numbers: list):\n    primes = [num for num in numbers if num > 1 and all((num % i != 0 for i in range(2, int(num ** 0.5) + 1)))]\n    odd_primes_tripled = [num * 3 for num in primes if num % 2 != 0]\n    even_div4_squared = [num ** 2 for num in numbers if num % 4 == 0 and num % 2 == 0]\n    return tuple(sorted((odd_primes_tripled, even_div4_squared)))", "input": "[5, 3, 7, 8, 28, 4, 12, 16, 18, 24, 32, 33, 35, 36]", "output": "([15, 9, 21], [64, 784, 16, 144, 256, 576, 1024, 1296])", "imports": [], "original_snippet": "def f(numbers: list):\n    primes = [num for num in numbers if num > 1 and all((num % i != 0 for i in range(2, int(num ** 0.5) + 1)))]\n    odd_primes_tripled = [num * 3 for num in primes if num % 2 != 0]\n    even_div4_squared = [num ** 2 for num in numbers if num % 4 == 0 and num % 2 == 0]\n    return tuple(sorted((odd_primes_tripled, even_div4_squared)))", "composite_functions": [], "_input_type": "list", "_output_type": "tuple"}
{"snippet": "def f(lst: list):\n    sorted_lst = sorted(lst)\n    multiplied_lst = [num * 3 if i % 2 == 0 else num * 2 for (i, num) in enumerate(sorted_lst)]\n    reversed_lst = multiplied_lst[::-1]\n    result = [round(num) for num in reversed_lst]\n    return result", "input": "[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]", "output": "[20, 27, 16, 21, 12, 15, 8, 9, 4, 3]", "imports": [], "original_snippet": "def f(lst: list):\n    sorted_lst = sorted(lst)\n    multiplied_lst = [num * 3 if i % 2 == 0 else num * 2 for (i, num) in enumerate(sorted_lst)]\n    reversed_lst = multiplied_lst[::-1]\n    result = [round(num) for num in reversed_lst]\n    return result", "composite_functions": [], "_input_type": "list", "_output_type": "list"}
{"snippet": "from typing import List\ndef f(matrix: List[List[int]]):\n    flattened = sorted([num for sublist in matrix for num in sublist if num % 2 == 0])\n    compressed = [str(num) * (num % 4) for num in flattened]\n    combined = ''.join(compressed)\n    return combined[2:5] or None", "input": "[[10, 11, 12], [13, 14, 15], [16, 17, 18]]", "output": "'101'", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(matrix: List[List[int]]):\n    flattened = sorted([num for sublist in matrix for num in sublist if num % 2 == 0])\n    compressed = [str(num) * (num % 4) for num in flattened]\n    combined = ''.join(compressed)\n    return combined[2:5] or None", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "from math import isqrt\nfrom sympy import isprime\ndef f(lower_bound: int):\n    upper_bound = lower_bound + 40\n    primes = [num for num in range(lower_bound, upper_bound + 1) if isprime(num)]\n    even_index_primes_with_prime_square = [(index, prime) for (index, prime) in enumerate(primes) if index % 2 != 0 and isprime(prime * prime)]\n    return [prime for (_, prime) in even_index_primes_with_prime_square if prime * prime % 2 != 0]", "input": "10", "output": "[]", "imports": ["from math import isqrt", "from sympy import isprime"], "original_snippet": "from math import isqrt\nfrom sympy import isprime\ndef f(lower_bound: int):\n    upper_bound = lower_bound + 40\n    primes = [num for num in range(lower_bound, upper_bound + 1) if isprime(num)]\n    even_index_primes_with_prime_square = [(index, prime) for (index, prime) in enumerate(primes) if index % 2 != 0 and isprime(prime * prime)]\n    return [prime for (_, prime) in even_index_primes_with_prime_square if prime * prime % 2 != 0]", "composite_functions": [], "_input_type": "int", "_output_type": "list"}
{"snippet": "def f(words: list) -> str:\n    valid_words = [word for word in words if len(word) % 2 == 0 and word.isalpha()]\n    vowels = 'aeiouAEIOU'\n    result = [''.join((char for char in word if char not in vowels)) for word in valid_words]\n    return '-'.join(result)", "input": "['Test', 'Word', 'Query', '']", "output": "'Tst-Wrd'", "imports": [], "original_snippet": "def f(words: list) -> str:\n    valid_words = [word for word in words if len(word) % 2 == 0 and word.isalpha()]\n    vowels = 'aeiouAEIOU'\n    result = [''.join((char for char in word if char not in vowels)) for word in valid_words]\n    return '-'.join(result)", "composite_functions": [], "_input_type": "list", "_output_type": "str"}
{"snippet": "from typing import Iterable, Dict, List\ndef f(names: Iterable[str]) -> Dict[str, List[str]]:\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    vowel_to_name_map = {}\n    for name in names:\n        vowels_letters = [c.lower() for c in name if c.lower() in vowels]\n        common_vowel = max(set(vowels_letters), key=vowels_letters.count)\n        if common_vowel not in vowel_to_name_map:\n            vowel_to_name_map[common_vowel] = []\n        vowel_to_name_map[common_vowel].append(name)\n    for vowel in vowel_to_name_map:\n        vowel_to_name_map[vowel].sort(key=len)\n    return vowel_to_name_map", "input": "['Alice', 'Bob', 'Eve', 'Ethan', 'Olivia', 'Aubrey']", "output": "{'e': ['Eve', 'Alice', 'Ethan', 'Aubrey'], 'o': ['Bob'], 'i': ['Olivia']}", "imports": ["from typing import Iterable, Dict, List"], "original_snippet": "from typing import Iterable, Dict, List\ndef f(names: Iterable[str]) -> Dict[str, List[str]]:\n    vowels = {'a', 'e', 'i', 'o', 'u'}\n    vowel_to_name_map = {}\n    for name in names:\n        vowels_letters = [c.lower() for c in name if c.lower() in vowels]\n        common_vowel = max(set(vowels_letters), key=vowels_letters.count)\n        if common_vowel not in vowel_to_name_map:\n            vowel_to_name_map[common_vowel] = []\n        vowel_to_name_map[common_vowel].append(name)\n    for vowel in vowel_to_name_map:\n        vowel_to_name_map[vowel].sort(key=len)\n    return vowel_to_name_map", "composite_functions": [], "_input_type": "list", "_output_type": "dict"}
{"snippet": "def f(nums: list, index: int):\n    flipped_nums = nums.copy()[:len(nums) // 2]\n    flipped_nums.reverse()\n    transformed_nums = flipped_nums + nums[len(nums) // 2:]\n    for i in range(0, len(transformed_nums) - 1, 2):\n        transformed_nums[i] = transformed_nums[i] ** 2 % 100\n        transformed_nums[i + 1] = (transformed_nums[i] + transformed_nums[i + 1]) % 100\n    sequence_value = sum(transformed_nums[:index])\n    return sequence_value", "input": "[10, 20, 30, 40, 50, 60], 3", "output": "20", "imports": [], "original_snippet": "def f(nums: list, index: int):\n    flipped_nums = nums.copy()[:len(nums) // 2]\n    flipped_nums.reverse()\n    transformed_nums = flipped_nums + nums[len(nums) // 2:]\n    for i in range(0, len(transformed_nums) - 1, 2):\n        transformed_nums[i] = transformed_nums[i] ** 2 % 100\n        transformed_nums[i + 1] = (transformed_nums[i] + transformed_nums[i + 1]) % 100\n    sequence_value = sum(transformed_nums[:index])\n    return sequence_value", "composite_functions": [], "_input_type": "tuple", "_output_type": "int"}
{"snippet": "import collections\ndef f(nodes: list):\n    depths = collections.defaultdict(int)\n    for node in nodes:\n        depth = node.get('depth', 0)\n        depths[depth] += 1\n    products = sum((key * value for (key, value) in depths.items() if key % 2 == 0 and value % 2 == 0))\n    predefined_list = list(range(200, 1, -2))\n    res = predefined_list[products]\n    return res * len(str(res)) if sum((int(digit) for digit in str(res))) % 5 != 0 else res + 1", "input": "[{'node_id': 1, 'depth': 2}, {'node_id': 2, 'depth': 3}, {'node_id': 3, 'depth': 4}, {'node_id': 4, 'depth': 2}]", "output": "576", "imports": ["import collections"], "original_snippet": "import collections\ndef f(nodes: list):\n    depths = collections.defaultdict(int)\n    for node in nodes:\n        depth = node.get('depth', 0)\n        depths[depth] += 1\n    products = sum((key * value for (key, value) in depths.items() if key % 2 == 0 and value % 2 == 0))\n    predefined_list = list(range(200, 1, -2))\n    res = predefined_list[products]\n    return res * len(str(res)) if sum((int(digit) for digit in str(res))) % 5 != 0 else res + 1", "composite_functions": [], "_input_type": "list", "_output_type": "int"}
{"snippet": "from typing import List\ndef f(numbers: List[int], x: int) -> List[List[int]]:\n    modified_list = []\n    for (i, element) in enumerate(numbers):\n        nested_list = []\n        for j in range(element):\n            nested_list.append((element + i + j) % x)\n        modified_list.append(nested_list)\n    return modified_list", "input": "[3, 2, 1], 5", "output": "[[3, 4, 0], [3, 4], [3]]", "imports": ["from typing import List"], "original_snippet": "from typing import List\ndef f(numbers: List[int], x: int) -> List[List[int]]:\n    modified_list = []\n    for (i, element) in enumerate(numbers):\n        nested_list = []\n        for j in range(element):\n            nested_list.append((element + i + j) % x)\n        modified_list.append(nested_list)\n    return modified_list", "composite_functions": [], "_input_type": "tuple", "_output_type": "list"}
