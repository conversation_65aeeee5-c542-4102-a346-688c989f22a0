"""
Dependency Injection Container for Absolute Zero Reasoner.

This module provides a simple dependency injection container to manage
component dependencies and improve testability.
"""

import logging
from typing import Any, Dict, Callable, Optional, Type, TypeVar
from absolute_zero_reasoner.exceptions import ConfigurationError

T = TypeVar('T')

logger = logging.getLogger(__name__)


class DIContainer:
    """Simple dependency injection container."""

    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable[[], Any]] = {}
        self._singletons: Dict[str, Any] = {}
        self._types: Dict[str, Type] = {}

    def register(self, name: str, service: Any) -> None:
        """
        Register a service instance.

        Args:
            name: Service name
            service: Service instance
        """
        self._services[name] = service
        logger.debug(f"Registered service: {name}")

    def register_factory(self, name: str, factory: Callable[[], Any]) -> None:
        """
        Register a service factory function.

        Args:
            name: Service name
            factory: Factory function that creates the service
        """
        self._factories[name] = factory
        logger.debug(f"Registered factory: {name}")

    def register_singleton(self, name: str, factory: Callable[[], Any]) -> None:
        """
        Register a singleton service factory.

        Args:
            name: Service name
            factory: Factory function that creates the service (called only once)
        """
        self._factories[name] = factory
        self._singletons[name] = None  # Mark as singleton
        logger.debug(f"Registered singleton: {name}")

    def register_type(self, name: str, service_type: Type[T], *args, **kwargs) -> None:
        """
        Register a service type with constructor arguments.

        Args:
            name: Service name
            service_type: Service class type
            *args: Constructor arguments
            **kwargs: Constructor keyword arguments
        """
        self._types[name] = service_type
        self._factories[name] = lambda: service_type(*args, **kwargs)
        logger.debug(f"Registered type: {name} -> {service_type.__name__}")

    def get(self, name: str) -> Any:
        """
        Get a service by name.

        Args:
            name: Service name

        Returns:
            Service instance

        Raises:
            ConfigurationError: If service is not found
        """
        # Check if it's a direct service instance
        if name in self._services:
            return self._services[name]

        # Check if it's a singleton
        if name in self._singletons:
            if self._singletons[name] is None:
                # Create singleton instance
                if name not in self._factories:
                    raise ConfigurationError(f"Singleton factory not found for service '{name}'")
                self._singletons[name] = self._factories[name]()
                logger.debug(f"Created singleton instance: {name}")
            return self._singletons[name]

        # Check if it's a factory
        if name in self._factories:
            service = self._factories[name]()
            logger.debug(f"Created service instance: {name}")
            return service

        raise ConfigurationError(f"Service '{name}' not found in container")

    def has(self, name: str) -> bool:
        """
        Check if a service is registered.

        Args:
            name: Service name

        Returns:
            True if service is registered, False otherwise
        """
        return (name in self._services or
                name in self._factories or
                name in self._singletons)

    def remove(self, name: str) -> None:
        """
        Remove a service from the container.

        Args:
            name: Service name to remove
        """
        self._services.pop(name, None)
        self._factories.pop(name, None)
        self._singletons.pop(name, None)
        self._types.pop(name, None)
        logger.debug(f"Removed service: {name}")

    def clear(self) -> None:
        """Clear all registered services."""
        self._services.clear()
        self._factories.clear()
        self._singletons.clear()
        self._types.clear()
        logger.debug("Cleared all services from container")

    def list_services(self) -> Dict[str, str]:
        """
        List all registered services and their types.

        Returns:
            Dictionary mapping service names to their types
        """
        services = {}

        for name in self._services:
            services[name] = f"instance:{type(self._services[name]).__name__}"

        for name in self._factories:
            if name in self._singletons:
                services[name] = "singleton"
            else:
                services[name] = "factory"

        return services


class ServiceLocator:
    """Service locator pattern implementation."""

    _instance: Optional['ServiceLocator'] = None
    _container: Optional[DIContainer] = None

    def __new__(cls) -> 'ServiceLocator':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._container = DIContainer()
        return cls._instance

    @classmethod
    def get_container(cls) -> DIContainer:
        """Get the global container instance."""
        if cls._container is None:
            cls._container = DIContainer()
        return cls._container

    @classmethod
    def reset(cls) -> None:
        """Reset the service locator (useful for testing)."""
        cls._instance = None
        cls._container = None


def configure_default_services(container: DIContainer) -> None:
    """
    Configure default services in the container.

    Args:
        container: Container to configure
    """
    # This function can be extended to register default services
    # For now, it's a placeholder for future default configurations
    logger.info("Configuring default services...")

    # Example: Register logging service
    container.register('logger', logging.getLogger('absolute_zero'))

    logger.info("Default services configured")