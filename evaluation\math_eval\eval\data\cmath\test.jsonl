{"grade": 1, "question": "芳芳买了一本书有99页，看了90页，她还剩多少页没有看？", "answer": "9", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "小勇拍皮球，第一次拍了77下，第二次比第一次少了9下，第二次拍了多少下？", "answer": "68", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "妈妈带了30元，买了一条鱼，剩6元，这条鱼多少钱？", "answer": "24", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "一年级有84人去郊游，二年级比一年级多去8人，二年级有多少人去郊游？", "answer": "92", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "同学们要种18棵树，第一组种了8棵，第二组种了7棵树，还有多少棵需要种？", "answer": "3", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "妈妈和小丽在家包饺子，妈妈包了15个，丽丽包了3个。丽丽还要包几个饺子才与妈妈一样多？", "answer": "12", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "小强原来有4辆小汽车。爸爸送给他一辆新车作为生日礼物。小强现在有几辆小汽车？", "answer": "5", "reasoning_step": 1, "num_digits": 1}
{"grade": 1, "question": "草坪上有灰鸽子19只，白鸽子比灰鸽子多9只。草坪上有白鸽子多少只？", "answer": "28", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "学校有菊花30盆，兰花70盆，一共有多少盆花？", "answer": "100", "reasoning_step": 1, "num_digits": 3}
{"grade": 1, "question": "马路两边各种40棵树，一共种树多少棵？", "answer": "80", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "教室里有42把椅子，拿走一些，还剩8把，拿走了多少把？", "answer": "34", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "一个皮球60元，一个毽子10元，毽子比皮球便宜多少元？", "answer": "50", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "学校原有25瓶胶水，又买回9瓶，现在有多少瓶？", "answer": "34", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "琪琪买零食花了17元，妹妹花了7元，琪琪比妹妹多花了几元？", "answer": "10", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "公共汽车上有7个人，第一站上来6个人，第二站上来3个人，现在车上有几个人？", "answer": "16", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "小明家住五楼，每上一层楼要用1分钟，小明从一楼走到五楼，要走多少分钟？", "answer": "4", "reasoning_step": 1, "num_digits": 1}
{"grade": 1, "question": "娜娜有23个糖果，婷婷有30个，两人一共有多少个糖果？", "answer": "53", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "学校里有58盆黄菊花，50盆红菊花，黄菊花比红菊花多多少盆？", "answer": "8", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "丽丽借给3位好朋友各4本故事书，她一共借出了多少本故事书？", "answer": "12", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "一盘花生有48颗，梅梅拿出几颗后，还剩下40颗？", "answer": "8", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "喜羊羊和灰太狼比赛骑木马，喜羊羊骑了50下，灰太狼骑了40下。喜羊羊比灰太狼多骑了多少下？", "answer": "10", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "妈妈买了一些梨，小刚吃了3个，妈妈吃了2个，小刚和妈妈一共吃了多少个？", "answer": "5", "reasoning_step": 1, "num_digits": 1}
{"grade": 1, "question": "小苹种7盆红花，又种了同样多的黄花，两种花共多少盆？", "answer": "14", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "光明小区棋牌院有69副象棋，60副跳棋，象棋比跳棋多多少副？", "answer": "9", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "有67颗五角星，第一大组发了14颗，第二大组发了6颗，还剩多少颗五角星？", "answer": "47", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "妈妈买来18个鸡蛋，吃了5个后，还剩多少个鸡蛋？", "answer": "13", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "小明有9只纸飞机，比小军多3只。小军有多少只纸飞机？", "answer": "6", "reasoning_step": 1, "num_digits": 1}
{"grade": 1, "question": "汽车站有40辆汽车，第一次开走20辆，第二次开走10辆，还剩多少辆？", "answer": "10", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "学雷锋小组上午修了20把椅子，下午修了10把，一天总共修了多少把椅子？", "answer": "30", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "老师买回14盒白粉笔和2盒彩粉笔，上课用了1盒粉笔，还剩几盒粉笔？", "answer": "15", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "同学们要做29个灯笼，已经做好了9个，还要做多少个？", "answer": "20", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "森林运动会上，小狐狸比小狗多跑9米，小兔比小狗多跑4米，小狐狸比小兔多跑多少米？", "answer": "5", "reasoning_step": 2, "num_digits": 1}
{"grade": 1, "question": "小灰兔拔了9个萝卜，小白兔拔的是小灰兔的2倍。它们一共拔了多少个萝卜？", "answer": "27", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "树上有21只小松鼠，树下有8只，一共有多少只小松鼠？", "answer": "29", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "小红已经写了8个字，还差5个字没写，小红要多少个字？", "answer": "13", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "杉杉捡了20个贝壳，还要捡39个，一共要捡多少个贝壳？", "answer": "59", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "学校体育室里有23个足球，被人借走了10个，还有多少个？", "answer": "13", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "小兔拔了26个萝卜，比小猪少6个，小猪拔了多少个？", "answer": "32", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "商店里有16个蛋糕，卖出去5个，还剩多少个？", "answer": "11", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "一艘船可以坐50人，船上已经有30人，还可以坐几人？", "answer": "20", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "马场上有20匹马，又来了21匹，现在马场上有多少匹？", "answer": "41", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "节日里，欢欢买了7个气球，回家路上爆了3个，他带回家几个气球？", "answer": "4", "reasoning_step": 1, "num_digits": 1}
{"grade": 1, "question": "乐乐有19枚邮票，文文有8枚邮票，一共有多少枚邮票？", "answer": "27", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "农民伯伯上午摘了16筐番茄，运送到食堂3筐后，下午又摘了6筐。现在农民伯伯需要把多少筐番茄运到市场上卖？", "answer": "19", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "亮亮有15张画片，送给弟弟5张，还剩下几张画片？", "answer": "10", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "电饭锅卖了8个，还剩下7个，原来一共有多少个？", "answer": "15", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "从前面数，贝贝排9，从后面数他排12，这一排一共有几人？", "answer": "20", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "阳阳有102张1元的纸币，他最多能换几张50元的纸币？", "answer": "2", "reasoning_step": 1, "num_digits": 3}
{"grade": 1, "question": "活动课上打乒乓球的有8人，做操的有24人。打乒乓球球和做操的同学共有多少人？", "answer": "32", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "苗苗家里有50个苹果，有30个梨，有20个桃。桃比苹果少多少个？", "answer": "30", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "小明有8本书，小丽有9本书，小强有6本书。小明和小丽的书总数比小强多几本？", "answer": "11", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "爸爸买一条毛巾要付4元，买一支笔用去同样多的钱，一共要付多少元钱?", "answer": "8", "reasoning_step": 1, "num_digits": 1}
{"grade": 1, "question": "婷婷买了12只气球，娜娜买了10只，她们一共买了多少只气球？", "answer": "22", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "商店新进了79台电脑，卖出9台后，还剩多少台电脑？", "answer": "70", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "弟弟今年4岁，哥哥比弟弟大8岁，2年前哥哥多少岁？", "answer": "10", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "修一条99米的路，第一天修了22米，第二天修了8米，还要修多少米才能修完？", "answer": "69", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "学校运动会中，参加游泳的有32名同学，参加跑步的有30名，参加这两项比赛的一共有多少名同学？", "answer": "62", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "伟伟现在身高98厘米，比去年长高了7厘米，伟伟去年身高多少厘米?", "answer": "91", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "停车场里有4辆小轿车，后来又开来3辆，停车场里一共有多少辆小轿车？", "answer": "7", "reasoning_step": 1, "num_digits": 1}
{"grade": 1, "question": "有2个小猴子各摘了8个桃子，一个小猴子吃了2个后，他们把剩下的都放进盒子里。盒子里有几个桃子？", "answer": "14", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "晚上停电，小文在家点了8支蜡烛，先被风吹灭了1支蜡烛，后来又被风吹灭了2支，最后还剩多少支蜡烛？", "answer": "5", "reasoning_step": 2, "num_digits": 1}
{"grade": 1, "question": "李伯伯家养了33只鸡，30只鸭，9只鹅。鸭和鹅一共多少只？", "answer": "39", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "一列火车的第10号车厢原有85人，到下一站后有23人下车，又到一站后有7人下车。再开车时，这节车厢有多少人？", "answer": "55", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "飞机场上有75架飞机，飞走了50架，现在机场上有飞机多少架？", "answer": "25", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "妈咪带了98元，买菜花了23元，买水果花了7元，还剩多少元？", "answer": "68", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "果园有96棵梨树，90棵桃树。果园里梨树比桃树多多少棵？", "answer": "6", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "水果店运进19个西瓜，卖出7个后，又运进了5个，水果店现在有多少个西瓜？", "answer": "17", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "小路要买一本书，要付13元，他只有4元，还差多少元钱？", "answer": "9", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "小红有7本新书，又买了4本。一共有多少本？", "answer": "11", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "狗妈妈生了9只小狗，这9只小狗分别住宅3个小房子里，最小的房子里住了2只小狗，稍大些的房子里住了3只小狗，那么最大的房子里住几只小狗？", "answer": "4", "reasoning_step": 2, "num_digits": 1}
{"grade": 1, "question": "一根彩带长67米，第一次用去14米，第二次用去6米，现在还剩多少米？", "answer": "47", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "幼儿园小朋友叠纸鹤，小刚叠了7个，小丽叠了5个，小红叠了6个，三个小朋友共叠多少个？", "answer": "18", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "一个饭盒7元，一条毛巾4元，妈妈只带了10元，买这两样东西还差多少元钱？", "answer": "1", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "一次上体育课排队，从左边开始报数，明明报了“7”，林林报了“10”;从右边开始报数，明明报了“7”，林林应该报几？", "answer": "3", "reasoning_step": 4, "num_digits": 1}
{"grade": 1, "question": "妈妈买来30个苹果，吃了一些，还剩下20个，那么妈妈吃了多少个苹果？", "answer": "10", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "买一个书包需要30元，买一本书需要20元，买一个玩具小汽车需要40元。买一个书包和一本书一共用多少元？", "answer": "50", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "树上一共有32个桃子，猴哥哥摘了13个，猴弟弟摘了7个，树上还剩多少个桃子？", "answer": "12", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "豆豆买一个羊肉串用了4元，买一瓶汽水用了2元，一共用了多少元钱？", "answer": "6", "reasoning_step": 1, "num_digits": 1}
{"grade": 1, "question": "我和小朋友玩捉迷藏的游戏。一共有16个小朋友藏起来了，我已经找到了5个，还有几个小朋友没找到？", "answer": "11", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "王叔叔家有白兔70只，黑兔20只，灰兔40只。白兔和黑兔共有多少只？", "answer": "90", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "一个贝壳37元，一个海螺20元，各买一个需要多少钱？", "answer": "57", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "小狮子今年8岁，狮妈妈是小狮子年龄的3倍，狮妈妈今年多少岁？", "answer": "24", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "小红爸爸给她买了4支铅笔，妈妈又给她买了一盒，小红现在有16支铅笔，妈妈买的那一盒铅笔有多少根？", "answer": "12", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "老师奖励小红花，小红有4朵，王利有6朵，张鹏有5朵，他们三人一共有多少朵？", "answer": "15", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "妈咪有39元，买梨子用了8元，还剩多少元？", "answer": "31", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "一只羽毛球要2元，波波付了50元，应找回多少元？", "answer": "48", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "学校舞蹈小组有33人，其中女生有20人，男生有多少人？", "answer": "13", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "同学们参加劳动，摘黄瓜46筐，摘的白瓜比黄瓜少10筐，摘白瓜多少筐？", "answer": "36", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "一（1）班上男生有26人，女生有20人。女生比男生少多少人？", "answer": "6", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "小苹种7盆红花，又种了同样多的黄花和紫花，三种花共多少盆？", "answer": "21", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "玩具飞机价格是30元，玩具汽车价格是15元，布娃娃价格是20元。一架飞机比一辆汽车贵多少钱？", "answer": "15", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "公交车上原来有10名乘客，到站后下来4个人，又上去8个人，现在车上有多少人？", "answer": "14", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "菲菲书包有15本书，我书包有10本，菲菲书包里的书比我的多多少本?", "answer": "5", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "鸟巢里有10只小鸟，先飞走了7只，又飞来了2只，鸟巢现在有几只小鸟？", "answer": "5", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "一双球鞋29元，一双布鞋比一双球鞋便宜9元。买一双球鞋和一双布鞋一共要用多少元?", "answer": "49", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "欣欣上午吃了26块饼干，下午吃了6块，一天一共吃了多少块饼干？", "answer": "32", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "一共有14个足球，小熊抱走了4，小狗抱来了3个，现在有多少个？", "answer": "13", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "奇奇和美美一共拍了80下篮球，其中奇奇拍了50下，那么美美拍了多少下？", "answer": "30", "reasoning_step": 1, "num_digits": 2}
{"grade": 1, "question": "姐姐今年8岁，姐姐比弟弟大2岁，哥哥比弟弟大5岁，哥哥今年几岁？", "answer": "11", "reasoning_step": 2, "num_digits": 2}
{"grade": 1, "question": "一（1）班有男生23人，女生20人，一共有多少人？", "answer": "43", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "张师傅上午修了18把椅子，下午修了29把椅子，一天共修了多少把椅子？", "answer": "47", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "小小图书室有52本故事书，科技书比故事书多6本，科技书有多少本？", "answer": "58", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "李师傅用一根长52米的绳子做跳绳，每根跳绳长6米，最多可以做多少根跳绳？", "answer": "8", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "一个皮球65元，一个球拍38元，皮球比球拍多几元？", "answer": "27", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "2个茶杯装一盒，19个茶杯可以装满多少盒？", "answer": "9", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "小明和小芳一起做跳绳，小明跳了67下，小芳跳了42下。小芳再跳多少下就和小明跳的一样多？", "answer": "25", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "有60只灯泡，每盏吊灯需装7只灯泡。这些灯泡可以装多少盏吊灯？", "answer": "8", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "有32名同学排队，每4名同学站一排，可以站几排？", "answer": "8", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "小丽的妈妈买回来两根绳，一根长15米，另一根长21米，截去一些做跳绳，还剩8米，做跳绳用去多少米？", "answer": "28", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "花店运来19朵玫瑰花，每2朵扎成一束，可以扎成多少束？", "answer": "6", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "一个三角形纸片有3个角，一个四边形纸片有4个角，那么6个三角形纸片和1个四边形共有多少个角？", "answer": "22", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "有25本笔记本，每5本笔记本放在一个盒子里，需要多少个盒子？", "answer": "5", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "元旦到了，二（4）班买了42个气球装点教室。如果每7个气球扎成一束，能扎成几束？", "answer": "6", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "妈妈买了3千克苹果和5千克梨，每千克梨6元，每千克苹果3元，苹果和梨共多少钱？", "answer": "39", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "李立用彩纸做花，已经用了45张，还差8张，李立要用多少张彩纸？", "answer": "53", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "书店有3捆书，每捆2本，每本8元，书店把这两捆书全部卖完，可以卖多少元？", "answer": "48", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "小方家有45只鸡，养的鸭比鸡少5只，小方家有鸭多少只？", "answer": "40", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "关村要修一条长92米的马路，已经修了54米，还有多少米没修？", "answer": "38", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "老师买了48根跳绳，平均分给6个班，每个班分几根？", "answer": "8", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "哥哥把21本书平均分给小里、小洛和小骏，小骏分到多少本？", "answer": "7", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "一本笔记本卖4元。欢欢有27元，可以买多少本笔记本？", "answer": "6", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "我们小组共有12人，我的左边有7人，我的右边有多少人？", "answer": "4", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "小华有95枚邮票，小明有83枚邮票，小明的邮票比小华多多少枚？", "answer": "12", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "小兰的妈妈做一套衣服用去3米布，25米布可以做多少套这样的衣服？", "answer": "8", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "二年级买来科技书18本，故事书24本。把这些书分给二年级6个班，平均每个班分多少本？", "answer": "7", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "同学们今天种了25棵树，前天种了12棵树，昨天种了22棵树，昨天和今天一共种了多少棵？", "answer": "47", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "一个篮球的售价是48元，一个足球的售价是39元，一个足球的售价比一个篮球少多少元？", "answer": "9", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "杯子里有72颗糖，拿走38颗，杯子里还有多少颗糖？", "answer": "34", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "红、黄、蓝三种气球一样多，一共15个，红气球有多少个？", "answer": "5", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "小平种了26盒红花，又种了同样多的黄花，两种花共种了多少盒？", "answer": "52", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "7辆小汽车收费多少元？(停车收费标准:小汽车：6元;面包车：8元)", "answer": "42", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "爸爸用50元买了7本笔记本，商店的阿姨找了爸爸1元，那么一本笔记本多少元？", "answer": "7", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "一个足球要36元，小丽有24元，还差多少元才能购买一个足球？", "answer": "12", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "学校买来74本课外书，平均分给9个班，每个班分多少本？", "answer": "8", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "小松鼠摘松果，每个筐里放了6个松果，一共放了8筐，小松鼠一共摘了多少个松果？", "answer": "48", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "2.二（2）班筹集班费87元，买一个篮球需要52元，买清洁工具需要46元，还差多少元？", "answer": "11", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "小丽家离学校53米远，一天她上学走了23米，想起忘记带手工纸，立即返回家中，拿了手工纸再上学，这次上学她一共走了多少米？", "answer": "99", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "同学们做了5盒大红花，每盒装9朵，送三好学生32朵，还剩下多少朵？", "answer": "13", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "刘老师有5盒乒乓球，每盒装6个，同学们借走了23个，还剩多少个？", "answer": "7", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "5头牛1天吃了30筐草，平均每头牛吃几筐草?", "answer": "6", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "二年级有男生20人，女生25人，每9人一组参加跳绳比赛，一共可以分成几组？", "answer": "5", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "一只兔子有4只脚，一只鸡有2只脚，4只兔子和一只鸡一共有多少只脚？", "answer": "18", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "合唱队有男同学38人，比女同学少15人，合唱队共有多少人？", "answer": "91", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "露露：我踢了45下。佳佳：我比露露多踢18下。乐乐：我比佳佳多踢21下。乐乐踢了多少下？", "answer": "84", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "9只兔子有多少条腿？", "answer": "36", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "一批产品，4人做9天可以完成。照这样计算，这批产品要在6天完成，需要多少人？", "answer": "6", "reasoning_step": 2, "num_digits": 1}
{"grade": 2, "question": "李明红剪五角星，第一次剪了13个，第二次和第一次剪的同样多，两次一共剪了多少个？", "answer": "26", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "27个小朋友去划船，每条船可以坐3人，需要几条船？", "answer": "9", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "一辆公交车原来有乘客46人，到西门站下车17人，又上车11人，公交车上现在有多少人？", "answer": "40", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "某商店新进一批洗衣粉，共有33袋，每7袋装一箱，可以装多少箱？", "answer": "4", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "李奶奶养了3只公鸡，15只母鸡。6只鸡住一个鸡笼，一共需要多少个鸡笼？", "answer": "3", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "二年级（1）班借来15本书。平均分给5个小组，每组分多少本？", "answer": "3", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "妈妈今年32岁，小明比妈妈小19岁，小明今年多少岁？", "answer": "13", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "妈妈买来9个桃，爸爸买来15个桃，把这些桃平均放在4个盘里，每盘放多少个桃?", "answer": "6", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "饲养员养了20只公鸡，12只母鸡，每4只放入一个笼子，需要多少个笼子?", "answer": "8", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "果园里有苹果树26棵，桃树35棵，苹果树和桃树一共有多少棵？", "answer": "61", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "绘画组有4组，每组7人，书法组比绘画组少20人，书法组有多少人？", "answer": "8", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "一个花店有菊花35朵，玫瑰花27朵，百合花29朵，这三种花一共有多少朵？", "answer": "91", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "同学们分4组做风车，每组做9只。送给幼儿园18只，还有多少只？", "answer": "18", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "有36盆盆景。把这些盆景摆成4排，平均每排摆几盆？", "answer": "9", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "一筐桃的总重量是38千克，其中筐的重量是2千克，那么桃的重量是多少千克？", "answer": "36", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "公交车上原有32人，到站后上来15人，又下车4人，现在公交车上有多少人？", "answer": "43", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "共有86个西瓜，李大爷运走了39个，王叔叔运走了27个，还剩多少个？", "answer": "20", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "李老师去商店买粉笔，每盒粉笔7元，老师买了8盒，老师应支付多少元？", "answer": "56", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "养兔场养兔8000只，昨天卖出3000只。还剩下多少只？", "answer": "5000", "reasoning_step": 1, "num_digits": 4}
{"grade": 2, "question": "食品加工厂某工作人员一上午生产了59个月饼，将这些月饼每8个装一袋，可以装满多少袋？", "answer": "7", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "曾老师买回33个桔子，每个住校生分得4个，可以分给多少个住校生？", "answer": "8", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "操场上打篮球的有21人，打排球的有25人，走了18人，现在操场上打篮球和排球的一共有多少人？", "answer": "28", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "食堂买回30袋大米，吃了4天后，还剩2袋，平均每天吃多少袋？", "answer": "7", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "小明有23张卡片，小丽的卡片比小明少18张，小丽和小明一共有多少张卡片？", "answer": "28", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "有46个人住旅馆，每间房住6个人，一共可以住满多少间房？", "answer": "7", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "一筐松果有39个，每个篮子里装5个松果，至少需要多少个篮子？", "answer": "8", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "小巧把59张彩色照片贴在相册上。每页贴了7张，可以贴多少页？", "answer": "8", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "小明今年12岁，在小明出生时妈妈23岁，妈妈今年多少岁？", "answer": "35", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "妈妈买了3块蛋糕，每块6元，买蛋糕用去多少元？", "answer": "18", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "小萌在超市买了3瓶饮料花了9元，如果买5瓶饮料需要多少元？", "answer": "15", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "小明看一本故事书，每天看5页，已经看了4天，还剩16页没看完，这本故事书一共有多少页？", "answer": "36", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "从教室搬11张桌子布置会场，每个老师搬一张，两个同学抬一张。现在有2位老师还要多少个同学帮忙才能一次搬完？", "answer": "18", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "小图书室有90本故事书，借出去一些后，还剩43本，借出多少本？", "answer": "47", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "妈妈今年42岁，小明今年16岁，30年后妈妈比小明大多少岁？", "answer": "26", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "一辆汽车上午运货23箱，下午运货12箱，下午比上午少运多少箱？", "answer": "11", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "二（1）班有25名学生。平均分成5个小组，每个小组有多少名学生？", "answer": "5", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "小林妈妈有98元，给小林买书包花了26元，还剩下多少元？", "answer": "72", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "二年级一班的4名老师带领23名学生租两辆车去旅游，其中一辆车上坐了13人，另一辆车上坐了多少人？", "answer": "14", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "爷爷有11个西瓜，一个篮子正好能装3个西瓜。爷爷的西瓜能装满多少个篮子？", "answer": "3", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "一大袋洗衣粉重10千克，一大袋洗衣粉里面有2小袋，一小袋洗衣粉重多少千克？", "answer": "5", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "松鼠每天吃5个松果，吃了3天，还剩20个。原来一共有多少个松果？", "answer": "35", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "王老师做了一些花奖励给明明和东东，明明得到15朵，东东得到23朵，王老师一共做了多少朵花？", "answer": "38", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "小明有63张卡片，小丽有45张，小丽给小明多少张后两人的卡片一样多？", "answer": "18", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "同学们排成一队，小丽前面有23人，后面有19人，这一队共有多少人？", "answer": "43", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "一年级有52名同学，准备乘两辆车去公园，一辆车上已经坐了21名同学，另一辆车上坐了多少人？", "answer": "31", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "一只猴子重32千克，一只山羊比它重7千克，这只山羊重多少千克？", "answer": "39", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "有12个小朋友参加跳绳比赛，4个人一组，可以分成几组？", "answer": "3", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "实验小学三，四年级的同学去植树，三年级种了37棵，四年级种了45棵，这两个年级一共种了多少棵树？", "answer": "82", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "花店运来19朵玫瑰花，每2朵扎成一束，还剩多少朵？", "answer": "1", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "猴妈妈摘来26个桃，平均分给4只小猴，每只小猴分得多少个桃？", "answer": "6", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "小红和小明去钓鱼，小红钓了27条鱼，小明钓的比小红多钓9条鱼，小明钓了多少条？", "answer": "36", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "学校体育室有92个篮球，每班借5个，共有9个班。还剩多少个篮球？", "answer": "47", "reasoning_step": 2, "num_digits": 2}
{"grade": 2, "question": "把20粒珠子，平均放入红、黄、蓝、白四个袋子里。每个袋子放多少粒珠子？", "answer": "5", "reasoning_step": 1, "num_digits": 2}
{"grade": 2, "question": "同学们擦玻璃，每间教室有4扇窗户，共有8间教室，擦了28扇，还剩多少扇窗子没擦？", "answer": "4", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "小猴摘了84个桃子，平均分给6只猴子，每只猴子能吃到几个桃子？", "answer": "14", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "李师傅4天加工40个零件，照这样计算，今年4月份全月一共可加工多少个零件？", "answer": "300", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "某班学生48人，其中21人参加数学竞赛，13人参加作文竞赛，而且有7人既参加数学竞赛又参加作文竞赛。那么只参加数学竞赛的有多少人？", "answer": "14", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "做一套校服用布3米。66米布最多能做多少套？", "answer": "22", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "同学们收集图片。张明、李红、蔡正明、王丹、熊伟、高伟、梅芳7人收集了名山图片，吴凤、李红、王丹、戴月红、高伟这5人收集了河流图片，吴心怡、张冬、李可这3人收集了奥运图片。收集名山图片和奥运图片的共有多少人？", "answer": "10", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "食堂运来一批蔬菜，原计划每天吃45千克，10天可以消费完这批蔬菜，后来根据大家意见，每天比原计划多吃5千克，这批蔬菜可以吃多少天？", "answer": "9", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "菜店运来6吨大白菜，上午卖出3000千克，下午全部卖完．下午卖出大白菜多少千克？", "answer": "3000", "reasoning_step": 2, "num_digits": 4}
{"grade": 3, "question": "李师傅加工490个零件，前5天共加工了350个零件，照这样计算，这批零件共需要多少天能加工完？", "answer": "7", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "“金秋”体育节的检阅方队中，每班36人，某小学有71个班，一共有多少人参加检阅？", "answer": "2556", "reasoning_step": 1, "num_digits": 4}
{"grade": 3, "question": "小豪家有个书架共5层，每层放36本书，现在要空出两层放碟片，把这些书放入3层中，每层比原来多放多少本？", "answer": "24", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "妈妈把一块长1米的布分成六块。第一块长3/6米，第二块长1/6米，剩下的那块长多少米？", "answer": "2/6", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "停车场上停放着5辆面包车，停放的轿车数量是面包车的4倍。停车场上一共停放着多少辆轿车和面包车？", "answer": "25", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "某荔枝园有一块长方形的果园，长16米，宽7米。这块荔枝园的面积是多少平方米？", "answer": "112", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "王叔叔和他的爸爸、妈妈去风景区游玩。如果风景区的每张门票售价是108元，王叔叔买所有人的门票一共应付多少钱？", "answer": "324", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "一项工程，8个人工作15小时可以完成，如果12个人工作，那么多少小时可以完成？", "answer": "10", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "阿亮7：35从家步行上学，7：44到达学校。他家到学校的距离是306米。阿亮每分钟走多少米？", "answer": "34", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "希望小学三年级8个班为地震灾区捐书656本，平均每班捐书多少本？", "answer": "82", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "李春从学校放学回家走了20分钟，他平均每分钟走110米，李春家离学校有多远？", "answer": "2200", "reasoning_step": 1, "num_digits": 4}
{"grade": 3, "question": "某小区的绿化带3/9的地种了柏树，剩下的种松树，种松树的地占整个绿化带的几分之几？", "answer": "6/9", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "一个没关紧的水龙头，一星期能流失约670千克的水。平均每天大约流失多少千克的水？", "answer": "96", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "王老师在教师节收到138朵花，如果每3朵扎成一束，可以扎几束？", "answer": "46", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "刘阳今年6岁，妈妈的年龄是刘阳年龄的5倍，奶奶的年龄又是妈妈年龄的2倍，奶奶今年多少岁？", "answer": "60", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "小华带了20元钱去文具店买学习用品，买一支钢笔用去总钱数的1/2，买一本笔记本用去总钱数的1/5，一支钢笔比一只笔记本贵多少钱？", "answer": "6", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "一套明信片14张，每张售价5元，今天一共卖出52套，一共收入多少元？", "answer": "3640", "reasoning_step": 2, "num_digits": 4}
{"grade": 3, "question": "为了美化校园环境，学校买了160盆菊花，分别摆在校门口两侧，平均每侧放多少盆？", "answer": "80", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "兴兴玩具厂生产了408个布娃娃，如果每3个布娃娃装一箱，一共可以装多少箱？", "answer": "136", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "王老师买了一根长绳，现在要把这根长绳剪成几根同样长的短绳。王老师把长绳连续对折三次，然后沿折痕剪开，每根短绳占这根长绳的几分之几", "answer": "1/8", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "货车平均每小时行87千米，从甲城到乙城行了16小时，甲、乙两城相距多少千米？", "answer": "1392", "reasoning_step": 1, "num_digits": 4}
{"grade": 3, "question": "电影院共有1400个座位。东风小学共有31个班，平均每班有43人，请问这个学校的所有学生都坐下后还剩多少座位？", "answer": "67", "reasoning_step": 2, "num_digits": 4}
{"grade": 3, "question": "某工地的一项工程，原计划由30人工作，每天工作8小时，50天完工，为了提前完工，实际由40人工作，每天工作10小时，可以提前几天完工？", "answer": "20", "reasoning_step": 5, "num_digits": 2}
{"grade": 3, "question": "民兵军训，4小时走16千米，为了达到目的地，每小时多走1千米，剩下的20千米要几小时？", "answer": "4", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "商店新进560千克苹果，如果5天卖完，平均每天卖多少千克？", "answer": "112", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "李明家去年养鸭600只，今年比去年多248只，今年养了多少只鸭？", "answer": "848", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "某剧场楼上有16排座位，每排可以坐42人，平均每个座位票价5元，若座位全部坐满，这个剧院可以收入多少元？", "answer": "3360", "reasoning_step": 2, "num_digits": 4}
{"grade": 3, "question": "一块边长为2米的正方形花布，它的面积是多少平方米？", "answer": "4", "reasoning_step": 1, "num_digits": 1}
{"grade": 3, "question": "新华书店购进故事书860本，文艺书410本，购进的故事书比文艺书多多少本?", "answer": "450", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "在一块边长8米的正方形水池外围上一条宽1米的小路，求小路的面积。", "answer": "36", "reasoning_step": 4, "num_digits": 2}
{"grade": 3, "question": "学校食堂运进1240千克食用油，用了8天，还剩40千克，平均每天用多少千克食用油？", "answer": "150", "reasoning_step": 2, "num_digits": 4}
{"grade": 3, "question": "丽丽从一楼走到三楼用了18秒，照这样计算，他从一楼到七楼需要多长时间？", "answer": "54", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "小强写一篇作文一共写了608个字，正好4面，每面有8行。平均每面写多少个字？", "answer": "152", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "用篱笆围成一个长方形的养鸡场，一边利用18米长的墙壁，篱笆共长40米，求养鸡场的面积。", "answer": "198", "reasoning_step": 3, "num_digits": 3}
{"grade": 3, "question": "有6个小组，每组4人，共摘了216千克苹果，平均每人摘了多少个苹果？", "answer": "9", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "一根绳子长25米，剪10米做一根长跳绳，剩下的每2米做一根短跳绳，可以做多少根短跳绳？", "answer": "7", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "冷饮公司每天生产960支奶油冰淇淋。每盒装6支，可以装几盒？", "answer": "160", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "5个工人10天修了500米，若速度不变，20人要修4000米，需要多少天？", "answer": "20", "reasoning_step": 4, "num_digits": 4}
{"grade": 3, "question": "学校新买来780本图书，分给6个年级的学生，平均每个年级可以分到多少本？", "answer": "130", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "超市里一个书包32元，买21个这样的书包需要多少元？", "answer": "672", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "在一块长30米，宽15米得长方形地理栽白薯，平均15平方分米栽一棵，平均每棵收白薯2.5千克，这块地共收白薯多少千克？", "answer": "7500", "reasoning_step": 4, "num_digits": 4}
{"grade": 3, "question": "为了绿化校园，学校买来了60盒鲜花，这些鲜花如果摆6排，每排可以摆多少盒？", "answer": "10", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "明明跑了450米，爷爷跑的米数相当于明明跑的2倍，爸爸跑的比明明和爷爷跑的总数还多850米。爸爸跑了多少米？", "answer": "2200", "reasoning_step": 3, "num_digits": 4}
{"grade": 3, "question": "羊圈里有黑羊8只，白羊448只。白羊是黑羊的多少倍？", "answer": "56", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "一本书有66页，芳芳准备3天看完，平均每天看多少页？", "answer": "22", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "小明的爸爸在河边挖了一块长方形菜地，长8米，宽5米．他用篱笆把不靠河边的三面围了起来，至少得用多少米篱笆？", "answer": "18", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "自行车厂4名工人5小时能安装自行车80辆，现在要10小时内安装800辆自行车，需增加多少名工人？", "answer": "16", "reasoning_step": 4, "num_digits": 3}
{"grade": 3, "question": "一个长方形运动场，长250米，宽200米，早操时三年级同学绕操场跑了两圈，同学们跑了多少米？", "answer": "1800", "reasoning_step": 3, "num_digits": 4}
{"grade": 3, "question": "一块长方形菜地，长6米，宽5米，四周围上篱笆，其中有一面靠墙，篱笆至少长多少米？", "answer": "16", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "一根铁丝可以围一个边长10分米的正方形，现在用它来围一个长方形，如果这个长方形的宽是7分米，那么长是多少分米？", "answer": "13", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "第一根电线长10.4米，第二根电线比第一根短1.8米。两根电线共长多少米？", "answer": "19", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "有160名学生，每两人用一张桌子，如果把这些桌子平均放在4间教室里，每间教室放多少张桌子？", "answer": "20", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "安装一条水管，头4天安装了120米，还要15天可装完，这条水管总长多少米？", "answer": "450", "reasoning_step": 3, "num_digits": 3}
{"grade": 3, "question": "某商店里手套12.4元一副，帽子35.7元一顶。买一副手套和一顶帽子一共要多少钱?", "answer": "48.1", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "两只熊猫4天吃了56千克竹子，平均每天一只熊猫吃多少千克竹子？", "answer": "14", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "三只大象和1只小河马共重16吨，2只大象和1只小河马共重11吨。请你算出1只小河马的体重。", "answer": "1", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "停车场里卡车的辆数是客车的6倍，客车有8辆，卡车有多少辆？", "answer": "48", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "学校举行大型团体表演。有18个班参加，每班站6行，每行9人。参加表演的一共有多少人？", "answer": "972", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "学校跑道内圈200米，若沿着内圈走5圈，一共走了多少米？", "answer": "1000", "reasoning_step": 1, "num_digits": 4}
{"grade": 3, "question": "三（1）班有50人，其中25人喜欢吃苹果，22人喜欢吃橘子，13人喜欢吃苹果又喜欢吃橘子。两种水果都不喜欢的有多少人？", "answer": "16", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "从学校到木兰湖有416千米，乘客车4小时可以到达，这两客车平均每小时行驶多少千米？", "answer": "104", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "果园里有桃树120棵，梨树的棵数是桃树的3倍，梨树有多少棵？", "answer": "360", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "小林看一本故事书，3天看了24页．照这样计算，7天可以看多少页？", "answer": "56", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "一捆树苗有12棵。三(1)班要栽13捆树苗，一共要栽多少棵？", "answer": "156", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "为响应“退耕还林”的号召，学校组织植树活动，同学们共领来435棵树苗。如果每行栽8棵，可以栽多少行？", "answer": "54", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "一张写字台的长是13分米，宽是6分米。它的面积是多少平方分米？", "answer": "78", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "甲、乙两个牧童在草地上放羊。乙有5只羊，甲说：“把你的羊给我2只，我的羊就是你的5倍了。”你能猜出甲原有多少只羊吗？", "answer": "13", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "张叔叔单位每加班一次给加班费65元，这个月张叔叔一共加班14次。张叔叔这个月可得加班费多少元？", "answer": "910", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "马拉松比赛，从起点开始设服务站，以后每隔1000米设一个。当小伟跑到第5个服务站时，他跑了多少米？", "answer": "4000", "reasoning_step": 2, "num_digits": 4}
{"grade": 3, "question": "商店上午卖出上衣48件，是下午卖出的4倍，下午卖出多少件？", "answer": "12", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "3千克鲜鱼可以制成1千克鱼干。750千克鲜鱼可以制成多少千克鱼干？", "answer": "250", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "小林看一本故事书，3天看了24页．照这样计算，全书160页，多少天可以看完？", "answer": "20", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "王洋的卧室地面长是8米，宽是5米，如果用面积为4平方分米的方砖铺地，需要方砖多少块", "answer": "1000", "reasoning_step": 2, "num_digits": 4}
{"grade": 3, "question": "学校共有科技图书760本，平均放在8个书架上，每个书架有5层，每层放多少本图书？", "answer": "19", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "学校图书室新买了24套儿童读物，每套12本，这批儿童读物共有多少本？", "answer": "288", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "小东看一本故事书，第一天看了2/8，第二天看了3/8，小东已经看了这本书的几分之几？", "answer": "5/8", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "三年级(1)班有学生56人，老师把全班同学的一半平均分成4组去擦玻璃，平均每组有多少人？", "answer": "7", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "饲养小组养了425只兔子，每6只关一个笼子，最少需要多少个笼子？", "answer": "71", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "一桶油连油带桶共重200千克，倒出一半油以后，连桶重110千克，原来油重多少千克？", "answer": "180", "reasoning_step": 3, "num_digits": 3}
{"grade": 3, "question": "爷爷今年63岁，小明今年7岁，爷爷的年龄是小明的多少倍？", "answer": "9", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "某制衣车间的8名工人4小时做了160件衣服，平均每人每小时做多少套衣服？", "answer": "5", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "一张长方形纸的2/7涂蓝色,3/7涂红色,没有涂色的部分占这张纸的几分之几?", "answer": "2/7", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "有大米84袋，每袋质量相同。用卡车一次最多运7袋，至少要运多少次？", "answer": "12", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "一辆汽车从甲地开往乙地，每小时行60千米，4小时到达。若要3小时到达，则每小时需要多行多少千米？", "answer": "20", "reasoning_step": 2, "num_digits": 2}
{"grade": 3, "question": "陈敏在期中考试中语文的93分，数学得98分．如果她语文、数学、英语三科的平均成绩是97分，你知道她英语得了多少分吗？", "answer": "100", "reasoning_step": 3, "num_digits": 3}
{"grade": 3, "question": "一辆洒水车，每分钟前进200米，洒水的宽度是7米．洒水车行驶8分钟，能给多大的地面洒水？", "answer": "11200", "reasoning_step": 2, "num_digits": 5}
{"grade": 3, "question": "池塘里有360条鲤鱼，有240条鲫鱼，池塘里一共有多少条鱼？", "answer": "600", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "爷爷今年77岁，欢欢今年7岁。今年爷爷的年龄是欢欢的几倍？", "answer": "11", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "一个长方形草坪的面积是42平方米，宽是3米，现在长不变，将宽增加到5米后，面积是多少平方米？", "answer": "70", "reasoning_step": 3, "num_digits": 2}
{"grade": 3, "question": "妙可的祖奶奶今年100周岁了，她是2月29日出生的．请你算一下，妙可的祖奶奶一共过了多少个生日？", "answer": "25", "reasoning_step": 2, "num_digits": 3}
{"grade": 3, "question": "王大爷养了210只兔子，平均装入7个笼子里，每个笼子里装多少只兔子？", "answer": "30", "reasoning_step": 1, "num_digits": 3}
{"grade": 3, "question": "三年级60个同学参加植树活动，如果每5人一组，可以分成几组？", "answer": "12", "reasoning_step": 1, "num_digits": 2}
{"grade": 3, "question": "有128个苹果，平均分给8个小朋友，每个小朋友分几个？", "answer": "16", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "用面包机烤面包时，第一面烤2分钟，第二面只要烤1分钟，即烤一片面包需要3分钟，小勤的面包机一次只能放2片，他每天早上吃3片面包，至少需要烤多少分钟？", "answer": "5", "reasoning_step": 4, "num_digits": 1}
{"grade": 4, "question": "一个数的千位上是2，个位上是3，十位和十分位上都是1，百位和百分位上都是8，这个数是多少？", "answer": "2813.18", "reasoning_step": 5, "num_digits": 6}
{"grade": 4, "question": "一件上衣156元，一条裤子144元，一件上衣和一条裤子一共多少元？", "answer": "300", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "水果店购回香梨和苹果各36箱，香梨每箱45元，苹果每箱55元。香梨和苹果一共花了多少元？", "answer": "3600", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "钱红和顾颖用计算机同时合打一份1820个字的材料。钱红每分钟打字120个，顾颖每分钟打字140个。她俩打完这份材料，需用多少分钟？", "answer": "7", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "张村挖一条长1200米的水渠，计划15天挖完。实际每天比计划多挖20米，实际用了多少天？", "answer": "12", "reasoning_step": 3, "num_digits": 4}
{"grade": 4, "question": "煤场上午运来煤1.5吨，下午又运来了一些，一天共运来煤4.36吨，下午运来多少吨？", "answer": "2.86", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "某花店里菊花有300朵，是百合花的12倍，百合花有多少朵？", "answer": "25", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "运输队运水泥，一辆大车每次运90包，一辆小车每次运35包，大车和小车各运16次，一共运水泥多少包？", "answer": "2000", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "水果店运来26箱橙子，每箱36千克，运来的苹果比橙子多278千克，水果店运来橙子和苹果共多少千克？", "answer": "2150", "reasoning_step": 4, "num_digits": 4}
{"grade": 4, "question": "一辆汽车从甲地开往乙地，每小时行驶76千米，行驶了7个小时，甲、乙两地的距离是多少千米？", "answer": "532", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "把2.01扩大到原数的100倍是多少？", "answer": "201", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "莉莉有129张邮票，明明有71张邮票，每张邮票售价2元，莉莉和明明可以卖多少元？", "answer": "400", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "汽车上山的速度为每小时36千米，行了5小时到达山顶，下山时按原路返回只用了4小时。汽车下山时平均每小时行多少千米？", "answer": "45", "reasoning_step": 2, "num_digits": 2}
{"grade": 4, "question": "某工厂去年生产农具3657件，今年比去年多生产679件，今年生产了多少件农具？", "answer": "4336", "reasoning_step": 1, "num_digits": 4}
{"grade": 4, "question": "可可在家里烙饼，锅里每次可烙两张饼，两面都要烙，每面要烙2分钟，烙7张饼要用几分钟？", "answer": "14", "reasoning_step": 3, "num_digits": 2}
{"grade": 4, "question": "1吨海水含盐20千克，100千克这样的海水含盐多少吨？", "answer": "0.002", "reasoning_step": 1, "num_digits": 4}
{"grade": 4, "question": "妈妈到超市买苹果用去21.76元，买梨用去19.39元，一共用去多少元？", "answer": "41.15", "reasoning_step": 1, "num_digits": 4}
{"grade": 4, "question": "春节前，工厂后勤工作人员带1000元买桔树，买了13盆桔树，还剩155元，平均每盆桔树的价格是多少元？", "answer": "65", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "春节快到了，小明准备送给几个好朋友祝福贺卡，他了解到每张贺卡2元，于是他带着买15张贺卡的钱来到商店，发现他想买的这种贺卡每张涨了1元．请帮小明算一算，他所带的钱现在可以买多少张贺卡？", "answer": "10", "reasoning_step": 3, "num_digits": 2}
{"grade": 4, "question": "一瓶橙汁是4元钱，一箱24瓶，王叔叔这个月卖了25箱，一共卖了多少元钱？", "answer": "2400", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "修路工人要修一条850米长的公路，已经修了19天，还有185米没修，平均每天修了多少米？", "answer": "35", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "学校买足球用去96.8，买排球用去56.6，买篮球用去103.2元。学校买足球、排球和篮球共用了多少元？", "answer": "256.6", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "由4个一、4个十分之一、4个千分之一组成的数是多少？", "answer": "4.404", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "王老师带了386元钱，买足球用去了130元，剩下的钱又买了8个篮球，平均每个篮球多少元？", "answer": "32", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "张兰在读一个小数时，把小数点丢了，结果读成了一万四千零二。如果原来的小数要读出两个零。那么原来的小数是多少？", "answer": "14.002", "reasoning_step": 1, "num_digits": 5}
{"grade": 4, "question": "学校举行团体操表演，每行25位学生，一共有36行，参加团体操表演的学生一共有多少位？", "answer": "900", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "水果店运来24箱水果，每箱25千克，这些水果一共多少千克？", "answer": "600", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "小华的四次跳远成绩分别为141厘米、160厘米、161厘米、162厘米。请你帮小华算出她跳远的平均成绩是多少厘米。", "answer": "156", "reasoning_step": 4, "num_digits": 3}
{"grade": 4, "question": "一块长方形地的面积是0.8公顷，它的宽是100米．那么它的长是多少米？", "answer": "80", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "学校买来练习本3640本，分给三年级256本，分给四年级378本，分给五年级444本，分给六年级322本。这四个年级一共分得多少本练习本？", "answer": "1400", "reasoning_step": 3, "num_digits": 4}
{"grade": 4, "question": "甲在乙的后面28千米，两人同时相向而行，甲每小时行16千米，乙每小时行9千米，问甲几小时追上乙？", "answer": "4", "reasoning_step": 2, "num_digits": 2}
{"grade": 4, "question": "王老师买香蕉用了10.75元，买橘子用了9.7元，他付给售货员25元，应找回多少元？", "answer": "4.55", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "新华书店运到2车图书，每辆车装125包，每包有80本。新华书店运到图书多少本？", "answer": "20000", "reasoning_step": 2, "num_digits": 5}
{"grade": 4, "question": "102，103，105，101，109，102，106，104这8个数的平均数是？", "answer": "104", "reasoning_step": 5, "num_digits": 3}
{"grade": 4, "question": "每箱可口可乐有18瓶，每瓶3元，爸爸拿80元钱买了一箱，还剩下多少元？", "answer": "26", "reasoning_step": 2, "num_digits": 2}
{"grade": 4, "question": "把21.06扩大到原数的1000倍是多少？", "answer": "21060", "reasoning_step": 1, "num_digits": 5}
{"grade": 4, "question": "10千克花生可榨油4.3千克，1千克花生可榨油多少千克？", "answer": "0.43", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "果园里种梨树18行，每行35棵。苹果树25行，每行32棵。果园里共有果树多少棵？", "answer": "1430", "reasoning_step": 3, "num_digits": 4}
{"grade": 4, "question": "小明和小芳同院，小芳上学每分走50米，12分到学校，小明上学每分比小芳多走10米，小明几分到学校？", "answer": "10", "reasoning_step": 3, "num_digits": 2}
{"grade": 4, "question": "张明看一本572页的故事书，如果他每天看22页，需要多少天才能看完？", "answer": "26", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "修一段长960米的公路，修了6天完成了全长的一半，余下的平均每天修80米，修完这段公路一共需要多少天？", "answer": "12", "reasoning_step": 3, "num_digits": 3}
{"grade": 4, "question": "海沧野生动物园的狮子一天要吃37千克的食物，十月份一个月要吃多少千克食物？", "answer": "1147", "reasoning_step": 1, "num_digits": 4}
{"grade": 4, "question": "小明在计算除法时，错将除数36看成63，结果得到商24。请你帮他算一算，正确的商应该是多少？", "answer": "42", "reasoning_step": 2, "num_digits": 2}
{"grade": 4, "question": "一台磨面机每小时磨面800千克，照这样计算，7台磨面机每小时能磨面粉多少千克？", "answer": "5600", "reasoning_step": 1, "num_digits": 4}
{"grade": 4, "question": "同学们进行跳绳比赛，规定每人跳3分钟。小红平均比小米多跳8个，小明共跳了321个。求小红一共跳了多少个？", "answer": "345", "reasoning_step": 3, "num_digits": 3}
{"grade": 4, "question": "一束鲜花30元，买5束送一束。王阿姨一次买5束，每束便宜多少元？", "answer": "5", "reasoning_step": 4, "num_digits": 2}
{"grade": 4, "question": "学校要进行作业展览，一共要展示270件作品，每块展板最多放25件，需要多少块展板？", "answer": "11", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "四年级同学们采集松树种子，（1）班采集了31.56千克，（2）班采集了33.45千克，（3）班采集了30.2千克。三个班一共采集了多少千克？", "answer": "95.21", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "用一个平底锅煎饼，每次最多能同时放2张饼，如果煎一张饼需要2分钟（假定正、反面各需1分钟）．煎3张饼至少需要几分钟？", "answer": "3", "reasoning_step": 3, "num_digits": 1}
{"grade": 4, "question": "甲数是100，比乙数的6倍少20，乙数是多少？", "answer": "20", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "一辆大巴车从张村出发，如果每小时行驶60千米，4小时就可以到达李庄，结果只用了3个小时就到达了，这辆车实际平均每小时行驶多少千米？", "answer": "80", "reasoning_step": 2, "num_digits": 2}
{"grade": 4, "question": "光明小学花了270元买新华字典，每本新华字典6元，能买多少本新华字典？", "answer": "45", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "南京到北京的公路长840千米，一辆汽车从南京开往北京，每小时行70千米，行11小时后，还剩多少千米？", "answer": "70", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "10千克油菜籽可榨油4.1千克，100千克油菜籽可榨油多少千克？", "answer": "41", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "莉莉每天装订图书31本，照这样计算，莉莉一个月可以装订多少本图书？（一个月按30天算）", "answer": "930", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "一条新建的高速公路，长100千米，宽50米．那么这条公路占地多少公顷？", "answer": "500", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "自行车3小时行45千米，汽车4小时行216千米。汽车平均每小时比自行车平均每小时多行多少千米？", "answer": "39", "reasoning_step": 3, "num_digits": 3}
{"grade": 4, "question": "一辆汽车从沈阳开往大连，4小时后行了480千米．照这样的速度，再行3小时才能到大连．沈阳到大连有多少千米？", "answer": "840", "reasoning_step": 3, "num_digits": 3}
{"grade": 4, "question": "一瓶油连瓶重3.5千克，用去油的一半后连瓶重1.8千克，原来这瓶里的油重多少千克？", "answer": "3.4", "reasoning_step": 2, "num_digits": 2}
{"grade": 4, "question": "1000张纸叠起来厚10.2厘米，平均每张纸厚多少毫米？", "answer": "0.102", "reasoning_step": 1, "num_digits": 4}
{"grade": 4, "question": "钢铁厂计划全年生产钢铁265.8万吨，结果上半年生产了155.7万吨，下半年生产了129.9万吨。全年超过计划多少万吨？", "answer": "19.8", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "一本漫画书有316页，林林第一天看了129页，第二天看了71页，还剩下多少页没看完？", "answer": "116", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "蓝天电影城每排有125个座位，一共有76排，每张票8元，若所有的票都已经售完，电影院可以收入多少元钱？", "answer": "76000", "reasoning_step": 2, "num_digits": 5}
{"grade": 4, "question": "将一个小数先扩大到原来的100倍，再缩小到原来的1/1000，又缩小到原来的1/10后是0.038，这个小数原来是多少？", "answer": "3.8", "reasoning_step": 3, "num_digits": 5}
{"grade": 4, "question": "小欣每天坚持练习毛笔字。他4个星期共写了448个毛笔字，平均每天写多少个毛笔字？", "answer": "16", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "一个养鸡场四月份卖出3500只鸡，五月份卖出5200只鸡，这两个月平均每个月卖出了多少只鸡？", "answer": "4350", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "李雷今年重42千克，比去年增加了3.6千克，他去年的体重是多少千克？", "answer": "38.4", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "一个工程队修路，第一天修了147米，第二天修了203米，第三天修了153米，三天一共修路多少米？", "answer": "503", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "10千克小麦可磨面粉8.3千克，照这样计算，1吨小麦可磨面粉多少千克？", "answer": "830", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "一块长方形菜地，它的长是50米，宽是40米，面积是多少平方米？", "answer": "2000", "reasoning_step": 1, "num_digits": 4}
{"grade": 4, "question": "农场有一块长200米，宽150米的长方形试验田，平均每公顷产稻谷14吨，这块试验田一共产稻谷多少吨？", "answer": "42", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "公园里有牡丹花117棵，菊花59棵，兰花83棵，公园中这三种花一共有多少棵？", "answer": "259", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "一种播种机的播种宽度是3米，播种机每小时行5千米，照这样计算，2小时可以播种多少公顷？", "answer": "3", "reasoning_step": 2, "num_digits": 1}
{"grade": 4, "question": "一只平底锅上只能煎两条鱼，用它煎一条鱼需要4分钟。（正反面各2分钟），那么，煎三条鱼至少需要几分钟？", "answer": "6", "reasoning_step": 2, "num_digits": 1}
{"grade": 4, "question": "一个电脑安装小组26天安装了226台电脑，比原计划多安装了18台，原计划每天安装多少台？", "answer": "8", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "张老师带领四年级（1）班共45人去栽树，张老师一人栽5棵，男生一人栽3棵，女生一人栽2棵，总共栽树115棵，问有多少名男生？", "answer": "22", "reasoning_step": 5, "num_digits": 3}
{"grade": 4, "question": "李伟昨天先从家出发走了400米买了张报纸，然后又走了600米去超市买了一瓶洗涤剂，共用了20分钟．他的平均速度是每分钟行多少米？", "answer": "50", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "公园举办玫瑰花展览，红玫瑰和黄玫瑰都摆了22行，红玫瑰每行56盆，黄玫瑰每行44盆，一共摆了红玫瑰和黄玫瑰多少盆？", "answer": "2200", "reasoning_step": 3, "num_digits": 4}
{"grade": 4, "question": "我国发射的“嫦娥一号”探月卫星，以每秒11千米的速度飞向月球，5分钟后离开地球有多少千米？", "answer": "3300", "reasoning_step": 1, "num_digits": 4}
{"grade": 4, "question": "一条新建的高速公路，长200千米，宽40米。这条公路占地多少公顷？", "answer": "800", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "四年级同学去参观博物馆，平均分成17队，每队都是27人，参观博物馆的学生共有多少人？", "answer": "459", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "某超市一次进了164箱纯牛奶和136箱酸牛奶，每箱纯牛奶和酸牛奶都是16盒，一共进了多少盒牛奶？", "answer": "4800", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "小红早晨7时从家步行到学校去，出发2分钟后，因事又立即返回家，然后又立即从家里赶往学校，到校门口时一看钟表正好是7时20分．如果小红每分钟走60米，她家到学校有多少米？", "answer": "960", "reasoning_step": 3, "num_digits": 3}
{"grade": 4, "question": "果园里有1586棵果树，其中梨树517棵，枣树383棵，剩下的是苹果树，苹果树有多少棵？", "answer": "686", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "一支钢笔的价钱是10.50元，是一支铅笔价格的10倍，一块手表的价钱是这支铅笔价钱的1000倍，一块手表价钱是多少元？", "answer": "1050", "reasoning_step": 2, "num_digits": 4}
{"grade": 4, "question": "王叔叔从县城出发去木兰乡送化肥。去的速度是50千米∕小时，用了4小时。返回时用了2小时。原路返回时平均每小时行多少千米？", "answer": "100", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "一筒羽毛球有8个，每个羽毛球3元，体育老师买羽毛球用了720元．他买了多少筒羽毛球？", "answer": "30", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "操场原是一个长300米，宽100米的长方形，经过拓宽，长和宽都增加了100米，现在的操场比过去增加了多少平方米？", "answer": "50000", "reasoning_step": 5, "num_digits": 5}
{"grade": 4, "question": "南京到上海的水路长392千米，一艘轮船从南京开出每小时行49千米，经过几时船到上海？", "answer": "8", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "某市高中一年级学生进行野外军训。晴天每天行20千米，雨天行10千米，在12天内行程为200千米。这期间有多少天是晴天？", "answer": "8", "reasoning_step": 5, "num_digits": 3}
{"grade": 4, "question": "一只船顺水行320千米需用8小时，水流速度为每小时15千米，这只船逆水行这段路程需用几小时？", "answer": "32", "reasoning_step": 4, "num_digits": 3}
{"grade": 4, "question": "停车场停放着12辆自行车和三轮车，自行车和三轮车共有车轮26个，自行车多少辆？", "answer": "10", "reasoning_step": 5, "num_digits": 2}
{"grade": 4, "question": "甲、乙两地相距425千米，王师傅开车从甲地到乙地出差，汽车每小时行85千米，已经走了170千米，还要几小时可以到达乙地？", "answer": "3", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "100千克稻谷可碾米75千克，1吨稻谷可碾米多少千克？", "answer": "750", "reasoning_step": 1, "num_digits": 3}
{"grade": 4, "question": "一个三位小数四舍五入到百分位结果是6.60，这个小数最大是多少？", "answer": "6.604", "reasoning_step": 1, "num_digits": 4}
{"grade": 4, "question": "有一块占地1公顷的正方形菜地，如果它的边各延长100米。那么菜地的面积增加多少公顷？", "answer": "3", "reasoning_step": 4, "num_digits": 3}
{"grade": 4, "question": "有五根木条，它们的长度分别是2厘米、3厘米、4厘米、5厘米和6厘米，从它们当中选出3根木条拼成一个三角形，共有多少种不同的选法？", "answer": "7", "reasoning_step": 5, "num_digits": 1}
{"grade": 4, "question": "一块长方形的玉米地，长600米，宽300米。如果每公顷平均收玉米10吨，这块玉米地能收玉米多少吨？", "answer": "180", "reasoning_step": 2, "num_digits": 3}
{"grade": 4, "question": "一个旅游区，上午有游客367人，下午有游客298人，这一天一共有游客多少人？", "answer": "665", "reasoning_step": 1, "num_digits": 3}
{"grade": 5, "question": "一组学生植树，每人栽6棵还剩4棵；如果其中3人各栽5棵，其余每人各栽7棵，正好栽完。这一组学生有多少人？", "answer": "10", "reasoning_step": 4, "num_digits": 2}
{"grade": 5, "question": "一个底面是正方形的长方体纸盒，它的侧面展开图是一个边长是21厘米的正方形。这个纸盒的表面积是多少平方厘米", "answer": "2646", "reasoning_step": 5, "num_digits": 4}
{"grade": 5, "question": "故宫的面积是72万平方米，比天安门广场面积的2倍少16万平方米．天安门广场的面积多少万平方米？", "answer": "44", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "一根竹竿插入水中，入水部分长5/14米，入泥部分1/14米，露出水面3/14米。这根竹竿长多少米？", "answer": "9/14", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "循环使用一本教科书，一年可以节约纸张2/5千克，如果一年循环使用3本教科书，可以节约纸张多少千克？", "answer": "6/5", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "在20米花坛的一侧，每隔4米栽一棵树苗（只栽一端）。一共需要多少棵树苗？", "answer": "5", "reasoning_step": 1, "num_digits": 2}
{"grade": 5, "question": "某校长方形操场面积2800平方米，因扩建，要把宽从40米增加到50米，长不变．扩建后的操场面积比原来增加多少平方米？", "answer": "700", "reasoning_step": 3, "num_digits": 4}
{"grade": 5, "question": "一个长方体的底面是周长16厘米的正方形，高3厘米，这个长方体的体积是多少立方厘米？", "answer": "48", "reasoning_step": 3, "num_digits": 2}
{"grade": 5, "question": "红领巾公园内一条林荫大道全长800米,在它的一侧从头到尾等距离地放着41个垃圾桶,每两个垃圾桶之间相距多少米？", "answer": "20", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "五年级同学到森林公园去春游，准备乘16人的面包车或乘24人的中巴客车，不论是专乘16人的面包车，还是专乘24人的中巴车，都正好坐满。五年级至少有多少同学去春游？", "answer": "48", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "把5克盐放入355克水中，盐的质量占盐水的几分之几？", "answer": "1/72", "reasoning_step": 1, "num_digits": 3}
{"grade": 5, "question": "一根长方体木料，体积是0.078立方分米，已知木料的长是2厘米，宽是3厘米，这根木料高是多少厘米？", "answer": "13", "reasoning_step": 2, "num_digits": 4}
{"grade": 5, "question": "有11个外形完全相同的羽毛球，其中10个是正品，一个是次品，且次品稍轻一些。如果用天平称，至少称几次就一定能把这个次品找出来？", "answer": "3", "reasoning_step": 3, "num_digits": 2}
{"grade": 5, "question": "把一根绳子对折，又对折，再对折，其中的3小段占这根绳子全长的几分之几？", "answer": "3/8", "reasoning_step": 1, "num_digits": 2}
{"grade": 5, "question": "一个底面是正方形的长方体纸盒，将它的侧面展开正好是一个边长是6厘米的正方形，做这个纸盒至少需要纸板多少平方米？", "answer": "216", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "在学校组织的“有奖读书活动”中，笑笑获得了一等奖，奖品是一本科普书，共80页，她每天看10页，那么她每天看这本书的几分之几？", "answer": "1/8", "reasoning_step": 1, "num_digits": 2}
{"grade": 5, "question": "航模小组女生有4人，男生有5人，男生占小组人数的几分之几？", "answer": "5/9", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "一个圆形水池周围每隔2米栽一棵杨树,共栽了40棵,水池的周长是多少米?", "answer": "80", "reasoning_step": 1, "num_digits": 2}
{"grade": 5, "question": "一座楼房每上1层要走16级台阶，到小英家要走64级台阶，小英家住在几楼？", "answer": "5", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "一个箱子里有20本书，其中19本质量相同，另有1本质量不足，轻一些。用天平至少称几次能保证找出这本书来？", "answer": "3", "reasoning_step": 3, "num_digits": 2}
{"grade": 5, "question": "鸡兔同笼，鸡和兔的只数相同，两种动物的腿加起来共有42条，鸡和兔的数量是多少？", "answer": "7", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "爷爷的果园种着桃树和梨树，明明数了数，发觉一共有36棵，他还发现桃树的棵数是梨树的2倍．爷爷的果园里种着梨树多少棵？", "answer": "12", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "小熊、小羊和小鹿共同修建了一个小水池。小羊每2天到池边喝一次水，小鹿每3天到池边喝一次水，小熊每4天到池边喝一次水。7月1日的这一天，它们三个又同时来到池边喝水，并约定月底的某一天早上8:00准时到池边喝水。你知道这一天是7月几日吗？", "answer": "24", "reasoning_step": 3, "num_digits": 2}
{"grade": 5, "question": "在一条公路上两侧每隔16米架设一根电线杆,共用电线杆52根,这条公路全长多少米？", "answer": "400", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "工程队修一条公路，原计划每天修路1.65千米，20天可以完成。实际只用了15天，实际平均每天修路多少千米？", "answer": "2.2", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "城东小学的同学们做早操，21个同学排成一排，每相邻的两个同学之间的距离相等，第一个人到最后一个人的距离是40米，相邻两个人间距多少米？", "answer": "2", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "两座楼房之间相距56米,每隔4米栽雪松一棵,一行能栽多少棵?", "answer": "13", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "某年级同学春游时租船游湖，若每只船乘10人，还多2个座位；若每只船多坐2人，可少租一条船，这时每人可节省5角钱。租一只船需要多少钱？", "answer": "24", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "有一根木料，打算把每根锯成3段，每锯开一处，需要5分钟，全部锯完需要多少分钟?", "answer": "10", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "3年前晶晶和爸爸的年龄和是45，爸爸比晶晶大35岁，今年爸爸多大？", "answer": "43", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "20以内最大的质数与最小的质数的2倍的和是多少？", "answer": "23", "reasoning_step": 4, "num_digits": 2}
{"grade": 5, "question": "一个表面积为36平方分米的正方体，沿一个面切成4个长方体后，表面积增加了多少平方分米？", "answer": "36", "reasoning_step": 4, "num_digits": 2}
{"grade": 5, "question": "学校举行书画竞赛，四、五年级共有75人获奖，其中五年级获奖人数是四年级的1.5倍，五年级各有多少同学获奖？", "answer": "45", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "五（3）班有男生25人，女生20人，女生人数是男生人数的几分之几？(用最简分数表示)", "answer": "4/5", "reasoning_step": 1, "num_digits": 2}
{"grade": 5, "question": "一个长方体，底面是个正方形，它的高是7厘米，所有的棱长之和是100厘米，这个长方体的体积是多少立方分米？", "answer": "0.567", "reasoning_step": 2, "num_digits": 4}
{"grade": 5, "question": "长方形的砖长48厘米，宽32厘米，用这样的砖铺成一块正方形的地面，至少需要砖多少块？", "answer": "6", "reasoning_step": 3, "num_digits": 2}
{"grade": 5, "question": "战斗机的飞行速度是4000千米/时，比超音速飞机速度的3倍还多700千米，超音速飞机每小时飞行多少千米？", "answer": "1100", "reasoning_step": 5, "num_digits": 4}
{"grade": 5, "question": "跳远比赛使用的沙坑长7米，宽3米，坑内沙子的厚度为0.8米，沙坑里有沙子多少立方米？", "answer": "16.8", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "一条公路21天修完，平均每天完成这条公路的几分之几？", "answer": "1/21", "reasoning_step": 1, "num_digits": 3}
{"grade": 5, "question": "食堂运来一些煤，第一天烧了5/8吨，第二天烧了1/4吨，两天一共烧了多少吨？", "answer": "7/8", "reasoning_step": 1, "num_digits": 2}
{"grade": 5, "question": "光每秒能传播30万千米，这个距离大约比地球赤道长度的7倍还多2万千米。地球赤道大约长多少万千米？", "answer": "4", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "一个长方体玻璃容器内存水5.5升，放入一个苹果后，水位上升至6升，这个苹果的体积是多少立方分米？", "answer": "0.5", "reasoning_step": 1, "num_digits": 2}
{"grade": 5, "question": "同学们去买作文书，如果每人出8元，就多出了8元；每人出7元，就多出了4元．那么有多少个同学去买书？", "answer": "4", "reasoning_step": 5, "num_digits": 1}
{"grade": 5, "question": "王师傅加工一批零件，每天加工20个，可以提前1天完成。工作4天后，由于改进了技术，每天可多加工5个，结果提前3天完成。问：这批零件有多少个？", "answer": "280", "reasoning_step": 5, "num_digits": 3}
{"grade": 5, "question": "张老师从网上下载了一些图片，一共占硬盘空间12MB，现在他准备用软盘把这些图片拷贝到学校的电脑里，每张软盘的空间是1.44MB，那么这些图片至少需要多少张这样的软盘？", "answer": "9", "reasoning_step": 2, "num_digits": 1}
{"grade": 5, "question": "一个长方形的周长是4.8米，长是宽的3倍．这个长方形的宽是多少米？", "answer": "0.6", "reasoning_step": 3, "num_digits": 2}
{"grade": 5, "question": "用100千克油菜籽可以炸出41千克菜籽油。1千克油菜籽可以炸出多少千克菜籽油？", "answer": "41/100", "reasoning_step": 1, "num_digits": 5}
{"grade": 5, "question": "三个工程队合修一条公路，甲队修了全长的2/9，乙队修了全长的4/27，剩下的由丙队修。丙队修了全长的几分之几？", "answer": "17/27", "reasoning_step": 2, "num_digits": 4}
{"grade": 5, "question": "一根3米长的方木，截成3断后，表面积增加了3.6平方米，这根方木的体积是多少立方米？", "answer": "0.027", "reasoning_step": 3, "num_digits": 4}
{"grade": 5, "question": "在一条长50米的跑道两旁,从头到尾每隔5米插一面彩旗,一共插多少面彩旗?", "answer": "22", "reasoning_step": 3, "num_digits": 2}
{"grade": 5, "question": "一个长方体水缸，长、宽、高分别是50厘米、24厘米、40厘米，若里面放进38.4升的水，水面距上口多少厘米？", "answer": "8", "reasoning_step": 3, "num_digits": 3}
{"grade": 5, "question": "美心蛋糕房特制一种生日蛋糕，每个需要0.32千克面粉，李师傅领了4千克面粉做蛋糕。他最多可以做几个生日蛋糕？", "answer": "12", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "某厂九月份计划生产6000部智能手机，实际上半月完成3600部，上半月完成全月计划的几分之几？", "answer": "3/5", "reasoning_step": 1, "num_digits": 4}
{"grade": 5, "question": "有一块长方形的菜地，长10.5米，宽8.2米，它的周长是多少米？", "answer": "37.4", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "张华离家到县城去上学，他以每分50米的速度走了2分后，发现按这个速度走下去就要迟到8分。于是他加快了速度，每分多走10米，结果到校时，离上课还有5分。张华家到学校的路程是多少？", "answer": "4000", "reasoning_step": 5, "num_digits": 4}
{"grade": 5, "question": "放假时，老师让每位学生写毛笔字25页，现在丽丽已经写完7页，没有写完的占总数的几分之几？", "answer": "18/25", "reasoning_step": 2, "num_digits": 4}
{"grade": 5, "question": "每千克花生仁批发价7.62元，零售价8.9元，刘大伯批发价买进这种花生仁240千克，零售价卖出后，一共可得毛利多少元？", "answer": "307.2", "reasoning_step": 2, "num_digits": 4}
{"grade": 5, "question": "一个长方形的长是9厘米，等于宽的1.5倍，这个长方形的面积是多少平方厘米？", "answer": "54", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "仓库有18.6吨水泥，现在用卡车运到工地，每辆卡车运2.5吨，需要多少辆卡车？", "answer": "8", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "小明的体重是35千克，他的体重比爸爸轻8/15，小明爸爸的体重是多少千克？", "answer": "75", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "一个长方体的底面是正方形，如果高增加3厘米，就成为一个正方体，这时表面积比原来增加了48平方厘米，原来长方体的体积是多少立方厘米？", "answer": "16", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "一个长方体的体积是56立方厘米，高是4厘米，它的底面积是多少平方厘米？", "answer": "14", "reasoning_step": 1, "num_digits": 2}
{"grade": 5, "question": "学校音乐小组有64人，现需通知外出演出，如果用打电话方式，每人通话1分钟，通知到每人需要多少分钟？", "answer": "7", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "在一个长方体上截下一个体积是72立方厘米的小长方体后，剩下的部分是一个棱长为6厘米的正方体，原来这个长方体的表面积是多少平方厘米？", "answer": "264", "reasoning_step": 5, "num_digits": 3}
{"grade": 5, "question": "李大爷以相同的速度在乡间布满电话线杆的小路上散步。他从第1根电话线杆走到第12根电话线杆用了22分钟。他如果走36分钟，应走到第几根电话线杆？", "answer": "19", "reasoning_step": 4, "num_digits": 2}
{"grade": 5, "question": "一个分数约分后得到的最简分数是5/7，已知原来的分数分子和分母的和是72，原来的分数是多少？", "answer": "30/42", "reasoning_step": 2, "num_digits": 4}
{"grade": 5, "question": "红星小学陈列馆正进行二期工程改造，现工地需要52吨沙子，用一辆载重量4.5吨的汽车运8次，余下的改用一辆载重3.5吨的汽车运，还要运多少次？", "answer": "5", "reasoning_step": 3, "num_digits": 2}
{"grade": 5, "question": ".李明家正在给新房子装修，已知卫生间的面积是6m2，如果用边长是0.3m的正方形瓷砖铺地，每块瓷砖的价格是9.83元，那么大约需要多少钱？（得数保留整数）", "answer": "659", "reasoning_step": 3, "num_digits": 3}
{"grade": 5, "question": "有一个仓库，从里面量得长是25米，宽是10米，存货高度为3米。这个仓库最多能存放货物多少立方米？", "answer": "750", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "等腰三角形一边长为4，另一边长为2，则其周长为多少？", "answer": "10", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "一根长10米的彩带，每1.5米可以包扎一个礼盒，这根彩带可以包扎几个礼盒？", "answer": "6", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "动物园里有9只骆驼，6只老虎，老虎的数量是骆驼的几分之几？", "answer": "6/9", "reasoning_step": 1, "num_digits": 2}
{"grade": 5, "question": "挂钟6点钟敲6下，10秒敲完，那么9点钟敲9下，几秒敲完?", "answer": "16", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "一块长是48厘米，宽和高都是6厘米的长方体铁块，把它铸造成棱长是3厘米的正方体铁块，可以铸造多少块？（损耗不计）", "answer": "64", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "小明每天早晨以每分钟0.15千米的速度练习跑步，每天坚持练12分钟。照这样计算，2009年共跑了多少千米？（2009年365天）", "answer": "657", "reasoning_step": 2, "num_digits": 4}
{"grade": 5, "question": "爷爷今年75岁，比小明岁数的5倍还多5岁．小明今年几岁？", "answer": "14", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "一个旅游团有10人出去旅行，由于临时改变计划，要紧急通知，如果用打电话的方式，每1分钟通知1人，最少要在几分钟内通知到所有人？", "answer": "4", "reasoning_step": 4, "num_digits": 2}
{"grade": 5, "question": "一箱水果糖有31袋，其中30袋质量相同，另外有一袋质量轻些，用天平至少称几次才能保证找出较轻的一袋？", "answer": "4", "reasoning_step": 4, "num_digits": 2}
{"grade": 5, "question": "每只小船租金30元，100元钱最多租几小时？", "answer": "3", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "甲数是3/20，乙数是3/10，甲数与乙数的和是多少？", "answer": "9/20", "reasoning_step": 1, "num_digits": 3}
{"grade": 5, "question": "合唱队有若干人，若12人站一排，余5人；若15人站一排还是余5人，这个合唱队至少有多少人？", "answer": "65", "reasoning_step": 5, "num_digits": 2}
{"grade": 5, "question": "有一块平行四边形钢板，底是8.4分米，高是3.5分米．如果每平方分米钢板重0.75千克，这块钢板重多少千克？", "answer": "22.05", "reasoning_step": 2, "num_digits": 4}
{"grade": 5, "question": "一个分数是18/30，把它的分子扩大2倍，要使分数的大小不变，分母应该加上多少？", "answer": "30", "reasoning_step": 2, "num_digits": 4}
{"grade": 5, "question": "凡凡和3位小朋友一共吃了1千克香蕉，平均每位小朋友吃了多少千克？", "answer": "250", "reasoning_step": 1, "num_digits": 3}
{"grade": 5, "question": "园林工人在一段公路的一边每隔4米栽一棵树，一共栽了17棵．现在要改成每隔6米栽一棵树．那么，不用移栽的树有多少棵？", "answer": "6", "reasoning_step": 3, "num_digits": 2}
{"grade": 5, "question": "有一条长800米的公路,在公路的一侧从头到尾每隔20米栽一棵杨树,需多少棵杨树苗?", "answer": "41", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "从一楼跑到五楼有96个台阶，小芳从一楼跑到20楼供需迈多少个台阶？", "answer": "456", "reasoning_step": 4, "num_digits": 3}
{"grade": 5, "question": "有一个梯形广告牌，它的上底长4米，下底长6.5米，高为2米，在它的正面涂上广告漆，每平方米需要10元钱，涂满整个广告牌需要花费多少钱？", "answer": "105", "reasoning_step": 4, "num_digits": 3}
{"grade": 5, "question": "一本书有83000个字，如果每页排25行，每行排24个字，从第1页起，排完这些字最少需要多少张纸？", "answer": "70", "reasoning_step": 3, "num_digits": 5}
{"grade": 5, "question": "赵叔叔要完成100个零件的加工任务，已经完成了74个，剩下的零件占这批零件的几分之几？", "answer": "13/50", "reasoning_step": 2, "num_digits": 4}
{"grade": 5, "question": "一个两位数，它既有因数2，又有因数3，还有因数5，这个两位数最小是多少？", "answer": "30", "reasoning_step": 3, "num_digits": 2}
{"grade": 5, "question": "公园大门前的公路长80米，要在公路两边栽上白杨树，每两棵树相距8米（两端也要种）。园林工人共需要准备多少棵树？", "answer": "22", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "码头上有两堆货物，第一堆有29吨，第二堆有32吨，第一堆货物的重量是第二堆的几分之几？", "answer": "29/32", "reasoning_step": 1, "num_digits": 4}
{"grade": 5, "question": "有一堆同样规格的小螺丝钉，不容易数出它们的个数，称得它们的质量是1.53千克，数出100个，这100个小螺丝钉的质量是0.03千克．这堆小螺丝钉一共有多少个？", "answer": "5100", "reasoning_step": 2, "num_digits": 4}
{"grade": 5, "question": "有一批正方形的砖，排成一个大正方形，余下32块；如果将它们改排成每边比原来多一块砖的正方形，就要差49块。这批砖原有多少块？", "answer": "1632", "reasoning_step": 5, "num_digits": 4}
{"grade": 5, "question": "买一块平行四边形的玻璃，底为80厘米，高为50厘米，每平方米的玻璃售价为23元，买这块玻璃共需要多少元？", "answer": "9.2", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "一个正方体木箱的棱长是1.5米，做这个木箱需要木板多少平方米？", "answer": "13.5", "reasoning_step": 2, "num_digits": 3}
{"grade": 5, "question": "同学们去植树，3人一组或5人一组都正好分完。已知参加植树的同学在40～50人之间，则这些同学最多有多少人？", "answer": "45", "reasoning_step": 2, "num_digits": 2}
{"grade": 5, "question": "合唱队有7人，暑假老师要通知合唱队的队员去演出，若每分钟通知一个人，至少需要多少分钟才能通知到每个队员？", "answer": "3", "reasoning_step": 3, "num_digits": 1}
{"grade": 5, "question": "有7个钢珠，其中有1个较轻的是次品，用天平称至少称几次，才能保证一定能找出来？", "answer": "2", "reasoning_step": 3, "num_digits": 1}
{"grade": 6, "question": "某小学在“献爱心--为汶川地震区捐款”活动中，六年级五个班共捐款8000元，其中一班捐款1500元，二班比一班多捐款200元，三班捐款1600元，四班与五班捐款数之比是3：5．四班捐款多少元？", "answer": "1200", "reasoning_step": 5, "num_digits": 4}
{"grade": 6, "question": "小俊在东西大道上跑步，若规定向东为正。他先向东跑了800米，然后又跑了一段之后，他位于出发点西边100米处，小俊第二段跑了多少米？", "answer": "900", "reasoning_step": 1, "num_digits": 3}
{"grade": 6, "question": "A车和B车同时从甲、乙两地相向开出，经过5小时相遇．然后，它们又各自按原速原方向继续行驶3小时，这时A车离乙地还有135千米，B车离甲地还有165千米．甲、乙两地相距多少千米？", "answer": "775", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "东西两个仓库共存粮480吨，东库存粮数是西库存粮数的1.4倍，求东库存粮多少吨？", "answer": "280", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "水果店运来苹果和梨共840千克，苹果的质量是梨的3倍，苹果重多少千克？", "answer": "630", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "一个足球场长120m，宽90m，把它画在一张纸上，长画了16cm，宽应该画多少厘米？", "answer": "12", "reasoning_step": 2, "num_digits": 2}
{"grade": 6, "question": "在一个底面半径为10厘米的圆柱形容器内，倒入10厘米深的水，然后将一个底面直径4厘米，高6厘米的圆锥形铅锤放入水中，容器中水面上升多少厘米？", "answer": "0.08", "reasoning_step": 5, "num_digits": 3}
{"grade": 6, "question": "商店运来桔子、苹果、梨共360千克，桔子与苹果的重量比是5：6，梨的重量是苹果的1/6，桔子有多少千克？", "answer": "150", "reasoning_step": 4, "num_digits": 3}
{"grade": 6, "question": "A、B两个数的和是616，A的个位上是0，若把0去掉，就与B相同，那么A是多少？", "answer": "560", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "在学校的数学竞赛活动中，一共有126人获奖．其中获得一、二、三等奖的人数比是1：2：3．获得一等奖的有多少人？", "answer": "21", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "一份稿件，甲单独打字需6小时完成，乙单独打字需10小时完成，现在甲单独打若干小时后，因有事由乙接着打完，共用了7小时，那么甲打字用了多少小时？", "answer": "9/2", "reasoning_step": 5, "num_digits": 2}
{"grade": 6, "question": "在一个比例中，两个外项互为倒数，其中一个内项是5/13，另一个内项是多少?", "answer": "13/5", "reasoning_step": 1, "num_digits": 3}
{"grade": 6, "question": "某汽车制造厂上半年生产小汽车36400辆，比原计划多生产3900辆，超产百分之几？", "answer": "12%", "reasoning_step": 2, "num_digits": 5}
{"grade": 6, "question": "蕉坝中心完小六年级三个班共植树120棵，已知六（1）、（2）、（3）班植树的棵树比为1：3：2，那（1）班植树多少棵？", "answer": "20", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "有一只羊栓在草地的木桩上，绳子的长度是6米，这只羊最多可以吃到多少平方米的草？", "answer": "113.04", "reasoning_step": 2, "num_digits": 5}
{"grade": 6, "question": "甲乙两人分别从A、B两地同时相向而行，甲每分钟行100米，乙每分钟行120米，12.5分钟后两人已相遇，彼此相距150米．A、B两地相距多少米？", "answer": "2600", "reasoning_step": 5, "num_digits": 4}
{"grade": 6, "question": "某皮鞋店调进女皮鞋225双，其中男装皮鞋的双数相当于女装皮鞋的40%，这个皮鞋店调进男装皮鞋多少双？", "answer": "90", "reasoning_step": 1, "num_digits": 3}
{"grade": 6, "question": "爸爸买了5000元国债，定期两年，到期时可取回本金和利息共5873元，这种国债的年利率是多少？", "answer": "0.0873", "reasoning_step": 3, "num_digits": 5}
{"grade": 6, "question": "人体中的血液约占体重的1/13,血液里约2/3是水.小东的体重是39千克,他的血液里大约含水多少千克?", "answer": "2", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "儿童公园有一个直径是15米的圆形金鱼池，在金鱼池周围要做4圈圆形栏杆，至少要用多少钢条？", "answer": "188.4", "reasoning_step": 2, "num_digits": 4}
{"grade": 6, "question": "某工厂有一堆煤，如果每天烧0.8吨，可以烧30天。如果每天节约0.2吨，可以多烧多少天？", "answer": "10", "reasoning_step": 3, "num_digits": 2}
{"grade": 6, "question": "我校在“创建绿色循环经济示范单位”活动中，打算在生物园新挖一个直径是6米，深12分米的圆形水池。这个水池的占地面积是多少平方米？", "answer": "28.26", "reasoning_step": 3, "num_digits": 4}
{"grade": 6, "question": "某会议室需要粉刷的面积是500平方米，每平方米需要涂料0.6千克，但实际粉刷时会有损耗，因此要多准备10%。实际应准备多少千克涂料？", "answer": "330", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "有黑色、白色、黄色的小棒各8根，混放在一起，从这些小棒之中至少要取出才能保证有4根颜色相同的小棒子？", "answer": "10", "reasoning_step": 2, "num_digits": 2}
{"grade": 6, "question": "贝贝、晶晶、欢欢、迎迎、妮妮五种福娃共10个，至少买多少个福娃才可以保证一定有两个一样的福娃？", "answer": "6", "reasoning_step": 2, "num_digits": 2}
{"grade": 6, "question": "“五一”期间，小熊商场所有商品“九五折”出售。空调原价2800元，“五一”期间，空调价格比原来便宜多少元？", "answer": "140", "reasoning_step": 2, "num_digits": 4}
{"grade": 6, "question": "商店以68元一件的价格购进一批衣服，售价为每件96元。一月份共售出115件，除去各种开支874元。那么这家商店还可以赚多少元？", "answer": "2346", "reasoning_step": 3, "num_digits": 4}
{"grade": 6, "question": "甲乙丙三数之和是170，乙比甲的2倍少4，丙比甲的3倍多6，求乙是多少？", "answer": "52", "reasoning_step": 5, "num_digits": 3}
{"grade": 6, "question": "育才小学有教师108人，其中女教师人数是男教师的3倍．男教师有多少人？", "answer": "27", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "一种糖水，糖和水按照1：150配制的；现有糖100克，可以配制这样的糖水多少克？", "answer": "15100", "reasoning_step": 2, "num_digits": 5}
{"grade": 6, "question": "一种自行车前齿轮的齿数是48，后齿轮的齿数是24。如果车轮的直径是66厘米，蹬一圈大约能走多少米？", "answer": "414.48", "reasoning_step": 5, "num_digits": 5}
{"grade": 6, "question": "某款书包打八折后售价是120元，如果打九折出售，买这款书包需要多少元？", "answer": "135", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "小丽和小明进行踢毽子比赛，小明5/6分钟踢了20个，小丽2/3分钟踢了18个，小丽每分钟比小明多踢多少个？", "answer": "3", "reasoning_step": 3, "num_digits": 2}
{"grade": 6, "question": "一种衣服现在打九折出售，每件卖45元，那么原价是多少元？", "answer": "50", "reasoning_step": 1, "num_digits": 2}
{"grade": 6, "question": "某件皮衣原价是1800元，现降价270元出售，该商品是打了几折出售的？", "answer": "8.5", "reasoning_step": 2, "num_digits": 4}
{"grade": 6, "question": "甲乙两车汽车同时从A地开往B地，当甲车行了全程的1/3时，乙车正好行了60千米；当甲车到达B地时，乙车行了全程的3/5，AB两地相距多少千米？", "answer": "300", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "一个卷烟厂上半年的销售额是3000万元，如果按照销售额45%缴纳消费税，上半年应缴纳消费税款多少万元？", "answer": "1350", "reasoning_step": 1, "num_digits": 4}
{"grade": 6, "question": "一个酒瓶的高度是30厘米，底面直径是8厘米，瓶里酒的高度是20厘米，把酒瓶塞拧紧后瓶口向下。这时酒的高度是24厘米。问酒瓶容积是多少毫升？", "answer": "1306.24", "reasoning_step": 5, "num_digits": 6}
{"grade": 6, "question": "一件上衣300元，若上衣比裤子少2/3，一条裤子多少钱？", "answer": "900", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "一种糖水，糖和水按照1：150配制的；要配制这样的糖水15100克，需要水多少克？", "answer": "15000", "reasoning_step": 1, "num_digits": 5}
{"grade": 6, "question": "一种自行车前齿轮的齿数是26，后齿轮的齿数是24。车轮的半径是35厘米，蹬一圈大约能走多少米？", "answer": "2.198", "reasoning_step": 5, "num_digits": 4}
{"grade": 6, "question": "六一班共50名同学，其中考试数学的优秀率为56%，六一班数学得优的有多少人？", "answer": "28", "reasoning_step": 1, "num_digits": 2}
{"grade": 6, "question": "在某高速公路上A、B两车正好相距96千米，现两车正好同时从两个不同的服务区上同向而行，A车每小时行95千米，B车每小时行107千米．经过几小时B车可以追上A车？", "answer": "8", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "一个圆锥形麦堆，底面直径2米，高0.6米，每立方米小麦约重500千克，这堆小麦重多少千克？", "answer": "314", "reasoning_step": 5, "num_digits": 3}
{"grade": 6, "question": "张老师到银行存款4500元，年利率是2.25％，扣除20％利息税，一年后取回本息多少元?", "answer": "4581", "reasoning_step": 5, "num_digits": 4}
{"grade": 6, "question": "六年级一共有146人，其中男生人数是女生人数的2倍多56人，男生有多少人？", "answer": "116", "reasoning_step": 4, "num_digits": 3}
{"grade": 6, "question": "一根3米长的圆柱形木料，锯成3段，表面积增加16平方分米，这根木料的底面积是多少平方分米？", "answer": "4", "reasoning_step": 2, "num_digits": 2}
{"grade": 6, "question": "红卫小学有4000本图书，育新小学有5000本图书，育新小学的图书比红卫小学多百分之几？", "answer": "25%", "reasoning_step": 2, "num_digits": 4}
{"grade": 6, "question": "张阿姨在端午节一共包了蛋黄粽与肉粽75个，蛋黄粽与肉粽的比是2：3．张阿姨包了多少个肉粽？", "answer": "45", "reasoning_step": 2, "num_digits": 2}
{"grade": 6, "question": "一只蝴蝶2时飞行15.6千米，一只蜜蜂的飞行速度是这只蝴蝶的2.4倍。这只蜜蜂每时飞行多少千米？", "answer": "18.72", "reasoning_step": 2, "num_digits": 4}
{"grade": 6, "question": "爸爸的工资是6500元，扣除3500元个税免征额后的部分需要按3%的税率缴纳个人所得税，爸爸缴税后实际得到的工资是多少元？", "answer": "6410", "reasoning_step": 3, "num_digits": 4}
{"grade": 6, "question": "用一根长8米的绳子围着一棵树绕4圈，还余1.72米。这棵树的直径约是多少米？", "answer": "0.5", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "一段圆柱形钢材，它的底面半径是4厘米，高是35厘米。已知1立方厘米的钢材重7.8克，这段钢材有多重？", "answer": "13715.52", "reasoning_step": 4, "num_digits": 7}
{"grade": 6, "question": "天天妈妈的服装店实行薄利多销的原则，一般在进价的基础上提高二成后作为销售价。照这样计算，一件进价为220元的衣服应标价多少元？", "answer": "264", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "一个棱长3分米的立方体玻璃缸中装满了水。李明将其中的水全部倒入了一个长5分米，宽2分米，高4.5分米的长方体玻璃缸中。这时水与玻璃缸的接触面面积是多少？", "answer": "47.8", "reasoning_step": 5, "num_digits": 3}
{"grade": 6, "question": "一项工程，甲独做要18天完成，乙独做要15天，二人合作6天，其余的由乙单独做，还要几天做完？", "answer": "4", "reasoning_step": 4, "num_digits": 2}
{"grade": 6, "question": "纸箱中有同样的红、黄色圆锥体各5个，至少拿出几个，才能保证一定有2个圆锥体都是红色？", "answer": "3", "reasoning_step": 2, "num_digits": 1}
{"grade": 6, "question": "一项工程，由甲单独做30天完成，这项工程先由甲乙两队合做8天，余下的甲队10天完成，那么乙单独做这项工程需要多少天完成？", "answer": "20", "reasoning_step": 3, "num_digits": 2}
{"grade": 6, "question": "一个圆锥形稻谷堆，底面周长12.56米，高0.8米．如果每立方米重0.75吨，这堆稻谷重多少吨？（结果保留一位小数）", "answer": "2.5", "reasoning_step": 5, "num_digits": 4}
{"grade": 6, "question": "粮库有94吨小麦和138吨玉米，如果每天运出小麦和玉米各是9吨，问几天后剩下的玉米是小麦的3倍？", "answer": "8", "reasoning_step": 5, "num_digits": 3}
{"grade": 6, "question": "张华骑车从家到车站用了30分钟，前18分钟，按每分钟205m的速度行驶，最后提高了速度，按每分钟256m的速度行驶，张华家到车站有多少米？", "answer": "6762", "reasoning_step": 4, "num_digits": 4}
{"grade": 6, "question": "一项工作，甲独做要8天才能完成，乙独做要6天才能完成。甲乙合作，每天完成这项工作的几分之几？", "answer": "7/24", "reasoning_step": 1, "num_digits": 3}
{"grade": 6, "question": "明光工业园区进行基础设施建设，去年实际投资380万元，比计划投资节省20万元，节省了百分之几？", "answer": "5%", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "在1：6000000的地图上，量得甲、乙两地间的距离是6.5cm。一辆货车从甲地到乙地行驶了6.5小时。求这辆车的速度是多少千米每小时。", "answer": "60", "reasoning_step": 2, "num_digits": 7}
{"grade": 6, "question": "盐城市区出租车的计费标准是：起步价（3千米以内，包括3千米）7元，以后每超过1千米（不足1千米的按1千米计算）另加价1.6元。请你算一算，乘车7千米要付多少钱？", "answer": "13.4", "reasoning_step": 5, "num_digits": 3}
{"grade": 6, "question": "一个袋子里有红、黄、蓝色的袜子各10只，则最少要拿多少只才能保证其中至少有两双颜色不相同的袜子？", "answer": "13", "reasoning_step": 2, "num_digits": 2}
{"grade": 6, "question": "某乡去年水稻总产量是1500吨，今年预计比去年增产一成五。今年水稻总产量预计是多少吨？", "answer": "1725", "reasoning_step": 2, "num_digits": 4}
{"grade": 6, "question": "原价360，如果打八折销售现价是？", "answer": "288", "reasoning_step": 1, "num_digits": 3}
{"grade": 6, "question": "春运期间，深圳到武汉的飞机票涨价10%后，票价为880元，春运前的飞机票价是多少元？", "answer": "800", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "一个课外小组把6米高的竹竿直立在地上，量得影子的长度是9.6米。同时测得一个烟囱的影长是32米，那么烟囱有多高？", "answer": "20", "reasoning_step": 2, "num_digits": 2}
{"grade": 6, "question": "客车和货车分别从相距840千米的两站同时相对开出，6小时在途中相遇。已知客车每小时行80千米，货车每小时行多少千米？", "answer": "60", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "生产240个零件，师徒二人合做6天完成，师傅单独做4天能完成全部任务的2/5，徒弟每天生产零件多少个？", "answer": "16", "reasoning_step": 4, "num_digits": 3}
{"grade": 6, "question": "一个底面半径是10cm的圆柱形水桶中装水，水中放一个底面半径是5cm的圆锥形铅锤，铅锤全部淹没。取出铅锤后桶面水面下降2cm，求铅锤的高。", "answer": "24", "reasoning_step": 5, "num_digits": 2}
{"grade": 6, "question": "一个长方体玻璃鱼缸，长50厘米，宽40厘米，高30厘米。做这个鱼缸至少需要玻璃多少平方厘米？", "answer": "7400", "reasoning_step": 5, "num_digits": 4}
{"grade": 6, "question": "一辆自行车的轮胎外直径为70厘米，如果每分钟转120圈，1小时约行多少千米？（得数保留整数）", "answer": "16", "reasoning_step": 4, "num_digits": 3}
{"grade": 6, "question": "某班一天有3人请假，出勤率是94%，下午请假的3人中又有1人到校，求下午的出勤率。", "answer": "0.96", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "甲、乙两人沿一个圆形的操场的边散步，从同一地点出发，背向而行，甲每分钟走40米．乙每分钟走38.5米，4分钟相遇，这个操场的直径是多少米？", "answer": "100", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "一个手机信号发射接收塔埋在地下与露出地面部分的比是3：18，埋在地下的部分是4米，那么这个塔的全长是多少米？", "answer": "28", "reasoning_step": 2, "num_digits": 2}
{"grade": 6, "question": "一台拖拉机每小时耕地2/7公顷，3台拖拉机14小时耕地多少公顷？", "answer": "12", "reasoning_step": 2, "num_digits": 2}
{"grade": 6, "question": "挖一条水渠，在比例尺是1:300的地图上，量得这条水渠长40厘米。这条水渠实际长多少米？", "answer": "120", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "某野生动物园，一共有东北虎和白虎16只，东北虎的只数是白虎的7倍，白虎有多少只？", "answer": "2", "reasoning_step": 2, "num_digits": 2}
{"grade": 6, "question": "一套桌椅的价钱共400元，其中椅子的价钱是桌子的60%．椅子的单价是多少？", "answer": "150", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "据信息产业部统计，到目前为止，我国电话用户达3.6亿户，其中移动电话用户是固定电话用户的2倍．求我国移动电话用户是多少亿户？", "answer": "2.4", "reasoning_step": 3, "num_digits": 2}
{"grade": 6, "question": "一项工程，甲单独做需要20天完成，乙单独做需要15天完成，甲先做了5天后，剩下的甲乙合做几天可以完成？", "answer": "45/7", "reasoning_step": 4, "num_digits": 3}
{"grade": 6, "question": "妈妈和李阿姨一起到超市购物，妈妈买了5千克大米和4千克面粉共付29.2元，李阿姨也买了同样的4千克大米和5千克面粉则共付28.4元，每千克大米多少钱？", "answer": "3.6", "reasoning_step": 5, "num_digits": 3}
{"grade": 6, "question": "甲乙两班共有学生88人，甲班比乙班多8人，求甲班有多少人？", "answer": "48", "reasoning_step": 3, "num_digits": 2}
{"grade": 6, "question": "某自来水公司为鼓励节约用水，采取按月分段计算的方法收取水费，12吨以内的每吨2.5元，超过12吨的部分，每吨3.8元，李奶奶家上个月的用水量是19吨，应缴水费多少元？", "answer": "56.6", "reasoning_step": 4, "num_digits": 3}
{"grade": 6, "question": "在一个半径为10米的圆柱体储水池里，把一个长5米，宽4米的长方体铁块全部放入水中，桶里的水面上升0.5米。求这段铁块的高是多少米。", "answer": "7.85", "reasoning_step": 5, "num_digits": 3}
{"grade": 6, "question": "广场中央有一个圆柱形喷水池，底面内直径是20米，深0.8米。若在池内的侧面和池底抹一层水泥，水泥面的面积是多少平方米？（得数保留整平方米）", "answer": "365", "reasoning_step": 5, "num_digits": 3}
{"grade": 6, "question": "六年级学生参加植树劳动，男生植了160棵，女生植的树比男生3/4的多5棵。 女生植树多少棵？", "answer": "125", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "某商场一件上衣打八折后卖400元，如果打7折出售，可以卖多少元？", "answer": "350", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "爸爸买了一辆售价12万元的家用轿车，按照规定缴纳10%的车辆购置税。爸爸买这辆车一共花了多少万元？", "answer": "13.2", "reasoning_step": 2, "num_digits": 3}
{"grade": 6, "question": "华的妈妈把1000元钱存入银行，定期三年。如果年利率按5.22%计算，到期时可以取回多少元？", "answer": "1156.6", "reasoning_step": 4, "num_digits": 5}
{"grade": 6, "question": "给直径为0.75米的水缸做一个木盖，木盖的直径比缸口直径大5厘米，这个木盖的面积是多少平方米？", "answer": "0.5024", "reasoning_step": 5, "num_digits": 5}
{"grade": 6, "question": "美元和人民币的汇率为7.19：1（1美元可兑换人民币7.19元）现在有140美元，可以兑换人民币多少元？", "answer": "1006.6", "reasoning_step": 1, "num_digits": 5}
{"grade": 6, "question": "猴山上6只猴分桃，总有1只猴至少分到了5个桃。这堆桃至少有多少个呢？", "answer": "25", "reasoning_step": 2, "num_digits": 2}
{"grade": 6, "question": "一杯约250毫升的鲜牛奶大约含有3/10克的钙质，小华每天喝2杯这样的牛奶，他在整个九月份通过喝牛奶可以摄取钙质多少克？", "answer": "18", "reasoning_step": 3, "num_digits": 3}
{"grade": 6, "question": "每辆汽车平均每千米排放160克二氧化碳。赵老师为了响应市政府“绿色出行”的号召，上下班由自驾车方式改为骑自行车方式。已知赵老师家距学校20千米。赵老师每天可以减少排放多少克的二氧化碳？", "answer": "6400", "reasoning_step": 2, "num_digits": 4}
{"grade": 6, "question": "一箱香蕉重1/20吨，15箱这样的香蕉重多少吨?", "answer": "3/4", "reasoning_step": 1, "num_digits": 3}
{"grade": 6, "question": "学校运来480本图书，分别分给8个班级，每个班级分多少本？", "answer": "60", "reasoning_step": 1, "num_digits": 3}
