"""Data construction utilities for the Absolute Zero Reasoner.

This module provides functions for generating and processing training data
for code-related tasks, including input-output pairs and prediction data.
"""

from typing import Any, Dict, List, Optional, Tuple, Union, cast

import pandas as pd
import torch
from numpy import random
from torch import Tensor
from transformers import AutoTokenizer
from transformers.tokenization_utils_base import PreTrainedTokenizerBase

from absolute_zero_reasoner.data_construction.process_data import (
    boxed_instruction, instruction_following)
from absolute_zero_reasoner.data_construction.prompts import (
    get_code_problem_generator_prompt, get_code_problem_predictor_prompt)
from absolute_zero_reasoner.utils.code_utils.parsers import \
    replace_main_function_name


def get_gen_code_io_data(
    io_data: List[Dict],
    target_data_len: int,
    problem_type: str,
    instruction_type: str,
    content_max_length: int,
    io_n: int,
    output_path: str,
    split: str,
    tokenizer: AutoTokenizer,
    banned_keywords: List[str],
    banned_assertion_keywords: List[str],
    weights: List[float] | None = None,
    enable_composite_function: bool = False,
) -> None:
    """Generate and save code input-output data for training or evaluation.
    
    This function processes a list of input-output examples, applies the specified
    instruction format, and saves the results to a parquet file. It handles various
    problem types and instruction formats while respecting token length constraints.
    
    Args:
        io_data: List of dictionaries containing input-output examples.
        target_data_len: Desired number of examples to generate.
        problem_type: Type of problem to generate data for (e.g., 'code_i', 'code_o').
        instruction_type: Format of instructions ('boxed', 'answer', etc.).
        content_max_length: Maximum token length for the generated content.
        io_n: Number of input-output pairs to include per example.
        output_path: Path where the generated data will be saved as a parquet file.
        split: Dataset split ('train', 'val', or 'test').
        tokenizer: Tokenizer instance for processing text.
        banned_keywords: List of keywords to filter out from the generated data.
        banned_assertion_keywords: Keywords to filter out from assertions.
        weights: Optional weights for sampling examples. Defaults to None.
        enable_composite_function: Whether to enable composite function generation.
                                 Defaults to False.
    """
    composite_function_n_min: int = -1,
    composite_function_n_max: int = -1,
    composite_chance: float = 0.5,
    remove_after_return: bool = False,
    num_inputs: int = 10,
    remove_input_from_snippet: bool = False,
    include_references: bool = True,
):
    return_io_data = []
    if instruction_type.startswith("boxed"):
        instruction_template = boxed_instruction
    elif instruction_type.startswith("answer"):
        instruction_template = instruction_following
    elif instruction_type.startswith("none"):
        instruction_template = "{}"
    else:
        raise ValueError(f"Invalid instruction type: {instruction_type}")

    if weights is None:
        weights = [1.0] * len(io_data)
    else:
        weights = list(weights)
    probabilities = [float(w) / sum(weights) for w in weights]

    idx = 0

    while len(return_io_data) < target_data_len:
        if not include_references and problem_type != "code_f":
            chosen_references = []
        else:
            # Sample indices based on probabilities
            chosen_indices = random.choice(
                len(io_data),
                size=min(io_n, len(io_data)),
                replace=False,
                p=probabilities,
            )
            chosen_references = [io_data[i] for i in chosen_indices]
        # composite functions is not used for code_f problem type
        if (
            problem_type != "code_f"
            and composite_function_n_max > 0
            and enable_composite_function
            and random.random() <= composite_chance
            and len(chosen_references) > composite_function_n_max
        ):
            # TODO: we only allow composite to sample from code snippets without composite functions
            io_without_composite_function_indices = [
                i for i in range(len(io_data)) if not io_data[i]["composite_functions"]
            ]
            io_without_composite_function_data = [
                io_data[i] for i in io_without_composite_function_indices
            ]
            io_without_composite_function_weights = [
                probabilities[i] for i in io_without_composite_function_indices
            ]
            # normalize the weights
            io_without_composite_function_probabilities = [
                w / sum(io_without_composite_function_weights)
                for w in io_without_composite_function_weights
            ]
            # number of composite functions to sample is either fixed or random
            composite_function_n = (
                composite_function_n_min
                if composite_function_n_min == composite_function_n_max
                else random.randint(composite_function_n_min, composite_function_n_max)
            )
            # Sample indices based on probabilities
            composite_function_indices = random.choice(
                len(io_without_composite_function_data),
                size=min(composite_function_n, len(io_without_composite_function_data)),
                replace=False,
                p=io_without_composite_function_probabilities,
            )
            composite_functions = [
                io_without_composite_function_data[i]
                for i in composite_function_indices
            ]
            for i, composite_function in enumerate(composite_functions):
                # TODO: need to also replace recursively called composite functions, ignore functions that have f as the last letter, only for function call f()
                composite_functions[i]["snippet"] = replace_main_function_name(
                    composite_function["snippet"], "f", f"g_{i}"
                )
            imports = []
        else:
            composite_functions = []
            if include_references:
                imports = chosen_references[0]["imports"]
            else:
                imports = []
        io_prompt = instruction_template.format(
            get_code_problem_generator_prompt(
                problem_type=problem_type,
                reference_snippets=chosen_references,
                banned_keywords=banned_keywords,
                banned_assertion_keywords=banned_assertion_keywords,
                composite_functions=composite_functions,
                remove_after_return=remove_after_return,
                num_inputs=num_inputs,
                remove_input_from_snippet=remove_input_from_snippet,
            )
        )
        # since we have abundant judge data, we can afford to filter out some data
        # Ensure io_prompt is a list of strings
        prompts: List[str] = [io_prompt] if isinstance(io_prompt, str) else io_prompt

        # Ensure tokenizer is a PreTrainedTokenizerBase or AutoTokenizer instance
        if not isinstance(tokenizer, (PreTrainedTokenizerBase, AutoTokenizer)):
            raise ValueError(f"tokenizer must be a PreTrainedTokenizerBase or AutoTokenizer, got {type(tokenizer)}")
        try:
            tokenized = tokenizer(
                prompts,
                padding=True,
                return_tensors="pt",
                truncation=True,
                max_length=content_max_length,
            )
            if not hasattr(tokenized, "input_ids") or not isinstance(tokenized.input_ids, torch.Tensor):
                raise ValueError("Tokenizer output is missing required 'input_ids' field")
        except Exception as e:
            raise ValueError(f"Failed to tokenize input: {str(e)}")

        # Check if the tokenized input is within the allowed length
        if (
            tokenized.input_ids is not None
            and len(tokenized.input_ids) > 0
            and len(tokenized.input_ids[0]) <= content_max_length
        ):
            io_item = {
                "data_source": "gen_" + problem_type,
                "prompt": [
                    {
                        "role": "user",
                        "content": io_prompt,
                    }
                ],
                "problem": "",
                "ability": "code",
                "reward_model": {
                    "style": "rule",
                    "ground_truth": "",
                },
                "extra_info": {
                    "split": split,
                    "index": idx,
                    "metric": "gen_" + problem_type,
                    "chosen_references": chosen_references,
                    "composite_functions": composite_functions,
                    "imports": imports,
                },
            }
            return_io_data.append(io_item)
            idx += 1

        if len(return_io_data) >= target_data_len:
            break

    # if io_data is not full, we sample upsample random data
    while len(return_io_data) < target_data_len:
        io_item = io_data[random.randint(0, len(io_data))]
        return_io_data.append(io_item)

    # output to parquet
    df = pd.DataFrame(return_io_data)
    df.to_parquet(output_path)


def get_pred_code_io_data(
    io_data: List[Dict[str, Any]],
    target_data_len: int,
    problem_type: str,
    instruction_type: str,
    content_max_length: int,
    output_path: str,
    split: str,
    tokenizer: PreTrainedTokenizerBase,
) -> None:
    """Process and save prediction data with proper type checking and error handling.
    
    This function processes input-output data for prediction tasks, applies the specified
    instruction format, and saves the results to a parquet file. It handles various
    problem types while respecting token length constraints and performing input validation.
    
    Args:
        io_data: List of dictionaries containing input-output examples for prediction.
        target_data_len: Desired number of examples to process.
        problem_type: Type of problem to process (e.g., 'code_i', 'code_o', 'code_e', 'code_f').
        instruction_type: Format of instructions to apply ('boxed', 'answer', 'none').
        content_max_length: Maximum token length for the generated content.
        output_path: Path where the processed data will be saved as a parquet file.
        split: Dataset split ('train', 'val', or 'test').
        tokenizer: Tokenizer instance for processing text.
        
    Raises:
        ValueError: If problem_type is not one of the supported types or if input data
                  is invalid for the specified problem type.
    """
    # Initialize all variables that might be used in the function
    given_inputs: List[str] = []
    given_outputs: List[str] = []
    hidden_inputs: List[str] = []
    hidden_outputs: List[str] = []
    input_output_pairs: List[Tuple[str, str]] = []
    io_prompt: str = ""
    return_io_data = []
    if instruction_type.startswith("boxed"):
        instruction_template = boxed_instruction
    elif instruction_type.startswith("answer"):
        instruction_template = instruction_following
    elif instruction_type.startswith("none"):
        instruction_template = "{}"
    else:
        raise ValueError(f"Invalid instruction type: {instruction_type}")

    for idx, io_item in enumerate(io_data):
        # Initialize all possibly unbound variables at the start of each loop
        given_inputs: List[str] = []
        given_outputs: List[str] = []
        hidden_inputs: List[str] = []
        hidden_outputs: List[str] = []
        input_output_pairs: List[Tuple[str, str]] = []
        io_prompt: str = ""
        if problem_type == "code_i":
            ground_truth = io_item["input"]
        elif problem_type == "code_o":
            ground_truth = io_item["output"]
        elif problem_type == "code_e":
            ground_truth = io_item["output"]
        elif problem_type == "code_f":
            ground_truth = io_item["snippet"]
        else:
            raise ValueError(f"Invalid problem type: {problem_type}")
        if problem_type == "code_f":
            # Initialize all variables with default values at the start
            inputs: List[str] = []
            outputs: List[str] = []

            # Safely get inputs and outputs with type checking
            if isinstance(io_item.get("inputs"), list):
                inputs = [str(x) for x in io_item["inputs"] if x is not None]
            if isinstance(io_item.get("outputs"), list):
                outputs = [str(x) for x in io_item["outputs"] if x is not None]

            # Calculate splits safely
            num_given_inputs = max(0, len(inputs) // 2)
            num_given_outputs = max(0, len(outputs) // 2)

            # Get slices safely
            given_inputs = inputs[:num_given_inputs]
            given_outputs = outputs[:num_given_outputs]
            hidden_inputs = inputs[num_given_inputs:]
            hidden_outputs = outputs[num_given_outputs:]

            # Create input-output pairs with type safety
            input_output_pairs = list(zip(given_inputs, given_outputs))  # Ensure this is a list for type safety
            io_prompt = instruction_template.format(
                get_code_problem_predictor_prompt(
                    problem_type=problem_type,
                    snippet=io_item["snippet"],
                    message=io_item["message"],
                    input_output_pairs=input_output_pairs,
                )
            )
        else:
            io_prompt = instruction_template.format(
                get_code_problem_predictor_prompt(
                    problem_type=problem_type,
                    snippet=io_item["snippet"],
                    input_args=io_item["input"],
                    output=io_item["output"],
                )
            )
        # since we have abundant judge data, we can afford to filter out some data
        # Ensure io_prompt is a list of strings
        prompts = [io_prompt] if isinstance(io_prompt, str) else io_prompt
        tokenized = tokenizer(prompts, padding=True, return_tensors="pt")
        if len(tokenized.input_ids[0]) <= content_max_length:
            output_io_item = {
                "data_source": "pred_" + problem_type,
                "prompt": [
                    {
                        "role": "user",
                        "content": io_prompt,
                    }
                ],
                "problem": io_item["snippet"],
                "ability": "code",
                "reward_model": {
                    "style": "rule",
                    "ground_truth": ground_truth,
                },
                "extra_info": {
                    "split": split,
                    "index": idx,
                    "metric": "pred_" + problem_type,
                    "imports": io_item["imports"],
                },
            }
            if (
                problem_type == "code_f"
            ):  # for code_f, we need to split the inputs and outputs into given and hidden, only show part of the inputs and outputs to the model
                output_io_item["extra_info"]["given_inputs"] = given_inputs
                output_io_item["extra_info"]["given_outputs"] = given_outputs
                output_io_item["extra_info"]["hidden_inputs"] = hidden_inputs
                output_io_item["extra_info"]["hidden_outputs"] = hidden_outputs
                output_io_item["extra_info"]["message"] = io_item["message"]
            else:
                output_io_item["extra_info"]["input"] = io_item["input"]
                output_io_item["extra_info"]["output"] = io_item["output"]
            return_io_data.append(output_io_item)

        if len(return_io_data) >= target_data_len:
            break

    # if io_data is not full, we sample upsample random data
    while len(return_io_data) < target_data_len:
        io_item = return_io_data[random.randint(0, len(return_io_data))]
        return_io_data.append(io_item)

    # output to parquet
    df = pd.DataFrame(return_io_data)
    df.to_parquet(output_path)
