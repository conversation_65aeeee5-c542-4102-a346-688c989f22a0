reflection_keywords = [
    "wait", "recheck", "retry", "rethink", "re-verify", "re-evaluate", 
    "check again", "try again", "think again", "verify again",
    "evaluate again", "let's correct", "however", "alternatively",
    "reconsider", "review", "revisit", "double-check", "cross-check", 
    "second look", "reassess", "inspect", "examine again", "re-examine", 
    "revise", "adjust", "modify", "recalibrate", "pause", "reflect", 
    "clarify", "confirm", "validate again", "on second thought", 
    "in retrospect", "upon reflection", "alternately", "perhaps", 
    "maybe", "on the other hand"
]