#!/usr/bin/env python3
"""
Test script for error handling improvements.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'absolute_zero_reasoner'))

from absolute_zero_reasoner.exceptions import (
    AbsoluteZeroError, ExecutionError, SecurityError, ResourceError,
    ConfigurationError, DataError, TrainingError, ValidationError,
    ModelError, RewardError
)
from absolute_zero_reasoner.utils.logging_config import (
    setup_logging, get_logger, log_exception, log_performance, get_error_summary
)

def test_custom_exceptions():
    """Test custom exception classes."""
    print("Testing Custom Exceptions")
    print("=" * 40)

    # Test base exception
    print("\n1. Testing base AbsoluteZeroError:")
    try:
        raise AbsoluteZeroError(
            "Test error",
            error_code="TEST001",
            details={"component": "test", "value": 42}
        )
    except AbsoluteZeroError as e:
        print(f"   Caught: {e}")
        print(f"   Error code: {e.error_code}")
        print(f"   Details: {e.details}")

    # Test specific exceptions
    print("\n2. Testing specific exception types:")

    exceptions_to_test = [
        (ExecutionError, "Code execution failed", {"code": "print('test')", "timeout": True}),
        (SecurityError, "Security violation", {"violation_type": "dangerous_import"}),
        (ResourceError, "Memory limit exceeded", {"resource_type": "memory"}),
        (ConfigurationError, "Invalid config", {"config_key": "batch_size"}),
        (DataError, "Data processing failed", {"data_type": "training"}),
        (TrainingError, "Training step failed", {"step": 100, "epoch": 5}),
        (ValidationError, "Validation failed", {"field": "input", "value": None}),
        (ModelError, "Model loading failed", {"model_name": "test_model"}),
        (RewardError, "Reward computation failed", {"reward_type": "code_execution"}),
    ]

    for exc_class, message, kwargs in exceptions_to_test:
        try:
            raise exc_class(message, **kwargs)
        except AbsoluteZeroError as e:
            print(f"   {exc_class.__name__}: {e}")

def test_logging_system():
    """Test enhanced logging system."""
    print("\n\nTesting Enhanced Logging System")
    print("=" * 40)

    # Setup logging
    logger = setup_logging(
        log_level="INFO",
        log_dir="logs",
        enable_structured_logging=True,
        enable_error_tracking=True
    )

    test_logger = get_logger("test_module")

    print("\n1. Testing structured logging:")
    test_logger.info("This is an info message")
    test_logger.warning("This is a warning message")

    print("\n2. Testing error logging with context:")
    try:
        # Simulate an error
        x = 1 / 0
    except Exception as e:
        log_exception(test_logger, e, {"operation": "division", "values": [1, 0]})

    print("\n3. Testing performance logging:")
    import time
    start_time = time.time()
    time.sleep(0.1)  # Simulate work
    duration = time.time() - start_time
    log_performance(test_logger, "test_operation", duration, items_processed=100)

    print("\n4. Testing error tracking:")
    # Generate some test errors
    for i in range(3):
        try:
            if i == 0:
                raise ValueError("Test value error")
            elif i == 1:
                raise TypeError("Test type error")
            else:
                raise RuntimeError("Test runtime error")
        except Exception as e:
            log_exception(test_logger, e, {"iteration": i})

    # Get error summary
    error_summary = get_error_summary()
    print(f"   Error summary: {error_summary}")

def test_error_recovery():
    """Test error recovery mechanisms."""
    print("\n\nTesting Error Recovery")
    print("=" * 40)

    def risky_operation(should_fail: bool = False):
        """Simulate a risky operation that might fail."""
        if should_fail:
            raise ExecutionError("Simulated execution failure", code="test_code")
        return "Success!"

    def safe_operation_wrapper(operation, *args, **kwargs):
        """Wrapper that provides error recovery."""
        try:
            return operation(*args, **kwargs), None
        except AbsoluteZeroError as e:
            print(f"   Caught AbsoluteZero error: {e}")
            return None, e
        except Exception as e:
            print(f"   Caught unexpected error: {e}")
            return None, e

    print("\n1. Testing successful operation:")
    result, error = safe_operation_wrapper(risky_operation, should_fail=False)
    print(f"   Result: {result}, Error: {error}")

    print("\n2. Testing failed operation with recovery:")
    result, error = safe_operation_wrapper(risky_operation, should_fail=True)
    print(f"   Result: {result}, Error type: {type(error).__name__}")

def main():
    """Main test execution."""
    print("Absolute Zero - Error Handling Tests")
    print("=" * 50)

    try:
        test_custom_exceptions()
        test_logging_system()
        test_error_recovery()

        print("\n" + "=" * 50)
        print("All error handling tests completed successfully!")

    except Exception as e:
        print(f"Test execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()