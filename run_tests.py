#!/usr/bin/env python3
"""
Test runner for Absolute Zero Reasoner.

This script runs all tests and provides comprehensive test reporting.
"""

import sys
import os
import unittest
import time
from pathlib import Path
from io import StringIO

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def discover_and_run_tests(test_dir="tests", pattern="test_*.py", verbosity=2):
    """
    Discover and run all tests in the specified directory.

    Args:
        test_dir: Directory containing test files
        pattern: Pattern to match test files
        verbosity: Test output verbosity level

    Returns:
        TestResult object
    """
    # Discover tests
    loader = unittest.TestLoader()
    start_dir = project_root / test_dir

    if not start_dir.exists():
        print(f"Test directory {start_dir} does not exist!")
        return None

    suite = loader.discover(str(start_dir), pattern=pattern)

    # Run tests
    runner = unittest.TextTestRunner(
        verbosity=verbosity,
        stream=sys.stdout,
        buffer=True
    )

    print(f"Running tests from {start_dir}")
    print("=" * 70)

    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()

    # Print summary
    print("\n" + "=" * 70)
    print("TEST SUMMARY")
    print("=" * 70)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"Execution time: {end_time - start_time:.2f} seconds")

    # Print detailed failure information
    if result.failures:
        print("\n" + "=" * 70)
        print("FAILURES")
        print("=" * 70)
        for test, traceback in result.failures:
            print(f"\nFAILED: {test}")
            print("-" * 50)
            print(traceback)

    if result.errors:
        print("\n" + "=" * 70)
        print("ERRORS")
        print("=" * 70)
        for test, traceback in result.errors:
            print(f"\nERROR: {test}")
            print("-" * 50)
            print(traceback)

    return result

def run_specific_test(test_module, test_class=None, test_method=None):
    """
    Run a specific test module, class, or method.

    Args:
        test_module: Name of the test module (e.g., 'test_validation')
        test_class: Optional test class name
        test_method: Optional test method name
    """
    # Import the test module
    try:
        module = __import__(f"tests.{test_module}", fromlist=[test_module])
    except ImportError as e:
        print(f"Failed to import test module {test_module}: {e}")
        return None

    # Create test suite
    loader = unittest.TestLoader()

    if test_method and test_class:
        # Run specific test method
        suite = loader.loadTestsFromName(f"{test_class}.{test_method}", module)
    elif test_class:
        # Run specific test class
        suite = loader.loadTestsFromName(test_class, module)
    else:
        # Run entire module
        suite = loader.loadTestsFromModule(module)

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    return runner.run(suite)

def check_test_coverage():
    """
    Check which components have tests and which don't.
    """
    print("TEST COVERAGE ANALYSIS")
    print("=" * 70)

    # List of main modules that should have tests
    main_modules = [
        "validation",
        "exceptions",
        "container",
        "interfaces",
        "utils.code_utils.python_executor",
        "utils.logging_config",
        "rewards.reward_managers",
        "trainer.ppo.azr_ray_trainer"
    ]

    test_dir = project_root / "tests"
    existing_tests = []

    if test_dir.exists():
        for test_file in test_dir.glob("test_*.py"):
            test_name = test_file.stem.replace("test_", "")
            existing_tests.append(test_name)

    print("Modules with tests:")
    for test in existing_tests:
        print(f"  ✓ {test}")

    print("\nModules without tests:")
    for module in main_modules:
        module_name = module.split(".")[-1]
        if module_name not in existing_tests:
            print(f"  ✗ {module}")

    coverage_percent = len(existing_tests) / len(main_modules) * 100
    print(f"\nTest coverage: {coverage_percent:.1f}% ({len(existing_tests)}/{len(main_modules)} modules)")

def main():
    """Main test runner."""
    import argparse

    parser = argparse.ArgumentParser(description="Run tests for Absolute Zero Reasoner")
    parser.add_argument("--module", "-m", help="Run tests for specific module")
    parser.add_argument("--class", "-c", dest="test_class", help="Run specific test class")
    parser.add_argument("--method", "-t", dest="test_method", help="Run specific test method")
    parser.add_argument("--coverage", action="store_true", help="Show test coverage analysis")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--quiet", "-q", action="store_true", help="Quiet output")

    args = parser.parse_args()

    # Set verbosity
    verbosity = 2
    if args.verbose:
        verbosity = 3
    elif args.quiet:
        verbosity = 1

    print("Absolute Zero Reasoner - Test Runner")
    print("=" * 70)

    if args.coverage:
        check_test_coverage()
        return

    if args.module:
        # Run specific module
        result = run_specific_test(args.module, args.test_class, args.test_method)
    else:
        # Run all tests
        result = discover_and_run_tests(verbosity=verbosity)

    if result is None:
        sys.exit(1)

    # Exit with error code if tests failed
    if result.failures or result.errors:
        sys.exit(1)
    else:
        print("\n🎉 All tests passed!")
        sys.exit(0)

if __name__ == "__main__":
    main()