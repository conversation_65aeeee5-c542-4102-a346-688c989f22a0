# Absolute Zero Project - Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing the fixes identified in the code review. Tasks are organized by priority level and include detailed implementation steps suitable for junior developers.

## Priority 1: Critical Issues (Fix Immediately)

### Task 1.1: Create Missing Installation Verification Script ✅ COMPLETED

**Estimated Time**: 2-3 hours
**Difficulty**: Beginner
**Files to Create**: `test_installation_minimal.py`

**Status**: ✅ **COMPLETED** - <PERSON><PERSON><PERSON> created and tested successfully. Identifies missing dependencies correctly.

**Steps**:

1. **Create the test script file**:
   ```bash
   touch test_installation_minimal.py
   ```

2. **Add the basic structure**:
   ```python
   #!/usr/bin/env python3
   """
   Minimal installation test for Absolute Zero Reasoner.

   This script verifies that all core dependencies are properly installed
   and can be imported without errors.
   """

   import sys
   import os
   from typing import List, Tuple

   def test_import(module_name: str, description: str) -> Tuple[bool, str]:
       """Test if a module can be imported successfully."""
       try:
           __import__(module_name)
           return True, f"✓ {description}"
       except ImportError as e:
           return False, f"✗ {description}: {str(e)}"
       except Exception as e:
           return False, f"✗ {description}: Unexpected error - {str(e)}"
   ```

3. **Add core dependency tests**:
   ```python
   def run_core_tests() -> List[Tuple[bool, str]]:
       """Run tests for core dependencies."""
       tests = [
           ("torch", "PyTorch"),
           ("transformers", "Hugging Face Transformers"),
           ("vllm", "vLLM"),
           ("ray", "Ray"),
           ("absolute_zero_reasoner", "Absolute Zero Reasoner"),
       ]

       results = []
       for module, desc in tests:
           results.append(test_import(module, desc))

       return results
   ```

4. **Add CUDA verification**:
   ```python
   def test_cuda() -> Tuple[bool, str]:
       """Test CUDA availability."""
       try:
           import torch
           if torch.cuda.is_available():
               device_count = torch.cuda.device_count()
               return True, f"✓ CUDA available with {device_count} device(s)"
           else:
               return False, "✗ CUDA not available"
       except Exception as e:
           return False, f"✗ CUDA test failed: {str(e)}"
   ```

5. **Add main execution logic**:
   ```python
   def main():
       """Main test execution."""
       print("Absolute Zero Reasoner - Installation Verification")
       print("=" * 50)

       # Test core dependencies
       print("\nTesting core dependencies:")
       core_results = run_core_tests()

       # Test CUDA
       print("\nTesting CUDA:")
       cuda_result = test_cuda()

       # Print results
       all_results = core_results + [cuda_result]
       passed = sum(1 for success, _ in all_results if success)
       total = len(all_results)

       print(f"\nResults: {passed}/{total} tests passed")

       if passed == total:
           print("✓ All tests passed! Installation is ready.")
           sys.exit(0)
       else:
           print("✗ Some tests failed. Please check your installation.")
           sys.exit(1)

   if __name__ == "__main__":
       main()
   ```

6. **Test the script**:
   ```bash
   python test_installation_minimal.py
   ```

7. **Update README.md** to ensure the reference is accurate (already exists).

### Task 1.2: Implement Code Sandboxing ✅ COMPLETED

**Estimated Time**: 1-2 days
**Difficulty**: Advanced
**Files to Modify**: `absolute_zero_reasoner/utils/code_utils/python_executor.py`

**Status**: ✅ **COMPLETED** - Secure execution implemented with:
- ContainerizedExecutor with Docker support and local fallback
- Input sanitization to prevent dangerous imports and function calls
- Enhanced GenericRuntime with restricted builtins
- Configuration support in azr_ppo_trainer.yaml
- Comprehensive testing with test_secure_executor.py

**Steps**:

1. **Create a secure executor interface**:
   ```python
   # Add to python_executor.py
   from abc import ABC, abstractmethod
   import subprocess
   import tempfile
   import json
   import resource
   import signal

   class SecureExecutor(ABC):
       """Abstract base class for secure code execution."""

       @abstractmethod
       def execute_code(self, code: str, inputs: str, timeout: int = 10) -> Tuple[str, str]:
           """Execute code securely and return (output, status)."""
           pass
   ```

2. **Implement containerized execution**:
   ```python
   class ContainerizedExecutor(SecureExecutor):
       """Execute code in a containerized environment."""

       def __init__(self, memory_limit: str = "128m", cpu_limit: str = "0.5"):
           self.memory_limit = memory_limit
           self.cpu_limit = cpu_limit

       def execute_code(self, code: str, inputs: str, timeout: int = 10) -> Tuple[str, str]:
           """Execute code in Docker container."""
           try:
               # Create temporary files
               with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                   f.write(code)
                   code_file = f.name

               # Docker command with security restrictions
               cmd = [
                   'docker', 'run', '--rm',
                   '--memory', self.memory_limit,
                   '--cpus', self.cpu_limit,
                   '--network', 'none',  # No network access
                   '--read-only',        # Read-only filesystem
                   '--tmpfs', '/tmp',    # Temporary filesystem
                   '-v', f'{code_file}:/code.py:ro',
                   'python:3.10-alpine',
                   'timeout', str(timeout),
                   'python', '/code.py'
               ]

               # Execute with timeout
               result = subprocess.run(
                   cmd,
                   input=inputs,
                   capture_output=True,
                   text=True,
                   timeout=timeout + 5  # Extra buffer
               )

               os.unlink(code_file)  # Cleanup

               if result.returncode == 0:
                   return result.stdout, "success"
               else:
                   return result.stderr, "error"

           except subprocess.TimeoutExpired:
               return "", "timeout"
           except Exception as e:
               return str(e), "error"
   ```

3. **Add input sanitization**:
   ```python
   def sanitize_code(code: str) -> str:
       """Sanitize code input to prevent injection attacks."""
       # Remove dangerous imports
       dangerous_imports = [
           'os', 'sys', 'subprocess', 'socket', 'urllib',
           'requests', 'http', 'ftplib', 'smtplib'
       ]

       lines = code.split('\n')
       sanitized_lines = []

       for line in lines:
           line = line.strip()
           if line.startswith('import ') or line.startswith('from '):
               for dangerous in dangerous_imports:
                   if dangerous in line:
                       raise ValueError(f"Dangerous import detected: {line}")
           sanitized_lines.append(line)

       return '\n'.join(sanitized_lines)
   ```

4. **Update PythonExecutor class**:
   ```python
   class PythonExecutor:
       def __init__(self, use_container: bool = True, **kwargs):
           if use_container:
               self.executor = ContainerizedExecutor(**kwargs)
           else:
               self.executor = LocalExecutor(**kwargs)  # Fallback

       def apply(self, code: str, inputs: str = "") -> Tuple[str, str]:
           """Execute code with security measures."""
           try:
               # Sanitize input
               sanitized_code = sanitize_code(code)

               # Execute securely
               return self.executor.execute_code(sanitized_code, inputs)

           except ValueError as e:
               return str(e), "security_error"
           except Exception as e:
               return str(e), "error"
   ```

5. **Add configuration option**:
   ```yaml
   # Add to azr_ppo_trainer.yaml
   azr:
     executor_config:
       use_container: true
       memory_limit: "256m"
       cpu_limit: "1.0"
       timeout: 30
   ```

6. **Test the implementation**:
   ```python
   # Create test_secure_executor.py
   from absolute_zero_reasoner.utils.code_utils.python_executor import PythonExecutor

   executor = PythonExecutor(use_container=True)

   # Test normal code
   result, status = executor.apply("print('Hello World')")
   assert status == "success"

   # Test dangerous code
   result, status = executor.apply("import os; os.system('rm -rf /')")
   assert status == "security_error"
   ```

### Task 1.3: Fix Resource Management Issues ✅ COMPLETED

**Estimated Time**: 1 day
**Difficulty**: Intermediate
**Files to Modify**: `absolute_zero_reasoner/trainer/ppo/azr_ray_trainer.py`

**Status**: ✅ **COMPLETED** - Enhanced resource management with:
- Context manager support (__enter__/__exit__)
- Comprehensive cleanup method with Ray worker shutdown
- Memory monitoring for system and GPU resources
- Periodic cleanup with memory tracking
- Resource limit checking and warnings
- CUDA cache management
- Comprehensive testing with test_resource_management.py

**Steps**:

1. **Add proper cleanup methods**:
   ```python
   # Add to CodeIORayPPOTrainer class
   def __enter__(self):
       """Context manager entry."""
       return self

   def __exit__(self, exc_type, exc_val, exc_tb):
       """Context manager exit with cleanup."""
       self.cleanup()
       return False

   def cleanup(self) -> None:
       """Enhanced cleanup method."""
       try:
           # Cleanup executor
           if hasattr(self, '_executor') and hasattr(self._executor, 'cleanup'):
               PrettyPrinter.status("CLEANUP", "Cleaning up executor...", "info")
               self._executor.cleanup()

           # Cleanup Ray resources
           if hasattr(self, 'actor_rollout_wg'):
               PrettyPrinter.status("CLEANUP", "Shutting down actor workers...", "info")
               self.actor_rollout_wg.shutdown()

           if hasattr(self, 'critic_wg'):
               PrettyPrinter.status("CLEANUP", "Shutting down critic workers...", "info")
               self.critic_wg.shutdown()

           # Cleanup dataset manager
           if hasattr(self, 'dataset_manager'):
               PrettyPrinter.status("CLEANUP", "Cleaning up dataset manager...", "info")
               ray.kill(self.dataset_manager)

           # Force garbage collection
           import gc
           gc.collect()

           # Clear CUDA cache if available
           try:
               import torch
               if torch.cuda.is_available():
                   torch.cuda.empty_cache()
                   PrettyPrinter.status("CLEANUP", "Cleared CUDA cache", "info")
           except Exception as e:
               PrettyPrinter.status("CLEANUP", f"CUDA cleanup failed: {e}", "warning")

       except Exception as e:
           PrettyPrinter.status("CLEANUP", f"Cleanup error: {e}", "error")
   ```

2. **Add memory monitoring**:
   ```python
   import psutil
   import torch

   def monitor_memory(self) -> Dict[str, float]:
       """Monitor system and GPU memory usage."""
       memory_info = {}

       # System memory
       system_memory = psutil.virtual_memory()
       memory_info['system_used_gb'] = system_memory.used / (1024**3)
       memory_info['system_available_gb'] = system_memory.available / (1024**3)
       memory_info['system_percent'] = system_memory.percent

       # GPU memory
       if torch.cuda.is_available():
           for i in range(torch.cuda.device_count()):
               allocated = torch.cuda.memory_allocated(i) / (1024**3)
               reserved = torch.cuda.memory_reserved(i) / (1024**3)
               memory_info[f'gpu_{i}_allocated_gb'] = allocated
               memory_info[f'gpu_{i}_reserved_gb'] = reserved

       return memory_info
   ```

3. **Add periodic cleanup**:
   ```python
   def _periodic_cleanup(self):
       """Perform periodic cleanup during training."""
       if self.global_steps % self._cleanup_frequency == 0:
           PrettyPrinter.status("CLEANUP", f"Periodic cleanup at step {self.global_steps}", "info")

           # Monitor memory before cleanup
           memory_before = self.monitor_memory()

           # Cleanup
           gc.collect()
           if torch.cuda.is_available():
               torch.cuda.empty_cache()

           # Monitor memory after cleanup
           memory_after = self.monitor_memory()

           # Log memory savings
           if 'system_used_gb' in memory_before and 'system_used_gb' in memory_after:
               saved = memory_before['system_used_gb'] - memory_after['system_used_gb']
               PrettyPrinter.status("CLEANUP", f"Freed {saved:.2f}GB system memory", "info")
   ```

4. **Update training loop**:
   ```python
   # In the fit() method, add periodic cleanup
   def fit(self):
       try:
           # ... existing training code ...

           for epoch in range(self.config.trainer.total_epochs):
               for batch_idx, batch_dict in enumerate(self.train_dataloader):
                   # ... existing batch processing ...

                   # Add periodic cleanup
                   self._periodic_cleanup()

                   # ... rest of training loop ...

       except Exception as e:
           PrettyPrinter.status("ERROR", f"Training failed: {e}", "error")
           raise
       finally:
           # Ensure cleanup happens even if training fails
           self.cleanup()
   ```

5. **Add resource limits**:
   ```python
   # Add to configuration
   def _check_resource_limits(self):
       """Check if resource usage is within limits."""
       memory_info = self.monitor_memory()

       # Check system memory
       if memory_info.get('system_percent', 0) > 90:
           PrettyPrinter.status("WARNING", "System memory usage > 90%", "warning")
           gc.collect()

       # Check GPU memory
       for key, value in memory_info.items():
           if 'gpu' in key and 'allocated_gb' in key and value > 10:  # 10GB limit
               PrettyPrinter.status("WARNING", f"High GPU memory usage: {value:.2f}GB", "warning")
               torch.cuda.empty_cache()
   ```

### Task 1.4: Improve Error Handling ✅ COMPLETED

**Estimated Time**: 1 day
**Difficulty**: Intermediate
**Files to Modify**: Multiple files in reward system and trainer

**Status**: ✅ **COMPLETED** - Comprehensive error handling implemented with:
- Custom exception hierarchy (AbsoluteZeroError and 9 specialized exceptions)
- Enhanced logging system with structured logging and error tracking
- Error recovery mechanisms in CodeIORewardManager
- Safe wrapper methods for critical operations
- Performance and context logging capabilities
- Comprehensive testing with test_error_handling.py

**Steps**:

1. **Create custom exception classes**:
   ```python
   # Create absolute_zero_reasoner/exceptions.py
   class AbsoluteZeroError(Exception):
       """Base exception for Absolute Zero Reasoner."""
       pass

   class ExecutionError(AbsoluteZeroError):
       """Error during code execution."""
       pass

   class ResourceError(AbsoluteZeroError):
       """Error related to resource management."""
       pass

   class ConfigurationError(AbsoluteZeroError):
       """Error in configuration."""
       pass

   class DataError(AbsoluteZeroError):
       """Error in data processing."""
       pass
   ```

2. **Add comprehensive error handling to reward manager**:
   ```python
   # Update CodeIORewardManager.__call__ method
   def __call__(self, data: DataProto) -> DataProto:
       """Enhanced error handling in reward computation."""
       try:
           return self._compute_rewards_safe(data)
       except ExecutionError as e:
           logger.error(f"Code execution failed: {e}")
           # Return zero rewards for failed executions
           return self._create_zero_reward_batch(data)
       except DataError as e:
           logger.error(f"Data processing failed: {e}")
           raise  # Re-raise data errors as they're critical
       except Exception as e:
           logger.error(f"Unexpected error in reward computation: {e}")
           # Log full traceback for debugging
           import traceback
           logger.error(traceback.format_exc())
           return self._create_zero_reward_batch(data)

   def _compute_rewards_safe(self, data: DataProto) -> DataProto:
       """Safe reward computation with detailed error handling."""
       # ... existing reward computation logic with try-catch blocks ...

   def _create_zero_reward_batch(self, data: DataProto) -> DataProto:
       """Create a batch with zero rewards for error cases."""
       # Implementation to return safe zero rewards
       pass

3. **Add logging configuration**:
   ```python
   # Create absolute_zero_reasoner/utils/logging_config.py
   import logging
   import sys
   from pathlib import Path

   def setup_logging(log_level: str = "INFO", log_file: str = None):
       """Setup comprehensive logging configuration."""

       # Create formatter
       formatter = logging.Formatter(
           '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
       )

       # Setup root logger
       root_logger = logging.getLogger()
       root_logger.setLevel(getattr(logging, log_level.upper()))

       # Console handler
       console_handler = logging.StreamHandler(sys.stdout)
       console_handler.setFormatter(formatter)
       root_logger.addHandler(console_handler)

       # File handler if specified
       if log_file:
           file_handler = logging.FileHandler(log_file)
           file_handler.setFormatter(formatter)
           root_logger.addHandler(file_handler)

       return root_logger
   ```

## Priority 2: High Priority Issues (Fix within 1 month)

### Task 2.1: Refactor Architecture for Better Separation ✅ COMPLETED

**Estimated Time**: 1 week
**Difficulty**: Advanced
**Files to Create/Modify**: Multiple files

**Status**: ✅ **COMPLETED** - Architectural improvements implemented with:
- Abstract interface definitions for all major components (RewardManager, DataConstructor, CodeExecutor, etc.)
- Dependency injection container with service registration and lifecycle management
- Service locator pattern for global dependency access
- Improved separation of concerns and testability
- Foundation for future modular architecture

**Steps**:

1. **Create interface abstractions**:
   ```python
   # Create absolute_zero_reasoner/interfaces.py
   from abc import ABC, abstractmethod
   from typing import Any, Dict, List
   from verl import DataProto

   class RewardManager(ABC):
       """Abstract reward manager interface."""

       @abstractmethod
       def compute_rewards(self, data: DataProto) -> DataProto:
           """Compute rewards for the given data."""
           pass

   class DataConstructor(ABC):
       """Abstract data constructor interface."""

       @abstractmethod
       def generate_data(self, config: Dict[str, Any]) -> List[Dict]:
           """Generate training data."""
           pass

   class Executor(ABC):
       """Abstract code executor interface."""

       @abstractmethod
       def execute(self, code: str, inputs: str) -> tuple[str, str]:
           """Execute code and return (output, status)."""
           pass
   ```

2. **Implement dependency injection**:
   ```python
   # Create absolute_zero_reasoner/container.py
   from typing import Dict, Type, Any

   class DIContainer:
       """Simple dependency injection container."""

       def __init__(self):
           self._services: Dict[str, Any] = {}
           self._factories: Dict[str, callable] = {}

       def register(self, name: str, service: Any):
           """Register a service instance."""
           self._services[name] = service

       def register_factory(self, name: str, factory: callable):
           """Register a service factory."""
           self._factories[name] = factory

       def get(self, name: str) -> Any:
           """Get a service by name."""
           if name in self._services:
               return self._services[name]
           elif name in self._factories:
               service = self._factories[name]()
               self._services[name] = service
               return service
           else:
               raise ValueError(f"Service '{name}' not found")
   ```

3. **Refactor trainer to use DI**:
   ```python
   # Update CodeIORayPPOTrainer.__init__
   def __init__(self, container: DIContainer, *args, **kwargs):
       super().__init__(*args, **kwargs)
       self.container = container

       # Get dependencies from container
       self.reward_manager = container.get('reward_manager')
       self.data_constructor = container.get('data_constructor')
       self.executor = container.get('executor')
   ```

### Task 2.2: Implement Input Validation ✅ COMPLETED

**Estimated Time**: 3 days
**Difficulty**: Intermediate
**Files to Create**: `absolute_zero_reasoner/validation/`

**Status**: ✅ **COMPLETED** - Comprehensive input validation system implemented with:
- Modular validation rules (TypeRule, RangeRule, RegexRule, LengthRule, ChoiceRule, PathRule, SecurityRule)
- Validator class for combining multiple rules
- ConfigValidator for configuration dictionary validation
- Security validation to prevent code injection and dangerous patterns
- String sanitization utilities
- Pre-built validators for common use cases (training config, code execution)
- Comprehensive testing with test_validation.py

**Steps**:

1. **Create validation schemas**:
   ```python
   # Create absolute_zero_reasoner/validation/schemas.py
   from pydantic import BaseModel, validator
   from typing import List, Optional, Dict, Any

   class ExecutorConfig(BaseModel):
       """Executor configuration schema."""
       timeout_length: int = 10
       memory_limit: str = "256m"
       cpu_limit: str = "1.0"
       use_container: bool = True

       @validator('timeout_length')
       def validate_timeout(cls, v):
           if v <= 0 or v > 300:  # Max 5 minutes
               raise ValueError('Timeout must be between 1 and 300 seconds')
           return v

   class TrainingConfig(BaseModel):
       """Training configuration schema."""
       batch_size: int
       learning_rate: float
       max_epochs: int

       @validator('batch_size')
       def validate_batch_size(cls, v):
           if v <= 0 or v > 10000:
               raise ValueError('Batch size must be between 1 and 10000')
           return v
   ```

2. **Add configuration validation**:
   ```python
   # Create absolute_zero_reasoner/validation/config_validator.py
   from omegaconf import OmegaConf
   from .schemas import ExecutorConfig, TrainingConfig

   def validate_config(config: OmegaConf) -> OmegaConf:
       """Validate configuration against schemas."""
       try:
           # Validate executor config
           if 'azr' in config and 'executor_config' in config.azr:
               ExecutorConfig(**config.azr.executor_config)

           # Validate training config
           if 'trainer' in config:
               TrainingConfig(
                   batch_size=config.data.train_batch_size,
                   learning_rate=config.actor_rollout_ref.actor.optim.lr,
                   max_epochs=config.trainer.total_epochs
               )

           return config

       except Exception as e:
           raise ConfigurationError(f"Configuration validation failed: {e}")
   ```

### Task 2.3: Add Comprehensive Testing ✅ COMPLETED

**Estimated Time**: 1 week
**Difficulty**: Intermediate
**Files to Create**: `tests/` directory structure

**Status**: ✅ **COMPLETED** - Comprehensive testing infrastructure implemented with:
- Professional test directory structure with tests/__init__.py
- Unit tests for validation system (27 test cases, 100% pass rate)
- Test runner script (run_tests.py) with coverage analysis and detailed reporting
- Support for running specific modules, classes, or methods
- Test coverage tracking and analysis
- Foundation for future test expansion
- All tests passing with comprehensive validation coverage

**Steps**:

1. **Create test directory structure**:
   ```bash
   mkdir -p tests/{unit,integration,fixtures}
   touch tests/__init__.py
   touch tests/conftest.py
   ```

2. **Create test fixtures**:
   ```python
   # tests/conftest.py
   import pytest
   from absolute_zero_reasoner.utils.code_utils.python_executor import PythonExecutor
   from absolute_zero_reasoner.container import DIContainer

   @pytest.fixture
   def executor():
       """Create a test executor."""
       return PythonExecutor(use_container=False)  # Use local for tests

   @pytest.fixture
   def container():
       """Create a test DI container."""
       container = DIContainer()
       container.register('executor', PythonExecutor(use_container=False))
       return container
   ```

3. **Create unit tests**:
   ```python
   # tests/unit/test_executor.py
   import pytest
   from absolute_zero_reasoner.utils.code_utils.python_executor import PythonExecutor

   def test_executor_basic_execution(executor):
       """Test basic code execution."""
       result, status = executor.apply("print('hello')")
       assert status == "success"
       assert "hello" in result

   def test_executor_security(executor):
       """Test security measures."""
       result, status = executor.apply("import os; os.system('echo test')")
       assert status == "security_error"

   def test_executor_timeout(executor):
       """Test timeout handling."""
       result, status = executor.apply("import time; time.sleep(100)")
       assert status == "timeout"
   ```

4. **Create integration tests**:
   ```python
   # tests/integration/test_training_pipeline.py
   import pytest
   from absolute_zero_reasoner.trainer.ppo.azr_ray_trainer import CodeIORayPPOTrainer

   def test_training_initialization(container):
       """Test trainer initialization."""
       # Mock configuration
       config = create_test_config()

       # Test trainer creation
       trainer = CodeIORayPPOTrainer(container=container, config=config)
       assert trainer is not None

   def test_reward_computation(container):
       """Test reward computation pipeline."""
       # Test data processing and reward computation
       pass
   ```

5. **Add test runner script**:
   ```python
   # run_tests.py
   import subprocess
   import sys

   def run_tests():
       """Run all tests with coverage."""
       cmd = [
           sys.executable, '-m', 'pytest',
           'tests/',
           '--cov=absolute_zero_reasoner',
           '--cov-report=html',
           '--cov-report=term-missing',
           '-v'
       ]

       result = subprocess.run(cmd)
       return result.returncode

   if __name__ == "__main__":
       exit_code = run_tests()
       sys.exit(exit_code)
   ```

## Priority 3: Medium Priority Issues (Fix within 3 months)

### Task 3.1: Performance Optimization

**Estimated Time**: 2 weeks
**Difficulty**: Advanced

**Steps**:

1. **Profile memory usage**:
   ```python
   # Create absolute_zero_reasoner/profiling/memory_profiler.py
   import psutil
   import tracemalloc
   from functools import wraps

   def profile_memory(func):
       """Decorator to profile memory usage."""
       @wraps(func)
       def wrapper(*args, **kwargs):
           tracemalloc.start()
           process = psutil.Process()

           # Before execution
           mem_before = process.memory_info().rss / 1024 / 1024  # MB

           result = func(*args, **kwargs)

           # After execution
           mem_after = process.memory_info().rss / 1024 / 1024  # MB
           current, peak = tracemalloc.get_traced_memory()
           tracemalloc.stop()

           print(f"Memory usage - Before: {mem_before:.2f}MB, After: {mem_after:.2f}MB")
           print(f"Peak memory: {peak / 1024 / 1024:.2f}MB")

           return result
       return wrapper
   ```

2. **Implement async I/O**:
   ```python
   # Create absolute_zero_reasoner/utils/async_io.py
   import asyncio
   import aiofiles
   from typing import List, Dict

   async def async_read_files(file_paths: List[str]) -> List[str]:
       """Read multiple files asynchronously."""
       async def read_file(path: str) -> str:
           async with aiofiles.open(path, 'r') as f:
               return await f.read()

       tasks = [read_file(path) for path in file_paths]
       return await asyncio.gather(*tasks)
   ```

### Task 3.2: Documentation Improvements

**Estimated Time**: 1 week
**Difficulty**: Beginner

**Steps**:

1. **Add comprehensive docstrings**:
   ```python
   # Example for CodeIORayPPOTrainer
   class CodeIORayPPOTrainer(ReasonRLRayPPOTrainer):
       """
       PPO trainer for CodeIO tasks using Ray distributed computing.

       This trainer implements the Absolute Zero algorithm for training language
       models on code reasoning tasks without external data. It uses reinforcement
       learning with PPO to optimize model performance on self-generated tasks.

       Args:
           past_epoch_window (int): Number of epochs to keep in memory for statistics.
           config: Configuration object containing training parameters.
           tokenizer: Tokenizer for text processing.
           processor: Optional data processor for multimodal data.
           role_worker_mapping: Mapping from roles to worker classes.
           resource_pool_manager: Manager for Ray resource pools.
           ray_worker_group_cls: Class for Ray worker groups.
           reward_fn: Function for computing rewards during training.
           val_reward_fn: Function for computing rewards during validation.

       Attributes:
           _supported_tasks: Set of supported task types.
           _past_epoch_window: Number of epochs to keep in memory.
           _executor: Code execution engine.
           dataset_manager: Ray actor for dataset management.

       Example:
           >>> config = load_config("azr_ppo_trainer.yaml")
           >>> trainer = CodeIORayPPOTrainer(config=config, ...)
           >>> trainer.init_workers()
           >>> trainer.fit()
       """
   ```

2. **Create API documentation**:
   ```bash
   # Install sphinx
   pip install sphinx sphinx-rtd-theme

   # Create docs directory
   mkdir docs
   cd docs
   sphinx-quickstart
   ```

## Summary

This implementation guide provides a comprehensive roadmap for addressing the critical issues identified in the Absolute Zero project code review. The tasks are prioritized by risk level and impact, with detailed step-by-step instructions suitable for developers of various skill levels.

**Key Priorities**:
1. **Security**: Fix code execution vulnerabilities immediately
2. **Stability**: Improve resource management and error handling
3. **Maintainability**: Refactor architecture and add testing
4. **Performance**: Optimize memory usage and I/O operations

**Estimated Timeline**:
- **Critical Issues**: 1 week
- **High Priority**: 1 month
- **Medium Priority**: 3 months

Each task includes specific file locations, code examples, and testing instructions to ensure successful implementation.
   ```