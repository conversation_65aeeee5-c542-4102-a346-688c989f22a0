#!/usr/bin/env python3
"""
Test script for secure code execution functionality.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'absolute_zero_reasoner'))

from absolute_zero_reasoner.utils.code_utils.python_executor import PythonExecutor, ContainerizedExecutor, SecurityError

def test_secure_execution():
    """Test secure code execution."""
    print("Testing Secure Code Execution")
    print("=" * 40)

    # Test 1: Normal code execution
    print("\n1. Testing normal code execution:")
    try:
        executor = PythonExecutor(use_secure_execution=True, executor_config={'use_docker': False})

        result, status = executor.apply_secure("print('Hello, secure world!')")
        print(f"   Result: {result.strip() if result else 'None'}")
        print(f"   Status: {status}")
    except Exception as e:
        print(f"   Error creating executor: {e}")
        import traceback
        traceback.print_exc()

    # Test 2: Security violation detection
    print("\n2. Testing security violation detection:")
    try:
        result, status = executor.apply_secure("import os; os.system('echo dangerous')")
        print(f"   Result: {result}")
        print(f"   Status: {status}")
    except Exception as e:
        print(f"   Exception caught: {e}")

    # Test 3: Mathematical computation
    print("\n3. Testing mathematical computation:")
    result, status = executor.apply_secure("result = 2 + 3 * 4\nprint(result)")
    print(f"   Result: {result.strip()}")
    print(f"   Status: {status}")

    # Test 4: Direct containerized executor
    print("\n4. Testing containerized executor (fallback mode):")
    container_executor = ContainerizedExecutor(use_docker=False)
    result, status = container_executor.execute_code("print('Container test')")
    print(f"   Result: {result.strip()}")
    print(f"   Status: {status}")

    print("\n" + "=" * 40)
    print("Secure execution tests completed!")

if __name__ == "__main__":
    test_secure_execution()