"""
Interface abstractions for Absolute Zero Reasoner.

This module defines abstract interfaces to reduce coupling between components
and improve testability and maintainability of the system.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple
from verl.protocol import DataProto
import torch


class RewardManager(ABC):
    """Abstract reward manager interface."""

    @abstractmethod
    def compute_rewards(self, data: DataProto, **kwargs) -> Tuple[torch.Tensor, Dict, List[Dict], List[Dict]]:
        """
        Compute rewards for the given data.

        Args:
            data: Input data protocol object
            **kwargs: Additional parameters for reward computation

        Returns:
            Tuple of (reward_tensor, scores_dict, data_dicts, valid_programs)
        """
        pass

    @abstractmethod
    def validate_input(self, data: DataProto) -> bool:
        """
        Validate input data format and content.

        Args:
            data: Input data to validate

        Returns:
            True if data is valid, False otherwise
        """
        pass


class DataConstructor(ABC):
    """Abstract data constructor interface."""

    @abstractmethod
    def generate_data(self, config: Dict[str, Any], num_samples: int = 1) -> List[Dict]:
        """
        Generate training data based on configuration.

        Args:
            config: Configuration parameters for data generation
            num_samples: Number of samples to generate

        Returns:
            List of generated data samples
        """
        pass

    @abstractmethod
    def validate_generated_data(self, data: List[Dict]) -> bool:
        """
        Validate generated data quality and format.

        Args:
            data: Generated data to validate

        Returns:
            True if data is valid, False otherwise
        """
        pass


class CodeExecutor(ABC):
    """Abstract code executor interface."""

    @abstractmethod
    def execute(self, code: str, inputs: str = "", timeout: int = 10) -> Tuple[str, str]:
        """
        Execute code and return results.

        Args:
            code: Code to execute
            inputs: Input data for the code
            timeout: Maximum execution time in seconds

        Returns:
            Tuple of (output, status) where status indicates success/failure
        """
        pass

    @abstractmethod
    def validate_code(self, code: str) -> bool:
        """
        Validate code for security and syntax issues.

        Args:
            code: Code to validate

        Returns:
            True if code is safe to execute, False otherwise
        """
        pass

    @abstractmethod
    def cleanup(self) -> None:
        """Clean up executor resources."""
        pass


class ModelManager(ABC):
    """Abstract model manager interface."""

    @abstractmethod
    def load_model(self, model_path: str, **kwargs) -> Any:
        """
        Load a model from the specified path.

        Args:
            model_path: Path to the model
            **kwargs: Additional loading parameters

        Returns:
            Loaded model object
        """
        pass

    @abstractmethod
    def save_model(self, model: Any, save_path: str, **kwargs) -> bool:
        """
        Save a model to the specified path.

        Args:
            model: Model object to save
            save_path: Path where to save the model
            **kwargs: Additional saving parameters

        Returns:
            True if save was successful, False otherwise
        """
        pass

    @abstractmethod
    def validate_model(self, model: Any) -> bool:
        """
        Validate model integrity and compatibility.

        Args:
            model: Model to validate

        Returns:
            True if model is valid, False otherwise
        """
        pass


class ConfigurationManager(ABC):
    """Abstract configuration manager interface."""

    @abstractmethod
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load configuration from file.

        Args:
            config_path: Path to configuration file

        Returns:
            Configuration dictionary
        """
        pass

    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate configuration parameters.

        Args:
            config: Configuration to validate

        Returns:
            True if configuration is valid, False otherwise
        """
        pass

    @abstractmethod
    def merge_configs(self, base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge two configuration dictionaries.

        Args:
            base_config: Base configuration
            override_config: Override configuration

        Returns:
            Merged configuration
        """
        pass


class DatasetManager(ABC):
    """Abstract dataset manager interface."""

    @abstractmethod
    def load_dataset(self, dataset_path: str, **kwargs) -> Any:
        """
        Load dataset from path.

        Args:
            dataset_path: Path to dataset
            **kwargs: Additional loading parameters

        Returns:
            Loaded dataset object
        """
        pass

    @abstractmethod
    def preprocess_data(self, data: Any, **kwargs) -> Any:
        """
        Preprocess dataset for training.

        Args:
            data: Raw dataset
            **kwargs: Preprocessing parameters

        Returns:
            Preprocessed dataset
        """
        pass

    @abstractmethod
    def validate_dataset(self, dataset: Any) -> bool:
        """
        Validate dataset format and content.

        Args:
            dataset: Dataset to validate

        Returns:
            True if dataset is valid, False otherwise
        """
        pass


class MetricsCollector(ABC):
    """Abstract metrics collector interface."""

    @abstractmethod
    def collect_metrics(self, **kwargs) -> Dict[str, Any]:
        """
        Collect system and training metrics.

        Args:
            **kwargs: Parameters for metric collection

        Returns:
            Dictionary of collected metrics
        """
        pass

    @abstractmethod
    def log_metrics(self, metrics: Dict[str, Any], step: Optional[int] = None) -> None:
        """
        Log metrics to tracking system.

        Args:
            metrics: Metrics to log
            step: Training step number
        """
        pass