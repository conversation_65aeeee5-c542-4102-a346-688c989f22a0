"""
Logging configuration for Absolute Zero Reasoner.

This module provides comprehensive logging setup with structured logging,
error tracking, and performance monitoring capabilities.
"""

import logging
import logging.handlers
import sys
import os
import traceback
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""

    def format(self, record):
        # Add structured information to log records
        if not hasattr(record, 'component'):
            record.component = getattr(record, 'name', 'unknown').split('.')[-1]

        if not hasattr(record, 'timestamp'):
            record.timestamp = datetime.utcnow().isoformat()

        # Format the message
        formatted = super().format(record)

        # Add exception information if present
        if record.exc_info:
            formatted += f"\nException: {self.formatException(record.exc_info)}"

        return formatted


class ErrorTracker:
    """Track and categorize errors for debugging."""

    def __init__(self):
        self.error_counts = {}
        self.recent_errors = []
        self.max_recent_errors = 100

    def track_error(self, error: Exception, context: Dict[str, Any] = None):
        """Track an error occurrence."""
        error_type = type(error).__name__
        error_msg = str(error)

        # Count errors by type
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1

        # Store recent errors
        error_info = {
            'timestamp': datetime.utcnow().isoformat(),
            'type': error_type,
            'message': error_msg,
            'context': context or {},
            'traceback': traceback.format_exc()
        }

        self.recent_errors.append(error_info)
        if len(self.recent_errors) > self.max_recent_errors:
            self.recent_errors.pop(0)

    def get_error_summary(self) -> Dict[str, Any]:
        """Get a summary of tracked errors."""
        return {
            'error_counts': self.error_counts,
            'total_errors': sum(self.error_counts.values()),
            'recent_error_count': len(self.recent_errors),
            'most_common_errors': sorted(
                self.error_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:5]
        }


# Global error tracker instance
error_tracker = ErrorTracker()


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    log_dir: Optional[str] = None,
    enable_structured_logging: bool = True,
    enable_error_tracking: bool = True,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    Setup comprehensive logging configuration.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Specific log file path
        log_dir: Directory for log files
        enable_structured_logging: Enable structured logging format
        enable_error_tracking: Enable error tracking
        max_file_size: Maximum size of log files before rotation
        backup_count: Number of backup files to keep

    Returns:
        Configured root logger
    """

    # Create log directory if specified
    if log_dir:
        Path(log_dir).mkdir(parents=True, exist_ok=True)
        if not log_file:
            log_file = os.path.join(log_dir, "absolute_zero.log")

    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))

    # Clear existing handlers
    root_logger.handlers.clear()

    # Create formatters
    if enable_structured_logging:
        formatter = StructuredFormatter(
            '%(timestamp)s - %(component)s - %(levelname)s - %(message)s'
        )
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    root_logger.addHandler(console_handler)

    # File handler with rotation if specified
    if log_file:
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)  # File gets all messages
        root_logger.addHandler(file_handler)

    # Error tracking handler
    if enable_error_tracking:
        error_handler = ErrorTrackingHandler()
        error_handler.setLevel(logging.ERROR)
        root_logger.addHandler(error_handler)

    return root_logger


class ErrorTrackingHandler(logging.Handler):
    """Custom handler to track errors."""

    def emit(self, record):
        if record.exc_info:
            error = record.exc_info[1]
            context = {
                'logger': record.name,
                'function': record.funcName,
                'line': record.lineno,
                'message': record.getMessage()
            }
            error_tracker.track_error(error, context)


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name."""
    return logging.getLogger(name)


def log_exception(logger: logging.Logger, error: Exception, context: Dict[str, Any] = None):
    """Log an exception with context information."""
    logger.error(
        f"Exception occurred: {type(error).__name__}: {error}",
        exc_info=True,
        extra={'context': context or {}}
    )


def log_performance(logger: logging.Logger, operation: str, duration: float, **kwargs):
    """Log performance metrics."""
    logger.info(
        f"Performance: {operation} completed in {duration:.3f}s",
        extra={'operation': operation, 'duration': duration, **kwargs}
    )


def get_error_summary() -> Dict[str, Any]:
    """Get summary of tracked errors."""
    return error_tracker.get_error_summary()