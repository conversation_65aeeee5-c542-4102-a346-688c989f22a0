#!/usr/bin/env python3
"""
Test script for resource management improvements.
"""

import sys
import os
import gc
import torch
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'absolute_zero_reasoner'))

def test_resource_management():
    """Test resource management functionality."""
    print("Testing Resource Management")
    print("=" * 40)

    try:
        # Test memory monitoring
        print("\n1. Testing memory monitoring:")

        # Create a simple class with just the memory monitoring method
        class TestTrainer:
            def monitor_memory(self):
                import psutil
                memory_info = {}

                try:
                    # System memory
                    system_memory = psutil.virtual_memory()
                    memory_info['system_used_gb'] = system_memory.used / (1024**3)
                    memory_info['system_available_gb'] = system_memory.available / (1024**3)
                    memory_info['system_percent'] = system_memory.percent

                    # GPU memory
                    if torch.cuda.is_available():
                        for i in range(torch.cuda.device_count()):
                            allocated = torch.cuda.memory_allocated(i) / (1024**3)
                            reserved = torch.cuda.memory_reserved(i) / (1024**3)
                            memory_info[f'gpu_{i}_allocated_gb'] = allocated
                            memory_info[f'gpu_{i}_reserved_gb'] = reserved
                except Exception as e:
                    print(f"   Memory monitoring failed: {e}")

                return memory_info

        test_trainer = TestTrainer()
        memory_info = test_trainer.monitor_memory()

        print(f"   System memory: {memory_info.get('system_used_gb', 0):.2f}GB used")
        print(f"   System memory: {memory_info.get('system_percent', 0):.1f}% used")

        if torch.cuda.is_available():
            for key, value in memory_info.items():
                if 'gpu' in key and 'allocated' in key:
                    print(f"   {key}: {value:.2f}GB")
        else:
            print("   No CUDA devices available")

        # Test garbage collection
        print("\n2. Testing garbage collection:")
        before_gc = len(gc.get_objects())
        gc.collect()
        after_gc = len(gc.get_objects())
        print(f"   Objects before GC: {before_gc}")
        print(f"   Objects after GC: {after_gc}")
        print(f"   Objects collected: {before_gc - after_gc}")

        # Test CUDA cache clearing
        print("\n3. Testing CUDA cache management:")
        if torch.cuda.is_available():
            # Allocate some memory
            x = torch.randn(1000, 1000, device='cuda')
            allocated_before = torch.cuda.memory_allocated() / (1024**3)
            print(f"   Memory allocated: {allocated_before:.2f}GB")

            # Clear cache
            del x
            torch.cuda.empty_cache()
            allocated_after = torch.cuda.memory_allocated() / (1024**3)
            print(f"   Memory after cleanup: {allocated_after:.2f}GB")
            print(f"   Memory freed: {allocated_before - allocated_after:.2f}GB")
        else:
            print("   No CUDA devices available for testing")

        print("\n" + "=" * 40)
        print("Resource management tests completed!")

    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_resource_management()